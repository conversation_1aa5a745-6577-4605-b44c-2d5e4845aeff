{
  "version": "0.2.0",
  "configurations": [
    {
      "name": "API Notification (Python)",
      "type": "python",
      "request": "launch",
      "module": "uvicorn",
      "args": ["app.main:app", "--reload"],
      "cwd": "${workspaceFolder}/notification-api",
      "jinja": true,
      "justMyCode": true
    },
    {
      "name": "Notification (React)",
      "type": "node",
      "request": "launch",
      "cwd": "${workspaceFolder}/notifications-portal",
      "runtimeExecutable": "npm",
      "runtimeArgs": ["run", "dev"],
      "console": "integratedTerminal",
      "restart": true
    },
    {
      "name": "Function Processor (Python)",
      "type": "python",
      "request": "launch",
      "program": "${workspaceFolder}/notification-event-processor-function/handler.py",
      "args": ["ntf-event-processor"],
      "console": "integratedTerminal",
      "jinja": true,
      "justMyCode": true
    },
    {
      "name": "Function Dispatcher (Python)",
      "type": "python",
      "request": "launch",
      "program": "${workspaceFolder}/notification-message-dispatcher-function/handler.py",
      "args": ["ntf-message-dispatcher"],
      "console": "integratedTerminal",
      "jinja": true,
      "justMyCode": true
    },
    {
      "name": "Function Event Receiver (Python)",
      "type": "python",
      "request": "launch",
      "program": "${workspaceFolder}/notification-event-receiver-function/handler.py",
      "args": ["ntf-event-receiver"],
      "console": "integratedTerminal",
      "jinja": true,
      "justMyCode": true
    },
    {
      "name": "Function Infield Monitor (Python)",
      "type": "python",
      "request": "launch",
      "program": "${workspaceFolder}/notification-infield-monitor-function/handler.py",
      "args": ["ntf-infield-monitor"],
      "console": "integratedTerminal",
      "jinja": true,
      "justMyCode": true
    },
    {
      "name": "Deploy Function (Python)",
      "type": "python",
      "request": "launch",
      "program": "${workspaceFolder}/notification-function-deployment/upload-cognite-function.py",
      "args": ["ntf-event-processor"],
      "console": "integratedTerminal",
      "jinja": true,
      "justMyCode": true
    },
  ]
}
