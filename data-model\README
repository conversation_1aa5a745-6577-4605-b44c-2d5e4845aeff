Commands to publish the data model

dev: python main.py
test: PYTHON_ENV=test python main.py
stg: PYTHON_ENV=stg python main.py
prod: PYTHON_ENV=prod python main.py

# CHANGELOG
### 27/07/2024
1_10_0

- Adding "notificationReference" property to "NotificationRawEvent" to bind the new notification chat to the original notification

- Adding "newUpdateTime" property to "NotificationOnScreen" to update the date when there is a new comment in the notification chat

### 15/05/2024
1_9_0

- Adding "externalUsers" property to "NotificationTemplate" to flag when template is assigned to receive external users;

### 16/05/2024

1_9_0

- Adding "externalUsers" property to "NotificationTemplate" type to flag when template is assigned for external users;

- Adding "subscribedExternalUsers" property to "NotificationTemplate" type to list external users assigned to a template;

- Adding "externalUsers" property to "NotificationEvent" type to list external users assigned to a notification event;

- Adding "externalSubscribers" property to "NotificationDeliverable" type to list external users assigned to a notification event;

### 15/04/2024

1_8_3

- Updating DM with User Management DM v4_2_1 changes;

### 12/04/2024

1_8_2

- Adding "allUsers" property to "NotificationTemplate" to flag when template is assigned for all users;

### 12/04/2024

1_7_2

- Updating DM with User Management DM v4_2_0 changes;

### 03/04/2024

1_7_1

- NotificationRawEventLog:
  - Adding new field "sourceExternalId" to store generically the externalId from external Apps (not imported via DM)

### 02/02/2024

1_6_0

- Updating UM data model version reference, to workaround the Asset Hierarchy tables problems

### 02/12/2024:

1_3_5 to 1_5_0

- NotificationRawEventLog:
  - Adding "NotificationRawEventLog" type to store the processedData and processResult properties moved out from "NotificationRawEvent", with an "open" space (not PROT);
- NotificationOnScreen:
  - Adding property "event" to store the NotificationEvent source event;
- NotificationDeliverable:
  - Adding property "event" to store the NotificationEvent source event;

### 01/29/2024:

1_3_4

- NotificationTemplate:
  - Switching "NotificationRole" property to "NotificationUserRoleSite" due to business changes;

### 01/25/2024:

1_3_3

- NotificationTemplate:
  - Adding "Deleted" property to flag when a template is deleted (soft delete);

### 01/23/2024:

1_3_2

- Renaming and Adding new fields to "NotificationRawEvent";
- NotificationEvent:
  - Adding "RawEvent" field, to have their relationship;

### 01/23/2024:

1_3_1

- NotificationRawEvent:
  - Adding new type "NotificationRawEvent" to receive Raw Events as they come, before being processed;
- Updating type description for "NotificationEvent";

### 01/16/2024:

1_3_0

- Updating DM with User Management DM v2_1_0 changes;

### 01/16/2024:

1_2_5

- Adding new type "NotificationLastAccess" to store the user last access in notification onscreen page

### 01/16/2024:

1_2_3 / 1_2_4

- NotificationUser:
  - Renaming, and then removing the field "notificationScreenLastAccess" as it didn't work properly

### 01/16/2024:

1_2_2

- NotificationUser:
  - Adding "notificationScreenLastAccess" property, to store the user last access in notification onscreen page

### 12/29/2023:

1_2_1

- NotificationRole:
  - Removing "usersComplements" property due DM changes;

### 12/29/2023:

1_2_0

- UserRoleSite:
  - Adding "usersComplements" property;
- Updating DM with User Management DM v2_0_3 changes;

### 12/28/2023:

1_1_0

- NotificationTemplate:
  - Removing "templateExtensions" property added previously;
- Updating DM with User Management DM v2_0_2 changes;

### 12/27/2023:

1_0_9

- NotificationTemplate:
  - Adding "templateExtensions" property, to test relationship;

### 12/22/2023:

1_0_8

- NotificationTemplate:
  - Removing "templateExtensions" property;
- NotificationTemplateExtension:
  - Adding "template" property (inverting relationship);
- NotificationDeliverable:
  - Adding missing field "severity";

### 12/19/2023:

1_0_7

- NotificationDeliverable:

  - Fixing "reportingSite" property, to single item instead of array;

- NotificationOnScreen:
  - Fixing "reportingSite" property, to single item instead of array;

### 12/18/2023:

1_0_6

- NotificationDeliverable:

  - Property "reportingSite" added;
  - Adjusting type description

- NotificationOnScreen:
  - Adjusting property description;

### 12/16/2023:

1_0_5

- Data model:

  - Updated UM space version to v2_0_0;
  - Reimporting "User" updated version from UM;
  - Reimporting "UserAzureAttributes" updated version from UM;
  - Reimporting "Role" updated version from UM;

- NotificationRole:

  - Property "roleApplicationConfigurations" removed from UM model;

- NotificationUser:

  - Property "displayName" added from UM model;

- UserAzureAttribute
  - Property "phoneNumber" added from UM model;
  - Reimporting "Shift" updated version from UM;

### 12/14/2023:

1_0_4

- Data model:

  - Updated UM space version to v1_0_0, after UM DM being recreated;
  - Reimporting "GroupCapability" updated version from UM;
  - Reimporting "ShiftType" updated version from UM;
  - Reimporting "Shift" updated version from UM;

- Added "CapabilityConfiguration" and "CapabilityCategory" types imported from UM;

### 12/08/2023:

1_0_3

- NotificationEvent:
  - Adding missing "Severity" object;

### 12/07/2023:

1_0_2

- Data model:
  - Renaming old NTF-COR-ALL-DML references to new NTF-COR-ALL-DMD space name.

### 12/06/2023:

1_0_1 - DMD

- Data model:

  - Updated UM space version to v1_0_4;

- ApplicationGroup moved to UM datamodel;
- Added NotificationApplicationGroup extending from UM ApplicationGroup type;
- Fixing some types description

### 11/30/2023:

1_0_7

- NotificationTemplate:
  - Renaming "subscribedUserRoles" to "subscribedRoles";

### 11/29/2023:

1_0_6

- Types descriptions adjustments;
- Added new type "NotificationApplication", extending from UM

### 11/28/2023:

v1_0_5

- Type descriptions adjustments;
- Replaced wrong 'temp_notifications' space mentions to 'NTF-COR-ALL-DML';

### 11/23/2023

v1_0_3 / v1_0_4

- Renamed types "@code";
- Imported "User" and "Role" UM types;
- Role (from UM):
  - Type changed to interface;
- User (from UM):
  - Type changed to interface;
- NotificationRole:
  - Added extension from "Role" UM type;
- NotificationUser:
  - Added extension from "User" UM type;
