[{"externalId": "i<PERSON>.g<PERSON><PERSON><EMAIL>-SOFTWAREENGINEER_SITE_STS-CLK", "space": "@instances_space", "type": {"externalId": "NotificationUser.roles", "space": "@model_space"}, "startNode": {"externalId": "igor.gab<PERSON><EMAIL>", "space": "@instances_space"}, "endNode": {"externalId": "SOFTWAREENGINEER_SITE_STS-CLK", "space": "@instances_space"}}, {"externalId": "<EMAIL>-SOFTWAREENGINEER_SITE_STS-CLK", "space": "@instances_space", "type": {"externalId": "NotificationUser.roles", "space": "@model_space"}, "startNode": {"externalId": "<EMAIL>", "space": "@instances_space"}, "endNode": {"externalId": "SOFTWAREENGINEER_SITE_STS-CLK", "space": "@instances_space"}}, {"externalId": "<EMAIL>-SOFTWAREENGINEER_SITE_STS-CLK", "space": "@instances_space", "type": {"externalId": "NotificationUser.roles", "space": "@model_space"}, "startNode": {"externalId": "<EMAIL>", "space": "@instances_space"}, "endNode": {"externalId": "SOFTWAREENGINEER_SITE_STS-CLK", "space": "@instances_space"}}, {"externalId": "<EMAIL>-SOFTWAREENGINEER_SITE_STS-CLK", "space": "@instances_space", "type": {"externalId": "NotificationUser.roles", "space": "@model_space"}, "startNode": {"externalId": "<EMAIL>", "space": "@instances_space"}, "endNode": {"externalId": "SOFTWAREENGINEER_SITE_STS-CLK", "space": "@instances_space"}}, {"externalId": "<EMAIL>-SOFTWAREENGINEER_SITE_STS-CLK", "space": "@instances_space", "type": {"externalId": "NotificationUser.roles", "space": "@model_space"}, "startNode": {"externalId": "<EMAIL>", "space": "@instances_space"}, "endNode": {"externalId": "SOFTWAREENGINEER_SITE_STS-CLK", "space": "@instances_space"}}]