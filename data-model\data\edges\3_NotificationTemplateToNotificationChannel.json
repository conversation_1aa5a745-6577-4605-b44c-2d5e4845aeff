[{"externalId": "Template Teste 1-NTFCHN-SMS", "space": "@instances_space", "type": {"externalId": "NotificationTemplate.channels", "space": "@model_space"}, "startNode": {"externalId": "Template Teste 1", "space": "@instances_space"}, "endNode": {"externalId": "NTFCHN-SMS", "space": "@instances_space"}}, {"externalId": "Template Teste 1-NTFCHN-EMAIL", "space": "@instances_space", "type": {"externalId": "NotificationTemplate.channels", "space": "@model_space"}, "startNode": {"externalId": "Template Teste 1", "space": "@instances_space"}, "endNode": {"externalId": "NTFCHN-EMAIL", "space": "@instances_space"}}, {"externalId": "Template Teste 1-NTFCHN-TEAMS", "space": "@instances_space", "type": {"externalId": "NotificationTemplate.channels", "space": "@model_space"}, "startNode": {"externalId": "Template Teste 1", "space": "@instances_space"}, "endNode": {"externalId": "NTFCHN-TEAMS", "space": "@instances_space"}}, {"externalId": "Template Teste 2-Luciana-NTFCHN-SMS", "space": "@instances_space", "type": {"externalId": "NotificationTemplate.channels", "space": "@model_space"}, "startNode": {"externalId": "Template Teste 2-Lucian<PERSON>", "space": "@instances_space"}, "endNode": {"externalId": "NTFCHN-SMS", "space": "@instances_space"}}, {"externalId": "Template Teste 2-Luciana-NTFCHN-EMAIL", "space": "@instances_space", "type": {"externalId": "NotificationTemplate.channels", "space": "@model_space"}, "startNode": {"externalId": "Template Teste 2-Lucian<PERSON>", "space": "@instances_space"}, "endNode": {"externalId": "NTFCHN-EMAIL", "space": "@instances_space"}}, {"externalId": "Template Teste 2-Luciana-NTFCHN-TEAMS", "space": "@instances_space", "type": {"externalId": "NotificationTemplate.channels", "space": "@model_space"}, "startNode": {"externalId": "Template Teste 2-Lucian<PERSON>", "space": "@instances_space"}, "endNode": {"externalId": "NTFCHN-TEAMS", "space": "@instances_space"}}, {"externalId": "Template Teste 3-Luciana-NTFCHN-SMS", "space": "@instances_space", "type": {"externalId": "NotificationTemplate.channels", "space": "@model_space"}, "startNode": {"externalId": "Template Teste 3-<PERSON><PERSON>", "space": "@instances_space"}, "endNode": {"externalId": "NTFCHN-SMS", "space": "@instances_space"}}, {"externalId": "Template Teste 3-Luciana-NTFCHN-EMAIL", "space": "@instances_space", "type": {"externalId": "NotificationTemplate.channels", "space": "@model_space"}, "startNode": {"externalId": "Template Teste 3-<PERSON><PERSON>", "space": "@instances_space"}, "endNode": {"externalId": "NTFCHN-EMAIL", "space": "@instances_space"}}, {"externalId": "Template Teste 3-Luciana-NTFCHN-TEAMS", "space": "@instances_space", "type": {"externalId": "NotificationTemplate.channels", "space": "@model_space"}, "startNode": {"externalId": "Template Teste 3-<PERSON><PERSON>", "space": "@instances_space"}, "endNode": {"externalId": "NTFCHN-TEAMS", "space": "@instances_space"}}, {"externalId": "Template Teste 4 - Igor-NTFCHN-TEAMS", "space": "@instances_space", "type": {"externalId": "NotificationTemplate.channels", "space": "@model_space"}, "startNode": {"externalId": "NTFTMP-Teste4-Igor", "space": "@instances_space"}, "endNode": {"externalId": "NTFCHN-TEAMS", "space": "@instances_space"}}]