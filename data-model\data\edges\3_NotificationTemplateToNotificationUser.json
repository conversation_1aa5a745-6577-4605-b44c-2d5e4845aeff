[{"externalId": "Template Teste 2-<PERSON>a-<PERSON><PERSON><EMAIL>", "space": "@instances_space", "type": {"externalId": "NotificationTemplate.subscribedUsers", "space": "@model_space"}, "startNode": {"externalId": "Template Teste 2-Lucian<PERSON>", "space": "@instances_space"}, "endNode": {"externalId": "<EMAIL>", "space": "UMG-COR-ALL-DAT"}}, {"externalId": "Template Teste 4 - <PERSON>-<PERSON><PERSON>.gab<PERSON><EMAIL>", "space": "@instances_space", "type": {"externalId": "NotificationTemplate.subscribedUsers", "space": "@model_space"}, "startNode": {"externalId": "NTFTMP-Teste4-Igor", "space": "@instances_space"}, "endNode": {"externalId": "igor.gab<PERSON><EMAIL>", "space": "UMG-COR-ALL-DAT"}}]