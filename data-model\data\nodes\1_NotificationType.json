[{"externalId": "NTFTYP-ANOMALY-DETECTION-APP-NRG", "name": "Anomaly Detection", "description": "Anomaly Detection", "application": {"space": "UMG-COR-ALL-DAT", "externalId": "APP-NRG"}}, {"externalId": "NTFTYP-TOTAL-COST-APP-THR", "name": "Total Cost", "description": "Total Cost", "application": {"space": "UMG-COR-ALL-DAT", "externalId": "APP-THR"}}, {"externalId": "NTFTYP-IDEAS-APPROVAL-APP-IMG", "name": "Ideas Approval", "description": "Ideas Approval notification info", "application": {"space": "UMG-COR-ALL-DAT", "externalId": "APP-IMG"}}, {"externalId": "NTFTYP-DIRECT-USAGE-CALCULATED-APP-THR", "name": "Direct Usage Calculated", "description": "Direct Usage Calculated", "application": {"space": "UMG-COR-ALL-DAT", "externalId": "APP-THR"}}, {"externalId": "NTFTYP-BOILER-EFFICIENCY-APP-NRG", "name": "Boiler Efficiency", "description": "Boiler Efficiency Metric for Notifications", "application": {"space": "UMG-COR-ALL-DAT", "externalId": "APP-NRG"}}]