[{"externalId": "Template Teste 1", "name": "Template Teste 1", "creator": {"space": "@instances_space", "externalId": "<EMAIL>"}, "notificationType": {"space": "@instances_space", "externalId": "NTFTYP-ANOMALY-DETECTION"}, "text": "An anomaly was detected for site <sitename> - unit <unit> - utility <utility> .", "severity": {"space": "@instances_space", "externalId": "NTFSVT-HIGH"}, "conditionalExpression": "", "adminLevel": true, "customChannelEnabled": true, "customFrequencyEnabled": true, "frequencyCronExpression": "0 0 12 ? * * *"}, {"externalId": "Template Teste 2-Lucian<PERSON>", "name": "Template Teste 2 - Lucian<PERSON>", "creator": {"space": "@instances_space", "externalId": "<EMAIL>"}, "notificationType": {"space": "@instances_space", "externalId": "NTFTYP-ANOMALY-DETECTION"}, "text": "An anomaly was detected for site <sitename> - unit <unit> - utility <utility> .", "severity": {"space": "@instances_space", "externalId": "NTFSVT-HIGH"}, "conditionalExpression": "", "adminLevel": true, "customChannelEnabled": true, "customFrequencyEnabled": true, "frequencyCronExpression": "0 0 12 ? * * *"}, {"externalId": "Template Teste 3-<PERSON><PERSON>", "name": "Template Teste 3 - <PERSON><PERSON>", "creator": {"space": "@instances_space", "externalId": "<EMAIL>"}, "notificationType": {"space": "@instances_space", "externalId": "NTFTYP-ANOMALY-DETECTION"}, "text": "An anomaly was detected for site <sitename> - unit <unit> - utility <utility> .", "severity": {"space": "@instances_space", "externalId": "NTFSVT-HIGH"}, "conditionalExpression": "", "adminLevel": true, "customChannelEnabled": true, "customFrequencyEnabled": true, "frequencyCronExpression": "0 0 12 ? * * *"}, {"externalId": "NTFTMP-Teste4-Igor", "name": "Template Teste 4 - <PERSON>", "creator": {"space": "@instances_space", "externalId": "igor.gab<PERSON><EMAIL>"}, "notificationType": {"space": "@instances_space", "externalId": "NTFTYP-TOTAL-COST"}, "text": "The total cost for site <sitename> - unit <unit> - utility <utility> .", "severity": {"space": "@instances_space", "externalId": "NTFSVT-HIGH"}, "conditionalExpression": "", "adminLevel": true, "customChannelEnabled": true, "customFrequencyEnabled": true, "frequencyCronExpression": "0 0 12 ? * * *"}]