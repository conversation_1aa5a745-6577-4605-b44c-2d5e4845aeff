from infra.cognite_client_factory import CogniteClientFactory

from infra.env_variables import EnvVariables

from utils import (
    get_data_model_metadata
)


def main():
    space = "NTF-COR-ALL-DML"
    instances_space = "NTF-COR-ALL-DAT"
    external_id = "Notifications"
    versions = ["1_0_0","1_0_1","1_0_2","1_0_3","1_0_4","1_0_5","1_0_6","1_0_7","1_0_8"]

    env_variables = EnvVariables()
    cognite_client = CogniteClientFactory.create(env_variables)
    views_dm = cognite_client.data_modeling.views.list(space=space, limit=-1)
    views_instances = cognite_client.data_modeling.views.list(
        space=instances_space, limit=-1
    )
    views = [view for view in views_dm]
    views.extend([view for view in views_instances])
    entity_versions = {item.external_id: item.version for item in views}
    instances_all = cognite_client.data_modeling.instances.list()
    metadata = get_data_model_metadata(
        env_variables.cognite.data_model_metadata_node_path,
        env_variables.cognite.data_model_metadata_edge_path,
        env_variables.cognite.graphql_model_space,
        env_variables.cognite.graphql_instances_space
    )
    
    print(instances_all)

    if not entity_versions:
        print("Deleting spaces")
        cognite_client.data_modeling.spaces.delete(space=[instances_space])
        print("No data model to delete")
        return
    print("Deleting instances view")
    cognite_client.data_modeling.views.delete(
        [
            (instances_space, entity, version)
            for entity, version in entity_versions.items()
        ]
    )
    cognite_client.data_modeling.views.delete(
        [(space, entity, version) for entity, version in entity_versions.items()]
    )

    print("Deleting instances container")
    cognite_client.data_modeling.containers.delete(
        [(space, entity) for entity in entity_versions]
    )

    print("Deleting data model")
    for version in versions:
        cognite_client.data_modeling.data_models.delete((space, external_id, version))

    # print(f"Deleting nodes")
    # for item in metadata.nodes:
    #     cognite_client.data_modeling..populate_fdm_node(item.entity_name, item.json_content)

    print("Deleting spaces")
    cognite_client.data_modeling.spaces.delete(space=[instances_space])


main()
