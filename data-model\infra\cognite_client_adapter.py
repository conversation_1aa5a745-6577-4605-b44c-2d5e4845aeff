import json
from typing import Any, Dict, List, Optional, TypeVar

from cognite.client import CogniteClient
from cognite.client.data_classes import TimeSeries
from cognite.client.data_classes.data_modeling import (
    EdgeApply,
    NodeApply,
    NodeOrEdgeData,
    ViewId,
)
from pydantic import BaseModel, Field

from infra.env_variables import EnvVariables

T = TypeVar("T")


class UpserTimeseriesRequest(BaseModel):
    external_id: str
    name: str
    unit: str
    metadata: Optional[dict[str, str]] = Field(default=None)


class CogniteClientAdapter:
    def __init__(
        self, cognite_client: CogniteClient, env_variables: EnvVariables
    ) -> None:
        self._cognite_client = cognite_client
        self._fdm_model_space = env_variables.cognite.graphql_model_space
        self._fdm_instances_space = env_variables.cognite.graphql_instances_space
        self._data_set_id = env_variables.cognite.data_set_id
        self._entity_versions: Dict[str, str] = {}

    def populate_fdm_node(
        self,
        entity: str,
        list_properties: List[Dict[str, Any]],
    ):
        entity_versions = self.__get_entities_versions()
        nodes = self.__create_nodes(entity, entity_versions[entity], list_properties)
        chunks = self.__create_default_pagination(nodes)

        for chunk in chunks:
            try:
                self._cognite_client.data_modeling.instances.apply(
                    nodes=chunk,  # type: ignore
                    skip_on_version_conflict=False,
                    replace=True,
                    auto_create_direct_relations=True,
                )
            except Exception as e:
                print(e)

    def populate_fdm_edge(
        self, entity: str, list_properties: List[Dict[str, Any]]
    ) -> None:
        entity_versions = self.__get_entities_versions()
        edges = self.__create_edges(
            list_properties, entity, entity_versions.get(entity)
        )
        chunks = self.__create_default_pagination(edges)
        for chunk in chunks:
            try:
                self._cognite_client.data_modeling.instances.apply(
                    edges=chunk,  # type: ignore
                    skip_on_version_conflict=False,
                    replace=True,
                    auto_create_direct_relations=True,
                    auto_create_end_nodes=False,
                    auto_create_start_nodes=False,
                )
            except Exception as e:
                print(e)

    def __create_nodes(
        self, entity: str, version: str, list_properties: List[Dict[str, Any]]
    ):
        model_space = self._fdm_model_space
        instances_space = self._fdm_instances_space
        return [
            NodeApply(
                instances_space,
                payload["externalId"],
                sources=[
                    NodeOrEdgeData(
                        ViewId(model_space, entity, version),
                        {
                            key: value
                            for key, value in payload.items()
                            if key != "externalId"
                        },
                    )
                ],
            )
            for payload in list_properties
        ]

    def __create_edges(
        self,
        list_properties: List[Dict[str, Any]],
        entity: str,
        version: Optional[str],
    ):
        model_space = self._fdm_model_space
        instances_space = self._fdm_instances_space
        return [
            EdgeApply(
                instances_space,
                payload["externalId"],
                type=(model_space, payload["type"]["externalId"]),
                start_node=(
                    payload["startNode"]["space"],
                    payload["startNode"]["externalId"],
                ),
                end_node=(
                    payload["endNode"]["space"],
                    payload["endNode"]["externalId"],
                ),
                sources=[
                    NodeOrEdgeData(
                        ViewId(model_space, entity, version), payload["source"]
                    )
                ]
                if version and "source" in payload
                else None,
            )
            for payload in list_properties
        ]

    def __create_default_pagination(self, resources: list[T]) -> list[list[T]]:
        return [
            resources[1000 * i : 1000 * (i + 1)]
            for i in range(int(len(resources) / 1000) + 1)  # type: ignore
        ]

    def __get_timeseries_units(self, external_ids: List[str]) -> Dict[str, str]:
        existing_items = self._cognite_client.time_series.retrieve_multiple(
            external_ids=external_ids,
            ignore_unknown_ids=True,
        )
        return {item.external_id: item.unit for item in existing_items}  # type: ignore

    def __upsert_timeseries(self, requests: List[UpserTimeseriesRequest]) -> None:
        external_ids = [request.external_id for request in requests]
        if not external_ids:
            print("No timeseries to upsert")
            return
        existing_items = self._cognite_client.time_series.retrieve_multiple(
            external_ids=[request.external_id for request in requests],
            ignore_unknown_ids=True,
        )
        if len(existing_items) > 0:
            print(f"Updating {len(existing_items)} timeseries")
            for existing_item in existing_items:
                updated = next(
                    (
                        item
                        for item in requests
                        if item.external_id == existing_item.external_id
                    ),
                    None,
                )
                existing_item.data_set_id = self._data_set_id
                if updated:
                    existing_item.unit = updated.unit
                    existing_item.metadata = updated.metadata or {}
            self._cognite_client.time_series.update(existing_items)

        existing_items_external_ids = [item.external_id for item in existing_items]
        items = [
            TimeSeries(
                external_id=request.external_id,
                name=request.name,
                unit=request.unit,
                data_set_id=self._data_set_id,
                metadata=request.metadata,
            )
            for request in requests
            if request.external_id not in existing_items_external_ids
        ]
        if len(items) > 0:
            print(f"Creating {len(items)} timeseries")
            self._cognite_client.time_series.create(items)  # type: ignore

    def __get_entities_versions(self) -> Dict[str, str]:
        if len(self._entity_versions) > 0:
            return self._entity_versions

        print("getting entities version")

        self._entity_versions = {
            item.external_id: item.version
            for item in self._cognite_client.data_modeling.views.list(
                space=self._fdm_model_space, limit=1000
            )
        }
        if not self._entity_versions:
            raise ValueError("Could not retreive the entity versions")

        return self._entity_versions
