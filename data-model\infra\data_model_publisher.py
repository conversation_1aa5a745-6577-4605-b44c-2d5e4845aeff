import json
from typing import Any

import requests
from cognite.client import CogniteClient
from cognite.client.data_classes.data_modeling import View, SpaceApply

from infra.env_variables import EnvVariables


class DataModelPublisher:
    def __init__(self, cognite_client: CogniteClient, env_variables: EnvVariables):
        self._cognite_client = cognite_client
        self._space = env_variables.cognite.graphql_model_space
        self._instances_space = env_variables.cognite.graphql_instances_space
        self._external_id = env_variables.cognite.graphql_model_external_id
        self._name = env_variables.cognite.graphql_model_name
        self._version = env_variables.cognite.graphql_model_version

    def publish(
        self, dm: str, node_external_ids: list[str], edge_external_ids: list[str]
    ) -> None:
        views = self._get_views()
        # self._delete_instances(node_external_ids, edge_external_ids)
        # self._delete_data_model(views)
        # self._delete_space()
        # self._create_space()
        self._publish_data_model(dm)

    def _get_views(self) -> list[View]:
        views_dm = self._cognite_client.data_modeling.views.list(
            space=self._space, limit=-1
        )
        views_instances = self._cognite_client.data_modeling.views.list(
            space=self._instances_space, limit=-1
        )
        views = [view for view in views_dm]
        views.extend([view for view in views_instances])
        return views

    def _publish_data_model(self, dm: str) -> None:
        print(f"Publishing Data Model {self._external_id} ")
        payload = {
            "query": "mutation createUpdateDataModel($dmCreate: GraphQlDmlVersionUpsert!) {upsertGraphQlDmlVersion(graphQlDmlVersion: $dmCreate) {\n errors {\n kind\n message\n hint\n location {\n start {\n line\n column\n }\n }\n }\n result {\n \n space\n externalId\n version\n name\n description\n graphQlDml\n createdTime\n lastUpdatedTime\n \n }\n }\n }\n ",  # noqa E501
            "variables": {
                "dmCreate": {
                    "space": self._space,
                    "externalId": self._external_id,
                    "version": self._version,
                    "graphQlDml": dm,
                    "name": self._name,
                    "description": "Data Model for Notification solution",
                }
            },
        }
        public_dm = self._get_publish_data_model()
        result = self._send_request(public_dm, payload)

        result_errors = (
            result["data"]["upsertGraphQlDmlVersion"]["errors"]
            if (
                result
                and result["data"]["upsertGraphQlDmlVersion"]
                and result["data"]["upsertGraphQlDmlVersion"]["errors"]
            )
            else result.get("errors", None)
        )

        if result_errors is not None:
            print(json.dumps(result_errors))
            raise ValueError("Could not create data model")

        print(f"Successfully published the {self._external_id} of the Data Model.")

    def _delete_data_model(self, views: list[View]) -> None:
        try:
            entity_versions = {item.external_id: item.version for item in views}
            if not entity_versions:
                print("No data model to delete")
                return
            print("Deleting instances view")
            self._cognite_client.data_modeling.views.delete(
                [
                    (self._space, entity, version)
                    for entity, version in entity_versions.items()
                ]
            )

            print("Deleting instances container")
            self._cognite_client.data_modeling.containers.delete(
                [(self._space, entity) for entity in entity_versions]
            )

            print("Deleting data model")
            self._cognite_client.data_modeling.data_models.delete(
                (self._space, self._external_id, self._version)
            )
        except Exception as e:
            print(f"Could not delete data model {e}")

    def _delete_instances(
        self, node_external_ids: list[str], edge_external_ids: list[str]
    ) -> None:
        try:
            print(f"{len(edge_external_ids)} edges to delete")
            if edge_external_ids:
                self._cognite_client.data_modeling.instances.delete(
                    edges=[(self._instances_space, id) for id in edge_external_ids]
                )
        except Exception as e:
            print(f"Could not delete edges {e}")

        try:
            print("Deleting instances")
            print(f"{len(node_external_ids)} nodes to delete")
            if node_external_ids:
                self._cognite_client.data_modeling.instances.delete(
                    nodes=[(self._instances_space, id) for id in node_external_ids]
                )
        except Exception as e:
            print(f"Could not delete nodes {e}")

    def _get_publish_data_model(self) -> str:
        return (
            self._cognite_client.data_modeling._get_base_url_with_base_path()
            + "/dml/graphql"
        )

    def _get_base_http_header(self) -> dict[str, str]:
        key, value = self._cognite_client.config.credentials.authorization_header()
        return {key: value, "Content-Type": "application/json"}

    def _send_request(self, url: str, payload: Any) -> Any:
        response = requests.post(
            url,
            data=json.dumps(payload),
            headers=self._get_base_http_header(),
        )

        if response.status_code == 200:
            print(response.status_code)
        else:
            print(response.status_code)
            raise ValueError(response.content.decode())
        return json.loads(response.content.decode())

    def _delete_space(self):
        try:
            self._cognite_client.data_modeling.spaces.delete(
                space=[self._instances_space, self._space]
            )
        except Exception as e:
            print(e)

    def _create_space(self):
        try:
            spaces = [
                SpaceApply(space=self._instances_space),
                SpaceApply(space=self._space),
            ]
            res = self._cognite_client.data_modeling.spaces.apply(spaces)
        except Exception as e:
            print(e)
