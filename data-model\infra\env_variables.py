import os
from typing import List, Optional

from pydantic import Field
from pydantic_settings import BaseSettings, SettingsConfigDict

from infra.models import CogniteProjects

python_env = os.getenv("PYTHON_ENV") or "dev"


class AuthVariables(BaseSettings):
    model_config = SettingsConfigDict(
        env_file=(".env", ".env." + python_env), env_prefix="auth_", extra="ignore"
    )

    client_id: str
    tenant_id: str
    secret: str
    scopes_str: str = Field(alias="auth_scopes")
    token_uri: str
    token_override: Optional[str]

    @property
    def scopes(self) -> List[str]:
        return self.scopes_str.split(" ")


class CogniteVariables(BaseSettings):
    model_config = SettingsConfigDict(
        env_file=(".env", ".env." + python_env), env_prefix="cognite_", extra="ignore"
    )
    base_uri: str
    client_name: str
    graphql_uri_raw: str = Field(alias="cognite_graphql_uri")
    graphql_model_space: str
    graphql_instances_space: str
    data_set_id: int
    graphql_model_external_id: str
    graphql_model_name: str
    graphql_model_version: str
    create_data_model: bool
    create_instances: bool
    data_model_metadata_node_path: str
    data_model_metadata_edge_path: str
    data_model_path: str
    project: CogniteProjects
    create_missing_original_ts: bool

    @property
    def graphql_uri(self) -> str:
        return (
            self.graphql_uri_raw.replace("@model_space", self.graphql_model_space)
            .replace("@model_external_id", self.graphql_model_external_id)
            .replace("@model_version", self.graphql_model_version)
        )


class EnvVariables:
    def __init__(self) -> None:
        self.auth = AuthVariables()  # type: ignore
        self.cognite = CogniteVariables()  # type: ignore
