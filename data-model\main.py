import json
from pathlib import Path

from infra.cognite_client_adapter import CogniteClientAdapter
from infra.cognite_client_factory import CogniteClientFactory
from infra.data_model_publisher import DataModelPublisher
from infra.env_variables import EnvVariables
from utils import (
    get_data_model_metadata,
    get_json_content_by_entity_name,
    validate_data,
)


def main():
    env_variables = EnvVariables()
    cognite_client = CogniteClientFactory.create(env_variables)
    adapter = CogniteClientAdapter(cognite_client, env_variables)
    metadata = get_data_model_metadata(
        env_variables.cognite.data_model_metadata_node_path,
        env_variables.cognite.data_model_metadata_edge_path,
        env_variables.cognite.graphql_model_space,
        env_variables.cognite.graphql_instances_space,
    )
    # TODO: Adapt validation to Notifications Data Model
    # validate_data(metadata)

    if env_variables.cognite.create_data_model:
        dm = Path(env_variables.cognite.data_model_path).read_text()
        publisher = DataModelPublisher(cognite_client, env_variables)
        publisher.publish(
            dm, metadata.get_node_external_ids(), metadata.get_edge_external_ids()
        )

    if not env_variables.cognite.create_instances:
        return

    for item in metadata.nodes:
        print(f"upserting node {item.entity_name}")
        adapter.populate_fdm_node(item.entity_name, item.json_content)

    for item in metadata.edges:
        print(f"upserting edge {item.entity_name}")
        adapter.populate_fdm_edge(item.entity_name, item.json_content)


if __name__ == "__main__":
    main()
