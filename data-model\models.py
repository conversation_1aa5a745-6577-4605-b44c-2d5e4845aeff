from typing import Any

from pydantic import BaseModel


class DataModelNodeOrEdge(BaseModel):
    index: int
    entity_name: str
    json_content: list[dict[str, Any]]


class DataModelMetadata(BaseModel):
    nodes: list[DataModelNodeOrEdge]
    edges: list[DataModelNodeOrEdge]

    def get_node_external_ids(self) -> list[str]:
        print("node external ids")
        return DataModelMetadata._get_external_ids(self.nodes)

    def get_edge_external_ids(self) -> list[str]:
        print("edge external ids")
        return DataModelMetadata._get_external_ids(self.edges)

    @staticmethod  # type: ignore
    def _get_external_ids(values: list[DataModelNodeOrEdge]) -> list[str]:
        external_ids: list[str] = []
        list_items = [value.json_content for value in values]

        for items in list_items:
            try:
                for item in items:
                    print(f"item: {item}")
                external_ids.extend([item["externalId"] for item in items])
            except Exception as e:
                print(e)

        print(external_ids)
        return external_ids
