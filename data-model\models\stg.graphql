"""
@name NotificationType
@code NTFTYP
Notifications categories defined by applications
"""
type NotificationType{
    """
    @name Name
    """
    name: String!
    """
    @name Description
    """
    description: String
    """
    @name Application
    """
    application: NotificationApplication
}

"""
@name NotificationChannel
@code NTFCHN
Reference Data: Teams, Email, SMS
Channels to send notifications
"""
type NotificationChannel{
    """
    @name Name
    """
    name: String!
    """
    @name Description
    """
    description: String!
    """
    @name Templates
    """
    templates: [NotificationTemplate]  @relation(
        type: { space: "NTF-COR-ALL-DMD", externalId: "NotificationTemplate.channels" }
        direction: INWARDS
    )
}

"""
@name NotificationSeverity
@code NTFSVT
Reference Data: Low, Medium, High
Notifications severity
"""
type NotificationSeverity{
    """
    @name Name
    """
    name: String!
    """
    @name Description
    """
    description: String!
}

"""
@name NotificationLogicalOperator
@code NTFLOP
Reference Data: >, <, =
Logical operators
"""
type NotificationLogicalOperator{
    """
    @name Name
    """
    name: String!
    """
    @name Description
    """
    description: String!
}

"""
@name NotificationTemplate
@code NTFTMP
Notification template
"""
type NotificationTemplate{
    """
    @name Name
    """
    name: String!
    """
    @name Creator
    """
    creator: NotificationUser
    """
    @name: NotificationType
    """
    notificationType: NotificationType
    """
    @name Text
    """
    text: String!
    """
    @name Severity
    """
    severity: NotificationSeverity
    """
    @name ConditionalExpression
    """
    conditionalExpression: String
    """
    @name AdminLevel
    """
    adminLevel: Boolean
    """
    @name CustomChannelEnabled
    """
    customChannelEnabled: Boolean
    """
    @name CustomFrequencyEnabled
    """
    customFrequencyEnabled: Boolean
    """
    @name Channels
    """
    channels: [NotificationChannel]
    """
    @name FrequencyCronExpression
    """
    frequencyCronExpression: String
    """
    @name SubscribedUsers
    """
    subscribedUsers: [NotificationUser]
    """
    @name SubscribedRoles
    """
    subscribedRoles: [NotificationUserRoleSite]
    """
    @name SubscribedApplicationGroups
    """
    subscribedApplicationGroups: [NotificationApplicationGroup]
    """
    @name Deleted
    """
    deleted: Boolean
}

"""
@name NotificationTemplateExtension
@code NTFTPLEXT
Notification Template customized by user, containing only editable fields
"""
type NotificationTemplateExtension{
    """
    @name Owner
    """
    owner: NotificationUser
    """
    @name Channels
    """
    channels: [NotificationChannel]
    """
    @name FrequencyCronExpression
    """
    frequencyCronExpression: String
    """
    @name Template
    """
    template: NotificationTemplate
}

"""
@name NotificationRawEvent
@code NTFRAWEVT
Raw Source notification events (jsons) sent from other applications
"""
type NotificationRawEvent {
    """
    @name SourceApplication
    """
    sourceApplication: Application
    """
    @name Properties
    """
    sourceJson: JSONObject
    """
    @name ProcessedDate
    """
    processedDate: Timestamp
    """
    @name ProcessResult
    """
    processResult: String
}

"""
@name NotificationRawEventLog
@code NTFRAWEVTLOG
Processing Result for Raw notification events, sent from other applications
"""
type NotificationRawEventLog {
    """
    @name ProcessedDate
    """
    processedDate: Timestamp
    """
    @name ProcessResult
    """
    processResult: String
    """
    @name Raw Event
    """
    rawEvent: NotificationRawEvent
    """
    @name Source ExternalId (From Source App)
    """
    sourceExternalId: String
}

"""
@name NotificationEvent
@code NTFEVT
Notifications processing result from NotificationRawEvent
"""
type NotificationEvent{
    """
    @name NotificationType
    """
    notificationType: NotificationType
    """
    @name UserRoles
    """
    roles: [NotificationRole]
    """
    @name ApplicationGroups
    """
    applicationGroups: [NotificationApplicationGroup]
    """
    @name Users
    """
    users: [NotificationUser]
    """
    @name Severity
    """
    severity: NotificationSeverity
    """
    @name Properties
    """
    properties: JSONObject
    """
    @name Raw Event
    """
    rawEvent: NotificationRawEvent
}

"""
@name NotificationOnScreen
@code NTFOSC
Notifications exhibited in the screen of the related users
"""
type NotificationOnScreen{
    """
    @name Subscribers
    """
    subscribers: [NotificationUser]
    """
    @name Template
    """
    template: NotificationTemplate
    """
    @name Severity
    """
    severity: NotificationSeverity
    """
    @name Text
    """
    text: String!
    """
    @name Comments
    """
    comments: [NotificationComment]
    """
    @name Reporting Site
    """
    reportingSite: ReportingSite
    """
    @name Event
    """
    event: NotificationEvent
}

"""
@name NotificationDeliverable
@code NTFDLV
Notifications result after being processed from source applications and 
ready to be delivered
"""
type NotificationDeliverable{
    """
    @name Subscribers
    """
    subscribers: [NotificationUser]
    """
    @name Template
    """
    template: NotificationTemplate
    """
    @name Text
    """
    text: String!
    """
    @name Channel
    """
    channel: NotificationChannel
    """
    @name scheduleDate
    """
    scheduleDate: Timestamp
    """ 
    @name deliveryDate
    """
    deliveredDate: Timestamp
    """
    @name Reporting Site
    """
    reportingSite: ReportingSite
    """
    @name Severity
    """
    severity: NotificationSeverity
    """
    @name Event
    """
    event: NotificationEvent
}

"""
@name NotificationComment
@code NTFCMT
Subscribers comments in the notifications
"""
type NotificationComment{
    """
    @name User
    """
    user: NotificationUser
    """
    @name Comment
    """
    comment: String!
}

"""
@name ApplicationGroup
@code APPGRP
Groups of users, defined by applications
"""
type NotificationApplicationGroup implements ApplicationGroup {
    """@name Name"""
    name: String
    """@name Description"""
    description: String
    """@name User"""
    users: [User]
    """@name Application"""
    application: Application
    
    ############

    """
    @name Templates
    """
    templates: [NotificationTemplate]  @relation(
        type: { space: "NTF-COR-ALL-DMD", externalId: "NotificationTemplate.subscribedApplicationGroups" }
        direction: INWARDS
    )
}


"""
@name NotificationUser
@code NTFUSR
System users, extending from User Management to relate with Templates and NotificationsOnScreen
"""
type NotificationUser implements User {
    """@name Display name"""
    displayName: String
    """@name First Name"""
    firstName: String
    """@name Last Name"""
    lastName: String
    """@name Email"""
    email: String
    """@name Active"""
    active: Boolean

    ##########

    """
    @name Templates
    """
    templates: [NotificationTemplate]  @relation(
        type: { space: "NTF-COR-ALL-DMD", externalId: "NotificationTemplate.subscribedUsers" }
        direction: INWARDS
    )

    """
    @name NotificationsOnScreen
    """
    notificationsOnScreen: [NotificationOnScreen]  @relation(
        type: { space: "NTF-COR-ALL-DMD", externalId: "NotificationOnScreen.subscribers" }
        direction: INWARDS
    )
}

"""
@name NotificationRole
@code NTFUSRROLE
System roles, extending from User Management to relate with Templates
"""
type NotificationRole implements Role {
    """@name Name"""
    name: String
    """@name Description"""
    description: String
    """@name Code"""
    code: String
    """@name RoleCategory"""
    roleCategory: RoleCategory
    """@name Site"""
    site: ReportingSite
    """@name Application"""
    application: Application
    """@name Editable"""
    editable: Boolean
    ############

    """
    @name Templates
    """
    templates: [NotificationTemplate] @relation(
        type: { space: "NTF-COR-ALL-DMD", externalId: "NotificationTemplate.subscribedRoles" }
        direction: INWARDS
    )
}

"""
@name NotificationUserRoleSite
@code NTFUSRROLESTE
System roles, extending from User Management to relate with Templates
"""
type NotificationUserRoleSite implements UserRoleSite {
  """@name Role"""
  role: Role
  """@name Reporting Site"""
  reportingSite: ReportingSite
  """@name Users"""
  usersComplements: [UserComplement] @relation(type: {space: "UMG-COR-ALL-DMD", externalId: "UserComplement.userRoleSite"}, direction: INWARDS)

  #############

  """
  @name Templates
  """
  templates: [NotificationTemplate] @relation(
      type: { space: "NTF-COR-ALL-DMD", externalId: "NotificationTemplate.subscribedRoles" }
      direction: INWARDS
  )
}

"""
@name NotificationApplication
@code NTFAPP
System applications, extending from User Management
"""
type NotificationApplication implements Application {
  """@name Name"""
  name: String
  """@name Alias"""
  alias: String
  """@name Description"""
  description: String
  """@name Url"""
  url: String
  """@name Azure App Id"""
  azureAppId: String
  """@name Icon URL"""
  iconUrl: String
}

"""
@name NotificationLastAccess
@code NTFLAC
Last time the User visited the Notification home screen
"""
type NotificationLastAccess {
  """
  @name User
  """
  user: NotificationUser
  """
  @name Date
  """
  date: Timestamp
}


#region IMPORT BEGIN (UMG-COR-ALL-DMD:UserManagementDOM:3_1_1)
#  Space: UMG-COR-ALL-DMD
#  Data model: UserManagement (UserManagementDOM)
#  Version: 3_1_1

 """
@name User
@code USR
@Description Celanese User of one or more applications
"""
interface User @import(dataModel: {externalId: "UserManagementDOM", version: "3_1_1", space: "UMG-COR-ALL-DMD"}) {
  """@name Display name"""
  displayName: String
  """@name First Name"""
  firstName: String
  """@name Last Name"""
  lastName: String
  """@name Email"""
  email: String
  """@name Active"""
  active: Boolean
}

"""
@name User Azure Attributes
@code USRAZRATR
@Description Azure properties related to the User 
"""
type UserAzureAttribute @import(dataModel: {externalId: "UserManagementDOM", version: "3_1_1", space: "UMG-COR-ALL-DMD"}) {
  """@name User"""
  user: User
  """@name Azure User Id"""
  azureUserId: String
  """@name Celanese Lan ID"""
  lanId: String
  """@name User Avatar Image File"""
  avatar: File
  """@name Company Name"""
  companyName: String
  """@name Job Title"""
  jobTitle: String
  """@name Department"""
  department: String
  """@name Manager"""
  manager: User
  """@name Phone Number"""
  phoneNumber: String
}

"""
@name User Complement
@code USRCMP
@Description Additional properties related to the User
"""
type UserComplement @import(dataModel: {externalId: "UserManagementDOM", version: "3_1_1", space: "UMG-COR-ALL-DMD"}) {
  """@name User"""
  userAzureAttribute: UserAzureAttribute
  """@name User Role Site"""
  userRoleSite: [UserRoleSite]
  """@name Headcount Locations"""
  headcountLocations: ReportingLocation
  """@name Reporting Sites"""
  reportingSites: [ReportingSite]
  """@name Reporting Locations"""
  reportingLocations: [ReportingLocation]
  """@name Office Locations"""
  officeLocation: ReportingLocation
  """@name Reporting Units"""
  reportingUnits: [ReportingUnit]
  """@name On Site Manager"""
  onSiteManagement: User
  """@name Shift Configuration"""
  shiftConfiguration: ShiftConfiguration
  """@name Employee Badge Number"""
  employeeBadgeNumber: Int32
  """@name Employee Status"""
  employeeStatus: EmployeeStatus
  """@name Start Date"""
  startDate: Timestamp
}

"""
@name Employee Status
@code EMST
@Description Status of and Employee
"""
type EmployeeStatus @import(dataModel: {externalId: "UserManagementDOM", version: "3_1_1", space: "UMG-COR-ALL-DMD"}) {
  """@name Name"""
  name: String
  """@name Description"""
  description: String
}

"""
@name Application
@code APP
@Description Celanese's applications
"""
interface Application @import(dataModel: {externalId: "UserManagementDOM", version: "3_1_1", space: "UMG-COR-ALL-DMD"}) {
  """@name Name"""
  name: String
  """@name Alias"""
  alias: String
  """@name Description"""
  description: String
  """@name Url"""
  url: String
  """@name Azure App Id"""
  azureAppId: String
  """@name Icon URL"""
  iconUrl: String
}

"""
@name Application Feature
@code APPFE
@Description Feature (permission) for each application (Ex: page, table, components, etc.)
"""
type ApplicationFeature @import(dataModel: {externalId: "UserManagementDOM", version: "3_1_1", space: "UMG-COR-ALL-DMD"}) {
  """@name Name"""
  name: String
  """@name Description"""
  description: String
  """@name Code"""
  code: String
  """@name Hidden"""
  hidden: Boolean
  """@name Parent Feature"""
  parentFeature: ApplicationFeature
  """@name Application"""
  application: Application
}

"""
@name Role Category
@code ROLCAT
@Description Category/Level of each Role
"""
type RoleCategory @import(dataModel: {externalId: "UserManagementDOM", version: "3_1_1", space: "UMG-COR-ALL-DMD"}) {
  """@name Name"""
  name: String
  """@name Description"""
  description: String
}

"""
@name User Role Site
@code ROSI
@Description Relation between Role and Site to associate with an User
"""
interface UserRoleSite @import(dataModel: {externalId: "UserManagementDOM", version: "3_1_1", space: "UMG-COR-ALL-DMD"}) {
  """@name Role"""
  role: Role
  """@name Reporting Site"""
  reportingSite: ReportingSite
  """@name Users"""
  usersComplements: [UserComplement] @relation(type: {space: "UMG-COR-ALL-DMD", externalId: "UserComplement.userRoleSite"}, direction: INWARDS)
}

"""
@name Role
@code ROL
@Description Role (position) of every application
"""
interface Role @import(dataModel: {externalId: "UserManagementDOM", version: "3_1_1", space: "UMG-COR-ALL-DMD"}) {
  """@name Name"""
  name: String
  """@name Description"""
  description: String
  """@name Code"""
  code: String
  """@name RoleCategory"""
  roleCategory: RoleCategory
  """@name Site"""
  site: ReportingSite
  """@name Application"""
  application: Application
  """@name Editable"""
  editable: Boolean
}

"""
@name Role Application Configuration
@code ROLAPCONF
@Description Configuration for a role and an application and the permissions related to them
"""
type RoleApplicationConfiguration @import(dataModel: {externalId: "UserManagementDOM", version: "3_1_1", space: "UMG-COR-ALL-DMD"}) {
  """@name Role"""
  role: Role
  """@name Application"""
  application: Application
  """@name Application Feature Permission"""
  featurePermission: [ApplicationFeaturePermission]
}

"""
@name Application Feature Permission
@code APPFEPMS
@Description Level of access for an application feature
"""
type ApplicationFeaturePermission @import(dataModel: {externalId: "UserManagementDOM", version: "3_1_1", space: "UMG-COR-ALL-DMD"}) {
  """@name Feature"""
  applicationFeature: ApplicationFeature
  """@name Permission"""
  featurePermissionType: FeaturePermissionType
}

"""
@name Feature Permission Type
@code FEPMSTY
@Description List about the possible permissions of a Feature
"""
type FeaturePermissionType @import(dataModel: {externalId: "UserManagementDOM", version: "3_1_1", space: "UMG-COR-ALL-DMD"}) {
  """@name Name"""
  name: String
  """@name Description"""
  description: String
}

"""
@name ApplicationGroup
@code APPGRP
Groups of users, defined by applications
"""
interface ApplicationGroup @import(dataModel: {externalId: "UserManagementDOM", version: "3_1_1", space: "UMG-COR-ALL-DMD"}) {
  """@name Name"""
  name: String
  """@name Description"""
  description: String
  """@name User"""
  users: [User]
  """@name Application"""
  application: Application
}

"""
@name Group Capability
@code GRPCPB
@Description CDF Groups's Capabilities (name, type, scope, action)
"""
type GroupCapability @import(dataModel: {externalId: "UserManagementDOM", version: "3_1_1", space: "UMG-COR-ALL-DMD"}) {
  """@name Capability Configuration"""
  capabilityConfiguration: CapabilityConfiguration
  """@name Type"""
  type: CapabilityScopeType
  """@name Scope"""
  scopes: [CapabilityScope]
  """@name Action"""
  actions: [CapabilityAction]
}

"""
@name Capability Scope
@code CPBSCP
@Description Scopes of each Capabilities
"""
type CapabilityScope @import(dataModel: {externalId: "UserManagementDOM", version: "3_1_1", space: "UMG-COR-ALL-DMD"}) {
  """@name Name"""
  name: String
  """@name Value"""
  value: String
}

"""
@name Capability Configurtion
@code CPBCONF
@Description Configuration of a capability
"""
type CapabilityConfiguration @import(dataModel: {externalId: "UserManagementDOM", version: "3_1_1", space: "UMG-COR-ALL-DMD"}) {
  """@name Name"""
  name: String
  """@name API Name"""
  apiName: String
  """@name Description"""
  description: String
  """@name Category"""
  category: CapabilityCategory
  """@name Possible Actions"""
  possibleActions: [CapabilityAction]
  """@name Possible Scope Types"""
  possibleScopeTypes: [CapabilityScopeType]
}

"""
@name Capability Category
@code CPBCAT
@Description Capability Category
"""
type CapabilityCategory @import(dataModel: {externalId: "UserManagementDOM", version: "3_1_1", space: "UMG-COR-ALL-DMD"}) {
  """@name Name"""
  name: String
  """@name Description"""
  description: String
}

"""
@name Capability Scope Type
@code CPBSCPTP
@Description Types of each Capabilities's scope 
"""
type CapabilityScopeType @import(dataModel: {externalId: "UserManagementDOM", version: "3_1_1", space: "UMG-COR-ALL-DMD"}) {
  """@name Name"""
  name: String
  """@name Value"""
  value: String
}

"""
@name Capability Action
@code CPBACT
@Description Actions of each Capability
"""
type CapabilityAction @import(dataModel: {externalId: "UserManagementDOM", version: "3_1_1", space: "UMG-COR-ALL-DMD"}) {
  """@name Name"""
  name: String
  """@name Value"""
  value: String
}

"""
@name         Shift Type
@Description  Types of work shifts
@code         SHTY
"""
type ShiftType @import(dataModel: {externalId: "UserManagementDOM", version: "3_1_1", space: "UMG-COR-ALL-DMD"}) {
  name: String
  description: String
  createdBy: String
  modifiedBy: String
}

"""
@name         Shift Configuration
@Description  Configuration of shifts for various shift schedules.
@code         SHCO
"""
type ShiftConfiguration @import(dataModel: {externalId: "UserManagementDOM", version: "3_1_1", space: "UMG-COR-ALL-DMD"}) {
  shiftName: String
  refSite: ReportingSite
  refUnit: ReportingUnit
  refShiftType: ShiftType
  refShiftConfigurationPattern: [ShiftConfigurationPattern]
  createdBy: String
  modifiedBy: String
}

"""
@name         Shift Configuration Pattern
@Description  Template defining configurations for work shifts
@code         SHCP
"""
type ShiftConfigurationPattern @import(dataModel: {externalId: "UserManagementDOM", version: "3_1_1", space: "UMG-COR-ALL-DMD"}) {
  workType: String
  startTime: Timestamp
  endTime: Timestamp
  totalHours: Float
  startDay: String
  endDay: String
  patternType: String
  createdBy: String
  modifiedBy: String
}

"""
@name Functional Location
@code FLOC
An assembly of equipment that perform a physical or chemical process, including production, transportation and storage
"""
type FunctionalLocation @import(dataModel: {externalId: "UserManagementDOM", version: "3_1_1", space: "UMG-COR-ALL-DMD"}) {
  """ @name Name  """
  name: String
  """
   @name Description 
  @ description Long text describing the assigned object 
  """
  description: String
  """ @name Parent """
  parent: FunctionalLocation
  reportingSite: [ReportingSite] @relation(type: {space: "EDG-COR-ALL-DMD", externalId: "ReportingSite.functionalLocations"}, direction: INWARDS)
  """ @name Aliases  """
  aliases: [String]
  """ @name Changed By  """
  changedBy: String
  """ @name Changed Date """
  changedDate: Timestamp
  """ @name Company Code """
  companyCode: String
  """ @name Company Code Description """
  companyCodeDescription: String
  """ @name Cost Center """
  costCenter: String
  """ @name Cost Center Description """
  costCenterDescription: String
  """ @name Created By """
  createdBy: String
  """ @name Created Date """
  createdDate: Timestamp
  """ @name FDM Last Time Updated """
  fdmLastTimeUpdated: Timestamp
  """ @name Functional Location """
  functionalLocation: String
  """
   @name Status
  @description Status set by SAP informing a user that a function has been executed - cannot be deleted or changed 
  """
  functionalLocationStatus: String
  """
   @name Maintenance Plant
  @description Plant where the assigned object is located
  """
  maintenancePlant: String
  """ @name Maintenance Plant Description """
  maintenancePlantDescription: String
  """ @name Planner Group """
  plannerGroup: String
  """ @name Planner Group Description """
  plannerGroupDescription: String
  """
   @name Planning Plant 
  @description Plant with planning authority over the assigned object - may or may not be the same as Maintenance Plant
  """
  planningPlant: String
  """  @name Planning Plant Description """
  planningPlantDescription: String
  """ @name Plant Section """
  plantSection: String
  """ @name Plant Section Description """
  plantSectionDescription: String
  """ @name Reporting Units """
  reportingUnits: [ReportingUnit] @relation(type: {space: "EDG-COR-ALL-DMD", externalId: "ReportingUnit.refersTo"}, direction: INWARDS)
}

"""
@code RLN
@Description Reporting line used to filter AA data
"""
type ReportingLine @import(dataModel: {externalId: "UserManagementDOM", version: "3_1_1", space: "UMG-COR-ALL-DMD"}) {
  name: String
  description: String
  legacyBusinessLine: BusinessLineLegacy
  manufactures: [Product]
  processType: ProcessType
  reportingUnit: ReportingUnit
  aliases: [String]
  code: String
  reportingSites: [ReportingSite] @relation(type: {space: "EDG-COR-ALL-DMD", externalId: "ReportingSite.reportingLines"}, direction: INWARDS)
}

"""
@code PTY
@Description Code used to filter AA data
"""
type ProcessType @import(dataModel: {externalId: "UserManagementDOM", version: "3_1_1", space: "UMG-COR-ALL-DMD"}) {
  name: String
  description: String
  code: String
}

"""
@code BUL
@Description only 2 BusinessLine used for reporting at this point
"""
type BusinessLine @import(dataModel: {externalId: "UserManagementDOM", version: "3_1_1", space: "UMG-COR-ALL-DMD"}) {
  name: String!
  description: String
}

"""
@code BUL
@Description these are currently used and map into the BusinessLine
"""
type BusinessLineLegacy @import(dataModel: {externalId: "UserManagementDOM", version: "3_1_1", space: "UMG-COR-ALL-DMD"}) {
  name: String!
  description: String
  mapsTo: BusinessLine
}

"@code REG"
type GeoRegion @import(dataModel: {externalId: "UserManagementDOM", version: "3_1_1", space: "UMG-COR-ALL-DMD"}) {
  name: String!
  description: String
}

"@code CTR"
type Country @import(dataModel: {externalId: "UserManagementDOM", version: "3_1_1", space: "UMG-COR-ALL-DMD"}) {
  name: String!
  iso3166Alpha2: String!
  iso3166Alpha3: String!
  iso3166Numeric: String!
  parent: GeoRegion
  flag: String
}

"""
@code UNT
@Description This is similar to SAP L2 FLOC
"""
type ReportingUnit @import(dataModel: {externalId: "UserManagementDOM", version: "3_1_1", space: "UMG-COR-ALL-DMD"}) {
  name: String
  description: String
  newBusinessLine: BusinessLine
  legacyBusinessLine: BusinessLineLegacy
  refersTo: [FunctionalLocation]
  physicalAreas: [JSONObject]
  costCenter: [CostCenter]
  aliases: [String]
  reportingSites: [ReportingSite] @relation(type: {space: "EDG-COR-ALL-DMD", externalId: "ReportingSite.reportingUnits"}, direction: INWARDS)
}

type ReportingLocation @import(dataModel: {externalId: "UserManagementDOM", version: "3_1_1", space: "UMG-COR-ALL-DMD"}) {
  name: String
  description: String
  reportingUnit: ReportingUnit
  aliases: [String]
  reportingSites: [ReportingSite] @relation(type: {space: "EDG-COR-ALL-DMD", externalId: "ReportingSite.reportingLocations"}, direction: INWARDS)
}

"@code PRO"
type Product @import(dataModel: {externalId: "UserManagementDOM", version: "3_1_1", space: "UMG-COR-ALL-DMD"}) {
  name: String!
  description: String
}

"@code TZ"
type TimeZone @import(dataModel: {externalId: "UserManagementDOM", version: "3_1_1", space: "UMG-COR-ALL-DMD"}) {
  name: String!
  description: String
}

"@code STS"
type ReportingSite @import(dataModel: {externalId: "UserManagementDOM", version: "3_1_1", space: "UMG-COR-ALL-DMD"}) {
  name: String!
  description: String
  country: Country
  crs: CoordinateReferenceSystem
  functionalLocations: [FunctionalLocation]
  language: Language
  reportingLocations: [ReportingLocation]
  reportingUnits: [ReportingUnit]
  timeZone: TimeZone
  aliases: [String]
  city: String
  latitude: Float
  longitude: Float
  physicalAddress: String
  postalCode: String
  siteCode: String
  stateOrProvince: String
  reportingLines: [ReportingLine]
}

"@code LAN"
type Language @import(dataModel: {externalId: "UserManagementDOM", version: "3_1_1", space: "UMG-COR-ALL-DMD"}) {
  name: String!
  description: String
  LanCode: String
  sapCode: String
  enablonCode: String
}

"@code CRS"
type CoordinateReferenceSystem @import(dataModel: {externalId: "UserManagementDOM", version: "3_1_1", space: "UMG-COR-ALL-DMD"}) {
  id: String!
  name: String!
}

"""
@code COSTCN
@Description Cost Center in SAP
"""
type CostCenter @import(dataModel: {externalId: "UserManagementDOM", version: "3_1_1", space: "UMG-COR-ALL-DMD"}) {
  """@name Name  """
  name: String!
  """@name Description  """
  description: String
  """@name Category Code  """
  categoryCode: String
  """@name Company Code  """
  companyCode: String
  """@name Profit Center Code  """
  profitCenterCode: String
  """@name Country  """
  country: Country
}

#endregion IMPORT END