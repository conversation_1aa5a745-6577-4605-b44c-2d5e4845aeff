#  Notifications Data Model
#  Space: NTF-COR-ALL-DML
#  Data model: Notifications (Notifications)
#  Version: 1_0_0

# Reference Data

"""
@name Application
@code APP
Celanese's Applications
"""
type Application{
    """
    @name Name
    """
    name: String!
    """
    @name Description
    """
    description: String
}

"""
@name NotificationType
@code NTP
Notifications categories by application
"""
type NotificationType{
    """
    @name Name
    """
    name: String!
    """
    @name Description
    """
    description: String
    """
    @name Application
    """
    application: Application
}

"""
@name NotificationChannel
@code NCHN
Reference Data: Teams, Email, SMS
Channels to send notifications
"""
type NotificationChannel{
    """
    @name Name
    """
    name: String!
    """
    @name Description
    """
    description: String!
}

"""
@name NotificationSeverity
@code NSV
Reference Data: Low, Medium, High
Notifications severity
"""
type NotificationSeverity{
    """
    @name Name
    """
    name: String!
    """
    @name Description
    """
    description: String!
}

"""
@name NotificationLogicalOperator
@code NLOP
Reference Data: >, <, =
Logical operators
"""
type NotificationLogicalOperator{
    """
    @name Name
    """
    name: String!
    """
    @name Description
    """
    description: String!
}

"""
@name NotificationTemplate
@code NTEMP
Notification's template
"""
type NotificationTemplate{
    """
    @name Name
    """
    name: String!
    """
    @name Creator
    """
    creator: NotificationUser
    """
    @name: NotificationType
    """
    notificationType: NotificationType
    """
    @name Text
    """
    text: String!
    """
    @name Severity
    """
    severity: NotificationSeverity
    """
    @name ConditionalExpression
    """
    conditionalExpression: String
    """
    @name IsAdminLevel
    """
    adminLevel: Boolean
    """
    @name IsCustomChannelEnabled
    """
    customChannelEnabled: Boolean
    """
    @name IsCustomFrequencyEnabled
    """
    customFrequencyEnabled: Boolean
    """
    @name Channels
    """
    channels: [NotificationChannel]
    """
    @name FrequencyCronExpression
    """
    frequencyCronExpression: String
    """
    @name SubscribedUsers
    """
    subscribedUsers: [NotificationUser]
    """
    @name SubscribedUserRoles
    """
    subscribedUserRoles: [NotificationUserRole]
    """
    @name SubscribedApplicationGroups
    """
    subscribedApplicationGroups: [ApplicationGroup]
    """
    @name NotificationTemplateExtensions
    """
    notificationTemplateExtensions: [NotificationTemplateExtension]   
}

"""
@name NotificationTemplateExtension
@code NTEMPEX
Template customized by user
"""
type NotificationTemplateExtension{
    """
    @name OwnerUser
    """
    ownerUser: NotificationUser
    """
    @name Channels
    """
    channels: [NotificationChannel]
    """
    @name FrequencyCronExpression
    """
    frequencyCronExpression: String
}

"""
@name Notification
@code N
Notifications from applications
"""
type Notification{
    """
    @name Subscribers
    """
    subscribers: [NotificationUser]
    """
    @name Template
    """
    template: NotificationTemplate
    """
    @name Severity
    """
    severity: NotificationSeverity
    """
    @name Text
    """
    text: String!
    """
    @name Comments
    """
    comments: [NotificationComment]
}

"""
@name NotificationDeliverable
@code NTF
Notifications from applications
"""
type NotificationDeliverable{
    """
    @name Subscribers
    """
    subscribers: [NotificationUser]
    """
    @name Template
    """
    template: NotificationTemplate
    """
    @name Text
    """
    text: String!
    """
    @name Channel
    """
    channel: NotificationChannel
    """
    @name scheduleDate
    """
    scheduleDate: Timestamp
    """ 
    @name deliveryDate
    """
    deliveredDate: Timestamp
}

"""
@name NotificationComment
@code NTFC
Subscribers comments in notifications
"""
type NotificationComment{
    """
    @name User
    """
    user: NotificationUser
    """
    @name Comment
    """
    comment: String!
}

"""
@name ApplicationGroup
@code APPG
Groups defined by applications
"""
type ApplicationGroup{
    """
    @name Name
    """
    name: String!
        """
    @name Description
    """
    description: String!
    """
    @name User
    """
    users: [NotificationUser]
    """
    @name Application
    """
    application: Application
}

"""
@name RawNotification
@code RWN
Raw notifications sent from applications
"""
type RawNotification{
    """
    @name NotificationType
    """
    notificationType: NotificationType
    """
    @name UserRoles
    """
    userRoles: [NotificationUserRole]
    """
    @name ApplicationGroups
    """
    applicationGroups: [ApplicationGroup]
    """
    @name Users
    """
    users: [NotificationUser]
    """
    @name Users
    """
    properties: JSONObject
}

"""
@name NotificationUser
@code USER
System users
"""
type NotificationUser {
    """
    @name Name
    """
    name: String!
    """
    @name Email
    """
    email: String!
    """
    @name Active
    """
    active: Boolean!
    """
    @name Roles
    """
    roles: [NotificationUserRole]
}

"""
@name NotificationUserRole
@code UROLE
Reference Data: Admin, Engineer, Operator, and Field Engineer
"""
type NotificationUserRole {
    """
    @name Name
    """
    name: String!
    """
    @name Description
    """
    description: String!
}