===========================================
#Version 4.0
#Changes:
# - Rename DM
#    Notifications -> Notification

# - Change DM ExternalId
#    Notifications -> NotificationDOM

# - Rename tables
#    RawNotification -> NotificationEvent
#    Notification -> NotificationOnScreen

# - Create relational fields with NotificationTemplates in tables below
#    NotificationChannel
#    NotificationUserRole
#    NotificationUser
#    NotificationApplicationGroup

# - Create relational field with NotificationOnScreen in tables below
#    NotificationUser

# - Create relational field with NotificationUser in tables below
#    NotificationUserRole

# - Create field to simulate relationship between users and applications - it will be import from User Management in the future
#    Add Application in NotificationUserRole

===========================================
"""
@name Application
@code APP
Celanese's Applications - import from User Management in the future
"""
type Application{
    """
    @name Name
    """
    name: String!
    """
    @name Description
    """
    description: String
}

"""
@name NotificationType
@code NTP
Notifications categories by application
"""
type NotificationType{
    """
    @name Name
    """
    name: String!
    """
    @name Description
    """
    description: String
    """
    @name Application
    """
    application: Application
}

"""
@name NotificationChannel
@code NCHN
Reference Data: Teams, Email, SMS
Channels to send notifications
"""
type NotificationChannel{
    """
    @name Name
    """
    name: String!
    """
    @name Description
    """
    description: String!
    """
    @name Templates
    """
    templates: [NotificationTemplate]  @relation(
        type: { space: "NTF-COR-ALL-DML", externalId: "NotificationTemplate.channels" }
        direction: INWARDS
    )
}

"""
@name NotificationSeverity
@code NSV
Reference Data: Low, Medium, High
Notifications severity
"""
type NotificationSeverity{
    """
    @name Name
    """
    name: String!
    """
    @name Description
    """
    description: String!
}

"""
@name NotificationLogicalOperator
@code NLOP
Reference Data: >, <, =
Logical operators
"""
type NotificationLogicalOperator{
    """
    @name Name
    """
    name: String!
    """
    @name Description
    """
    description: String!
}

"""
@name NotificationTemplate
@code NTEMP
Notification's template
"""
type NotificationTemplate{
    """
    @name Name
    """
    name: String!
    """
    @name Creator
    """
    creator: NotificationUser
    """
    @name: NotificationType
    """
    notificationType: NotificationType
    """
    @name Text
    """
    text: String!
    """
    @name Severity
    """
    severity: NotificationSeverity
    """
    @name ConditionalExpression
    """
    conditionalExpression: String
    """
    @name AdminLevel
    """
    adminLevel: Boolean
    """
    @name CustomChannelEnabled
    """
    customChannelEnabled: Boolean
    """
    @name CustomFrequencyEnabled
    """
    customFrequencyEnabled: Boolean
    """
    @name Channels
    """
    channels: [NotificationChannel]
    """
    @name FrequencyCronExpression
    """
    frequencyCronExpression: String
    """
    @name SubscribedUsers
    """
    subscribedUsers: [NotificationUser]
    """
    @name SubscribedUserRoles
    """
    subscribedUserRoles: [NotificationUserRole]
    """
    @name SubscribedApplicationGroups
    """
    subscribedApplicationGroups: [ApplicationGroup]
    """
    @name NotificationTemplateExtensions
    """
    notificationTemplateExtensions: [NotificationTemplateExtension]   
}

"""
@name NotificationTemplateExtension
@code NTEMPEX
Template customized by user
"""
type NotificationTemplateExtension{
    """
    @name OwnerUser
    """
    ownerUser: NotificationUser
    """
    @name Channels
    """
    channels: [NotificationChannel]
    """
    @name FrequencyCronExpression
    """
    frequencyCronExpression: String
}

"""
@name NotificationOnScreen
@code NTFOS
Notifications from applications
"""
type NotificationOnScreen{
    """
    @name Subscribers
    """
    subscribers: [NotificationUser]
    """
    @name Template
    """
    template: NotificationTemplate
    """
    @name Severity
    """
    severity: NotificationSeverity
    """
    @name Text
    """
    text: String!
    """
    @name Comments
    """
    comments: [NotificationComment]
}

"""
@name NotificationDeliverable
@code NTFD
Notifications from applications
"""
type NotificationDeliverable{
    """
    @name Subscribers
    """
    subscribers: [NotificationUser]
    """
    @name Template
    """
    template: NotificationTemplate
    """
    @name Text
    """
    text: String!
    """
    @name Channel
    """
    channel: NotificationChannel
    """
    @name scheduleDate
    """
    scheduleDate: Timestamp
    """ 
    @name deliveryDate
    """
    deliveredDate: Timestamp
}

"""
@name NotificationComment
@code NTFC
Subscribers comments in notifications
"""
type NotificationComment{
    """
    @name User
    """
    user: NotificationUser
    """
    @name Comment
    """
    comment: String!
}

"""
@name ApplicationGroup
@code APPG
Groups defined by applications
"""
type ApplicationGroup{
    """
    @name Name
    """
    name: String!
        """
    @name Description
    """
    description: String!
    """
    @name User
    """
    users: [NotificationUser]
    """
    @name Application
    """
    application: Application
    """
    @name Templates
    """
    templates: [NotificationTemplate]  @relation(
        type: { space: "NTF-COR-ALL-DML", externalId: "NotificationTemplate.subscribedApplicationGroups" }
        direction: INWARDS
    )
}

"""
@name NotificationEvent
@code NTFE
Raw notifications sent from applications
"""
type NotificationEvent{
    """
    @name NotificationType
    """
    notificationType: NotificationType
    """
    @name UserRoles
    """
    userRoles: [NotificationUserRole]
    """
    @name ApplicationGroups
    """
    applicationGroups: [ApplicationGroup]
    """
    @name Users
    """
    users: [NotificationUser]
    """
    @name Properties
    """
    properties: JSONObject
}

"""
@name NotificationUser
@code USER
System users - import from User Management in the future
"""
type NotificationUser {
    """
    @name Name
    """
    name: String!
    """
    @name Email
    """
    email: String!
    """
    @name Active
    """
    active: Boolean!
    """
    @name Roles
    """
    roles: [NotificationUserRole]
    """
    @name Templates
    """
    templates: [NotificationTemplate]  @relation(
        type: { space: "NTF-COR-ALL-DML", externalId: "NotificationTemplate.subscribedUsers" }
        direction: INWARDS
    )
        """
    @name NotificationsOnScreen
    """
    notificationsOnScreen: [NotificationOnScreen]  @relation(
        type: { space: "NTF-COR-ALL-DML", externalId: "NotificationOnScreen.subscribers" }
        direction: INWARDS
    )    
}

"""
@name NotificationUserRole
@code UROLE
Reference Data: Admin, Engineer, Operator, and Field Engineer
System's roles - import from User Management in the future
"""
type NotificationUserRole {
    """
    @name Name
    """
    name: String!
    """
    @name Description
    """
    description: String!
    """
    @name Application
    """
    application: Application
    """
    @name Templates
    """
    templates: [NotificationTemplate]  @relation(
        type: { space: "NTF-COR-ALL-DML", externalId: "NotificationTemplate.subscribedUserRoles" }
        direction: INWARDS
    )
    """
    @name Users
    """
    users: [NotificationUser]  @relation(
        type: { space: "NTF-COR-ALL-DML", externalId: "NotificationUser.roles" }
        direction: INWARDS
    )
}