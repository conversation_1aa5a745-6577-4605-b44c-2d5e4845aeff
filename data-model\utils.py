import json
from pathlib import Path
from typing import Any, Union

from graphql import parse

from infra.env_variables import EnvVariables
from models import DataModelMetadata, DataModelNodeOrEdge


def get_json_value(file_name: str, model_space: str, instances_space: str) -> Any:
    with open(file_name) as f:
        content = f.read()
        json_content = content.replace("@instances_space", instances_space).replace(
            "@model_space", model_space
        )
        return json.loads(json_content)


def validate_data(metadata: DataModelMetadata) -> None:
    validate_model()
    tags = get_json_content_by_entity_name(metadata.nodes, "UtilityManagementTag")
    units = get_json_content_by_entity_name(metadata.nodes, "UtilityManagementUnit")
    allocations = get_json_content_by_entity_name(metadata.nodes, "UtilityAllocation")

    unit_ids: set[str] = {unit["externalId"] for unit in units}
    tag_ids: set[str] = {tag["externalId"] for tag in tags}

    validate_allocations(unit_ids, tag_ids, allocations)
    validate_tags(tags)


def validate_model():
    env_variables = EnvVariables()
    raw_model = open(env_variables.cognite.data_model_path, "r")
    raw_model_text = raw_model.read()
    parsed_model = parse(raw_model_text)
    code_list = []
    duplicated_codes = []
    has_error = False
    default_types = [
        "BusinessLine",
        "BusinessLineLegacy",
        "ReportingUnit",
        "ReportingLocation",
        "Product",
        "Cdf3dEntity",
        "Cdf3dModel",
        "Cdf3dConnectionProperties",
    ]
    for model in parsed_model.definitions:
        model_dict = model.to_dict()
        type_name = model_dict["name"]["value"]
        if type_name in default_types:
            continue
        description = model_dict["description"]["value"].split("\n")
        has_name = False
        has_code = False
        has_description = False
        for descr in description:
            if "@name" in descr:
                has_name = len(descr.split("@name")[1].strip()) > 0
            elif "@code" in descr:
                code = descr.split("@code")[1].strip()
                if code in code_list:
                    duplicated_codes.append(code)
                code_list.append(code)
                has_code = len(code) > 0
            else:
                has_description = len(descr.strip()) > 0
        if not (has_name and has_code and has_description):
            print(
                "The type "
                + type_name
                + " doesn't have @name, @code and/or description"
            )
            has_error = True
    if len(duplicated_codes) > 0:
        print("There are duplicated Type Codes: ", duplicated_codes)
        has_error = True
    if has_error:
        raise ValueError(
            "There are errors on the Model. Check the console for detailed information."
        )


def validate_tags(tags):
    for tag in tags:
        tag_id = tag["externalId"]
        is_constant = tag["tagType"]["externalId"] == "Constant"
        is_variable = tag["tagType"]["externalId"] == "Variable"
        has_default_uom = not is_none_or_empty(tag["defaultUom"])
        has_default_value = not is_none_or_empty(tag["defaultValue"])

        if is_constant and (not has_default_uom or not has_default_value):
            raise ValueError(f"Tag {tag_id} is incorret")
        if not is_constant and (has_default_uom or has_default_value):
            raise ValueError(f"Tag {tag_id} is incorret")
        if is_variable and (
            not tag["originalTimeseries"] or not tag["overrideTimeseries"]
        ):
            raise ValueError(f"Tag {tag_id} is incorret")


def validate_allocations(
    unit_ids: set[str], tag_ids: set[str], allocations: list[dict[str, Any]]
):
    for allocation in allocations:
        if allocation["tag"]["externalId"] not in tag_ids:
            raise ValueError(f"Tag {allocation['tag']['externalId']} not found")
        if allocation["unit"]["externalId"] not in unit_ids:
            raise ValueError(f"Unit {allocation['unit']['externalId']} not found")


def is_none_or_empty(value: Union[str, float, None]) -> bool:
    return value is None or value == ""


def get_data_model_metadata(
    node_path: str, edge_path: str, model_space: str, instances_space: str
) -> DataModelMetadata:
    nodes = __get_node_or_edge_list(node_path, model_space, instances_space)
    edges = __get_node_or_edge_list(edge_path, model_space, instances_space)

    return DataModelMetadata(nodes=nodes, edges=edges)


def __get_node_or_edge_list(
    base_path: str, model_space: str, instances_space: str
) -> list[DataModelNodeOrEdge]:
    result: list[DataModelNodeOrEdge] = []
    for file in Path(base_path).iterdir():
        id_spplited = file.stem.split("_")
        result.append(
            DataModelNodeOrEdge(
                index=int(id_spplited[0]),
                entity_name=id_spplited[1],
                json_content=get_json_value(
                    f"{base_path}/{file.name}", model_space, instances_space
                ),
            )
        )
    return sorted(result, key=lambda x: x.index)


def get_json_content_by_entity_name(
    metadata: list[DataModelNodeOrEdge],
    entity_name: str,
):
    return [
        model.json_content for model in metadata if model.entity_name == entity_name
    ][
        0
    ]  # type: ignore
