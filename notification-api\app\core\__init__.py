from app.core.cognite_client_factory import (
    CogniteClientFactory as _cogniteClientFactory,
)
from app.core.env_variables import EnvVariables as _envVariables
from app.core.gql_client_factory import GqlClientFactory as _gqlClientFactory
from app.core.graphql_client import GraphQLClient as _graphQLClient
from app.core.global_dependencies import IBox

import app.repositories as _repositories
import app.models as models
import app.services as _services
import app.queries as queries

env = _envVariables()


# REPOSITORIOS
class _RepositoryList:
    def __init__(
        self,
    ):
        _cognite_client = _cogniteClientFactory.create(env)
        _gql_client_factory = _gqlClientFactory.create(_cognite_client, env)
        _gql_client = _graphQLClient(_gql_client_factory)
        self.application = _repositories.ApplicationRepository(
            _cognite_client, _gql_client, env
        )
        self.channels = _repositories.ChannelsRepository(
            _cognite_client, _gql_client, env
        )
        self.notification_type = _repositories.NotificationTypeRepository(
            _cognite_client, _gql_client, env
        )
        self.notification_template = _repositories.NotificationTemplateRepository(
            _cognite_client, _gql_client, env
        )
        self.notification_severities = _repositories.SeveritiesRepository(
            _cognite_client, _gql_client, env
        )
        self.notification_event = _repositories.NotificationEventRepository(
            _cognite_client, _gql_client, env
        )
        self.user = _repositories.UserRepository(_cognite_client, _gql_client, env)
        self.role = _repositories.RoleRepository(_cognite_client, _gql_client, env)
        self.reporting_site = _repositories.ReportingSiteRepository(_cognite_client, _gql_client, env)
        self.reporting_location = _repositories.ReportingLocationRepository(_cognite_client, _gql_client, env)
        self.team = _repositories.TeamRepository(_cognite_client, _gql_client, env)
        self.notification_template_extension = (
            _repositories.NotificationTemplateExtensionRepository(
                _cognite_client, _gql_client, env
            )
        )
        self.notification_on_screen = _repositories.NotificationOnScreenRepository(
            _cognite_client, _gql_client, env
        )
        self.notification_last_access = _repositories.NotificationLastAccessRepository(
            _cognite_client, _gql_client, env
        )
        self.timeseries = _repositories.TimeseriesRepository(
            _cognite_client
        )
        self.notification_application_group = _repositories.NotificationApplicationGroupRepository(
            _cognite_client, _gql_client, env
        )


# SERVICES
class _ServiceList:
    def __init__(
        self,
    ):
        repository = _RepositoryList()
        self.application = _services.ApplicationService(repository.application)
        self.notification_type = _services.NotificationTypeService(
            repository.notification_type
        )
        self.notification_template = _services.NotificationTemplateService(
            repository.notification_template
        )
        self.channels = _services.ChannelsService(repository.channels)
        self.severities = _services.SeveritiesService(
            repository.notification_severities
        )
        self.notification_event = _services.NotificationEventService(
            repository.notification_event, repository.application
        )
        self.user = _services.UserService(repository.user)
        self.role = _services.RoleService(repository.role)
        self.reporting_site = _services.ReportingSiteService(repository.reporting_site)
        self.reporting_location = _services.ReportingLocationService(repository.reporting_location)
        self.team = _services.TeamService(repository.team)
        self.notification_template_extension = (
            _services.NotificationTemplateExtensionService(
                repository.notification_template_extension
            )
        )

        self.notification_on_screen_service = _services.NotificationOnScreenService(
            repository.notification_on_screen,
            repository.notification_last_access
        )

        self.notification_last_access_service = _services.NotificationLastAccessService(
            repository.notification_last_access
        )
        self.timeseries_service = _services.TimeseriesService(
            repository.timeseries, env
        )
        self.notification_application_group = _services.NotificationApplicationGroupService(
            repository.notification_application_group
        )


def repositories() -> _RepositoryList:
    return _RepositoryList()


def services() -> _ServiceList:
    return _ServiceList()
