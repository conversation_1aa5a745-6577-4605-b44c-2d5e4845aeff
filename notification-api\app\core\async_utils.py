import typing
from typing import Annotated, Any, Callable, ParamSpec, TypeVar

from anyio import to_thread
from typing_extensions import Doc

T_Retval = TypeVar("T_Retval")
P = ParamSpec("P")


async def run_sync(
        func: Callable[..., T_Retval],
        *args: object,
        cancellable: bool = False,
):
    return await to_thread.run_sync(func, *args, cancellable=cancellable)


class BackgroundTaskProtocol(typing.Protocol):
    def add_task(
            self,
            func: Annotated[
                Callable[P, Any],
                Doc(
                    """
                    The function to call after the response is sent.
                    It can be a regular `def` function or an `async def` function.
                    """
                ),
            ],
            *args: P.args,
            **kwargs: P.kwargs,
    ) -> None:
        """Protocol to the fastapi Background task"""
