from typing import Any
import uuid

import jwt
from fastapi import Depends, HTTPException, Request
from fastapi.security import HTT<PERSON>uthorizationCredentials, HTTPBearer

from app.core.azure_ad_verify_token import verify_jwt
from app.core.logger import logger
import app.core as core

clientConf = {
    "azure_ad_app_id": core.env.auth.vue_app_azure_ad_client_id,
    "azure_ad_jwks_uri": "https://login.microsoftonline.com/common/discovery/keys",
    "azure_ad_issuer_userLogin": f"https://login.microsoftonline.com/{core.env.auth.tenant_id}/v2.0",
    "azure_ad_issuer_servicePrincipal": "https://sts.windows.net/7a3c88ff-a5f6-449d-ac6d-e8e3aa508e37/"
}

user_key = core.env.auth.vue_app_azure_ad_user_key


class JWTBearer(HTTPBearer):
    def __init__(self, auto_error: bool = True):
        super(JW<PERSON><PERSON>ear<PERSON>, self).__init__(auto_error=auto_error)

    async def __call__(self, request: Request):
        credentials: HTTPAuthorizationCredentials = await super(JWTBearer, self).__call__(request)
        if credentials:
            if not credentials.scheme == "Bearer":
                raise HTTPException(status_code=403, detail="Invalid authentication scheme.")
            payload = self.verify_jwt(credentials.credentials)
            if "Exception" in payload:
                raise HTTPException(status_code=403, detail=payload["Message"])
            
            # TREATMENT TO DEFINE EMAIL ALWAYS AS LOWER TEXT
            if payload.get("preferred_username") is not None and payload.get("preferred_username") != "":
                payload["preferred_username"] = str(payload.get("preferred_username"))

            if ((not payload.get("appId") and not payload.get("aud")) or 
                (payload.get("aud") and not self.is_valid_uuid(payload.get("aud")))):
                raise HTTPException(status_code=403, detail="Invalid authentication token.")

            return payload
        else:
            raise HTTPException(status_code=403, detail="Invalid authorization code.")

    def verify_jwt(self, jwtoken: str) -> Any:
        try:
            try:
                if self._is_provider_msal(jwtoken):
                    return self._verify_msal(jwtoken)
                else:
                    return self._verify_password(jwtoken)
            except:
                return self._verify_msal_servicePrincipal(jwtoken)
        except Exception as e:
            payload = {"Exception": True, "Message": str(e)}
        return payload

    def _is_provider_msal(self, jwttoken):
        headers = jwt.get_unverified_header(jwttoken)
        if "provider" in headers and headers["provider"] == "FMNH":
            return False
        else:
            return True

    def _verify_msal(self, jwttoken):
        return verify_jwt(
            token=jwttoken,
            valid_audiences=clientConf["azure_ad_app_id"],
            issuer=clientConf["azure_ad_issuer_userLogin"],
            jwks_uri=clientConf["azure_ad_jwks_uri"],
            verify=True,
        )
    
    def _verify_msal_servicePrincipal(self, jwttoken):
        return verify_jwt(
            token=jwttoken,
            valid_audiences=clientConf["azure_ad_app_id"],
            issuer=clientConf["azure_ad_issuer_servicePrincipal"],
            jwks_uri=clientConf["azure_ad_jwks_uri"],
            verify=True,
        )

    def _verify_password(self, jwttoken):
        return jwt.decode(
            jwt=jwttoken,
            # s["VUE_APP_PASSWORD_TOKEN_KEY"],
            algorithms="HS256",
            audience=clientConf["azure_ad_app_id"],
            issuer=clientConf["azure_ad_issuer_userLogin"],
        )
    
    def is_valid_uuid(self, val):
        try:
            uuid.UUID(str(val))
            return True
        except ValueError:
            return False


get_user = JWTBearer()


class UserHasPermission(object):
    def __init__(self, permission):
        self.permission = permission

    def __call__(self, request: Request, user=Depends(get_user)):
        # print(user)
        permissions_from_db = request.app.state.permissions.get(user[user_key], [])
        permissions_from_ad = user.get("roles", [])
        permissions = list(set(permissions_from_db + permissions_from_ad))
        logger.info(f"{user[user_key]} - {len(permissions)} Permissions - {', '.join(permissions)}")
        if self.permission in permissions:
            logger.info(f"User has {self.permission} permission")
            return None
        else:
            logger.error(f"User rejected - No {self.permission} permission")
            raise HTTPException(status_code=403, detail=f"You need {self.permission} permission")
