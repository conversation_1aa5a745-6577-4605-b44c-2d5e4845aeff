import time
from typing import Any, Dict, Optional, List
from cachetools import TTLCache
from gql import gql
from app.core.cognite_client_factory import (
    CogniteClientFactory as _cogniteClientFactory,
)
from app.core.env_variables import EnvVariables as _envVariables
from app.core.gql_client_factory import GqlClientFactory as _gqlClientFactory
from app.core.graphql_client import GraphQLClient as _graphQLClient
from cognite.client.data_classes.data_modeling import (
    ViewId,
)
from cognite.client.data_classes.data_modeling.query import (
    Query,
    Select,
    NodeResultSetExpression,
    EdgeResultSetExpression,
    SourceSelector,
    InstanceSort,
)
from cognite.client.data_classes.filters import Equals, HasData, And


import threading
import logging
import app.repositories as _repositories
import app.core as core
import app.utils as Utils

logger = logging.getLogger(__name__)
env = _envVariables()


class CacheClient:
    _instance: Optional["CacheClient"] = None
    _lock: threading.Lock = threading.Lock()

    _token_refresh_interval: int = 3000
    _last_token_refresh: float = 0

    def __new__(cls, *args, **kwargs):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super(CacheClient, cls).__new__(cls)
        return cls._instance

    def __init__(self):
        if hasattr(self, "_initialized") and self._initialized:
            return

        self.cache = TTLCache(maxsize=1000, ttl=3600)

        self._initialize_clients()

        self._initialized = True

    def _initialize_clients(self):
        cognite_client = _cogniteClientFactory.create(env)
        gql_client_factory = _gqlClientFactory.create(cognite_client, env)
        graphql_client = _graphQLClient(gql_client_factory)

        self._cognite_client = cognite_client
        self._graphql_client = graphql_client

        self.reporting_site_repository = _repositories.ReportingSiteRepository(
            cognite_client, graphql_client, env
        )

        self._last_token_refresh = time.time()

        logger.info("API clients initialized with fresh tokens")

    def _check_token_expiration(self):
        """Check if token refresh is needed and reinitialize clients if necessary"""
        current_time = time.time()
        if current_time - self._last_token_refresh > self._token_refresh_interval:
            logger.info("Token expiration interval reached, refreshing connections")
            with self._lock:
                if (
                    current_time - self._last_token_refresh
                    > self._token_refresh_interval
                ):
                    self._initialize_clients()

    def initialize(self) -> "CacheClient":
        try:
            self.cache["cognite_views"] = self.__views_cognite()
            self.cache["channels"] = self.__fetch_channels()
            self.cache["severities"] = self.__fetch_severities()
            self.cache["reporting_sites"] = self.__fetch_reporting_site()
            self.cache["teams"] = self.__fetch_teams()

            logger.info("Cache initialized successfully")

        except Exception as e:
            logger.error(f"Error initializing cache: {str(e)}")

        return self

    def get(self, key: str) -> Any:
        if key in self.cache:
            logger.debug(f"Cache hit for '{key}'")
            return self.cache[key]

        logger.debug(f"Cache miss for '{key}', fetching from database")
        data = self._fetch_from_db(key)
        if data is not None:
            self.cache[key] = data

        return data

    def set(self, key: str, value: Any) -> None:
        self.cache[key] = value
        logger.debug(f"Value set in cache for '{key}'")

    def invalidate(self, key: str) -> None:
        if key in self.cache:
            del self.cache[key]
            logger.debug(f"Cache invalidated for '{key}'")

    def clear(self) -> "CacheClient":
        self.cache.clear()
        logger.info("Cache completely cleared")
        return self

    def get_ttl(self) -> int:
        return self.cache.ttl

    def get_size(self) -> int:
        return len(self.cache)

    def get_max_size(self) -> int:
        return self.cache.maxsize

    def get_keys(self) -> List[str]:
        return list(self.cache.keys())

    def _fetch_from_db(self, key: str) -> Any:

        self._check_token_expiration()

        try:
            fetch_methods = {
                "cognite_views": self.__views_cognite,
                "channels": self.__fetch_channels,
                "severities": self.__fetch_severities,
                "reporting_sites": self.__fetch_reporting_site,
                "teams": self.__fetch_teams,
            }

            if key in fetch_methods:
                logger.info(f"Fetching '{key}' from database")
                return fetch_methods[key]()

            logger.warning(f"Fetch method not implemented for '{key}'")
            return None

        except Exception as e:
            logger.error(f"Error fetching '{key}' from database: {str(e)}")
            return None

    def __views_cognite(self):
        try:
            ntf_cor_views = self._cognite_client.data_modeling.views.list(
                space=env.cognite.fdm_model_space,
                limit=1000,
            )

            asset_hierarcy_views = self._cognite_client.data_modeling.views.list(
                space=env.spaces.asset_hierarcy_model_space,
                limit=1000,
            )

            um_cor_views = self._cognite_client.data_modeling.views.list(
                space=env.spaces.um_model_space,
                limit=1000,
            )

            cognite_views = {
                env.cognite.fdm_model_space: ntf_cor_views.data,
                env.spaces.asset_hierarcy_model_space: asset_hierarcy_views.data,
                env.spaces.um_model_space: um_cor_views.data,
            }

            return cognite_views if cognite_views else None

        except Exception as e:
            logger.error(f"Error fetching Cognite views: {str(e)}")
            return None

    def __fetch_severities(self):
        try:
            items = self._graphql_client.query(
                core.queries.severities_queries.LIST_ALL_SEVERITIES,
                "listNotificationSeverity",
            )
            return items if items else []
        except (KeyError, TypeError) as e:
            logger.error(f"Error extracting severities: {str(e)}")
            return None

    def __fetch_channels(self):
        try:
            items = self._graphql_client.query(
                core.queries.notification_channels.channels_list,
                "listNotificationChannel",
            )
            return items if items else None
        except Exception as e:
            logger.error(f"Error fetching channels: {str(e)}")
            return None

    def __fetch_reporting_site(self):
        views = self.get("cognite_views")

        reporting_site_view = Utils.cognite.find_view_by_external_id(
            views[env.spaces.asset_hierarcy_model_space],
            core.env.cognite_entities.reporting_site,
        )

        reporting_unit_view = Utils.cognite.find_view_by_external_id(
            views[env.spaces.asset_hierarcy_model_space],
            core.env.cognite_entities.reporting_unit,
        )

        reporting_site_cursor = None
        reporting_units_cursor = None
        reporting_units_infos_cursor = None
        has_cursor = True
        response_query = None
        response = []

        views = {
            "reporting_site": reporting_site_view.version,
            "reporting_unit": reporting_unit_view.version,
        }

        query = Query(
            with_={
                "reporting_site": NodeResultSetExpression(
                    filter=Equals(
                        reporting_site_view.as_property_ref("isActive"), True
                    ),
                    limit=10000,
                    chain_to="destination",
                    direction="outwards",
                ),
                "reporting_units": EdgeResultSetExpression(
                    from_="reporting_site",
                    limit=10000,
                    direction="outwards",
                    chain_to="destination",
                    filter=Equals(
                        ["edge", "type"],
                        {
                            "space": core.env.spaces.asset_hierarcy_model_space,
                            "externalId": "ReportingSite.reportingUnits",
                        },
                    ),
                    node_filter=HasData(
                        views=[
                            ViewId(
                                core.env.spaces.asset_hierarcy_model_space,
                                core.env.cognite_entities.reporting_unit,
                                reporting_unit_view.version,
                            )
                        ]
                    ),
                ),
                "reporting_units_infos": NodeResultSetExpression(
                    from_="reporting_units",
                    filter=Equals(
                        reporting_unit_view.as_property_ref("isActive"), True
                    ),
                    limit=10000,
                ),
            },
            select={
                "reporting_site": Select(
                    [
                        SourceSelector(
                            ViewId(
                                core.env.spaces.asset_hierarcy_model_space,
                                core.env.cognite_entities.reporting_site,
                                reporting_site_view.version,
                            ),
                            ["name", "siteCode", "isActive"],
                        )
                    ],
                    sort=[InstanceSort(reporting_site_view.as_property_ref("name"))],
                    limit=10000,
                ),
                "reporting_units": Select(limit=10000),
                "reporting_units_infos": Select(
                    [
                        SourceSelector(
                            ViewId(
                                core.env.spaces.asset_hierarcy_model_space,
                                core.env.cognite_entities.reporting_unit,
                                reporting_unit_view.version,
                            ),
                            ["name", "description", "isActive"],
                        ),
                    ],
                    sort=[InstanceSort(reporting_unit_view.as_property_ref("name"))],
                    limit=10000,
                ),
            },
            cursors={
                "reporting_site": reporting_site_cursor,
                "reporting_units": reporting_units_cursor,
                "reporting_units_infos": reporting_units_infos_cursor,
            },
        )

        while has_cursor:
            response_query = self._cognite_client.data_modeling.instances.query(query)
            reporting_sites = self.reporting_site_repository.format_response(
                response_query, views
            )

            if len(reporting_sites) > 0:
                response.extend(reporting_sites)

            if response_query:
                has_cursor = False
                cursor_keys = [
                    "reporting_site",
                    "reporting_units",
                    "reporting_units_infos",
                ]

                for key in cursor_keys:
                    if (
                        key in response_query.cursors
                        and len(response_query[key]) == 10000
                    ):
                        query.cursors[key] = response_query.cursors[key]
                        has_cursor = True
            else:
                has_cursor = False

        return response if response else None

    def __fetch_teams(self):
        try:
            items = self._graphql_client.query(
                core.queries.team.list_team_by_filter,
                "listTeam",
            )
            return items if items else None
        except Exception as e:
            logger.error(f"Error fetching teams: {str(e)}")
            return None
