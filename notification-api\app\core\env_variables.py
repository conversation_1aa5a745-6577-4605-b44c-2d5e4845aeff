from os import getenv
from typing import List, Optional

from dotenv import load_dotenv


def get_env_or_throw(name: str) -> str:
    value = getenv(name)
    if value is None:
        raise EnvironmentError(f"{name} is not set")
    return value


class ApiVariables:
    def __init__(self) -> None:
        self.title = get_env_or_throw("API_TITLE")
        self.version = get_env_or_throw("API_VERSION")
        self.url = get_env_or_throw("API_URL")
        self.port = int(get_env_or_throw("API_PORT") or 8000)
        self.python_env = get_env_or_throw("PYTHON_ENV")


class AuthVariables:
    def __init__(self) -> None:
        self.client_id = get_env_or_throw("AUTH_CLIENT_ID")
        self.tenant_id = get_env_or_throw("AUTH_TENANT_ID")
        self.secret = get_env_or_throw("AUTH_SECRET")
        self.scopes: List[str] = get_env_or_throw("AUTH_SCOPES").split(",")
        self.token_uri = get_env_or_throw("AUTH_TOKEN_URI")
        self.token_override: Optional[str] = getenv("AUTH_TOKEN_OVERRIDE")
        self.valid_origins = get_env_or_throw("VALID_ORIGINS")
        self.vue_app_azure_ad_user_key = get_env_or_throw("VUE_APP_AZURE_AD_USER_KEY")
        self.vue_app_azure_ad_client_id = get_env_or_throw("VUE_APP_AZURE_AD_CLIENT_ID").split(",")


class CogniteVariables:
    def __init__(self) -> None:
        self.base_uri = get_env_or_throw("COGNITE_BASE_URI")
        self.project = get_env_or_throw("COGNITE_PROJECT")
        self.client_name = get_env_or_throw("COGNITE_CLIENT_NAME")
        self.graphql_uri = get_env_or_throw("COGNITE_GRAPHQL_URI")
        self.data_set_id = int(get_env_or_throw("COGNITE_DATA_SET_ID"))
        self.fdm_model_space = get_env_or_throw("COGNITE_GRAPHQL_MODEL_SPACE")
        self.save_on_dev = int(getenv("COGNITE_SAVE_ON_DEV")) == 1
        self.ignore_fdm_updates = int(getenv("COGNITE_IGNORE_FDM_UPDATE")) == 1
        self.environment = get_env_or_throw("COGNITE_ENVIRONMENT")
        self.cognite_fusion_url = get_env_or_throw("COGNITE_FUSION_URL")
        self.urlDataSetDetails = f'{self.cognite_fusion_url}/{self.project}/data-sets/data-set'
        self.urlLinkedAssets = f'{self.cognite_fusion_url}/{self.project}/explore/asset'
        self.urlTimeSeriesDetails = f'{self.cognite_fusion_url}/{self.project}/explore/timeSeries'


class SpacesVariables:
    def __init__(self) -> None:
        self.um_instance_space = get_env_or_throw("UM_INSTANCE_SPACE")
        self.um_model_space = get_env_or_throw("UM_MODEL_SPACE")
        self.ntf_instance_space = get_env_or_throw("NTF_INSTANCE_SPACE")
        self.ntf_prot_instance_space = get_env_or_throw("NTF_PROT_INSTANCE_SPACE")
        self.assethierarcy_instace_space = get_env_or_throw("ASSETHIERARCHY_INSTANCE_SPACE")
        self.asset_hierarcy_model_space = get_env_or_throw("ASSETHIERARCHY_MODEL_SPACE")

class EntitysVariable:
    def __init__(self) -> None:
        self.notification_template = "NotificationTemplate"
        self.notification_user = "NotificationUser"
        self.notification_channel = "NotificationChannel"
        self.notification_application_group = "NotificationApplicationGroup"
        self.user = "User"
        self.user_complement = "UserComplement"
        self.user_azure_attribute = "UserAzureAttribute"
        self.notification_user_role_site = "NotificationUserRoleSite"
        self.notification_severity = "NotificationSeverity"
        self.notification_type = "NotificationType"
        self.application = "Application"
        self.notification_application = "NotificationApplication"
        self.reporting_site = "ReportingSite"
        self.role = "Role"
        self.reporting_unit = "ReportingUnit"
        self.reporting_location = "ReportingLocation"
        self.notification_on_screen = "NotificationOnScreen"
        self.notification_comment = "NotificationComment"
        self.notification_event = "NotificationEvent"


class EnvVariables:
    def __init__(self) -> None:
        try:
            load_dotenv()
        except Exception as e:
            print(e)
        self.api = ApiVariables()
        self.auth = AuthVariables()
        self.cognite = CogniteVariables()
        self.spaces = SpacesVariables()
        self.cognite_entities = EntitysVariable()
