import app.services.application_service as services
import app.repositories as repositories
import app.models as models

class Box:
    def __init__(self):
        self.data = {}

    def add(self, key, value):
        self.data[key] = value

    def remove(self, key):
        if key in self.data:
            del self.data[key]
    
    def get(self, key):
        if key in self.data:
            return self.data[key]
        return None
    
    def change(self, key, value):
        if key in self.data:
            self.data[key] = value

class GlobalDependencies:
    def __init__(self):
        self.models = models
        self.repositories = repositories
        self.services = services
        self.box = Box()

IBox = GlobalDependencies()
