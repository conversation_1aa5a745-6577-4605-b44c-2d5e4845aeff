from typing import Any, Dict, List, Optional
from gql import Client, gql
from graphql import DocumentNode
from retry import retry


class GraphQLClient:
    def __init__(self, client: Client):
        self.client = client

    def query(
        self, query: str, list_name: str, variable_values: Dict[str, Any] | None = None
    ) -> List[Any]:
        result = self.client.execute(gql(query), variable_values=variable_values)
        return result[list_name]["items"]

    def query_unlimited(
        self,
        query: str,
        list_name: str,
        variable_values: Dict[str, Any] | None = None,
        subItem: str = None,
    ) -> List[Any]:
        result_list = []
        has_next_page = True
        page_info = None
        variable_values = variable_values if variable_values else {}
        while has_next_page:
            variable_values["after"] = page_info.get("endCursor") if page_info else None
            result = self.client.execute(gql(query), variable_values=variable_values)
            result_list.extend(result[list_name]["items"])
            page_info = result[list_name].get("pageInfo", None)
            has_next_page = page_info.get("hasNextPage", False) if page_info else False

        return result_list

    def queryPageInfo(
        self, query: str, list_name: str, variable_values: Dict[str, Any] | None = None
    ) -> List[Any]:
        result = self.client.execute(gql(query), variable_values=variable_values)
        return result[list_name]["pageInfo"]

    def queryPaged(
        self,
        query: str,
        modelName: str,
        page: int,
        totalItensPerPage: int,
        variable_values: Dict[str, Any] | None = None,
    ) -> List[Any]:
        """
        [EN] Method for pagination of a query in cognite.\n
        [PT-BR] Metodo para realizar a paginação de uma query no cognite.\n

        Attention: The query must have some variables, like "filter", "first"  and "after", for example:
            query List{modelName}($filter: _List{modelName}Filter, $first: Int = 10, $after: String) {
                    list{modelName}(filter: $filter, first: $first, after: $after) {
                        items {
                            externalId
                            properties
                            space
                            createdTime
                        }
                    }
                }

        How to use:
            self._graphql_client.queryPaged(
                    YOUR_QUERY,
                    YOUR_MODEL_NAME,
                    PAGE,
                    TOTAL_ITENS_PER_PAGE,
                    FILTER
                )
        """

        jump = (page - 1) * totalItensPerPage
        variable_values["first"] = jump
        listName = "list{modelName}".replace("{modelName}", modelName)

        query_page_info = """
            query MyQuery($first: Int, $after: String, $filter: _List{modelName}Filter = {}) {
                list{modelName}(first: $first, after: $after, filter: $filter) {
                        pageInfo {
                            endCursor
                            hasNextPage
                            hasPreviousPage
                            startCursor
                        }
                    }
                }
        """
        query_page_info = query_page_info.replace("{modelName}", modelName)
        result_page_info = self.client.execute(
            gql(query_page_info), variable_values=variable_values
        )
        end_cursor = result_page_info[listName]["pageInfo"]["endCursor"]
        variable_values["first"] = totalItensPerPage
        variable_values["after"] = end_cursor
        result = self.query(query, listName, variable_values=variable_values)
        return result

    def count(
        self, modelName: str, variable_values: Dict[str, Any] | None = None
    ) -> Any:
        """
        [EN] Method for counting how many records are in the model.\n
        [PT-BR] Metodo para contar quantos registros contem dentro da model.\n
        """
        listName = "list{modelName}".replace("{modelName}", modelName)

        query_count = """
            query MyQuery($filter: _List{modelName}Filter = {}) {
                list{modelName}(filter: $filter) {
                    items {
                        externalId
                    }
                }
            }
        """
        query_count = query_count.replace("{modelName}", modelName)
        filter = variable_values.copy()
        del filter["first"]
        result_page_info = self.client.execute(gql(query_count), filter)

        return len(result_page_info[listName]["items"])

    def queryLastedRecords(
        self,
        query: str,
        modelName: str,
        number_of_lasted: int,
        filter: Dict[str, Any] | None = None,
    ) -> List[Any]:
        """
        [EN] Method for find the last records in a data model.\n
        [PT-BR] Metodo para realizar a busca dos ultimos itens cadastrados em um data model.\n
        """

        result_count = self.count(modelName, filter)
        jump = result_count - number_of_lasted
        if jump < 0:
            jump = 0

        filter["first"] = jump

        listName = "list{modelName}".replace("{modelName}", modelName)

        query_page_info = """
            query MyQuery($first: Int, $after: String, $filter: _List{modelName}Filter = {}) {
                list{modelName}(first: $first, after: $after, filter: $filter) {
                        pageInfo {
                            endCursor
                            hasNextPage
                            hasPreviousPage
                            startCursor
                        }
                    }
                }
        """
        query_page_info = query_page_info.replace("{modelName}", modelName)
        result_page_info = self.client.execute(
            gql(query_page_info), variable_values=filter
        )
        end_cursor = result_page_info[listName]["pageInfo"]["endCursor"]
        filter["first"] = number_of_lasted
        filter["after"] = end_cursor
        result = self.query(query, listName, variable_values=filter)
        return result

    def queryDefaultList(
        self,
        modelName: str,
        fields: str,
        filter: Dict[str, Any] | None = None,
    ) -> List[Any]:
        """
        [PT-BR] Metodo para realizar uma listagem padrão de uma tabela.\n
        [EN] Method for default list of a table.\n
        """

        listName = "list{modelName}".replace("{modelName}", modelName)

        runtime_query = (
            """
            query RuntimeQueryList($filter: _List{modelName}Filter = {}) {
                list{modelName}(filter: $filter) {
                        items {
                            """
            + fields
            + """
                        }
                    }
                }
        """
        )
        runtime_query = runtime_query.replace("{modelName}", modelName)
        result = self.query(runtime_query, listName, variable_values=filter)
        return result

    def getRelationshipEdges(
        self,
        modelName: str,
        space: str,
        externalId: str,
        fieldName: str,
    ) -> List[Any]:
        """
        [PT-BR] Metodo para recuperar o relacionamento de um campo especifico.
        [EN] Method for retrieving the relationship of a specific field.
        """

        listName = "list{modelName}".replace("{modelName}", modelName)
        filterObj={}
        filterObj["filter"] = {"and": [
                        {"space": {"eq": space}},
                        {"externalId": {"eq": externalId}}
                    ]}

        runtime_query = """
            query GetEdges ($filter: _List{modelName}Filter, $first: Int = 1000, $after: String){
                list{modelName}(
                    filter: $filter, first: $first, after: $after) {
                        items {
                            {fieldName} (first: $first){
                                edges {
                                    externalId
                                    space
                                }
                            }
                        }
                    }
                }
        """

        runtime_query = (
            runtime_query.replace("{modelName}", modelName)
            .replace("{fieldName}", fieldName)
        )

        data = self.query_unlimited(runtime_query, listName, filterObj)

        extracted_data = [
            {
                "externalId": item["externalId"],
                "space": item["space"],
                "instanceType": "edge",
            }
            for item in data[0][fieldName]["edges"]
        ]

        return extracted_data

    def execute(self, query: str) -> None:
        self._execute(gql(query))

    @retry(tries=4, backoff=2, delay=1)
    def _execute(self, document: DocumentNode, variable_values: Optional[Dict[str, Any]]):
        return self.client.execute(document, variable_values=variable_values)