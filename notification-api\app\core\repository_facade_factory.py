from data.actual_billing_repository import ActualBillingRepository
from data.billing_cycle_repository import BillingCycleRepository
from data.client_repository import ClientRepository
from data.datapoint_repository import DatapointRepository
from data.execution_history_repository import ExecutionHistoryRepository
from data.monthly_report_repository import MonthlyReportRepository
from data.report_data_series_repository import ReportDataSeriesRepository
from data.repository_facade import RepositoryFacade
from data.tag_repository import TagRepository
from data.unit_repository import UnitRepository
from data.utility_price_repository import UtilityPriceRepository
from data.utility_type_repository import UtilityTypeRepository

from .cognite_client_factory import CogniteClientFactory
from .env_variables import EnvVariables
from .gql_client_factory import GqlClientFactory
from .graphql_client import GraphQLClient


class RepositoryFacadeFactory:
    @staticmethod
    def create(env_variables: EnvVariables) -> RepositoryFacade:
        cognite_client = CogniteClientFactory.create(env_variables)
        gql_client = GqlClientFactory.create(cognite_client, env_variables)

        graphql_client = GraphQLClient(gql_client)
        actual_billing_repository = ActualBillingRepository(graphql_client)
        billing_cycle_repository = BillingCycleRepository(graphql_client)
        tag_repository = TagRepository(graphql_client, cognite_client)
        unit_repository = UnitRepository(graphql_client)
        utility_price_repository = UtilityPriceRepository(cognite_client)
        client_repository = ClientRepository(graphql_client)
        utility_type_repository = UtilityTypeRepository(graphql_client)
        monthly_report_repository = MonthlyReportRepository(
            cognite_client, graphql_client, env_variables
        )

        report_data_series_repository = ReportDataSeriesRepository(
            cognite_client, graphql_client, env_variables
        )
        execution_history_repository = ExecutionHistoryRepository(
            cognite_client, graphql_client, env_variables
        )

        def generate_cognite_client_for_saving():
            if not env_variables.cognite.save_on_dev:
                return cognite_client
            if env_variables.cognite.project.endswith("-dev"):
                return cognite_client
            new_variables = env_variables
            new_variables.cognite.project = f"{new_variables.cognite.project}-dev"
            return CogniteClientFactory.create(new_variables)

        datapoint_repository = DatapointRepository(
            generate_cognite_client_for_saving(),
            env_variables.cognite.data_set_id,
        )
        return RepositoryFacade(
            tag_repository,
            unit_repository,
            utility_price_repository,
            datapoint_repository,
            report_data_series_repository,
            execution_history_repository,
            client_repository,
            utility_type_repository,
            monthly_report_repository,
            actual_billing_repository,
            billing_cycle_repository,
        )
