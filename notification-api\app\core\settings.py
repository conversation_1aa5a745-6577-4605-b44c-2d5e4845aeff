from pydantic import computed_field
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    cognite_graphql_uri: str

    @computed_field
    @property
    def data_model_id(self) -> tuple[str, str, str]:
        items = self.cognite_graphql_uri.split("/")
        space_idx = items.index("spaces")
        data_model_idx = items.index("datamodels")
        version_idx = items.index("versions")

        return (items[space_idx + 1], items[data_model_idx + 1], items[version_idx + 1])
