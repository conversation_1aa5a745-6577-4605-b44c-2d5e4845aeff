2025-08-16 23:05:52,972 - app.core.cache_client - INFO - API clients initialized with fresh tokens
2025-08-16 23:05:52,979 - app.core.logger - INFO - Local Development Mode, No Authentication
2025-08-16 23:05:52,979 - app.core.logger - INFO - Auto-bind router: application
2025-08-16 23:05:52,984 - app.core.logger - INFO - Local Development Mode, No Authentication
2025-08-16 23:05:52,986 - app.core.logger - INFO - Auto-bind router: cron-expression
2025-08-16 23:05:52,990 - app.core.logger - INFO - Local Development Mode, No Authentication
2025-08-16 23:05:52,992 - app.core.logger - INFO - Auto-bind router: notification-application-group
2025-08-16 23:05:53,016 - app.core.logger - INFO - Local Development Mode, No Authentication
2025-08-16 23:05:53,017 - app.core.logger - INFO - Auto-bind router: notification-channels
2025-08-16 23:05:53,024 - app.core.logger - INFO - Local Development Mode, No Authentication
2025-08-16 23:05:53,027 - app.core.logger - INFO - Auto-bind router: notification-event
2025-08-16 23:05:53,034 - app.core.logger - INFO - Local Development Mode, No Authentication
2025-08-16 23:05:53,038 - app.core.logger - INFO - Auto-bind router: notification-on-screen
2025-08-16 23:05:53,041 - app.core.logger - INFO - Local Development Mode, No Authentication
2025-08-16 23:05:53,041 - app.core.logger - INFO - Auto-bind router: notification-severities
2025-08-16 23:05:53,052 - app.core.logger - INFO - Local Development Mode, No Authentication
2025-08-16 23:05:53,058 - app.core.logger - INFO - Auto-bind router: notification-template
2025-08-16 23:05:53,061 - app.core.logger - INFO - Local Development Mode, No Authentication
2025-08-16 23:05:53,061 - app.core.logger - INFO - Auto-bind router: notification-type
2025-08-16 23:05:53,064 - app.core.logger - INFO - Local Development Mode, No Authentication
2025-08-16 23:05:53,065 - app.core.logger - INFO - Auto-bind router: reporting-location
2025-08-16 23:05:53,070 - app.core.logger - INFO - Local Development Mode, No Authentication
2025-08-16 23:05:53,071 - app.core.logger - INFO - Auto-bind router: reporting-sites
2025-08-16 23:05:53,075 - app.core.logger - INFO - Local Development Mode, No Authentication
2025-08-16 23:05:53,076 - app.core.logger - INFO - Auto-bind router: reporting-unit
2025-08-16 23:05:53,083 - app.core.logger - INFO - Local Development Mode, No Authentication
2025-08-16 23:05:53,113 - app.core.logger - INFO - Auto-bind router: roles
2025-08-16 23:05:53,116 - app.core.logger - INFO - Local Development Mode, No Authentication
2025-08-16 23:05:53,117 - app.core.logger - INFO - Auto-bind router: team
2025-08-16 23:05:53,121 - app.core.logger - INFO - Local Development Mode, No Authentication
2025-08-16 23:05:53,122 - app.core.logger - INFO - Auto-bind router: timeseries
2025-08-16 23:05:53,130 - app.core.logger - INFO - Local Development Mode, No Authentication
2025-08-16 23:05:53,135 - app.core.logger - INFO - Auto-bind router: user
2025-08-16 23:05:53,874 - app.core.cache_client - ERROR - Error fetching Cognite views: Unauthorized | code: 401 | X-Request-ID: b3abc059-fe86-939a-80ba-bf48728c4f0f
2025-08-16 23:05:53,879 - gql.transport.aiohttp - INFO - >>> {"query": "query IntrospectionQuery {\n  __schema {\n    queryType {\n      name\n    }\n    mutationType {\n      name\n    }\n    subscriptionType {\n      name\n    }\n    types {\n      ...FullType\n    }\n    directives {\n      name\n      description\n      locations\n      args {\n        ...InputValue\n      }\n    }\n  }\n}\n\nfragment FullType on __Type {\n  kind\n  name\n  description\n  fields(includeDeprecated: true) {\n    name\n    description\n    args {\n      ...InputValue\n    }\n    type {\n      ...TypeRef\n    }\n    isDeprecated\n    deprecationReason\n  }\n  inputFields {\n    ...InputValue\n  }\n  interfaces {\n    ...TypeRef\n  }\n  enumValues(includeDeprecated: true) {\n    name\n    description\n    isDeprecated\n    deprecationReason\n  }\n  possibleTypes {\n    ...TypeRef\n  }\n}\n\nfragment InputValue on __InputValue {\n  name\n  description\n  type {\n    ...TypeRef\n  }\n  defaultValue\n}\n\nfragment TypeRef on __Type {\n  kind\n  name\n  ofType {\n    kind\n    name\n    ofType {\n      kind\n      name\n      ofType {\n        kind\n        name\n        ofType {\n          kind\n          name\n          ofType {\n            kind\n            name\n            ofType {\n              kind\n              name\n              ofType {\n                kind\n                name\n              }\n            }\n          }\n        }\n      }\n    }\n  }\n}"}
2025-08-16 23:05:54,595 - gql.transport.aiohttp - INFO - <<< {"error":{"code":401,"message":"Unauthorized"}}
2025-08-16 23:05:54,817 - app.core.cache_client - ERROR - Error fetching channels: 401, message='Unauthorized', url=URL('https://az-eastus-1.cognitedata.com/api/v1/projects/celanese-dev/userapis/spaces/NTF-COR-ALL-DMD/datamodels/NotificationDOM/versions/2_1_4/graphql')
2025-08-16 23:05:54,824 - gql.transport.aiohttp - INFO - >>> {"query": "query IntrospectionQuery {\n  __schema {\n    queryType {\n      name\n    }\n    mutationType {\n      name\n    }\n    subscriptionType {\n      name\n    }\n    types {\n      ...FullType\n    }\n    directives {\n      name\n      description\n      locations\n      args {\n        ...InputValue\n      }\n    }\n  }\n}\n\nfragment FullType on __Type {\n  kind\n  name\n  description\n  fields(includeDeprecated: true) {\n    name\n    description\n    args {\n      ...InputValue\n    }\n    type {\n      ...TypeRef\n    }\n    isDeprecated\n    deprecationReason\n  }\n  inputFields {\n    ...InputValue\n  }\n  interfaces {\n    ...TypeRef\n  }\n  enumValues(includeDeprecated: true) {\n    name\n    description\n    isDeprecated\n    deprecationReason\n  }\n  possibleTypes {\n    ...TypeRef\n  }\n}\n\nfragment InputValue on __InputValue {\n  name\n  description\n  type {\n    ...TypeRef\n  }\n  defaultValue\n}\n\nfragment TypeRef on __Type {\n  kind\n  name\n  ofType {\n    kind\n    name\n    ofType {\n      kind\n      name\n      ofType {\n        kind\n        name\n        ofType {\n          kind\n          name\n          ofType {\n            kind\n            name\n            ofType {\n              kind\n              name\n              ofType {\n                kind\n                name\n              }\n            }\n          }\n        }\n      }\n    }\n  }\n}"}
2025-08-16 23:05:55,541 - gql.transport.aiohttp - INFO - <<< {"error":{"code":401,"message":"Unauthorized"}}
2025-08-16 23:05:55,765 - app.core.cache_client - ERROR - Error initializing cache: 401, message='Unauthorized', url=URL('https://az-eastus-1.cognitedata.com/api/v1/projects/celanese-dev/userapis/spaces/NTF-COR-ALL-DMD/datamodels/NotificationDOM/versions/2_1_4/graphql')
2025-08-16 23:11:55,053 - app.core.cache_client - INFO - API clients initialized with fresh tokens
2025-08-16 23:11:55,067 - app.core.logger - INFO - Local Development Mode, No Authentication
2025-08-16 23:11:55,068 - app.core.logger - INFO - Auto-bind router: application
2025-08-16 23:11:55,073 - app.core.logger - INFO - Local Development Mode, No Authentication
2025-08-16 23:11:55,075 - app.core.logger - INFO - Auto-bind router: cron-expression
2025-08-16 23:11:55,078 - app.core.logger - INFO - Local Development Mode, No Authentication
2025-08-16 23:11:55,082 - app.core.logger - INFO - Auto-bind router: notification-application-group
2025-08-16 23:11:55,084 - app.core.logger - INFO - Local Development Mode, No Authentication
2025-08-16 23:11:55,085 - app.core.logger - INFO - Auto-bind router: notification-channels
2025-08-16 23:11:55,110 - app.core.logger - INFO - Local Development Mode, No Authentication
2025-08-16 23:11:55,112 - app.core.logger - INFO - Auto-bind router: notification-event
2025-08-16 23:11:55,120 - app.core.logger - INFO - Local Development Mode, No Authentication
2025-08-16 23:11:55,125 - app.core.logger - INFO - Auto-bind router: notification-on-screen
2025-08-16 23:11:55,127 - app.core.logger - INFO - Local Development Mode, No Authentication
2025-08-16 23:11:55,127 - app.core.logger - INFO - Auto-bind router: notification-severities
2025-08-16 23:11:55,137 - app.core.logger - INFO - Local Development Mode, No Authentication
2025-08-16 23:11:55,144 - app.core.logger - INFO - Auto-bind router: notification-template
2025-08-16 23:11:55,145 - app.core.logger - INFO - Local Development Mode, No Authentication
2025-08-16 23:11:55,146 - app.core.logger - INFO - Auto-bind router: notification-type
2025-08-16 23:11:55,148 - app.core.logger - INFO - Local Development Mode, No Authentication
2025-08-16 23:11:55,150 - app.core.logger - INFO - Auto-bind router: reporting-location
2025-08-16 23:11:55,151 - app.core.logger - INFO - Local Development Mode, No Authentication
2025-08-16 23:11:55,152 - app.core.logger - INFO - Auto-bind router: reporting-sites
2025-08-16 23:11:55,155 - app.core.logger - INFO - Local Development Mode, No Authentication
2025-08-16 23:11:55,156 - app.core.logger - INFO - Auto-bind router: reporting-unit
2025-08-16 23:11:55,162 - app.core.logger - INFO - Local Development Mode, No Authentication
2025-08-16 23:11:55,166 - app.core.logger - INFO - Auto-bind router: roles
2025-08-16 23:11:55,169 - app.core.logger - INFO - Local Development Mode, No Authentication
2025-08-16 23:11:55,170 - app.core.logger - INFO - Auto-bind router: team
2025-08-16 23:11:55,173 - app.core.logger - INFO - Local Development Mode, No Authentication
2025-08-16 23:11:55,174 - app.core.logger - INFO - Auto-bind router: timeseries
2025-08-16 23:11:55,183 - app.core.logger - INFO - Local Development Mode, No Authentication
2025-08-16 23:11:55,193 - app.core.logger - INFO - Auto-bind router: user
2025-08-16 23:11:56,242 - app.core.cache_client - ERROR - Error fetching Cognite views: Unauthorized | code: 401 | X-Request-ID: 3af01f6d-6075-9632-9796-094bd0f56357
2025-08-16 23:11:56,261 - gql.transport.aiohttp - INFO - >>> {"query": "query IntrospectionQuery {\n  __schema {\n    queryType {\n      name\n    }\n    mutationType {\n      name\n    }\n    subscriptionType {\n      name\n    }\n    types {\n      ...FullType\n    }\n    directives {\n      name\n      description\n      locations\n      args {\n        ...InputValue\n      }\n    }\n  }\n}\n\nfragment FullType on __Type {\n  kind\n  name\n  description\n  fields(includeDeprecated: true) {\n    name\n    description\n    args {\n      ...InputValue\n    }\n    type {\n      ...TypeRef\n    }\n    isDeprecated\n    deprecationReason\n  }\n  inputFields {\n    ...InputValue\n  }\n  interfaces {\n    ...TypeRef\n  }\n  enumValues(includeDeprecated: true) {\n    name\n    description\n    isDeprecated\n    deprecationReason\n  }\n  possibleTypes {\n    ...TypeRef\n  }\n}\n\nfragment InputValue on __InputValue {\n  name\n  description\n  type {\n    ...TypeRef\n  }\n  defaultValue\n}\n\nfragment TypeRef on __Type {\n  kind\n  name\n  ofType {\n    kind\n    name\n    ofType {\n      kind\n      name\n      ofType {\n        kind\n        name\n        ofType {\n          kind\n          name\n          ofType {\n            kind\n            name\n            ofType {\n              kind\n              name\n              ofType {\n                kind\n                name\n              }\n            }\n          }\n        }\n      }\n    }\n  }\n}"}
2025-08-16 23:11:57,018 - gql.transport.aiohttp - INFO - <<< {"error":{"code":401,"message":"Unauthorized"}}
2025-08-16 23:11:57,241 - app.core.cache_client - ERROR - Error fetching channels: 401, message='Unauthorized', url=URL('https://az-eastus-1.cognitedata.com/api/v1/projects/celanese-dev/userapis/spaces/NTF-COR-ALL-DMD/datamodels/NotificationDOM/versions/2_1_4/graphql')
2025-08-16 23:11:57,257 - gql.transport.aiohttp - INFO - >>> {"query": "query IntrospectionQuery {\n  __schema {\n    queryType {\n      name\n    }\n    mutationType {\n      name\n    }\n    subscriptionType {\n      name\n    }\n    types {\n      ...FullType\n    }\n    directives {\n      name\n      description\n      locations\n      args {\n        ...InputValue\n      }\n    }\n  }\n}\n\nfragment FullType on __Type {\n  kind\n  name\n  description\n  fields(includeDeprecated: true) {\n    name\n    description\n    args {\n      ...InputValue\n    }\n    type {\n      ...TypeRef\n    }\n    isDeprecated\n    deprecationReason\n  }\n  inputFields {\n    ...InputValue\n  }\n  interfaces {\n    ...TypeRef\n  }\n  enumValues(includeDeprecated: true) {\n    name\n    description\n    isDeprecated\n    deprecationReason\n  }\n  possibleTypes {\n    ...TypeRef\n  }\n}\n\nfragment InputValue on __InputValue {\n  name\n  description\n  type {\n    ...TypeRef\n  }\n  defaultValue\n}\n\nfragment TypeRef on __Type {\n  kind\n  name\n  ofType {\n    kind\n    name\n    ofType {\n      kind\n      name\n      ofType {\n        kind\n        name\n        ofType {\n          kind\n          name\n          ofType {\n            kind\n            name\n            ofType {\n              kind\n              name\n              ofType {\n                kind\n                name\n              }\n            }\n          }\n        }\n      }\n    }\n  }\n}"}
2025-08-16 23:11:57,976 - gql.transport.aiohttp - INFO - <<< {"error":{"code":401,"message":"Unauthorized"}}
2025-08-16 23:11:58,202 - app.core.cache_client - ERROR - Error initializing cache: 401, message='Unauthorized', url=URL('https://az-eastus-1.cognitedata.com/api/v1/projects/celanese-dev/userapis/spaces/NTF-COR-ALL-DMD/datamodels/NotificationDOM/versions/2_1_4/graphql')
