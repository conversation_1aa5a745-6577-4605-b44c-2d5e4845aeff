import asyncio
from contextlib import asynccontextmanager
from fastapi import <PERSON><PERSON><PERSON>, Request
from fastapi.middleware import Middleware
from fastapi.middleware.cors import CORSMiddleware
from starlette.responses import JSONResponse

import app.core as core
import app.middleware as middleware
from app.core.router_setup import bind_routers
from app.core.cache_client import CacheClient
from app.core.cache_global import set_cache_instance

# cors
origins = core.env.auth.valid_origins.split(",")
cache_client = CacheClient()

@asynccontextmanager
async def lifespan(app: FastAPI):
    # This block of code will run before FastAPI server startup
    print("Starting Server")
    
    global cache
    loop = asyncio.get_running_loop()
    cache = await loop.run_in_executor(None, cache_client.initialize)

    set_cache_instance(cache)

    app.state.cache = cache

    yield
    # This block of code will run before finishing FastAPI service
    print("Finishing server")

# Initialize fastAPI
app = FastAPI(
    title=core.env.api.title,
    version=core.env.api.version,
    docs_url=core.env.api.url,
    middleware=[
        Middleware(
            middleware.ResponseMiddleware,
            exclude_urls=["/docs", "/redoc", core.env.api.url],
        )
    ],
    lifespan=lifespan,
)

# add cors middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"]
)

@app.exception_handler(Exception)
async def error_exception_handler(request: Request, exc: Exception):
    response = JSONResponse(content={"message": str(exc)}, status_code=500)
    # Since the CORSMiddleware is not executed when an unhandled server exception
    # occurs, we need to manually set the CORS headers ourselves if we want the FE
    # to receive a proper JSON 500, opposed to a CORS error.
    # Setting CORS headers on server errors is a bit of a philosophical topic of
    # discussion in many frameworks, and it is currently not handled in FastAPI.
    # See dotnet core for a recent discussion, where ultimately it was
    # decided to return CORS headers on server failures:
    # https://github.com/dotnet/aspnetcore/issues/2378
    origin = request.headers.get("origin")
    if origin:
        # Have the middleware do the heavy lifting for us to parse
        # all the config, then update our response headers
        cors = CORSMiddleware(
            app=app,
            allow_origins=origins,
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"]
        )

        # Logic directly from Starlette's CORSMiddleware:
        # https://github.com/encode/starlette/blob/master/starlette/middleware/cors.py#L152

        response.headers.update(cors.simple_headers)
        has_cookie = "cookie" in request.headers

        # If request includes any cookie headers, then we must respond
        # with the specific origin instead of '*'.
        if cors.allow_all_origins and has_cookie:
            response.headers["Access-Control-Allow-Origin"] = origin

        # If we only allow specific origins, then we have to mirror back
        # the Origin header in the response.
        elif not cors.allow_all_origins and cors.is_allowed_origin(origin=origin):
            response.headers["Access-Control-Allow-Origin"] = origin
            response.headers.add_vary_header("Origin")

    return response

# include auto routers
bind_routers(app)