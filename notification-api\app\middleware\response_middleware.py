import json
from fastapi import Request
from starlette.types import ASGIApp, Message

class HttpStatusError(Exception):
    def __init__(self, status_code, message):
        self.status_code = status_code
        self.message = message

class ResponseMiddleware:
    def __init__(self, app, exclude_urls=[]):
        self.app = app
        self.exclude_urls = exclude_urls

    async def __call__(self, scope, receive, send):

        async def response_exception(e: Exception):
            httpCode = getattr(e, 'status_code', getattr(e, 'code', 500))
            error_response = {
                "code": httpCode,
                "status": "Error",
                "message": str(getattr(e, 'message', get_http_message(httpCode))),
                "exception": str(e),
            }
            error_response_json = json.dumps(error_response).encode("utf-8")

            error_response_headers = [
                (b"content-type", b"application/json"),
                (b"content-length", str(len(error_response_json)).encode("utf-8")),
            ]

            await send({
                "type": "http.response.start",
                "status": getattr(e, 'status_code', getattr(e, 'code', 500)),
                "headers": error_response_headers,
            })
            await send({
                "type": "http.response.body",
                "body": error_response_json,
            })

        def get_http_code(method: str) -> int:
            if method == 'GET':
                return 200
            elif method == 'POST':
                return 201
            elif method == 'PUT':
                return 200
            elif method == 'DELETE':
                return 200
            else:
                return 500

        def get_http_message(http_status_code):
            error_messages = {
                100: "Continue",
                101: "Switching Protocols",
                200: "OK",
                201: "Created",
                202: "Accepted",
                203: "Non-Authoritative Information",
                204: "No Content",
                205: "Reset Content",
                206: "Partial Content",
                300: "Multiple Choices",
                301: "Moved Permanently",
                302: "Found",
                303: "See Other",
                304: "Not Modified",
                305: "Use Proxy",
                307: "Temporary Redirect",
                400: "Bad Request",
                401: "Unauthorized",
                402: "Payment Required",
                403: "Forbidden",
                404: "Not Found",
                405: "Method Not Allowed",
                406: "Not Acceptable",
                407: "Proxy Authentication Required",
                408: "Request Timeout",
                409: "Conflict",
                410: "Gone",
                411: "Length Required",
                412: "Precondition Failed",
                413: "Payload Too Large",
                414: "URI Too Long",
                415: "Unsupported Media Type",
                416: "Range Not Satisfiable",
                417: "Expectation Failed",
                500: "Internal Server Error",
                501: "Not Implemented",
                502: "Bad Gateway",
                503: "Service Unavailable",
                504: "Gateway Timeout",
                505: "HTTP Version Not Supported",
            }

            if http_status_code in error_messages:
                return error_messages[http_status_code]
            else:
                return "Unknown Status Code"


        async def asgi(message: Message):
            nonlocal scope
            request = Request(scope)

            if request.url.path in self.exclude_urls:
                await self.app(scope, receive, send)
            else:
                try:
                    self.exclude_urls.append("/openapi.json")

                    if message["type"] == "http.response.start" and message.get('status') >= 400:
                        raise HttpStatusError(message.get('status'), get_http_message(message.get('status')))

                    if message["type"] == "http.response.body" and not any([scope["path"].startswith(endpoint) for endpoint in self.exclude_urls]):
                        response = {
                            "code": get_http_code(scope['method']),
                            "status": "OK",
                            "message": json.loads(message['body'].decode("utf-8")),
                        }
                        response_json = json.dumps(response).encode("utf-8")

                        response_headers = [
                            (b"content-type", b"application/json"),
                            (b"content-length", str(len(response_json)).encode("utf-8")),
                        ]

                        await send({
                            "type": "http.response.start",
                            "status": response['code'],
                            "headers": response_headers,
                        })
                        await send({
                            "type": "http.response.body",
                            "body": response_json,
                        })

                except HttpStatusError as e:
                    await response_exception(e)
                except Exception as e:
                    await response_exception(e)

        try:
            await self.app(scope, receive, asgi)
        except Exception as e:
            await response_exception(e)
