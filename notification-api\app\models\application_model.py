from pydantic import BaseModel, Field
from typing import Any, List, Optional
import app.models as models


class ApplicationBaseModel(BaseModel):
    externalId: str
    name: str
    alias: Optional[str]
    description: str


class ApplicationCreateModel(BaseModel):
    name: str
    description: str

class ApplicationModel(ApplicationBaseModel):
    name: str
    alias: Optional[str]
    description: str
    externalId: str
    notificationTypes: Optional[List[Any]] = None

    def mapFromResult(item: Any):
        return ApplicationModel(
            name=item.get("name", ""),
            alias=item.get("alias", ""),
            description=item.get("description", ""),
            externalId=item.get("externalId", ""),
            notificationTypes=[
                models.NotificationTypeModel.mapFromResult(subitem)
                for subitem in item.get("notificationTypes", [])
                if subitem
            ]
            if item.get("notificationTypes")
            else [],
        )