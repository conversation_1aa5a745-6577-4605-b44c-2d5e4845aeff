from pydantic import BaseModel, Field
from typing import Any, List, Optional, Union
from app.models.relation_model import RelationModel
import app.models as models

class BasicModel(BaseModel):
    externalId: str
    space: str

class ReportingSiteModelNotificationApplicationGroup(BaseModel):
    externalId: Optional[str] = None
    name: Optional[str] = None
    space: Optional[str] = None
    siteCode: Optional[str] = None

class RoleModelNotificationApplicationGroup(BaseModel):
    externalId: str
    name: str
    space: str
    users: List[models.UserModel]
    site: Optional[ReportingSiteModelNotificationApplicationGroup] = None

class NotificationApplicationGroup(BaseModel):
    externalId: str
    space: str
    name: Optional[str] = ""
    description: Optional[str] = ""
    users: Optional[List[models.UserModel]] = None
    externalUsers: Optional[List[str]] = None
    blocklist: Optional[List[models.UserModel]] = None
    application: Optional[BasicModel] = None
    usersRoles: Optional[List[RoleModelNotificationApplicationGroup]] = None
    blocklistRoles: Optional[List[RoleModelNotificationApplicationGroup]] = None
    createdBy: Optional[models.UserModel] = None
    editedBy: Optional[models.UserModel] = None
    editedAt: Optional[str] = None

    def mapFromResult(self: Any):
        
        def get_value(obj, field: str, default=None):
            if hasattr(obj, 'get') and callable(getattr(obj, 'get')):
                return obj.get(field, default)
            else:
                return getattr(obj, field, default)
            
        def extract_item(obj, model_class):
            """
            Converts a dictionary or object to an instance of model_class.
            Returns the object if it's already an instance of model_class.
            """

            if isinstance(obj, model_class):
                return obj
            elif isinstance(obj, dict):
                return model_class(**obj)
            elif hasattr(obj, '__dict__'):
                return model_class(**obj.__dict__)
            else:
                return model_class(**vars(obj))
            
        def safe_list_conversion(items, model_class):
            """
            Converts a list-like object into a list of model_class instances,
            handling various input formats.
            """
            
            if not items:
                return []
            
            if isinstance(items, dict) and 'items' in items:
                items = items['items']
            elif hasattr(items, 'items') and not callable(getattr(items, 'items')):
                items = items.items
            
            if not items:
                return []
            
            result = []
            for item in items:
                if item:
                    result.append(extract_item(item, model_class))
            return result
        
        return NotificationApplicationGroup(
            externalId=self.get("externalId", ""),
            space=self.get("space", ""),
            name=self.get("name", ""),
            description=self.get("description", ""),
            users=safe_list_conversion(get_value(self, "users", []), models.UserModel),
            blocklist=safe_list_conversion(get_value(self, "blocklist", []), models.UserModel),
            externalUsers=(
                self.get("externalUsers", []) if self.get("externalUsers") else []
            ),
            application=extract_item(self.get("application", {}), BasicModel) if self.get("application") else None,
            usersRoles=safe_list_conversion(get_value(self, "usersRoles", []), RoleModelNotificationApplicationGroup),
            blocklistRoles=safe_list_conversion(get_value(self, "blocklistRoles", []), RoleModelNotificationApplicationGroup),
            createdBy=extract_item(self.get("createdBy", {}), models.UserModel) if self.get("createdBy") else None,
            editedBy=extract_item(self.get("editedBy", {}), models.UserModel) if self.get("editedBy") else None,
            editedAt=self.get("editedAt", None),
        )


class NotificationApplicationGroupCreateModel(NotificationApplicationGroup):
    externalId: Optional[str] = None
    space: Optional[str] = None
    name: str
    description: str
    users: Optional[List[str]] = None
    externalUsers: Optional[List[str]] = None
    blocklist: Optional[List[str]] = None
    application: Union[str, dict]
    createdBy: Optional[RelationModel] = None
    editedBy: Optional[RelationModel] = None
    editedAt: Optional[str] = None
    usersRoles: Optional[List[str]] = None
    blocklistRoles: Optional[List[str]] = None


def parse_notification_application_groups(
    data: List[dict],
) -> List[NotificationApplicationGroup]:
    application_groups = []
    for item in data:
        users = (
            [models.UserModel(**user) for user in item["users"]]
            if item.get("users")
            else []
        )

        blocklist = (
            [models.UserModel(**user) for user in item["blocklist"]]
            if item.get("blocklist")
            else []
        )
        
        users_roles = (
            [RoleModelNotificationApplicationGroup(**role) for role in item["usersRoles"]]
            if item.get("usersRoles")
            else []
        )

        blocklist_roles = (
            [RoleModelNotificationApplicationGroup(**role) for role in item["blocklistRoles"]]
            if item.get("blocklistRoles")
            else []
        )

        application_groups.append(
            NotificationApplicationGroup(
                externalId=item["externalId"],
                space=item["space"],
                name=item["name"],
                description=item["description"],
                users=users,
                blocklist=blocklist,
                externalUsers=item["externalUsers"],
                application=item["application"],
                usersRoles=users_roles,
                blocklistRoles=blocklist_roles,
                createdBy=models.UserModel(**item["createdBy"]) if item.get("createdBy") else None,
                editedBy=models.UserModel(**item["editedBy"]) if item.get("editedBy") else None,
                editedAt=item.get("editedAt"),
            )
        )

    return application_groups
