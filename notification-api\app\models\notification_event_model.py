from pydantic import BaseModel
from typing import Any, List, Optional, Dict
from datetime import datetime
import app.models as models
import json


class RelationModel(BaseModel):
    space: str
    externalId: str


class NotificationRawEventModel(BaseModel):
    requestReferenceId: Optional[str] = None
    notificationType: str
    description: str
    severity: Optional[str] = None
    roles: Optional[List[str]] = None
    applicationGroups: Optional[List[str]] = None
    users: Optional[List[str]] = None
    externalUsers: Optional[List[str]] = None
    properties: List[Dict[str, Any]]
    notificationReference: Optional[str] = None
    appId: Optional[str] = None


class NotificationEventModel(BaseModel):
    externalId: Optional[str]
    space: Optional[str]
    notificationType: Optional[models.NotificationTypeModel]
    userRoles: Optional[List[models.NotificationRoleModel]]
    applicationGroups: Optional[List[models.NotificationApplicationGroup]]
    users: Optional[List[models.NotificationUserModel]]
    properties: Optional[List[Any]]
    createdTime: Optional[Any]

    @staticmethod
    def mapFromResult(item: Any) -> "NotificationEventModel":
        externalId = item.get("externalId")
        space = item.get("space")
        notificationTypeDict = item.get("notificationType")
        if notificationTypeDict is not None:
            notificationType = models.NotificationTypeModel(**notificationTypeDict)
        else:
            notificationType = None

        userRoles = [
            models.NotificationRoleModel(**role) for role in item.get("userRoles", [])
        ]
        applicationGroups = [
            models.NotificationApplicationGroup(**group)
            for group in item.get("applicationGroups", [])
        ]
        users = [models.NotificationUserModel(**user) for user in item.get("users", [])]
        createdTime = item.get("createdTime")

        properties = []

        if item["properties"] is not None:
            property = item["properties"]
            properties = property.get("properties", [])

        return NotificationEventModel(
            externalId=externalId,
            space=space,
            notificationType=notificationType,
            userRoles=userRoles,
            applicationGroups=applicationGroups,
            users=users,
            properties=properties,
            createdTime=createdTime,
        )
