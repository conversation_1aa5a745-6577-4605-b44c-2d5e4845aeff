from pydantic import BaseModel
from typing import Any

class NotificationLogicalOperatorModel(BaseModel):
    externalId: str
    name: str
    description: str

    def mapFromResult(item: Any):
        return NotificationLogicalOperatorModel(
            externalId=item.get("externalId", ""),
            name=item.get("name", ""),
            description=item.get("description", ""),            
        )
