from pydantic import BaseModel
from typing import Dict, List, Optional

class NotificationOnScreenFilterModel(BaseModel):
    sort_column: Optional[Dict[str,str]] = {}
    search: Optional[str] = None
    site_external_id: Optional[str] = None
    application_external_id: Optional[str] = None
    notification_type_external_id: Optional[str] = None
    severity_external_ids: Optional[List[str]] = []
    start_period: Optional[str] = None
    end_period: Optional[str] = None