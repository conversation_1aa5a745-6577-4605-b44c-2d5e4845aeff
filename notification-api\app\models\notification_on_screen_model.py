from pydantic import BaseModel
import app.models as models
from typing import Any, List, Optional, Dict


class NotificationOnScreenUserModel(BaseModel):
    email: str
    externalId: str
    firstName: str
    lastName: str
    displayName: str
    space: str

    @staticmethod
    def mapFromResult(item: Any) -> "NotificationOnScreenUserModel":
        return NotificationOnScreenUserModel(
            email=item.get("email", ""),
            externalId=item.get("externalId", ""),
            firstName=item.get("firstName", ""),
            lastName=item.get("lastName", ""),
            displayName=item.get("displayName", ""),
            space=item.get("space", ""),
        )


class NotificationOnScreenCommentModel(BaseModel):
    comment: str
    externalId: str
    space: str
    createdTime: str
    user: NotificationOnScreenUserModel

    @staticmethod
    def mapFromResult(item: Any) -> "NotificationOnScreenCommentModel":
        user_data = item.get("user", {})
        return NotificationOnScreenCommentModel(
            comment=item.get("comment"),
            externalId=item.get("externalId"),
            space=item.get("space"),
            createdTime=item.get("createdTime"),
            user=NotificationOnScreenUserModel.mapFromResult(user_data),
        )


class NotificationOnScreenChatModel(BaseModel):
    comments: Optional[List[NotificationOnScreenCommentModel]] = []
    createdTime: str
    externalId: str
    space: str
    text: str
    notificationType: str
    application: str
    site: Optional[str] = None
    severityExternalId: str
    severityDescription: str
    rawEvent: models.NotificationRawEventModel

    def mapFromResult(item: Any):
        comments = [
            NotificationOnScreenCommentModel.mapFromResult(comment)
            for comment in item.get("comments").get("items")
        ]

        notificationTypeObject = item.get("template", {}).get("notificationType", {})
        eventObject = item.get("event", {})

        users = list(map(lambda item: item.get("externalId"), eventObject.get("users", {}).get("items", [])))
        roles = list(map(lambda item: NotificationOnScreenChatModel.remove_substring(item.get("externalId", "")), eventObject.get("roles", {}).get("items", [])))
        applicationGroups = list(map(lambda item: item.get("name"), eventObject.get("applicationGroups", {}).get("items", [])))

        return NotificationOnScreenChatModel(
            comments=comments,
            externalId=item.get("externalId"),
            createdTime=item.get("createdAt"),
            space=item.get("space", ""),
            text=item.get("text", ""),
            notificationType=(
                notificationTypeObject.get("name", "")
                if notificationTypeObject
                else "-"),
            site=item.get("reportingSite").get("name") if item.get("reportingSite") else None,
            application=(
                notificationTypeObject.get("application", {}).get("name", "")
                if notificationTypeObject
                else "-"
            ),
            severityExternalId=(
                item.get("severity", {}).get("externalId", "")
                if notificationTypeObject
                else "-"
            ),
            severityDescription=(
                item.get("severity", {}).get("description", "")
                if notificationTypeObject
                else "-"
            ),
            rawEvent={
                "notificationType": notificationTypeObject.get("name", ""),
                "description": notificationTypeObject.get("description", ""),
                "severity": item.get("severity", {}).get("description", ""),
                "properties": eventObject.get("properties", {}).get("properties", {}),
                "users": users,
                "externalUsers": eventObject.get("externalUsers", []),
                "roles": roles,
                "notificationReference": item.get("externalId"),
                "applicationGroups": applicationGroups,
                "appId": notificationTypeObject.get("application", {}).get("azureAppId", "")
            }
            
        )
    
    def remove_substring(external_id):
      return external_id.replace("UserRoleSite_", "")
  

class NotificationOnScreenModel(BaseModel):
    date: str
    updateDate: Optional[str]
    severity: dict[str, Any]
    severityLevel: int = 0
    site: str
    notificationType: str
    application: str
    applicationIconUrl: Optional[str] = None
    notificationMessage: str
    externalId: str
    comments: Optional[List[NotificationOnScreenCommentModel]] = None

    def mapFromResult(item: Any):

        severity_level_mapping = {
            "LOW": 1,
            "MEDIUM": 2,
            "HIGH": 3,
        }

        if item.get("template"):
            comments_data = (
                item.get("comments", {}).get("items", [])
                if item.get("comments")
                else []
            )
            comments = [
                NotificationOnScreenCommentModel.mapFromResult(comment)
                for comment in comments_data
                if len(comments_data) > 0
            ]

            notification_type_object = item.get("template", {}).get(
                "notificationType", {}
            )
            application_icon_url = (
                notification_type_object.get("application", {}).get("iconUrl", "")
                if notification_type_object
                else "-"
            )

            severity = item.get("severity", {})
            severity_name = severity.get("name", "").upper()
            severity_level = severity_level_mapping.get(severity_name, 0)

            return NotificationOnScreenModel(
                externalId=item.get("externalId"),
                date=item.get("createdAt", ""),
                updateDate=(
                    item.get("newUpdateTime", "") 
                    if item.get("newUpdateTime") 
                    else ""
                ),
                severity=severity,
                severityLevel=severity_level,
                notificationMessage=item.get("text", ""),
                comments=comments if comments else None,
                applicationIconUrl=application_icon_url,
                site=(
                    item.get("reportingSite", {}).get("name", "")
                    if item.get("reportingSite")
                    else ""
                ),
                notificationType=(
                    notification_type_object.get("name", "")
                    if notification_type_object
                    else "-"
                ),
                application=(
                    notification_type_object.get("application", {}).get("name", "")
                    if notification_type_object
                    else "-"
                ),
            )


class NotificationTextRequestModel(BaseModel):
    comment: str
