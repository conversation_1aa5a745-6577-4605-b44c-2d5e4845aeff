from pydantic import BaseModel
from typing import Any, List, Optional


class NotificationRoleModel(BaseModel):
    externalId: str
    name: str
    description: Optional[str] = None
    siteCode: Optional[str] = None
    userIds: Optional[List[str]] = []
    space: Optional[str] = None

    def mapFromResult(item: Any):


        site_code =  item.get("reportingSite").get("siteCode") if item.get("reportingSite") else None
        role = item.get("role") if item.get("role") else None
        name = role.get("name") if role else ''
        display_name = f'({site_code}) {name}' if site_code else name
        
        description = ''
        if role is not None:
            description = role.get("description") if role.get("description") else ''
        

        return NotificationRoleModel(
            externalId=item.get("externalId", ""),
            name=display_name,
            description=description,
            siteCode = site_code,
            userIds=[
                subitem.get("externalId")
                for subitem in item.get("users", {}).get("items", [])
                if subitem
            ]
            if item.get("users")
            else [],
            space=item.get("space", ""),
        )
