from pydantic import BaseModel
from typing import Any, Optional

class NotificationSeverityModel(BaseModel):
    externalId: str
    name: str
    description: str
    space: Optional[str] = None

    def mapFromResult(item: Any):
        return NotificationSeverityModel(
            externalId=item.get("externalId", ""),
            name=item.get("name", ""),
            description=item.get("description", ""),       
            space=item.get("space", "")     
        )
