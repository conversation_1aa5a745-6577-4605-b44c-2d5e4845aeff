from pydantic import BaseModel
from typing import Any, List, Optional
import app.models as models
import app.utils as utils


class RelationModel(BaseModel):
    space: Optional[str] = None
    externalId: str

    def mapFromResult(item: Any):
        return RelationModel(
            externalId=item.get("externalId", "") if item else "",
            space=item.get("space", "") if item else "",
        )

    def mapFromJson(item: dict):
        return RelationModel(
            externalId=item.externalId if item else None,
            space=item.space if item else None,
        )


class NotificationTemplateExtensionModel(BaseModel):
    externalId: str = None
    space: Optional[str] = None
    owner: Optional[RelationModel] = None
    channels: Optional[List[models.NotificationChannelModel]] = []
    frequencyCronExpression: Optional[str] = ""
    template: Optional[RelationModel] = None

    def mapFromResult(item: Any):
        return NotificationTemplateExtensionModel(
            externalId=item.get("externalId", ""),
            space=item.get("space", ""),
            owner=RelationModel.mapFromResult(item.get("owner")),
            channels=[
                models.NotificationChannelModel.mapFromResult(subitem)
                for subitem in item.get("channels", {}).get("items", [])
                if subitem
            ]
            if item.get("channels")
            else [],
            frequencyCronExpression=item.get("frequencyCronExpression", ""),
            template=RelationModel.mapFromResult(item.get("template")),
        )


class NotificationTemplateExtensionEditModel(BaseModel):
    externalId: str = None
    space: Optional[str] = None
    owner: Optional[RelationModel] = None
    channels: Optional[List[models.NotificationChannelModel]] = []
    frequencyCronExpression: Optional[utils.scheduleModel]
    template: Optional[RelationModel] = None

    def mapFromResult(item: Any):
        cron_expression = item.get("frequencyCronExpression", "")
        converted_cron = None
        if cron_expression is not None and len(cron_expression) > 0:
            converted_cron = utils.cron.describe(cron_expression)

        return NotificationTemplateExtensionEditModel(
            externalId=item.get("externalId", ""),
            space=item.get("space", ""),
            owner=RelationModel.mapFromResult(item.get("owner")),
            channels=[
                models.NotificationChannelModel.mapFromResult(subitem)
                for subitem in item.get("channels", {}).get("items", [])
                if subitem
            ]
            if item.get("channels")
            else [],
            frequencyCronExpression=converted_cron,
            template=RelationModel.mapFromResult(item.get("template")),
        )

    def mapFromJson(item: Any):
        cron_expression = item.frequencyCronExpression
        converted_cron = None
        if cron_expression is not None and len(cron_expression) > 0:
            converted_cron = utils.cron.describe(cron_expression)

        return NotificationTemplateExtensionEditModel(
            externalId=item.externalId,
            space=item.space,
            owner=RelationModel.mapFromJson(item.owner),
            channels=[subitem for subitem in item.channels if subitem]
            if item.channels
            else [],
            frequencyCronExpression=converted_cron,
            template=RelationModel.mapFromJson(item.template),
        )
