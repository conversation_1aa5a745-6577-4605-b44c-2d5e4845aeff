from pydantic import BaseModel
from typing import Dict, List, Optional

class NotificationTemplateFilterRequestModel(BaseModel):
    adminLevel: Optional[bool] = None
    sortColumn: Optional[Dict[str,str]] = {}
    notification_type_external_ids: Optional[List[str]] = []
    application_external_ids: Optional[List[str]] = []
    search: Optional[str] = None
    channels_external_ids: Optional[List[str]] = []
    subscribers_external_ids: Optional[List[str]] = []