from pydantic import BaseModel, Field
from typing import Any, List, Optional, Union, Dict, Annotated
from datetime import datetime
import app.models as models
import app.utils as utils
from fastapi import Query
import json

MIN_PAGE_SIZE = 10

class RelationModel(BaseModel):
    space: Optional[str] = None
    externalId: str

    def mapFromResult(item: Any):
        return RelationModel(
            externalId=item.get("externalId", "") if item else "",
            space=item.get("space", "") if item else "",
        )

    def mapFromJson(item: dict):
        return RelationModel(
            externalId=item.externalId if item else None,
            space=item.space if item else None,
        )


class NotificationTemplateCreateModel(BaseModel):
    code: str = "NTFTEMP"
    externalId: Optional[str] = None
    name: str
    creator: Optional[RelationModel] = None
    notificationType: RelationModel
    text: str
    severity: RelationModel
    conditionalExpression: str
    adminLevel: bool
    customChannelEnabled: bool
    customFrequencyEnabled: bool
    channels: Optional[List[RelationModel]]
    frequencyCronExpression: Optional[Union[utils.scheduleModel, str]] = None
    subscribedUsers: Optional[List[str]] = None
    subscribedUserRoles: Optional[List[str]] = None
    subscribedApplicationGroups: Optional[List[str]] = None
    subscribedExternalUsers: Optional[List[str]] = None
    notificationTemplateExtensions: Optional[List[RelationModel]] = None
    emailRequester: Optional[str] = None
    deleted: Optional[bool] = False
    allUsers: Optional[bool] = False
    externalUsers: Optional[bool] = False
    subject: Optional[str] = None
    reportingSite: Optional[RelationModel] = None
    blocklist: Optional[List[str]] = None
    blocklistRoles: Optional[List[str]] = None

    def mapfromJson(item: dict):
        return NotificationTemplateCreateModel(
            externalId=item.externalId,
            name=item.name,
            creator=RelationModel.mapFromJson(item.creator),
            notificationType=RelationModel.mapFromJson(item.notificationType),
            text=item.text,
            severity=RelationModel.mapFromJson(item.severity),
            conditionalExpression=item.conditionalExpression,
            adminLevel=item.adminLevel,
            customChannelEnabled=item.customChannelEnabled,
            customFrequencyEnabled=item.customFrequencyEnabled,
            channels=(
                [
                    RelationModel.mapFromJson(subitem)
                    for subitem in item.channels
                    if subitem
                ]
                if item.channels
                else []
            ),
            frequencyCronExpression=item.frequencyCronExpression,
            subscribedExternalUsers=item.subscribedExternalUsers,
            subscribedUsers=(
                [subitem.externalId for subitem in item.subscribedUsers if subitem]
                if item.subscribedUsers
                else []
            ),
            blocklist=(
                [subitem for subitem in item.blocklist] if item.blocklist else []
            ),
            blocklistRoles=(
                [subitem for subitem in item.blocklistRoles]
                if item.blocklistRoles
                else []
            ),
            subscribedUserRoles=(
                [subitem.externalId for subitem in item.subscribedRoles if subitem]
                if item.subscribedRoles
                else []
            ),
            subscribedApplicationGroups=(
                [
                    subitem.externalId
                    for subitem in item.subscribedApplicationGroups
                    if subitem
                ]
                if item.subscribedApplicationGroups
                else []
            ),
            notificationTemplateExtensions=(
                [
                    RelationModel.mapFromJson(subitem)
                    for subitem in item.notificationTemplateExtensions
                    if subitem
                ]
                if item.notificationTemplateExtensions
                else []
            ),
            emailRequester=item.creator.externalId,
            deleted=item.deleted,
            allUsers=item.allUsers,
            externalUsers=item.externalUsers,
            subject=item.subject,
        )

    def __setattr__(self, key, value):
        if key == "code":
            raise AttributeError("Cannot modify CODE")
        super().__setattr__(key, value)

    def generate_external_id(self) -> str:
        result = (
            (self.code + "-" + self.notificationType.externalId + "-" + self.name)
            .replace(" ", "-")
            .replace("(", "")
            .replace(")", "")
            .upper()
        )
        return result


class NotificationTemplateModel(BaseModel):
    externalId: str
    name: str
    creator: Optional[models.NotificationUserModel]
    notificationType: Optional[models.NotificationTypeModel]
    text: str
    severity: Optional[models.NotificationSeverityModel]
    conditionalExpression: str
    adminLevel: bool
    customChannelEnabled: bool
    customFrequencyEnabled: bool
    channels: Optional[List[models.NotificationChannelModel]]
    frequencyCronExpression: str
    subscribedUsers: Optional[List[models.NotificationUserModel]]
    subscribedRoles: Optional[List[models.NotificationRoleModel]]
    subscribedApplicationGroups: Optional[List[models.NotificationApplicationGroup]]
    subscribedExternalUsers: Optional[List[str]] = None
    notificationTemplateExtensions: Optional[
        List[models.NotificationTemplateExtensionModel]
    ]
    deleted: Optional[bool] = False
    allUsers: Optional[bool] = False
    externalUsers: Optional[bool] = False
    subject: Optional[str] = None
    blocklist: Optional[List[models.NotificationUserModel]] = []
    blocklistRoles: Optional[List[models.NotificationRoleModel]] = []

    def mapFromResult(item: Any):
        creator = item.get("creator")
        notification_type = item.get("notificationType")
        severity = item.get("severity")

        creator_model = (
            models.NotificationUserModel.mapFromResult(creator) if creator else None
        )

        notification_type_model = (
            models.NotificationTypeModel.mapFromResult(notification_type)
            if notification_type
            else None
        )

        severity_model = (
            models.NotificationSeverityModel.mapFromResult(severity)
            if severity
            else None
        )

        return NotificationTemplateModel(
            externalId=item.get("externalId", ""),
            name=item.get("name", ""),
            creator=creator_model,
            notificationType=notification_type_model,
            text=item.get("text", ""),
            severity=severity_model,
            conditionalExpression=item.get("conditionalExpression", ""),
            adminLevel=item.get("adminLevel", False),
            customChannelEnabled=item.get("customChannelEnabled", False),
            customFrequencyEnabled=item.get("customFrequencyEnabled", False),
            channels=(
                [
                    models.NotificationChannelModel.mapFromResult(subitem)
                    for subitem in item.get("channels", {}).get("items", [])
                    if subitem
                ]
                if item.get("channels")
                else []
            ),
            frequencyCronExpression=item.get("frequencyCronExpression", ""),
            subscribedUsers=(
                [
                    models.NotificationUserModel.mapFromResult(subitem)
                    for subitem in item.get("subscribedUsers", {}).get("items", [])
                    if subitem
                ]
                if item.get("subscribedUsers")
                else []
            ),
            subscribedRoles=(
                [
                    models.NotificationRoleModel.mapFromResult(subitem)
                    for subitem in item.get("subscribedRoles", {}).get("items", [])
                    if subitem
                ]
                if item.get("subscribedRoles")
                else []
            ),
            subscribedExternalUsers=(
                item.get("subscribedExternalUsers", {})
                if item.get("subscribedExternalUsers")
                else []
            ),
            subscribedApplicationGroups=(
                [
                    models.NotificationApplicationGroup.mapFromResult(subitem)
                    for subitem in item.get("subscribedApplicationGroups", {}).get(
                        "items", []
                    )
                    if subitem
                ]
                if item.get("subscribedApplicationGroups")
                else []
            ),
            notificationTemplateExtensions=(
                [
                    models.NotificationTemplateExtensionModel.mapFromResult(subitem)
                    for subitem in item.get("notificationTemplateExtensions", {}).get(
                        "items", []
                    )
                    if subitem
                ]
                if item.get("notificationTemplateExtensions")
                else []
            ),
            deleted=item.get("deleted", False),
            allUsers=item.get("allUsers", False),
            externalUsers=item.get("externalUsers", False),
            subject=item.get("subject", None),
        )
        
class NotificationBasicInfoModel(BaseModel):
    externalId: str
    creator: Optional[models.NotificationUserModel]
    customChannelEnabled: bool
    customFrequencyEnabled: bool

    def map_from_result(item: Any):
        creator = item.get("creator")

        creator_model = (
            models.NotificationUserModel.mapFromResult(creator) if creator else None
        )

        return NotificationBasicInfoModel(
            externalId=item.get("externalId", ""),
            creator=creator_model,
            customChannelEnabled=item.get("customChannelEnabled", False),
            customFrequencyEnabled=item.get("customFrequencyEnabled", False),
        )

class NotificationTemplateEditModel(BaseModel):
    name: str
    creator: Optional[RelationModel] = None
    channels: Optional[List[models.NotificationChannelModel]]
    conditionalExpression: str
    customChannelEnabled: bool
    customFrequencyEnabled: bool
    externalId: str
    severity: Optional[models.NotificationSeverityModel]
    space: str
    text: str
    frequencyCronExpression: Optional[utils.scheduleModel]
    subscribedUsers: List[dict]
    subscribedUserRoles: List[models.NotificationRoleModel]
    subscribedApplicationGroups: List[models.NotificationApplicationGroup]
    notificationType: str
    notificationTypeName: str
    notificationTypeEntity: Optional[str]
    application: str
    applicationName: str
    adminLevel: bool
    deleted: Optional[bool] = False
    editedBy: str
    editedAt: str

    def mapFromResult(item: Any):
        cron_expression = item.get("frequencyCronExpression", "")
        converted_cron = None

        if cron_expression is not None and len(cron_expression) > 0:
            converted_cron = utils.cron.describe(cron_expression)

        if item.get("creator") is not None:
            if (
                item.get("creator").get("firstName") is not None
                and item.get("creator").get("lastName") is not None
            ):
                editedBy = (
                    item.get("creator").get("lastName")
                    + ", "
                    + item.get("creator").get("firstName")
                )
            else:
                editedBy = item.get("creator").get("email")

        return NotificationTemplateEditModel(
            name=item.get("name", ""),
            creator=RelationModel.mapFromResult(item.get("creator")),
            externalId=item.get("externalId", ""),
            text=item.get("text", ""),
            severity=item.get("severity"),
            notificationType=item.get("notificationType", {}).get("externalId", ""),
            notificationTypeName=item.get("notificationType", {}).get("name", ""),
            notificationTypeEntity=item.get("notificationType", {}).get(
                "entityType", ""
            ),
            application=item.get("notificationType")
            .get("application")
            .get("externalId"),
            applicationName=item.get("notificationType").get("application").get("name"),
            adminLevel=item.get("adminLevel"),
            conditionalExpression=item.get("conditionalExpression", ""),
            customChannelEnabled=item.get("customChannelEnabled", False),
            customFrequencyEnabled=item.get("customFrequencyEnabled", False),
            space=item.get("space", ""),
            channels=(
                [
                    models.NotificationChannelModel.mapFromResult(subitem)
                    for subitem in item.get("channels", {}).get("items", [])
                    if subitem
                ]
                if item.get("channels")
                else []
            ),
            frequencyCronExpression=converted_cron,
            subscribedUsers=(
                item.get("subscribedUsers", {}).get("items", [])
                if item.get("subscribedUsers")
                else []
            ),
            subscribedUserRoles=(
                [
                    models.NotificationRoleModel.mapFromResult(subitem)
                    for subitem in item.get("subscribedRoles", {}).get("items", [])
                    if subitem
                ]
                if item.get("subscribedRoles")
                else []
            ),
            subscribedApplicationGroups=(
                [
                    models.NotificationApplicationGroup.mapFromResult(subitem)
                    for subitem in item.get("subscribedApplicationGroups", [])
                    if subitem
                ]
                if item.get("subscribedApplicationGroups")
                else []
            ),
            deleted=item.get("deleted", False),
            editedBy=editedBy,
            editedAt=item.get("lastUpdatedTime", ""),
        )


class NotificationTemplateEditResponseModel(BaseModel):
    name: str
    creator: Optional[RelationModel] = None
    channels: Optional[List[models.NotificationChannelModel]]
    conditionalExpression: str
    customChannelEnabled: bool
    customFrequencyEnabled: bool
    externalId: str
    severity: Optional[models.NotificationSeverityModel]
    space: str
    text: str
    frequencyCronExpression: Optional[utils.scheduleModel]
    subscribedUsers: List[dict]
    subscribedUserRoles: List[models.RoleModel]
    subscribedApplicationGroups: List[models.NotificationApplicationGroup]
    subscribedExternalUsers: Optional[List[str]] = None
    notificationType: str
    notificationTypeName: str
    notificationTypeEntity: Optional[str]
    application: str
    applicationName: str
    adminLevel: bool
    deleted: Optional[bool] = False
    editedBy: str
    editedAt: str
    allUsers: Optional[bool] = False
    externalUsers: Optional[bool] = False
    subject: Optional[str] = None
    blocklist: Optional[List[dict]] = []
    blocklistRoles: Optional[List[models.RoleModel]] = []
    notificationTypeProperties: Optional[List[Dict[str, Any]]] = []

    def mapFromResult(item: Any):
        cron_expression = item.get("frequencyCronExpression", "")
        converted_cron = None

        day_of_week = []

        if cron_expression:
            parts = cron_expression.split()
            if len(parts) >= 6:
                days_str = parts[5].upper()

                if days_str != "*":
                    day_map = {
                        "MON": "Monday",
                        "TUE": "Tuesday",
                        "WED": "Wednesday",
                        "THU": "Thursday",
                        "FRI": "Friday",
                        "SAT": "Saturday",
                        "SUN": "Sunday",
                    }
                    day_of_week = [day_map.get(day, day) for day in days_str.split(",")]

            converted_cron = (
                utils.cron.describe(cron_expression) if utils.cron else None
            )

            if converted_cron and hasattr(converted_cron, "day_of_week"):
                converted_cron.day_of_week = day_of_week

        return NotificationTemplateEditResponseModel(
            name=item.get("name", ""),
            creator=RelationModel.mapFromResult(item.get("creator")),
            externalId=item.get("externalId", ""),
            text=item.get("text", ""),
            severity=item.get("severity"),
            notificationType=item.get("notificationType", ""),
            notificationTypeName=item.get("notificationTypeName", ""),
            notificationTypeEntity=item.get("notificationTypeEntity", ""),
            notificationTypeProperties=item.get("notificationTypeProperties", []),
            application=item.get("application", ""),
            applicationName=item.get("applicationName", ""),
            adminLevel=item.get("adminLevel"),
            conditionalExpression=item.get("conditionalExpression", ""),
            customChannelEnabled=item.get("customChannelEnabled", False),
            customFrequencyEnabled=item.get("customFrequencyEnabled", False),
            space=item.get("space", ""),
            channels=(
                [
                    models.NotificationChannelModel.mapFromResult(subitem)
                    for subitem in item.get("channels", [])
                    if subitem
                ]
                if item.get("channels")
                else []
            ),
            frequencyCronExpression=converted_cron,
            subscribedUsers=(
                item.get("subscribedUsers", [])
                if item.get("subscribedUsers") and not item.get("allUsers", False)
                else []
            ),
            blocklist=item.get("blocklist", []),
            subscribedExternalUsers=(
                item.get("subscribedExternalUsers", {})
                if item.get("subscribedExternalUsers")
                else []
            ),
            subscribedUserRoles=(
                [
                    models.RoleModel(**role)
                    for role in item.get("subscribedUserRoles", [])
                ]
                if item.get("subscribedUserRoles")
                else []
            ),
            blocklistRoles=(
                [models.RoleModel(**role) for role in item.get("blocklistRoles", [])]
                if item.get("blocklistRoles")
                else []
            ),
            subscribedApplicationGroups=(
                [
                    models.NotificationApplicationGroup.mapFromResult(subitem)
                    for subitem in item.get("subscribedApplicationGroups", [])
                    if subitem and not item.get("allUsers", False)
                ]
                if item.get("subscribedApplicationGroups")
                else []
            ),
            deleted=item.get("deleted", False),
            editedBy=item.get("editedBy", ""),
            editedAt=item.get("editedAt", ""),
            allUsers=item.get("allUsers", False),
            externalUsers=item.get("externalUsers", False),
            subject=item.get("subject", ""),
        )


class NotificationTemplateCreateRequestModel(BaseModel):
    notificationTemplate: NotificationTemplateCreateModel
    isAdminEdition: bool


class NotificationTemplateByApplicationGroupResponseModel(BaseModel):
    externalId: str
    name: str
    subscribedUsers: Optional[List[RelationModel]] = None
    subscribedRoles: Optional[List[RelationModel]] = None
    subscribedApplicationGroups: Optional[List[RelationModel]] = None
    subscribedExternalUsers: Optional[List[str]] = None

    def mapFromResult(item: Any):
        return NotificationTemplateByApplicationGroupResponseModel(
            externalId=item.get("externalId", ""),
            name=item.get("name", ""),
            subscribedUsers=(
                [
                    RelationModel.mapFromResult(subitem)
                    for subitem in item.get("subscribedUsers", {}).get("items", [])
                    if subitem
                ]
                if item.get("subscribedUsers")
                else []
            ),
            subscribedRoles=(
                [
                    RelationModel.mapFromResult(subitem)
                    for subitem in item.get("subscribedRoles", {}).get("items", [])
                    if subitem
                ]
                if item.get("subscribedRoles")
                else []
            ),
            subscribedExternalUsers=(
                item.get("subscribedExternalUsers", {})
                if item.get("subscribedExternalUsers")
                else []
            ),
            subscribedApplicationGroups=(
                [
                    RelationModel.mapFromResult(subitem)
                    for subitem in item.get("subscribedApplicationGroups", {}).get(
                        "items", []
                    )
                    if subitem
                ]
                if item.get("subscribedApplicationGroups")
                else []
            ),
        )


def common_template_request_params(
    is_admin_edition: Annotated[Union[bool, None], Query(alias="isAdminEdition")] = None,
    subscribers_ids: Annotated[Union[list[str], None], Query(alias="subscribersIds")] = None,
    application_id: Annotated[Union[str, None], Query(alias="applicationId")] = None,
    page_size: Annotated[int, Query(alias="pageSize")] = MIN_PAGE_SIZE,
    cursor: Annotated[Union[str, None], Query(alias="cursor")] = None,
    q: Annotated[Union[str, None], Query(alias="q")] = None,
    channels: Annotated[Union[list[str], None], Query(alias="channels")] = None,
    notification_type: Annotated[Union[str, None], Query(alias="notificationType")] = None,
    sort_column: Annotated[Union[str, None], Query(alias="sortColumn")] = None,
):
    return dict(
        is_admin_edition=is_admin_edition,
        page_size=page_size,
        cursor=cursor,
        q=q,
        channels=channels,
        notification_type=notification_type,
        application_id=application_id,
        subscribers_ids=subscribers_ids,
        sort_column=json.loads(sort_column),
    )