from pydantic import BaseModel
from typing import Any, List, Optional


class NotificationTemplateTableModel(BaseModel):
    code: str = "NTFTEMP"

    externalId: str
    space: str
    templateName: str
    application: str
    channels: List[str]
    sendTo: List[str]
    creator: str
    customChannelEnabled: bool
    customFrequencyEnabled: bool
    editedBy: str
    editedAt: str
    allUsers: Optional[bool] = False
    externalUsers: Optional[bool] = False
    adminLevel: bool

    def __setattr__(self, key, value):
        if key == "code":
            raise AttributeError("Cannot modify CODE")
        super().__setattr__(key, value)

    def mapFromResult(item: Any):
        users = []
        roles = []
        groups = []
        send_to = []
        editedBy = ""

        if not item.get("allUsers", False):
            for user in item.get("subscribedUsers", {}).get("items", []):
                name = "none"
                
                if (
                    user.get("firstName") is not None
                    and user.get("lastName") is not None
                ):
                    name = user.get("lastName") + ", " + user.get("firstName")

                users.append(name)

            for subitem in item.get("subscribedRoles", {}).get("items", []):
                role = subitem.get("role")

                if isinstance(role, dict):
                    role_name = role.get("name")
                    role_site_code = (
                        subitem.get("reportingSite").get("siteCode")
                        if subitem.get("reportingSite")
                        else None
                    )
                    role_display_name = (
                        f"({role_site_code}) {role_name}" if role_site_code else role_name
                    )
                    roles.append(role_display_name)

            for subitem in item.get("subscribedApplicationGroups", {}).get("items", []):
                y = subitem.get("name")
                groups.append(y)

            if len(users) > 0:
                send_to.extend(users)

            if len(roles) > 0:
                send_to.extend(roles)

            if len(groups) > 0:
                send_to.extend(groups)

            send_to.sort()

        if item.get("creator") is not None:
            if (
                item.get("creator").get("firstName") is not None
                and item.get("creator").get("lastName") is not None
            ):
                editedBy = (
                    item.get("creator").get("lastName")
                    + ", "
                    + item.get("creator").get("firstName")
                )
            else:
                editedBy = item.get("creator").get("email")

        return NotificationTemplateTableModel(
            externalId=item.get("externalId", ""),
            creator=(
                ""
                if item.get("creator") is None
                else item.get("creator").get("externalId")
            ),
            space=item.get("space", ""),
            templateName=item.get("name", ""),
            customChannelEnabled=item.get("customChannelEnabled", False),
            customFrequencyEnabled=item.get("customFrequencyEnabled", False),
            application=(
                item.get("notificationType").get("application", "").get("alias", "")
                if item.get("notificationType")
                and item.get("notificationType").get("application", "")
                else "-"
            ),
            channels=(
                [
                    subitem.get("description")
                    for subitem in item.get("channels", {}).get("items", [])
                    if subitem
                ]
                if item.get("channels")
                else []
            ),
            sendTo=["All Users"] if item.get("allUsers", False) else send_to,
            editedBy=editedBy,
            editedAt=item.get("lastUpdatedTime", ""),
            allUsers=item.get("allUsers") if item.get("allUsers") else False,
            adminLevel=item.get("adminLevel") if item.get("adminLevel") else False,
        )
