from pydantic import BaseModel
from typing import Any, List, Optional, Dict
import app.models as models


class NotificationTypeModel(BaseModel):
    name: str
    space: Optional[str] = None
    description: Optional[str] = None
    externalId: str
    application: models.ApplicationModel
    hasTemplates: Optional[bool] = None
    entityType: Optional[str] = ""
    properties: Optional[List[Dict[str, Any]]] = []

    def mapFromResult(item: Any):
        properties = []
        if item.get("properties") is not None and len(item.get("properties")) > 0:
            properties = sorted(item.get("properties", []), key=lambda x: x["name"])

        return NotificationTypeModel(
            name=item.get("name", ""),
            description=item.get("description", ""),
            externalId=item.get("externalId", ""),
            application=models.ApplicationModel.mapFromResult(
                item.get("application", {})
            )
            if item.get("application", {})
            else None,
            space=item.get("space", ""),
            entityType=item.get("entityType", ""),
            properties=properties
        )
