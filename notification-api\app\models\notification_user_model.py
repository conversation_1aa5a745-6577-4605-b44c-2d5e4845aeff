from pydantic import BaseModel
from typing import Any, List, Optional
import app.models as models


class NotificationUserModel(BaseModel):
    externalId: str
    space: Optional[str] = None
    name: str = None
    email: str = None
    active: bool
    admin: bool
    roles: Optional[List[models.NotificationRoleModel]] = []

    def mapFromResult(item: Any):
        return NotificationUserModel(
            externalId=item.get("externalId", ""),
            space=item.get("space","") if item.get("space") is not None else "",
            name=item.get("name", "") if item.get("name") is not None else "",
            email=item.get("email", "") if item.get("email") is not None else "",
            active=item.get("active", False),
            admin=item.get("admin", False),
            roles=[
                models.NotificationRoleModel.mapFromResult(subitem)
                for subitem in item.get("roles", {}).get("items", [])
                if subitem
            ]
            if item.get("roles")
            else []
        )
