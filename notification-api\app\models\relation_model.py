from typing import Any, Optional
from pydantic import BaseModel


class RelationModel(BaseModel):
    space: Optional[str] = None
    externalId: str

    def mapFromResult(self: Any):
        return RelationModel(
            externalId=self.get("externalId", "") if self else "",
            space=self.get("space", "") if self else "",
        )

    def mapFromJson(self: dict):
        return RelationModel(
            externalId=self.externalId if self else None,
            space=self.space if self else None,
        )