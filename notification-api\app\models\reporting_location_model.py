from typing import Optional, List, Annotated, Union
from pydantic import BaseModel
from fastapi import Query

class ReportingLocationModel(BaseModel):
    externalId: Optional[str] = None
    name: Optional[str] = None
    space: Optional[str] = None
    description: Optional[str] = None

def common_request_params(
    reporting_unit_external_ids: Annotated[
        Union[str, None], Query(alias="reportingUnitExternalId")
    ] = None,
):
    units = []

    if reporting_unit_external_ids:
        for unit in reporting_unit_external_ids.split(','):
            units.append(unit)

    return dict(
        units=units,
    )
