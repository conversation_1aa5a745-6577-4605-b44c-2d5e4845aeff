from typing import Optional, List
from pydantic import BaseModel

class ReportingUnitModel(BaseModel):
    externalId: Optional[str] = None
    name: Optional[str] = None
    space: Optional[str] = None
    description: Optional[str] = None

def parse_units(data: List[dict]) -> List[ReportingUnitModel]:
    units = []
    reporing_units = data[0].get("reportingUnits", [])

    for item in reporing_units:
        units.append(ReportingUnitModel(**item))

    return units