from typing import List, Optional
from pydantic import BaseModel
import app.models as models


class RoleModel(BaseModel):
    externalId: str
    name: str
    space: str
    users: List[models.UserModel]
    site: Optional[models.ReportingSiteModel] = None


class ResponseSearchUsersTerm(BaseModel):
    users: List[models.UserModel] = []
    roles: List[RoleModel] = []
    applicationGroups: List[models.NotificationApplicationGroup] = []


def parse_roles(data: List[dict]) -> List[RoleModel]:
    roles = []
    for item in data:
        users = [models.UserModel(**user_attr) for user_attr in item["users"]]

        roles.append(
            RoleModel(
                externalId=item["externalId"],
                name=item["name"],
                space=item["space"],
                users=users,
                site=item.get("site", {}),
            )
        )
    return roles


def map_from_result(data: List[dict]) -> List[RoleModel]:
    roles = []
    for item in data:
        users = item["usersComplements"]["items"]
        user_info = [
            models.UserModel(**user_attr["userAzureAttribute"]["user"])
            for user_attr in users
            if user_attr.get("userAzureAttribute") 
            and user_attr["userAzureAttribute"].get("user")
        ]

        site_code = ""
        reporting_site = item.get("reportingSite", {})

        role_name = item.get("role", {}).get("name", "")
        if reporting_site:
            site_code = reporting_site.get("siteCode", "")

        name_with_site_code = f"({site_code}) {role_name}" if site_code else role_name

        roles.append(
            RoleModel(
                externalId=item["externalId"],
                name=name_with_site_code,
                space=item["space"],
                users=user_info,
                site=(
                    models.ReportingSiteModel(
                        externalId=reporting_site.get("externalId"),
                        name=reporting_site.get("name"),
                        space=reporting_site.get("space"),
                        siteCode=reporting_site.get("siteCode"),
                    )
                    if reporting_site
                    else {}
                ),
            )
        )
    return roles
