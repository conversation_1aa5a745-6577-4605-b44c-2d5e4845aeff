from typing import Optional, Annotated, Union, List, Any
from pydantic import BaseModel
from fastapi import Query

class TeamModel(BaseModel):
    externalId: Optional[str] = None
    name: Optional[str] = None
    space: Optional[str] = None
    description: Optional[str] = None

def parse_teams_from_filter(data: List[dict], params: Any) -> List[TeamModel]:
    teams = []
    print(params)


    for item in data:
        unit_info = item.get("reportingUnits", {}).get("items", [])

        if params.get("units"):
            result = [unit for unit in unit_info if unit['externalId'] in params["units"]]

            if len(result) > 0 and unit_info:
                teams.append(TeamModel(**item))
        else: 
            teams.append(TeamModel(**item))
            
    return teams

def common_request_params(
    reporting_site_external_id: Annotated[
        Union[str, None], Query(alias="reportingSiteExternalId")
    ] = None,
    reporting_unit_external_ids: Annotated[
        Union[str, None], Query(alias="reportingUnitExternalId")
    ] = None,
):
    site = reporting_site_external_id
    units = []

    if reporting_unit_external_ids:
        for unit in reporting_unit_external_ids.split(','):
            units.append(unit)

    return dict(
        site=site,
        units=units,
    )