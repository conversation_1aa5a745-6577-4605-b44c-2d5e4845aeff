from typing import Any, List, Optional
from pydantic import BaseModel
import app.models as models
import app.utils as utils

    
class DataPointModel(BaseModel):
    timestamp: str = None
    value: Optional[float] = None


class ResponseTimeseriesDetails(BaseModel):
    name:Optional[str] = ""
    description:Optional[str] = ""
    units:Optional[str] = ""
    id:Optional[int] = None
    externalId:Optional[str] = ""
    isString:bool = False
    isStep:bool = False
    datasetName:Optional[str] = ""
    assetName:Optional[str] = ""
    createdAt:Optional[str] = ""
    updatedAt:Optional[str] = ""
    lastReading:Optional[int] = ""
    datapointsItems:Optional[List[DataPointModel]] = []
    urlTimeSeriesDetails:str = ""
    urlDataSetDetails:str = ""
    urlLinkedAssets:str = ""


    def mapFromResult(item: Any):
        return ResponseTimeseriesDetails(
            name=getattr(item, "name", ""),
            description=getattr(item, "description", ""),
            units=getattr(item, "unit", ""),
            id=getattr(item, "id", ""),
            externalId=getattr(item, "external_id", ""),
            isString=getattr(item, "is_string", False),
            isStep=getattr(item, "is_step", False),
            createdAt=utils.date.convertUnixToFormattedTimeStampUTC(getattr(item, "created_time", None)),
            updatedAt=utils.date.convertUnixToFormattedTimeStampUTC(getattr(item, "last_updated_time", None))
        )
