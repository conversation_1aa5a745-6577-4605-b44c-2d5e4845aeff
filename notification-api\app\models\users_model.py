from typing import Annotated, List, Optional, Union, Any
from pydantic import BaseModel
from fastapi import Query


class UserModel(BaseModel):
    email: str
    externalId: str
    firstName: Optional[str] = "-"
    lastName: Optional[str] = "-"
    teams: Optional[object] = []


def parse_users(data: List[dict]) -> List[UserModel]:
    users = []

    for item in data:

        user = UserModel(
                email=item["email"],
                externalId=item["externalId"],  # Note o snake_case
                firstName=item.get("firstName") or "",  # Trata None
                lastName=item.get("lastName") or "",
                displayName=item["displayName"]
            )
        if user:
            users.append(user)

    return users


def parse_users_from_filter(data: List[dict], params: Any) -> List[UserModel]:
    users = []

    for item in data:
        user_info = item.get("userAzureAttribute", {}).get("user")
        user_teams = user_info.get("teams", []).get("items", [])

        if params.get("teams"):
            result = [team for team in user_teams if team['externalId'] in params["teams"]]

            if len(result) > 0 and user_info:
                users.append(UserModel(**user_info))
        else: 
            if user_info:
                users.append(UserModel(**user_info))
            
    return users


def common_user_request_params(
    reporting_site_external_id: Annotated[
        Union[str, None], Query(alias="reportingSiteExternalId")
    ] = None,
    reporting_unit_external_ids: Annotated[
        Union[str, None], Query(alias="reportingUnitExternalId")
    ] = None,
    reporting_location_external_ids: Annotated[
        Union[str, None], Query(alias="reportingLocationExternalId")
    ] = None,
    teams: Annotated[Union[str, None], Query(alias="team")] = None,
    role_external_ids: Annotated[
        Union[str, None], Query(alias="roleExternalId")
    ] = None,
    role_external_site_ids: Annotated[
        Union[str, None], Query(alias="roleExternalSiteId")
    ] = None,
    role_external_app_ids: Annotated[
        Union[str, None], Query(alias="roleExternalAppId")
    ] = None,
    user: Annotated[
        Union[str, None], Query(alias="user")
    ] = "",

    page_size: Annotated[int, Query(alias="pageSize")] = 1000,
    cursor: Annotated[Union[str, None], Query(alias="cursor")] = None,
):
    search_tags = []
    teams_array = []
    site = reporting_site_external_id
    units = []
    locations = []

    if reporting_site_external_id:
        search_tags.append(reporting_site_external_id)

    if reporting_unit_external_ids:
        for unit in reporting_unit_external_ids.split(','):
            search_tags.append(unit)
            units.append(unit)

    if reporting_location_external_ids:
        for location in reporting_location_external_ids.split(','):
            search_tags.append(location)
            locations.append(location)

    if role_external_ids:
        for roles in role_external_ids.split(','):
            search_tags.append(roles)

    if role_external_site_ids:
        for roles_site in role_external_site_ids.split(','):
            search_tags.append(roles_site)

    if role_external_app_ids:
        for roles_app in role_external_app_ids.split(','):
            search_tags.append(roles_app)

    if teams:
        for team in teams.split(','):
            teams_array.append(team)

    return dict(
        searchTags=search_tags,
        teams=teams_array,
        page_size=page_size,
        cursor=cursor,
        user=user.lower(),
        site=site,
        units=units,
        locations=locations
    )
