from .application_queries import (
    APPLICATION_EXTERNAL_ID_LIST,
)
from .notification_type_queries import (
    NOTIFICATIONS_TYPE_LIST,
    NOTIFICATION_TYPE_TEMPLATES_FIRST,
    NOTIFICATION_TYPE_IN_TEMPLATES_BY_FILTER,
)
from .notification_template_queries import (
    NOTIFICATION_TEMPLATE,
    LIST_TEMPLATES_BY_USERS,
    LIST_TEMPLATE_BY_APPLICATION_GROUPS,
    SEARCH_NOTIFICATION_TEMPLATE,
    GET_BASIC_TEMPLATE_INFO,
)
from .channel_queries import LIST_ALL_CHANNELS
from .notification_event_queries import NOTIFICATION_EVENT_LIST
from .severities_queries import LIST_ALL_SEVERITIES
from .user_queries import LIST_USER_BY_FILTER, LIST_USER_BY_TAGS, SEARCH_USER_BY_EMAIL
from .role_queries import (SEARCH_ROLESITEUSERS_BY_NAME_AND_APP,ROLES_BY_USER)
from .notification_on_screen_queries import (
    GET_NOTIFICATION_ON_SCREEN_WITH_CHAT,
    LIST_NOFITICATION_ON_SCREEN_IDS_BY_USER_AND_DATE,
    GET_NOTIFICATION_ON_SCREEN_PAGE_INFO,
    LIST_NOTIFICATION_ON_SCREEN_INFO,
    LIST_NOTIFICATION_IDS_BY_FILTER,
    LIST_SITES_APPLICATIONS_AND_NOTIFICATION_TYPES,
    LIST_NOTIFICATION_ON_SCREEN_BY_USER_AND_FILTER,
    FIND_NOTIFICATION_COMMENT_BY_ID,
)

from .notification_last_access_queries import GET_LAST_ACCESS_DATE

from .notification_template_extension_queries import (
    LIST_NOTIFICATION_TEMPLATE_EXTENSION_RELATION_MODEL,
    LIST_NOTIFICATION_TEMPLATE_EXTENSION,
)
from .team_queries import TEAM_BY_FILTER


class ApplicationsQueries:
    def __init__(self):
        self.external_id_list = APPLICATION_EXTERNAL_ID_LIST


class NotificationTypeQueries:
    def __init__(self):
        self.notification_type_list = NOTIFICATIONS_TYPE_LIST
        self.notification_type_templates_first = NOTIFICATION_TYPE_TEMPLATES_FIRST
        self.list_type_in_templates = NOTIFICATION_TYPE_IN_TEMPLATES_BY_FILTER


class NotificationTemplateQueries:
    def __init__(self):
        self.notification_template_list = NOTIFICATION_TEMPLATE
        self.notification_template_list_by_users = LIST_TEMPLATES_BY_USERS
        self.notification_template_list_by_application_groups = LIST_TEMPLATE_BY_APPLICATION_GROUPS
        self.notification_template_search = SEARCH_NOTIFICATION_TEMPLATE
        self.notification_template_basic_info = GET_BASIC_TEMPLATE_INFO


class NotificationEventQueries:
    def __init__(self):
        self.notification_event_list = NOTIFICATION_EVENT_LIST


class ChannelsQueries:
    def __init__(self):
        self.channels_list = LIST_ALL_CHANNELS


class SeveritiesQueries:
    def __init__(self):
        self.severities_list = LIST_ALL_SEVERITIES


class UserQueries:
    def __init__(self):
        self.user_by_filter = LIST_USER_BY_FILTER
        self.search_user_by_email = SEARCH_USER_BY_EMAIL
        self.list_user_by_tags = LIST_USER_BY_TAGS


class RoleQueries:
    def __init__(self):
        self.role_search_rolesiteusers_by_name_and_app = SEARCH_ROLESITEUSERS_BY_NAME_AND_APP
        self.role_by_user = ROLES_BY_USER


class NotificationOnScreenQueries:
    def __init__(self):
        self.list_notification_on_screen_ids = LIST_NOFITICATION_ON_SCREEN_IDS_BY_USER_AND_DATE
        self.get_page_info = GET_NOTIFICATION_ON_SCREEN_PAGE_INFO
        self.list_notification_on_screen_info = LIST_NOTIFICATION_ON_SCREEN_INFO
        self.list_notification_on_screen_ids_by_filter = LIST_NOTIFICATION_IDS_BY_FILTER
        self.list_sites_applications_notification_types = (
            LIST_SITES_APPLICATIONS_AND_NOTIFICATION_TYPES
        )
        self.get_notification_on_screen_with_chat = GET_NOTIFICATION_ON_SCREEN_WITH_CHAT
        self.list_by_user_and_filter = LIST_NOTIFICATION_ON_SCREEN_BY_USER_AND_FILTER
        self.find_notification_comment_by_id = FIND_NOTIFICATION_COMMENT_BY_ID


class NotificationLastAccessQueries:
    def __init__(self):
        self.get_last_access_date = GET_LAST_ACCESS_DATE


class NotificationTemplateExtensionQueries:
    def __init__(self):
        self.list_relation_model = LIST_NOTIFICATION_TEMPLATE_EXTENSION_RELATION_MODEL
        self.list_extensions = LIST_NOTIFICATION_TEMPLATE_EXTENSION


class TeamQueries: 
    def __init__(self):
        self.list_team_by_filter = TEAM_BY_FILTER


applications = ApplicationsQueries()
notification_types = NotificationTypeQueries()
notification_templates = NotificationTemplateQueries()
notification_channels = ChannelsQueries()
notification_event = NotificationEventQueries()
notification_severities = SeveritiesQueries()
users = UserQueries()
roles = RoleQueries()
notification_on_screen_queries = NotificationOnScreenQueries()
notification_last_access_queries = NotificationLastAccessQueries()
notification_template_extensions = NotificationTemplateExtensionQueries()
team = TeamQueries()
