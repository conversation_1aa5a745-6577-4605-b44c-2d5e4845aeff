NOTIFICATION_EVENT_LIST = """
            query ListNotificiationEvent($filter: _ListNotificationEventFilter, $first: Int = 10, $after: String) {
                listNotificationEvent(filter: $filter, first: $first, after: $after) {
                    items {
                        externalId
                        properties
                        space
                        createdTime
                        notificationType {
                            space
                            externalId
                            name
                            description
                            application {
                                space
                                externalId
                                name
                                description
                                alias
                            }
                            entityType
                            properties
                        }
                    }
                }
            }
        """