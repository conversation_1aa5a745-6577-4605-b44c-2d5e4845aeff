LIST_NOFITICATION_ON_SCREEN_IDS_BY_USER_AND_DATE = """
    query ListNotificationOnScreenIdsByUser($space:String!, $externalId:String!, $filter: _ListNotificationOnScreenFilter) {
      getNotificationUserById(
        instance: {
          space: $space,
          externalId: $externalId
        }) {
        items {
          notificationsOnScreen(first: 1000, filter: $filter) {
            items {
              externalId
            }
          }
        }
      }
    }
"""

GET_NOTIFICATION_ON_SCREEN_PAGE_INFO = """
    query GetNotificationOnScreenPageInfo($filter: _ListNotificationOnScreenFilter, $first: Int) {
        listNotificationOnScreen(filter: $filter, first: $first) {
            pageInfo {
                endCursor
            }
        }
    }
"""

LIST_NOTIFICATION_ON_SCREEN_INFO = """
    query ListNotificationOnScreenInfo($filter: _ListNotificationOnScreenFilter, $first: Int, $after: String) {
        listNotificationOnScreen(filter: $filter, first: $first, after: $after) {
            items {
                externalId
                createdTime
                createdAt
                severity {
                    description
                    externalId
                }
                reportingSite {
                    externalId
                    name
                }
                template {
                    notificationType {
                        application {
                            name
                            externalId
                            iconUrl
                        }
                        externalId
                        name
                    }
                }
                text
                externalId
            }
        }
    }
"""

LIST_NOTIFICATION_IDS_BY_FILTER = """
    query ListNofiticationOnScreenExternalIdsByFilter($filter: _ListNotificationOnScreenFilter) {
        listNotificationOnScreen(filter: $filter) {
            items {
                externalId
            }
        }
    }
"""

LIST_SITES_APPLICATIONS_AND_NOTIFICATION_TYPES = """
    query ListNotificationOnScreenInfo($filter: _ListNotificationOnScreenFilter) {
        listNotificationOnScreen(filter: $filter) {
            items {
                reportingSite {
                    externalId
                    name
                }
            template {
                notificationType {
                    application {
                        name
                        externalId
                    }
                        externalId
                        name
                    }
                }
            }
        }
    }
"""

GET_NOTIFICATION_ON_SCREEN_WITH_CHAT = """
    query MyQuery {
        getNotificationOnScreenById(instance: {space: "$space", externalId: "$externalId"}) {
            items {
                createdTime
                createdAt
                externalId
                space
                text
              	reportingSite {
                  name
                }
              	template {
                  notificationType {
                    name
                    description
                    application {
                      name
                      azureAppId
                    }
                  }
                }
                comments {
                    items {
                        comment
                        externalId
                        space
                        createdTime
                        user {
                            email
                            externalId
                            firstName
                            lastName
                            displayName
                            space
                        }
                    }
                }
                severity {
                    externalId
                    description
                }
                event {
                    properties
                    users {
                        items {
                            externalId
                            space
                            email
                        }
                    }
                    roles {
                        items {
                            externalId
                            role {
                              externalId
                              name
                            }
                        }
                    }
                    applicationGroups {
                        items {
                            externalId
                            name
                            description
                        }
                    }
                    externalUsers
                }
            }
        }
    }
"""

LIST_NOTIFICATION_ON_SCREEN_BY_USER_AND_FILTER = """
    query ListNotificationOnScreenIdsByUser($filter_user: _ListNotificationUserFilter, $filter: _ListNotificationOnScreenFilter) {
        listNotificationUser(filter: $filter_user) {
            items {
                notificationsOnScreen(first: 1000, filter: $filter) {
                    items {
                        externalId
                        space
                        text
                        createdTime
                        createdAt
                        newUpdateTime
                        template {
                            externalId
                            space
                            notificationType {
                                externalId
                                space
                                name
                                description
                                application {
                                    externalId
                                    space
                                    name
                                    description
                                }
                            }
                        }
                        severity {
                            externalId
                            space
                            name
                            description
                        }
                        comments {
                            items {
                                externalId
                                space
                                comment
                                createdTime
                                user {
                                    externalId
                                    space
                                }
                            }
                        }
                        reportingSite {
                            externalId
                            space
                            name
                            description
                        }
                    }
                }
            }
        }
    }
"""

FIND_NOTIFICATION_COMMENT_BY_ID = """
    query FindNotificationCommentById($filter: _ListNotificationCommentFilter) {
        listNotificationComment(filter: $filter) {
            items {
                comment
                externalId
            }
        }
    }
"""