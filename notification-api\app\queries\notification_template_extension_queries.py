LIST_NOTIFICATION_TEMPLATE_EXTENSION_RELATION_MODEL = """
    query GetNotificationTemplateExtension($filter: _ListNotificationTemplateExtensionFilter) {
        listNotificationTemplateExtension(filter: $filter) {
            items {
                externalId
                space
            }
        }
    }
"""

LIST_NOTIFICATION_TEMPLATE_EXTENSION = """
    query GetNotificationTemplateExtension($filter: _ListNotificationTemplateExtensionFilter) {
        listNotificationTemplateExtension(filter: $filter) {
            items {
                externalId
                space
                owner{
                    externalId
                    space
                }
                channels{
                    items{
                        externalId
                        space
                        name
                        description
                    }
                }
                frequencyCronExpression
                template{
                    externalId
                    space
                }
            }
        }
    }
"""
