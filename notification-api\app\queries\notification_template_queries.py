GET_BASIC_TEMPLATE_INFO = """
    query GetNotificationTemplates(
        $templates_filter: _ListNotificationTemplateFilter,
        $after: String, 
        $pageSize: Int,
    ) {
        listNotificationTemplate(
            filter: $templates_filter
            after: $after
            first: $pageSize
        ) {
            items {
                externalId
                space
                creator {
                    externalId
                    space
                  	email
                  	firstName
                  	lastName
                  	displayName
                }
                customChannelEnabled
                customFrequencyEnabled
                name
                subscribedUsers {
                    items {
                        externalId
                        space
                    }
                }
                subscribedRoles {
                    items {
                        externalId
                        space
                    }
                }
                subscribedExternalUsers
                subscribedApplicationGroups {
                    items {
                        externalId
                        space
                    }
                }
            }
        }
    }
"""

NOTIFICATION_TEMPLATE = """
    query GetNotificationTemplates(
        $templates_filter: _ListNotificationTemplateFilter,
        $after: String, 
        $pageSize: Int,
    ) {
        listNotificationTemplate(
            filter: $templates_filter
            after: $after
            first: $pageSize
        ) {
            items {
                space
                externalId
                name
                customChannelEnabled
                customFrequencyEnabled
                text
                conditionalExpression
                frequencyCronExpression
                adminLevel
                deleted
                creator {
                    externalId
                    space
                  	firstName
                  	lastName
                  	displayName
                }
                notificationType {
                    space
                    externalId
                    name
                    description
                    application {
                        space
                        externalId
                        name
                        alias
                        description
                    }
                    entityType
                    properties
                }
                severity {
                    externalId
                    space
                    name
                    description
                }
                adminLevel
                channels ( sort: { description: ASC_NULLS_LAST } ) {
                    items {
                        space
                        externalId
                        name
                        description
                    }
                }
                subscribedUsers (sort: { firstName: ASC_NULLS_LAST }) {
                    items {
                        space  
                        externalId
                        firstName
                        lastName
                    }
                }
                subscribedExternalUsers
                subscribedRoles {
                    items {
                        externalId
                        role {
                          description
                          name
                        }
                      	reportingSite {
                          siteCode
                        }
                    }
                }
                subscribedApplicationGroups(sort: { description: ASC_NULLS_LAST }) {
                    items {
                        externalId
                        space
                        description
                        name
                        application {
                            externalId
                            space
                        }
                        blocklistRoles(first: 1000) {
                            items {
                                usersComplements(first: 1000) {
                                    items {
                                        userAzureAttribute {
                                            user {
                                                externalId
                                            }
                                        }
                                    }
                                }
                                externalId
                            }
                        }
                        blocklist(first: 1000) {
                            items {
                                email
                                externalId
                            }
                        }
                        users {
                            items {
                                email
                                externalId
                                space
                                firstName
                                lastName
                            }
                        }
                        usersRoles {
                            items {
                                externalId
                                role {
                                    name
                                }
                                reportingSite {
                                    siteCode
                                }
                            }
                        }
                    }
                }
                blocklist(first: 1000) {
                    items {
                        externalId
                    }
                }
                blocklistRoles(first: 1000) {
                    items {
                        usersComplements(first: 1000) {
                            items {
                                userAzureAttribute {
                                    user {
                                        externalId
                                    }
                                }
                            }
                        }
                        externalId
                    }
                }
                lastUpdatedTime
                allUsers
            }
            pageInfo {
                hasNextPage
                endCursor
            }
        }
    }
"""

SEARCH_NOTIFICATION_TEMPLATE = """
    query GetNotificationTemplates(
        $query: String!
        $pageSize: Int,
        $filter: _SearchNotificationTemplateFilter
    ) {
        searchNotificationTemplate(
            query: $query
            first: $pageSize
            filter: $filter
        ) {
            items {
                space
                externalId
                name
                customChannelEnabled
                customFrequencyEnabled
                creator {
                    externalId
                  	email
                  	firstName
                  	lastName
                  	displayName
                }
                notificationType {
                    externalId
                    name
                    description
                    application {
                        externalId
                        name
                        alias
                        description
                    }
                    entityType
                    properties
                }
                severity {
                    externalId
                    space
                }
                adminLevel
                channels ( sort: { description: ASC_NULLS_LAST }) {
                    items {
                        space
                        externalId
                        name
                        description
                    }
                }
                subscribedUsers (sort: { firstName: ASC_NULLS_LAST }) {
                    items {
                        space  
                        externalId
                        firstName
                        lastName
                    }
                }
                subscribedExternalUsers
                subscribedRoles {
                    items {
                        externalId
                        role {
                          name
                        }
                      	reportingSite {
                          siteCode
                        }
                    }
                }
                subscribedApplicationGroups(sort: { description: ASC_NULLS_LAST }) {
                    items {
                        externalId
                        description
                        name
                        application {
                            externalId
                            space
                        }
                        users {
                            items {
                                externalId
                                space
                                firstName
                                lastName
                            }
                        }
                        usersRoles {
                            items {
                                externalId
                                role {
                                    name
                                }
                                reportingSite {
                                    siteCode
                                }
                            }
                        }
                        blocklistRoles(first: 1000) {
                            items {
                                usersComplements(first: 1000) {
                                    items {
                                        userAzureAttribute {
                                            user {
                                                externalId
                                            }
                                        }
                                    }
                                }
                                externalId
                            }
                        }
                        blocklist(first: 1000) {
                            items {
                                externalId
                            }
                        }
                    }
                }
                blocklist(first: 1000) {
                    items {
                        externalId
                    }
                }
                blocklistRoles(first: 1000) {
                    items {
                        usersComplements(first: 1000) {
                            items {
                                userAzureAttribute {
                                    user {
                                        externalId
                                    }
                                }
                            }
                        }
                        externalId
                    }
                }
                lastUpdatedTime
                allUsers
            }
            pageInfo {
                hasNextPage
                endCursor
            }
        }
    }
"""

LIST_TEMPLATES_BY_USERS = """
    query GetTemplatesByUsers(
        $user_filter: _ListNotificationUserFilter,  
        $templates_filter: _ListNotificationTemplateFilter,
        $after: String, 
        $pageSize: Int,
    ) {
        listNotificationUser(filter: $user_filter, after: $after) {
            items {
                templates(first: $pageSize, filter: $templates_filter) {
                    items {
                        space
                        externalId
                        name
                        customChannelEnabled
                        customFrequencyEnabled
                        creator {
                            externalId
                            email
                            firstName
                            lastName
                            displayName
                        }
                        notificationType {
                            externalId
                            name
                            description
                            application {
                                externalId
                                name
                                alias
                                description
                            }
                            entityType
                        }
                        severity {
                            externalId
                            space
                        }
                        adminLevel
                        channels ( sort: { description: ASC_NULLS_LAST } ) {
                            items {
                                space
                                externalId
                                name
                                description
                            }
                        }
                        subscribedUsers (sort: { firstName: ASC_NULLS_LAST }) {
                            items {
                                space  
                                externalId
                                firstName
                                lastName
                            }
                        }
                        subscribedExternalUsers
                        subscribedRoles {
                            items {
                                externalId
                                role {
                                name
                                }
                                reportingSite {
                                siteCode
                                }
                            }
                        }
                        subscribedApplicationGroups(sort: { description: ASC_NULLS_LAST }, first: 1000) {
                            items {
                                externalId
                                description
                                name
                                application {
                                    externalId
                                    space
                                }
                                blocklistRoles(first: 1000) {
                                    items {
                                        usersComplements(first: 1000) {
                                            items {
                                                userAzureAttribute {
                                                    user {
                                                        externalId
                                                    }
                                                }
                                            }
                                        }
                                        externalId
                                    }
                                }
                                blocklist(first: 1000) {
                                    items {
                                        externalId
                                    }
                                }
                            }
                        }
                        blocklist(first: 1000) {
                            items {
                                externalId
                            }
                        }
                        blocklistRoles(first: 1000) {
                            items {
                                usersComplements(first: 1000) {
                                    items {
                                        userAzureAttribute {
                                            user {
                                                externalId
                                            }
                                        }
                                    }
                                }
                                externalId
                            }
                        }
                        lastUpdatedTime
                        allUsers
                    }
                }
            }
            pageInfo {
                hasNextPage
                endCursor
            }
        }
    }
"""

LIST_TEMPLATE_BY_APPLICATION_GROUPS = """
    query GetTemplatesByApplicationGroups($filter: _ListNotificationApplicationGroupFilter) {
        listNotificationApplicationGroup(filter: $filter) {
            items {
                templates(first: 1000) {
                    items {
                        externalId
                    }
                }
            }
        }
    }
"""