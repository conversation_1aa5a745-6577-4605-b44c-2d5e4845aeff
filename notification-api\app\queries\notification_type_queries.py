NOTIFICATIONS_TYPE_LIST = """
            query listNotificationTypeList($filter: _ListNotificationTypeFilter)  {
                listNotificationType(filter: $filter, first: 1000)  {
                    items {
                        name
                        description
                        externalId
                        space
                        application {
                            name
                            externalId
                            alias
                            description
                        }
                        entityType
                        properties
                    }
                }
            }
"""


NOTIFICATION_TYPE_TEMPLATES_FIRST = """
            query notificationTypeCountTemplates($filter: _ListNotificationTemplateFilter) {
                listNotificationTemplate(first: 1, filter: $filter) {
                    items {
                        name
                        externalId
                        notificationType {
                            name
                            description
                            externalId
                            application {
                                name
                                description
                                externalId
                            }
                            entityType
                        }
                    }
                }
            }
"""
NOTIFICATION_TYPE_IN_TEMPLATES_BY_FILTER = """
    query notificationTypeInTemplates($filter: _ListNotificationTemplateFilter, $after: String) {
        listNotificationTemplate(first: 1000, filter: $filter, after: $after) {
            items {
                notificationType {
                    externalId
                }
            }
        }
    }
"""
