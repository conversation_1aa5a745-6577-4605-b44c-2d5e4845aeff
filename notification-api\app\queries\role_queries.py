ROLES_BY_USER = """
    query GetRolesByUser ($filter: _ListUserComplementFilter){
        listUserComplement(filter: $filter) {
            items {
              userRoleSite{
                items{
                  externalId
                }
              }
            }
        }
    }
"""

SEARCH_ROLESITEUSERS_BY_NAME_AND_APP = """
        query getUserRoleSite_By_Site_and_App($filter: _ListNotificationUserRoleSiteFilter){
        listNotificationUserRoleSite(filter:$filter, first: 1000){
            items{
				space
                externalId
                usersComplements{
                    items{
                        userAzureAttribute{
                            user{
                                externalId
                                space
                                email
                                firstName
                                lastName
                            }
                        }
                    }
                }
                role{
                    externalId
                    name
                    description
                    space
                    application{
                        externalId
                    }
                    roleCategory{
                        externalId
                    }
                }
                reportingSite{
                    externalId
                    name
                    description
                    space
                    siteCode
                }
            }
        }
    }
"""