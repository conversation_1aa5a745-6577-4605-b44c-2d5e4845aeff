LIST_USER_BY_FILTER = """
    query GetNotificationUserByFilter($filter: _ListUserFilter, $first: Int = 1000, $after:String) {
        listUser(filter: $filter, first: $first, after: $after) {
            pageInfo {
                endCursor
                hasNextPage
                hasPreviousPage
                startCursor
            }
            items {
                externalId
                firstName
                lastName
                email
                active
            }
        }
    }
"""

SEARCH_USER_BY_EMAIL = """
    query SearchUserByEmail(
        $query: String!
        $first: Int = 10
        $filter: _SearchUserFilter
    ) {
        searchUser(
            query: $query
            first: $first
            filter: $filter
        ) {
            items {
                email
                externalId
                firstName
                lastName
                displayName
            }
        }
    }
"""

LIST_USER_BY_TAGS = """
query GetUsers($after: String, $pageSize: Int = 1000, $userComplementFilter: _ListUserComplementFilter, $teamFilter: _ListTeamFilter) {
  listUserComplement(
    after: $after
    first: $pageSize
    filter: $userComplementFilter
    sort: {sortingKey: ASC}
  ) {
    items {
      reportingSites {
        items {
          externalId
        }
      }
      reportingLocations {
        items {
          externalId
        }
      }
      reportingUnits {
        items {
          externalId
        }
      }
      userRoleSite {
        items {
          externalId
        }
      }
      userAzureAttribute {
        user {
          externalId
          space
          email
          displayName
          firstName
          lastName
          teams(first: 1000, filter: $teamFilter) {
            items {
              externalId
              space
              name
              description
            }
          }
        }
      }
    }
    pageInfo {
      hasNextPage
      endCursor
    }
  }
}
"""
