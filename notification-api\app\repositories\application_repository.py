from typing import List, Any, Optional
from uuid import uuid4
from cognite.client import CogniteClient
from cognite.client.data_classes.data_modeling import NodeApply, NodeOrEdgeData, ViewId
from app.core.env_variables import EnvVariables
from app.core.graphql_client import Graph<PERSON><PERSON>lient
from app.models import ApplicationModel
import app.core as core
from cognite.client.data_classes.data_modeling import (
    ViewId,
    PropertyId,
)
from cognite.client.data_classes.data_modeling.query import (
    Query,
    Select,
    NodeResultSetExpression,
    SourceSelector,
)
from cognite.client.data_classes.filters import Equals, Not, And, In, Exists, Or
from app.core.cache_global import get_cache_instance
import app.utils as Utils

ENTITY = "Application"


class ApplicationRepository:
    def __init__(
        self,
        cognite_client: CogniteClient,
        graphql_client: GraphQLClient,
        env_variables: EnvVariables,
    ):
        self._cognite_client = cognite_client
        self._graphql_client = graphql_client
        self._fdm_model_space = env_variables.cognite.fdm_model_space
        self._fdm_instances_space = env_variables.spaces.ntf_instance_space

    def find(
        self,
        codes: List[str],
    ) -> List[Any]:
        response: List[ApplicationModel] = []

        _cache = get_cache_instance()
    
        cognite_views = _cache.get("cognite_views")[self._fdm_model_space]

        notification_application_view = Utils.cognite.find_view_by_external_id(
            cognite_views,
            core.env.cognite_entities.notification_application,
        )

        notification_type_view = Utils.cognite.find_view_by_external_id(
            cognite_views,
            core.env.cognite_entities.notification_type,
        ).version

        notification_template_view = Utils.cognite.find_view_by_external_id(
            cognite_views,
            core.env.cognite_entities.notification_template,
        )

        views = {
            "notification_application": notification_application_view.version,
            "notification_type": notification_type_view, 
            "notification_template": notification_template_view.version
        }

        templates_from_notification_types_cursor = None
        notification_types_from_applications_cursor = None
        applications_cursor = None
        has_cursor = True
        response_query = None

        query = Query(
            with_={
                "applications": NodeResultSetExpression(
                    filter=And(
                        In(["node", "externalId"], codes),
                        Exists(notification_application_view.as_property_ref("alias"))
                    ),
                    limit=10000,
                    chain_to="destination",
                    direction="outwards",
                ),
                "notification_types_from_applications": NodeResultSetExpression(
                    from_="applications",
                    limit=10000,
                    chain_to="destination",
                    direction="inwards",
                    through=PropertyId(
                        ViewId(
                            core.env.cognite.fdm_model_space,
                            core.env.cognite_entities.notification_type,
                            notification_type_view,
                        ),
                        "application"
                    )
                ),
                "templates_from_notification_types": NodeResultSetExpression(
                    from_="notification_types_from_applications",
                    filter=Or(
                        Equals(notification_template_view.as_property_ref("deleted"), False),
                        Not(Exists(notification_template_view.as_property_ref("deleted")))
                    ),
                    limit=10000,
                    chain_to="destination",
                    direction="inwards",
                    through=PropertyId(
                        ViewId(
                            core.env.cognite.fdm_model_space,
                            core.env.cognite_entities.notification_template,
                            notification_template_view.version,
                        ),
                        "notificationType"
                    )
                ),
            },
            select={
                "applications": Select(
                    [
                        SourceSelector(
                            ViewId(
                                core.env.cognite.fdm_model_space,
                                core.env.cognite_entities.notification_application,
                                notification_application_view.version,
                            ),
                            ["name","alias","description"],
                        )
                    ],
                    limit=10000,
                ),
                "notification_types_from_applications": Select(
                    [
                        SourceSelector(
                            ViewId(
                                core.env.cognite.fdm_model_space,
                                core.env.cognite_entities.notification_type,
                                notification_type_view,
                            ),
                            ["*"],
                        )
                    ],
                    limit=10000,
                ),
                "templates_from_notification_types": Select(
                    [
                        SourceSelector(
                            ViewId(
                                core.env.cognite.fdm_model_space,
                                core.env.cognite_entities.notification_template,
                                notification_template_view.version,
                            ),
                            ["notificationType", "deleted"],
                        )
                    ],
                    limit=10000,
                ),
            },
            cursors={
                "applications": applications_cursor,
                "notification_types_from_applications": notification_types_from_applications_cursor,
                "templates_from_notification_types": templates_from_notification_types_cursor
            },
        )
 
        while has_cursor:
            response_query = self._cognite_client.data_modeling.instances.query(query)
            applications = self.format_find_response(response_query, views)

            if len(applications) > 0:
                response.extend(applications)

            if response_query:
                has_cursor = False
                cursor_keys = [
                    "notification_types_from_applications",
                    "templates_from_notification_types",
                ]

                for key in cursor_keys:
                    if key in response_query.cursors and len(response_query[key]) == 10000:
                        query.cursors[key] = response_query.cursors[key]
                        has_cursor = True
            else:
                has_cursor = False 

        return response
    

    def format_find_response(self, response, views): 
        applications_response = response["applications"].dump()
        notification_types_response = response["notification_types_from_applications"].dump()
        templates_response = response["templates_from_notification_types"].dump()
        applications = []

        for application in applications_response:
            properties = application.get("properties", {}).get(core.env.cognite.fdm_model_space, {}).get(
                f"{core.env.cognite_entities.notification_application}/{views['notification_application']}", {}
            )
            application_externalId = application.get("externalId")
            filter_notification_type = [
                notification_type for notification_type in notification_types_response
                if notification_type.get("properties", {}).get(core.env.cognite.fdm_model_space, {}).get(
                    f"{core.env.cognite_entities.notification_type}/{views['notification_type']}", {}
                ).get("application", {}).get("externalId", "") == application_externalId
            ]
            filter_notification_types_externalIds = [
                notification_type.get("externalId", "") for notification_type in filter_notification_type
            ]

            notification_types = [
                {
                    "name": notification_type.get("properties", {}).get(core.env.cognite.fdm_model_space, {}).get(
                            f"{core.env.cognite_entities.notification_type}/{views['notification_type']}", {}
                        ).get("name", ""),
                    "space": notification_type.get("space", ""),
                    "description": notification_type.get("properties", {}).get(core.env.cognite.fdm_model_space, {}).get(
                            f"{core.env.cognite_entities.notification_type}/{views['notification_type']}", {}
                        ).get("description", ""),
                    "externalId": notification_type.get("externalId", ""),
                    "application": {
                        "externalId": application.get("externalId"),
                        "name": properties.get("name", ""),
                        "alias": properties.get("alias", ""),
                        "description": properties.get("description", ""),
                    },
                    "hasTemplates": True if len([
                        template for template in templates_response
                        if template.get("properties", {}).get(core.env.cognite.fdm_model_space, {}).get(
                            f"{core.env.cognite_entities.notification_template}/{views['notification_template']}", {}
                        ).get("notificationType", {}).get("externalId", "") == notification_type.get("externalId")
                    ]) > 0 else False
                } for notification_type in filter_notification_type
            ]

            applications.append({
                'externalId': application.get("externalId"),
                'name': properties.get("name", ""),
                'alias': properties.get("alias", ""),
                'description': properties.get("description", ""),
                'notificationTypes': sorted(notification_types, key=lambda x: x['name'].lower()),
            })

        return sorted(applications, key=lambda x: x['alias'].lower())
    
    def find_external_ids(self, filter):
        return self._graphql_client.query(
            core.queries.applications.external_id_list,
            "listApplication",
            filter
        )
