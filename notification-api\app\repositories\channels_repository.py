from typing import List, Any
from cognite.client import Cognite<PERSON>lient
from app.core.env_variables import EnvVariables
from app.core.graphql_client import GraphQLClient
from app.core.cache_global import get_cache_instance

ENTITY = "NotificationChannel"


class ChannelsRepository:
    def __init__(
        self,
        cognite_client: CogniteClient,
        graphql_client: GraphQLClient,
        env_variables: EnvVariables,
    ):
        self._cognite_client = cognite_client
        self._graphql_client = graphql_client
        self._fdm_model_space = env_variables.cognite.fdm_model_space
        self._fdm_instances_space = env_variables.spaces.ntf_instance_space

    def findAll(
        self,
    ) -> List[Any]:

        _cache = get_cache_instance()

        return _cache.get("channels")
