from datetime import datetime
from typing import List, Any
from cognite.client import CogniteClient
import pytz
from app.core.env_variables import EnvVariables
from app.core.graphql_client import GraphQLClient
import app.core as core
from app.core.settings import Settings
import app.queries as queries
import app.models as models
import app.utils as Utils
from cognite.client.data_classes.data_modeling import (
    NodeApply,
    NodeOrEdgeData,
    ViewId,
    NodeId,
    PropertyId,
)
from cognite.client.data_classes.data_modeling.query import (
    Query,
    Select,
    NodeResultSetExpression,
    EdgeResultSetExpression,
    SourceSelector,
)
from cognite.client.data_classes.filters import Equals, HasData, And, Not
from app.core.cache_global import get_cache_instance


class NotificationApplicationGroupRepository:
    def __init__(
        self,
        cognite_client: CogniteClient,
        graphql_client: GraphQLClient,
        env_variables: EnvVariables,
    ):
        self._cognite_client = cognite_client
        self._graphql_client = graphql_client
        self._fdm_model_space = env_variables.cognite.fdm_model_space
        self._fdm_instances_space = env_variables.spaces.ntf_instance_space

        settings = Settings()
        self._data_model_id = settings.data_model_id

    def save(
        self, request: models.NotificationApplicationGroupCreateModel, user_id: str
    ):

        _cache = get_cache_instance()
        view = Utils.cognite.find_view_by_external_id(
            _cache.get("cognite_views")[core.env.cognite.fdm_model_space],
            core.env.cognite_entities.notification_application_group,
        )

        users = [
            models.RelationModel(
                externalId=user, space=core.env.spaces.um_instance_space
            )
            for user in request.users
        ]
        blocklist = (
            [
                models.RelationModel(
                    externalId=user, space=core.env.spaces.um_instance_space
                )
                for user in request.blocklist
            ]
            if request.blocklist
            else []
        )
        users_roles = (
            [
                models.RelationModel(
                    externalId=user, space=core.env.spaces.um_instance_space
                )
                for user in request.usersRoles
            ]
            if request.usersRoles
            else []
        )
        blocklist_roles = (
            [
                models.RelationModel(
                    externalId=user, space=core.env.spaces.um_instance_space
                )
                for user in request.blocklistRoles
            ]
            if request.blocklistRoles
            else []
        )
        request.application = {
            "externalId": request.application,
            "space": core.env.spaces.um_instance_space,
        }

        del request.users
        del request.blocklist
        del request.usersRoles
        del request.blocklistRoles

        entity_versions = view.version  # GET ENTITY VERSION

        is_update = True if request.externalId is not None else False
        template_external_id = (
            Utils.generateExternalId('APPGRP') if request.externalId is None else request.externalId
        )

        del request.externalId
        del request.space

        if is_update:
            fmt = "%Y-%m-%dT%H:%M:%S"
            now = datetime.now(pytz.utc)
            formatted_now = now.strftime(fmt)

            request.editedBy = models.RelationModel(
                externalId=user_id, space=core.env.spaces.um_instance_space
            )
            request.editedAt = formatted_now
        else:
            request.createdBy = models.RelationModel(
                externalId=user_id, space=core.env.spaces.um_instance_space
            )

        notification_application_group_nodes = NodeApply(
            self._fdm_instances_space,
            template_external_id,
            sources=[
                NodeOrEdgeData(
                    ViewId(
                        self._fdm_model_space,
                        core.env.cognite_entities.notification_application_group,
                        entity_versions,
                    ),
                    request.model_dump(),
                )
            ],
        )

        self._cognite_client.data_modeling.instances.apply(
            nodes=notification_application_group_nodes,
            replace=is_update,
        )

        # CREATE TEMPLATE RELATIONSHIP WITH USERS
        Utils.cognite.createRelationship(
            users,
            notification_application_group_nodes,
            entity_versions,
            self._graphql_client,
            self._cognite_client,
            self._fdm_model_space,
            self._fdm_instances_space,
            "users",
            is_update,
        )

        # CREATE TEMPLATE RELATIONSHIP WITH BLOCKLIST
        Utils.cognite.createRelationship(
            blocklist,
            notification_application_group_nodes,
            entity_versions,
            self._graphql_client,
            self._cognite_client,
            self._fdm_model_space,
            self._fdm_instances_space,
            "blocklist",
            is_update,
        )

        # CREATE TEMPLATE RELATIONSHIP WITH USERS ROLES
        Utils.cognite.createRelationship(
            users_roles,
            notification_application_group_nodes,
            entity_versions,
            self._graphql_client,
            self._cognite_client,
            self._fdm_model_space,
            self._fdm_instances_space,
            "usersRoles",
            is_update,
        )

        # CREATE TEMPLATE RELATIONSHIP WITH BLOCKLIST ROLES
        Utils.cognite.createRelationship(
            blocklist_roles,
            notification_application_group_nodes,
            entity_versions,
            self._graphql_client,
            self._cognite_client,
            self._fdm_model_space,
            self._fdm_instances_space,
            "blocklistRoles",
            is_update,
        )

        request.externalId = template_external_id

        return request

    def get_by_filter(self, id: str, type: str, name: str = None):

        _cache = get_cache_instance()

        cognite_views = _cache.get("cognite_views")

        notification_views = cognite_views[core.env.cognite.fdm_model_space]

        user_management_views = cognite_views[core.env.spaces.um_model_space]

        notification_user_view = Utils.cognite.find_view_by_external_id(
            notification_views,
            core.env.cognite_entities.notification_user,
        )

        notification_application_group_view = Utils.cognite.find_view_by_external_id(
            notification_views,
            core.env.cognite_entities.notification_application_group,
        )

        notification_user_role_site_view = Utils.cognite.find_view_by_external_id(
            notification_views,
            core.env.cognite_entities.notification_user_role_site,
        ).version

        user_complement_view = Utils.cognite.find_view_by_external_id(
            user_management_views,
            core.env.cognite_entities.user_complement,
        ).version

        user_azure_attribute_view = Utils.cognite.find_view_by_external_id(
            user_management_views,
            core.env.cognite_entities.user_azure_attribute,
        ).version

        user_view = Utils.cognite.find_view_by_external_id(
            user_management_views,
            core.env.cognite_entities.user,
        )

        reporting_site_view = Utils.cognite.find_view_by_external_id(
            cognite_views[core.env.spaces.asset_hierarcy_model_space],
            core.env.cognite_entities.reporting_site,
        ).version

        role_view = Utils.cognite.find_view_by_external_id(
            user_management_views,
            core.env.cognite_entities.role,
        ).version

        views = {
            "notification_user": notification_user_view.version,
            "notification_application_group": notification_application_group_view.version,
            "notification_user_role_site": notification_user_role_site_view,
            "user_complement": user_complement_view,
            "user_azure_attribute": user_azure_attribute_view,
            "user": user_view.version,
            "reporting_site": reporting_site_view,
            "role": role_view,
        }

        application_groups_cursor = None
        users_in_blocklist_application_groups_cursor = None
        users_in_application_groups_cursor = None
        user_complement_in_users_roles_in_template_cursor = None
        user_complement_in_blocklist_roles_in_template_cursor = None

        has_cursor = True
        response_query = None
        response = []
        filter_variable = []

        if type == "byApplication":
            filter_variable.append(
                Equals(
                    notification_application_group_view.as_property_ref("application"),
                    {"externalId": id, "space": core.env.spaces.um_instance_space},
                )
            )

        if type == "byExternalId":
            filter_variable.append(Equals(["node", "externalId"], id))

        if type == "byName":
            filter_variable.append(
                Equals(
                    notification_application_group_view.as_property_ref("name"),
                    name,
                )
            )
        if type == "validExits":
            filter_variable.append(
                Equals(
                    notification_application_group_view.as_property_ref("name"),
                    name,
                )
            )
            if id is not None:
                filter_variable.append(Not(Equals(["node", "externalId"], id)))

        query = Query(
            with_={
                "application_groups": NodeResultSetExpression(
                    filter=And(*filter_variable),
                    limit=10000,
                    chain_to="destination",
                    direction="outwards",
                ),
                "createdBy": NodeResultSetExpression(
                    from_="application_groups",
                    limit=10000,
                    chain_to="destination",
                    direction="outwards",
                    through=PropertyId(
                        ViewId(
                            core.env.cognite.fdm_model_space,
                            core.env.cognite_entities.notification_application_group,
                            notification_application_group_view.version,
                        ),
                        "createdBy",
                    ),
                ),
                "editedBy": NodeResultSetExpression(
                    from_="application_groups",
                    limit=10000,
                    chain_to="destination",
                    direction="outwards",
                    through=PropertyId(
                        ViewId(
                            core.env.cognite.fdm_model_space,
                            core.env.cognite_entities.notification_application_group,
                            notification_application_group_view.version,
                        ),
                        "editedBy",
                    ),
                ),
                "subscribed_users_in_application_groups": EdgeResultSetExpression(
                    from_="application_groups",
                    limit=10000,
                    direction="outwards",
                    chain_to="destination",
                    filter=Equals(
                        ["edge", "type"],
                        {
                            "space": core.env.cognite.fdm_model_space,
                            "externalId": "NotificationApplicationGroup.users",
                        },
                    ),
                    node_filter=HasData(
                        views=[
                            ViewId(
                                core.env.cognite.fdm_model_space,
                                core.env.cognite_entities.notification_user,
                                notification_user_view.version,
                            )
                        ]
                    ),
                ),
                "users_in_application_groups": NodeResultSetExpression(
                    from_="subscribed_users_in_application_groups",
                    filter=Equals(
                        notification_user_view.as_property_ref("deleted"), False
                    ),
                    limit=10000,
                ),
                "blocklist_in_application_groups": EdgeResultSetExpression(
                    from_="application_groups",
                    limit=10000,
                    direction="outwards",
                    chain_to="destination",
                    filter=Equals(
                        ["edge", "type"],
                        {
                            "space": core.env.cognite.fdm_model_space,
                            "externalId": "NotificationApplicationGroup.blocklist",
                        },
                    ),
                    node_filter=HasData(
                        views=[
                            ViewId(
                                core.env.cognite.fdm_model_space,
                                core.env.cognite_entities.notification_user,
                                notification_user_view.version,
                            )
                        ]
                    ),
                ),
                "users_in_blocklist_application_groups": NodeResultSetExpression(
                    from_="blocklist_in_application_groups",
                    filter=Equals(
                        notification_user_view.as_property_ref("deleted"), False
                    ),
                    limit=10000,
                ),
                # getting the edge of subscribed roles
                "users_roles_in_template": EdgeResultSetExpression(
                    from_="application_groups",
                    limit=10000,
                    direction="outwards",
                    chain_to="destination",
                    filter=Equals(
                        ["edge", "type"],
                        {
                            "space": core.env.cognite.fdm_model_space,
                            "externalId": "NotificationApplicationGroup.usersRoles",
                        },
                    ),
                    node_filter=HasData(
                        views=[
                            ViewId(
                                core.env.cognite.fdm_model_space,
                                core.env.cognite_entities.notification_user_role_site,
                                notification_user_role_site_view,
                            )
                        ]
                    ),
                ),
                # getting the edge of blocklist roles
                "blocklist_roles_in_template": EdgeResultSetExpression(
                    from_="application_groups",
                    limit=10000,
                    direction="outwards",
                    chain_to="destination",
                    filter=Equals(
                        ["edge", "type"],
                        {
                            "space": core.env.cognite.fdm_model_space,
                            "externalId": "NotificationApplicationGroup.blocklistRoles",
                        },
                    ),
                    node_filter=HasData(
                        views=[
                            ViewId(
                                core.env.cognite.fdm_model_space,
                                core.env.cognite_entities.notification_user_role_site,
                                notification_user_role_site_view,
                            )
                        ]
                    ),
                ),
                # getting the roles in the subscribed roles
                "roles_in_template": NodeResultSetExpression(
                    from_="users_roles_in_template", limit=10000
                ),
                # getting the roles in the blocklist roles
                "roles_in_template_blocklist": NodeResultSetExpression(
                    from_="blocklist_roles_in_template", limit=10000
                ),
                # getting the reporting sites from the roles
                "reporting_sites_from_roles_in_template": NodeResultSetExpression(
                    from_="roles_in_template",
                    limit=10000,
                    chain_to="destination",
                    direction="outwards",
                    through=PropertyId(
                        ViewId(
                            core.env.cognite.fdm_model_space,
                            core.env.cognite_entities.notification_user_role_site,
                            notification_user_role_site_view,
                        ),
                        "reportingSite",
                    ),
                ),
                # getting the reporting sites from the blocklist roles
                "reporting_sites_from_roles_in_template_blocklist": NodeResultSetExpression(
                    from_="roles_in_template_blocklist",
                    limit=10000,
                    chain_to="destination",
                    direction="outwards",
                    through=PropertyId(
                        ViewId(
                            core.env.cognite.fdm_model_space,
                            core.env.cognite_entities.notification_user_role_site,
                            notification_user_role_site_view,
                        ),
                        "reportingSite",
                    ),
                ),
                # getting the reporting sites from roles in template
                "role_from_roles_in_template": NodeResultSetExpression(
                    from_="roles_in_template",
                    limit=10000,
                    chain_to="destination",
                    direction="outwards",
                    through=PropertyId(
                        ViewId(
                            core.env.cognite.fdm_model_space,
                            core.env.cognite_entities.notification_user_role_site,
                            notification_user_role_site_view,
                        ),
                        "role",
                    ),
                ),
                # getting the reporting sites from roles in blocklist
                "role_from_roles_in_template_blocklist": NodeResultSetExpression(
                    from_="roles_in_template_blocklist",
                    limit=10000,
                    chain_to="destination",
                    direction="outwards",
                    through=PropertyId(
                        ViewId(
                            core.env.cognite.fdm_model_space,
                            core.env.cognite_entities.notification_user_role_site,
                            notification_user_role_site_view,
                        ),
                        "role",
                    ),
                ),
                # getting the users complements from the roles
                "user_complement_in_users_roles_in_template": EdgeResultSetExpression(
                    from_="users_roles_in_template",
                    limit=10000,
                    direction="inwards",
                    chain_to="destination",
                    filter=Equals(
                        ["edge", "type"],
                        {
                            "space": core.env.spaces.um_model_space,
                            "externalId": "UserComplement.userRoleSite",
                        },
                    ),
                    node_filter=HasData(
                        views=[
                            ViewId(
                                core.env.spaces.um_model_space,
                                core.env.cognite_entities.user_complement,
                                user_complement_view,
                            )
                        ]
                    ),
                ),
                # getting the users complements from the blocklist roles
                "user_complement_in_blocklist_roles_in_template": EdgeResultSetExpression(
                    from_="blocklist_roles_in_template",
                    limit=10000,
                    direction="inwards",
                    chain_to="destination",
                    filter=Equals(
                        ["edge", "type"],
                        {
                            "space": core.env.spaces.um_model_space,
                            "externalId": "UserComplement.userRoleSite",
                        },
                    ),
                    node_filter=HasData(
                        views=[
                            ViewId(
                                core.env.spaces.um_model_space,
                                core.env.cognite_entities.user_complement,
                                user_complement_view,
                            )
                        ]
                    ),
                ),
                # getting the user azure attribute from users complements from the roles
                "user_complement_in_users_roles": NodeResultSetExpression(
                    from_="user_complement_in_users_roles_in_template",
                    limit=10000,
                ),
                # getting the user azure attribute from users complements from the blocklist roles
                "user_complement_in_blocklist_roles": NodeResultSetExpression(
                    from_="user_complement_in_blocklist_roles_in_template",
                    limit=10000,
                ),
                # getting the user azure attribute from users complements from the roles
                "user_azure_attribute_in_users_roles_in_template": NodeResultSetExpression(
                    from_="user_complement_in_users_roles",
                    limit=10000,
                    chain_to="destination",
                    direction="outwards",
                    through=PropertyId(
                        ViewId(
                            core.env.spaces.um_model_space,
                            core.env.cognite_entities.user_complement,
                            user_complement_view,
                        ),
                        "userAzureAttribute",
                    ),
                ),
                # getting the user azure attribute from users complements from the blocklist roles
                "user_azure_attribute_in_blocklist_roles_in_template": NodeResultSetExpression(
                    from_="user_complement_in_blocklist_roles",
                    limit=10000,
                    chain_to="destination",
                    direction="outwards",
                    through=PropertyId(
                        ViewId(
                            core.env.spaces.um_model_space,
                            core.env.cognite_entities.user_complement,
                            user_complement_view,
                        ),
                        "userAzureAttribute",
                    ),
                ),
                # getting the user info from user azure attribute from users complements from the roles
                "user_in_users_roles": NodeResultSetExpression(
                    from_="user_azure_attribute_in_users_roles_in_template",
                    limit=10000,
                    chain_to="destination",
                    direction="outwards",
                    through=PropertyId(
                        ViewId(
                            core.env.spaces.um_model_space,
                            core.env.cognite_entities.user_azure_attribute,
                            user_azure_attribute_view,
                        ),
                        "user",
                    ),
                    filter=Equals(user_view.as_property_ref("deleted"), False),
                ),
                # getting the user info from user azure attribute from users complements from the blocklist roles
                "user_in_blocklist_roles": NodeResultSetExpression(
                    from_="user_azure_attribute_in_blocklist_roles_in_template",
                    limit=10000,
                    chain_to="destination",
                    direction="outwards",
                    through=PropertyId(
                        ViewId(
                            core.env.spaces.um_model_space,
                            core.env.cognite_entities.user_azure_attribute,
                            user_azure_attribute_view,
                        ),
                        "user",
                    ),
                    filter=Equals(user_view.as_property_ref("deleted"), False),
                ),
            },
            select={
                "application_groups": Select(
                    [
                        SourceSelector(
                            ViewId(
                                core.env.cognite.fdm_model_space,
                                core.env.cognite_entities.notification_application_group,
                                notification_application_group_view.version,
                            ),
                            ["*"],
                        )
                    ],
                    limit=10000,
                ),
                "subscribed_users_in_application_groups": Select(limit=10000),
                "users_in_application_groups": Select(
                    [
                        SourceSelector(
                            ViewId(
                                core.env.cognite.fdm_model_space,
                                core.env.cognite_entities.notification_user,
                                notification_user_view.version,
                            ),
                            ["*"],
                        )
                    ],
                    limit=10000,
                ),
                "blocklist_in_application_groups": Select(limit=10000),
                "users_in_blocklist_application_groups": Select(
                    [
                        SourceSelector(
                            ViewId(
                                core.env.cognite.fdm_model_space,
                                core.env.cognite_entities.notification_user,
                                notification_user_view.version,
                            ),
                            ["*"],
                        )
                    ],
                    limit=10000,
                ),
                "users_roles_in_template": Select(limit=10000),
                "blocklist_roles_in_template": Select(limit=10000),
                "roles_in_template": Select(
                    [
                        SourceSelector(
                            ViewId(
                                core.env.cognite.fdm_model_space,
                                core.env.cognite_entities.notification_user_role_site,
                                notification_user_role_site_view,
                            ),
                            ["*"],
                        )
                    ],
                    limit=10000,
                ),
                "roles_in_template_blocklist": Select(
                    [
                        SourceSelector(
                            ViewId(
                                core.env.cognite.fdm_model_space,
                                core.env.cognite_entities.notification_user_role_site,
                                notification_user_role_site_view,
                            ),
                            ["*"],
                        )
                    ],
                    limit=10000,
                ),
                "reporting_sites_from_roles_in_template": Select(
                    [
                        SourceSelector(
                            ViewId(
                                core.env.spaces.asset_hierarcy_model_space,
                                core.env.cognite_entities.reporting_site,
                                reporting_site_view,
                            ),
                            ["*"],
                        )
                    ],
                    limit=10000,
                ),
                "reporting_sites_from_roles_in_template_blocklist": Select(
                    [
                        SourceSelector(
                            ViewId(
                                core.env.spaces.asset_hierarcy_model_space,
                                core.env.cognite_entities.reporting_site,
                                reporting_site_view,
                            ),
                            ["*"],
                        )
                    ],
                    limit=10000,
                ),
                "role_from_roles_in_template": Select(
                    [
                        SourceSelector(
                            ViewId(
                                core.env.spaces.um_model_space,
                                core.env.cognite_entities.role,
                                role_view,
                            ),
                            ["*"],
                        )
                    ],
                    limit=10000,
                ),
                "role_from_roles_in_template_blocklist": Select(
                    [
                        SourceSelector(
                            ViewId(
                                core.env.spaces.um_model_space,
                                core.env.cognite_entities.role,
                                role_view,
                            ),
                            ["*"],
                        )
                    ],
                    limit=10000,
                ),
                "user_complement_in_users_roles_in_template": Select(limit=10000),
                "user_complement_in_blocklist_roles_in_template": Select(limit=10000),
                "user_complement_in_users_roles": Select(
                    [
                        SourceSelector(
                            ViewId(
                                core.env.spaces.um_model_space,
                                core.env.cognite_entities.user_complement,
                                user_complement_view,
                            ),
                            ["searchTags", "userAzureAttribute"],
                        )
                    ],
                    limit=10000,
                ),
                "user_complement_in_blocklist_roles": Select(
                    [
                        SourceSelector(
                            ViewId(
                                core.env.spaces.um_model_space,
                                core.env.cognite_entities.user_complement,
                                user_complement_view,
                            ),
                            ["searchTags", "userAzureAttribute"],
                        )
                    ],
                    limit=10000,
                ),
                "user_azure_attribute_in_users_roles_in_template": Select(
                    [
                        SourceSelector(
                            ViewId(
                                core.env.spaces.um_model_space,
                                core.env.cognite_entities.user_azure_attribute,
                                user_azure_attribute_view,
                            ),
                            ["user"],
                        )
                    ],
                    limit=10000,
                ),
                "user_azure_attribute_in_blocklist_roles_in_template": Select(
                    [
                        SourceSelector(
                            ViewId(
                                core.env.spaces.um_model_space,
                                core.env.cognite_entities.user_azure_attribute,
                                user_azure_attribute_view,
                            ),
                            ["user"],
                        )
                    ],
                    limit=10000,
                ),
                "user_in_users_roles": Select(
                    [
                        SourceSelector(
                            ViewId(
                                core.env.spaces.um_model_space,
                                core.env.cognite_entities.user,
                                user_view.version,
                            ),
                            ["email", "firstName", "lastName"],
                        )
                    ],
                    limit=10000,
                ),
                "user_in_blocklist_roles": Select(
                    [
                        SourceSelector(
                            ViewId(
                                core.env.spaces.um_model_space,
                                core.env.cognite_entities.user,
                                user_view.version,
                            ),
                            ["email", "firstName", "lastName"],
                        )
                    ],
                    limit=10000,
                ),
                "createdBy": Select(
                    [
                        SourceSelector(
                            ViewId(
                                core.env.cognite.fdm_model_space,
                                core.env.cognite_entities.notification_user,
                                notification_user_view.version,
                            ),
                            ["*"],
                        )
                    ],
                    limit=10000,
                ),
                "editedBy": Select(
                    [
                        SourceSelector(
                            ViewId(
                                core.env.cognite.fdm_model_space,
                                core.env.cognite_entities.notification_user,
                                notification_user_view.version,
                            ),
                            ["*"],
                        )
                    ],
                    limit=10000,
                ),
            },
            cursors={
                "application_groups": application_groups_cursor,
                "users_in_blocklist_application_groups": users_in_blocklist_application_groups_cursor,
                "users_in_application_groups": users_in_application_groups_cursor,
                "user_complement_in_users_roles_in_template": user_complement_in_users_roles_in_template_cursor,
                "user_complement_in_blocklist_roles_in_template": user_complement_in_blocklist_roles_in_template_cursor,
            },
        )

        while has_cursor:
            response_query = self._cognite_client.data_modeling.instances.query(query)
            application_groups = self.format_response(response_query, views)

            if len(application_groups) > 0:
                response.extend(application_groups)

            if response_query:
                has_cursor = False
                cursor_keys = [
                    "application_groups",
                    "users_in_blocklist_application_groups",
                    "users_in_application_groups",
                    "user_complement_in_users_roles_in_template",
                    "user_complement_in_blocklist_roles_in_template",
                ]

                for key in cursor_keys:
                    if (
                        key in response_query.cursors
                        and len(response_query[key]) == 10000
                    ):
                        query.cursors[key] = response_query.cursors[key]
                        has_cursor = True
            else:
                has_cursor = False

        return response

    def delete_application_group(self, external_id: str):
        application_group = NodeId(
            space=self._fdm_instances_space, external_id=external_id
        )
        self._cognite_client.data_modeling.instances.delete(nodes=application_group)

    def format_response(self, response, views: dict):
        application_groups_result = response["application_groups"].dump()
        users_edge_application_group_result = response[
            "subscribed_users_in_application_groups"
        ].dump()
        users_in_application_group_result = response[
            "users_in_application_groups"
        ].dump()
        blocklist_edge_application_group_result = response[
            "blocklist_in_application_groups"
        ].dump()
        users_roles_edge_application_group_result = response[
            "users_roles_in_template"
        ].dump()
        blocklist_roles_edge_application_group_result = response[
            "blocklist_roles_in_template"
        ].dump()

        users_in_blocklist_application_group_result = response[
            "users_in_blocklist_application_groups"
        ].dump()

        created_by_users = response["createdBy"].dump()
        edited_by_users = response["editedBy"].dump()

        users_roles = self._process_roles(
            response["roles_in_template"].dump(),
            response["reporting_sites_from_roles_in_template"].dump(),
            response["role_from_roles_in_template"].dump(),
            response["user_complement_in_users_roles"].dump(),
            response["user_azure_attribute_in_users_roles_in_template"].dump(),
            response["user_in_users_roles"].dump(),
            views,
            core,
        )

        blocklist_roles = self._process_roles(
            response["roles_in_template_blocklist"].dump(),
            response["reporting_sites_from_roles_in_template_blocklist"].dump(),
            response["role_from_roles_in_template_blocklist"].dump(),
            response["user_complement_in_blocklist_roles"].dump(),
            response["user_azure_attribute_in_blocklist_roles_in_template"].dump(),
            response["user_in_blocklist_roles"].dump(),
            views,
            core,
        )

        grouped_edges = self._group_edges(users_edge_application_group_result)
        blocklist_edges = self._group_edges(blocklist_edge_application_group_result)
        users_roles_edges = self._group_edges(users_roles_edge_application_group_result)
        blocklist_roles_edges = self._group_edges(
            blocklist_roles_edge_application_group_result
        )

        application_groups = []

        for application in application_groups_result:

            users_in_edge = grouped_edges.get(application.get("externalId", ""), [])

            blocklist_in_edge = blocklist_edges.get(
                application.get("externalId", ""), []
            )

            users_roles_in_edge = users_roles_edges.get(
                application.get("externalId", ""), []
            )

            blocklist_roles_in_edge = blocklist_roles_edges.get(
                application.get("externalId", ""), []
            )

            filtered_users = [
                user
                for user in users_in_application_group_result
                if user["externalId"] in users_in_edge
            ]
            filtered_users_blocklist = (
                [
                    user
                    for user in users_in_blocklist_application_group_result
                    if user["externalId"] in blocklist_in_edge
                ]
                if len(blocklist_in_edge) > 0
                else []
            )
            filtered_users_roles = [
                role
                for role in users_roles
                if role["externalId"] in users_roles_in_edge
            ]
            filtered_users_blocklist_roles = [
                role
                for role in blocklist_roles
                if role["externalId"] in blocklist_roles_in_edge
            ]

            notification_application_group_properties = (
                application.get("properties", {})
                .get(core.env.cognite.fdm_model_space, {})
                .get(
                    f"{core.env.cognite_entities.notification_application_group}/{views['notification_application_group']}",
                    {},
                )
            )

            users = [
                {
                    "email": user.get("properties", {})
                    .get(core.env.cognite.fdm_model_space, {})
                    .get(
                        f"{core.env.cognite_entities.notification_user}/{views['notification_user']}",
                        {},
                    )
                    .get("email", ""),
                    "externalId": user.get("externalId", ""),
                    "firstName": user.get("properties", {})
                    .get(core.env.cognite.fdm_model_space, {})
                    .get(
                        f"{core.env.cognite_entities.notification_user}/{views['notification_user']}",
                        {},
                    )
                    .get("firstName", ""),
                    "lastName": user.get("properties", {})
                    .get(core.env.cognite.fdm_model_space, {})
                    .get(
                        f"{core.env.cognite_entities.notification_user}/{views['notification_user']}",
                        {},
                    )
                    .get("lastName", ""),
                }
                for user in filtered_users
            ]
            blocklist = (
                [
                    {
                        "email": user.get("properties", {})
                        .get(core.env.cognite.fdm_model_space, {})
                        .get(
                            f"{core.env.cognite_entities.notification_user}/{views['notification_user']}",
                            {},
                        )
                        .get("email", ""),
                        "externalId": user.get("externalId", ""),
                        "firstName": user.get("properties", {})
                        .get(core.env.cognite.fdm_model_space, {})
                        .get(
                            f"{core.env.cognite_entities.notification_user}/{views['notification_user']}",
                            {},
                        )
                        .get("firstName", ""),
                        "lastName": user.get("properties", {})
                        .get(core.env.cognite.fdm_model_space, {})
                        .get(
                            f"{core.env.cognite_entities.notification_user}/{views['notification_user']}",
                            {},
                        )
                        .get("lastName", ""),
                    }
                    for user in filtered_users_blocklist
                ]
                if len(filtered_users_blocklist) > 0
                else []
            )

            created_by_external_id = notification_application_group_properties.get(
                "createdBy", {}
            ).get("externalId", "")
            created_result = next(
                (
                    item
                    for item in created_by_users
                    if item.get("externalId", "") == created_by_external_id
                ),
                {},
            )
            created_properties = (
                created_result.get("properties", {})
                .get(core.env.cognite.fdm_model_space, {})
                .get(
                    f"{core.env.cognite_entities.notification_user}/{views['notification_user']}",
                    {},
                )
            )
            created_by_user = created_properties

            edited_by_external_id = notification_application_group_properties.get(
                "editedBy", {}
            ).get("externalId", "")

            edited_result = next(
                (
                    item
                    for item in edited_by_users
                    if item.get("externalId", "") == edited_by_external_id
                ),
                {},
            )
            edited_properties = (
                edited_result.get("properties", {})
                .get(core.env.cognite.fdm_model_space, {})
                .get(
                    f"{core.env.cognite_entities.notification_user}/{views['notification_user']}",
                    {},
                )
            )
            edited_by_user = edited_properties

            application_groups.append(
                {
                    "externalId": application.get("externalId", ""),
                    "space": application.get("space", ""),
                    "name": application.get("properties", {})
                    .get(core.env.cognite.fdm_model_space, {})
                    .get(
                        f"{core.env.cognite_entities.notification_application_group}/{views['notification_application_group']}",
                        {},
                    )
                    .get("name", ""),
                    "description": application.get("properties", {})
                    .get(core.env.cognite.fdm_model_space, {})
                    .get(
                        f"{core.env.cognite_entities.notification_application_group}/{views['notification_application_group']}",
                        {},
                    )
                    .get("description", ""),
                    "application": {
                        "externalId": application.get("properties", {})
                        .get(core.env.cognite.fdm_model_space, {})
                        .get(
                            f"{core.env.cognite_entities.notification_application_group}/{views['notification_application_group']}",
                            {},
                        )
                        .get("application", "")
                        .get("externalId", ""),
                        "space": application.get("properties", {})
                        .get(core.env.cognite.fdm_model_space, {})
                        .get(
                            f"{core.env.cognite_entities.notification_application_group}/{views['notification_application_group']}",
                            {},
                        )
                        .get("application", "")
                        .get("space", ""),
                    },
                    "externalUsers": application.get("properties", {})
                    .get(core.env.cognite.fdm_model_space, {})
                    .get(
                        f"{core.env.cognite_entities.notification_application_group}/{views['notification_application_group']}",
                        {},
                    )
                    .get("externalUsers", []),
                    "users": users,
                    "blocklist": blocklist,
                    "usersRoles": filtered_users_roles,
                    "blocklistRoles": filtered_users_blocklist_roles,
                    "createdBy": (
                        {
                            "email": created_by_user.get("email", ""),
                            "externalId": created_by_external_id,
                            "firstName": created_by_user.get("firstName", ""),
                            "lastName": created_by_user.get("lastName", ""),
                            "teams": [],
                        }
                        if created_by_user
                        else {}
                    ),
                    "editedBy": (
                        {
                            "email": edited_by_user.get("email", ""),
                            "externalId": edited_by_external_id,
                            "firstName": edited_by_user.get("firstName", ""),
                            "lastName": edited_by_user.get("lastName", ""),
                            "teams": [],
                        }
                        if edited_by_user
                        else {}
                    ),
                    "editedAt": notification_application_group_properties.get(
                        "editedAt", ""
                    ),
                }
            )

        return application_groups

    def _group_edges(self, users_edge_application_group_result):
        """Groups edges by their start node."""
        grouped_edges = {}
        for edge in users_edge_application_group_result:
            start_node = edge["startNode"]["externalId"]
            end_node = edge["endNode"]["externalId"]

            if start_node not in grouped_edges:
                grouped_edges[start_node] = []

            grouped_edges[start_node].append(end_node)
        return grouped_edges

    def _get_site_info(self, reporting_site_id, reporting_sites, views, core):
        """Extract site information from reporting sites data."""
        filter_site = next(
            (
                item
                for item in reporting_sites
                if item["externalId"] == reporting_site_id
            ),
            {},
        )

        filter_site_properties = (
            filter_site.get("properties", {})
            .get(core.env.spaces.asset_hierarcy_model_space, {})
            .get(
                f"{core.env.cognite_entities.reporting_site}/{views['reporting_site']}",
                {},
            )
        )

        site_code = filter_site_properties.get("siteCode", "")
        return {
            "externalId": filter_site.get("externalId", ""),
            "space": filter_site.get("space", ""),
            "name": filter_site_properties.get("name", ""),
            "siteCode": site_code,
        }, site_code

    def _get_role_name(self, role_external_id, roles_data, site_code, views, core):
        """Get role name from role data."""
        filter_role = next(
            (item for item in roles_data if item["externalId"] == role_external_id), {}
        )

        filter_role_properties = (
            filter_role.get("properties", {})
            .get(core.env.spaces.um_model_space, {})
            .get(f"{core.env.cognite_entities.role}/{views['role']}", {})
        )

        return f"({site_code}) {filter_role_properties.get('name', '')}"

    def _get_user_list(
        self,
        role_external_id,
        user_complement_data,
        user_azure_data,
        users_data,
        views,
        core,
    ):
        """Get list of users associated with a role."""
        # Get user complements for the role
        filter_users = [
            user
            for user in user_complement_data
            if role_external_id
            in user.get("properties", {})
            .get(core.env.spaces.um_model_space, {})
            .get(
                f"{core.env.cognite_entities.user_complement}/{views['user_complement']}",
                {},
            )
            .get("searchTags", [])
        ]

        # Get Azure attributes
        users_complement = [
            user.get("properties", {})
            .get(core.env.spaces.um_model_space, {})
            .get(
                f"{core.env.cognite_entities.user_complement}/{views['user_complement']}",
                {},
            )
            .get("userAzureAttribute", {})
            .get("externalId", "")
            for user in filter_users
        ]

        user_azure_attribute = [
            user.get("properties", {})
            .get(core.env.spaces.um_model_space, {})
            .get(
                f"{core.env.cognite_entities.user_azure_attribute}/{views['user_azure_attribute']}",
                {},
            )
            .get("user", {})
            .get("externalId", "")
            for user in user_azure_data
            if user["externalId"] in users_complement
        ]

        # Get final user list
        users_in_role = [
            {
                "email": user.get("properties", {})
                .get(core.env.spaces.um_model_space, {})
                .get(f"{core.env.cognite_entities.user}/{views['user']}", {})
                .get("email", ""),
                "externalId": user.get("externalId", ""),
                "firstName": user.get("properties", {})
                .get(core.env.spaces.um_model_space, {})
                .get(f"{core.env.cognite_entities.user}/{views['user']}", {})
                .get("firstName", ""),
                "lastName": user.get("properties", {})
                .get(core.env.spaces.um_model_space, {})
                .get(f"{core.env.cognite_entities.user}/{views['user']}", {})
                .get("lastName", ""),
            }
            for user in users_data
            if user["externalId"] in user_azure_attribute
        ]

        return sorted(users_in_role, key=lambda x: x["email"].lower())

    def _process_roles(
        self,
        roles_data,
        reporting_sites,
        roles_source,
        user_complement_data,
        user_azure_data,
        users_data,
        views,
        core,
    ):
        """Process roles and return formatted role information."""
        processed_roles = []

        for role in roles_data:
            properties = (
                role.get("properties", {})
                .get(core.env.cognite.fdm_model_space, {})
                .get(
                    f"{core.env.cognite_entities.notification_user_role_site}/{views['notification_user_role_site']}",
                    {},
                )
            )

            # Get reporting site info
            reporting_site_id = properties.get("reportingSite", {}).get(
                "externalId", ""
            )
            site, site_code = self._get_site_info(
                reporting_site_id, reporting_sites, views, core
            )

            # Get role info
            role_external_id = properties.get("role", {}).get("externalId", "")
            role_name = self._get_role_name(
                role_external_id, roles_source, site_code, views, core
            )

            # Get users
            users = self._get_user_list(
                role.get("externalId", ""),
                user_complement_data,
                user_azure_data,
                users_data,
                views,
                core,
            )

            processed_roles.append(
                {
                    "externalId": role.get("externalId", ""),
                    "space": role.get("space", ""),
                    "name": role_name,
                    "site": site,
                    "users": users,
                }
            )

        return processed_roles
