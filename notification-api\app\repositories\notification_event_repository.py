from typing import Any, TypeVar
from cognite.client import CogniteClient
from app.core.env_variables import EnvVariables
from app.core.graphql_client import GraphQLClient
import app.queries as queries
import math as math
import app.utils as Utils
import json

from uuid import uuid4
from cognite.client.data_classes.data_modeling import NodeApply, NodeOrEdgeData, ViewId
from app.core.cache_global import get_cache_instance


ENTITY = "NotificationEvent"
ENTITY_RAW_EVENT = "NotificationRawEvent"
ENTITY_RAW_EVENT_LOG = "NotificationRawEventLog"
T = TypeVar("T")


class NotificationEventRepository:
    def __init__(
        self,
        cognite_client: CogniteClient,
        graphql_client: GraphQLClient,
        env_variables: EnvVariables,
    ):
        self._cognite_client = cognite_client
        self._graphql_client = graphql_client
        self._fdm_model_space = env_variables.cognite.fdm_model_space
        self._fdm_prot_instances_space = env_variables.spaces.ntf_prot_instance_space
        self._fdm_instances_space = env_variables.spaces.ntf_instance_space
        self._asset_hierarchy_instance_space = (
            env_variables.spaces.assethierarcy_instace_space
        )

    def find_by_filter(
        self,
        filter: Any = None,
    ) -> Any:
        my_query = queries.notification_event.notification_event_list
        items = self._graphql_client.query_unlimited(
            my_query, "listNotificationEvent", filter
        )

        ordered_result = Utils.list.order(items, [("createdTime", "DESC")])
        return ordered_result

    def save_raw_event(self, request):
        try:
            _cache = get_cache_instance()
            # GET ENTITY VIEW
            cognite_views = _cache.get("cognite_views")[self._fdm_model_space]

            view = Utils.cognite.find_view_by_external_id(
                cognite_views, ENTITY_RAW_EVENT
            )

            request["sourceJson"] = json.loads(request["sourceJson"])
            requestReferenceId = request["sourceJson"]["requestReferenceId"]
            reportingSiteId = next(
                (
                    item["value"]
                    for item in request["sourceJson"]["properties"]
                    if item.get("name") == "site"
                ),
                None,
            )
            request["reportingSite"] = (
                {
                    "externalId": reportingSiteId,
                    "space": self._asset_hierarchy_instance_space,
                }
                if reportingSiteId
                else None
            )

            rawEventExternalId = Utils.generateExternalId("NTFRAWEVT")
            rawEventNode = NodeApply(
                self._fdm_prot_instances_space,
                rawEventExternalId,
                sources=[
                    NodeOrEdgeData(
                        ViewId(self._fdm_model_space, ENTITY_RAW_EVENT, view.version),
                        request,
                    )
                ],
            )

            viewEventLog = Utils.cognite.find_view_by_external_id(
                cognite_views, ENTITY_RAW_EVENT_LOG
            )

            logData = {}
            logData["rawEvent"] = {
                "externalId": rawEventExternalId,
                "space": self._fdm_prot_instances_space,
            }
            externalIdLog = Utils.generateExternalId("NTFRAWEVTLOG")
            rawEventLogNode = NodeApply(
                self._fdm_instances_space,
                externalIdLog,
                sources=[
                    NodeOrEdgeData(
                        ViewId(
                            self._fdm_model_space,
                            ENTITY_RAW_EVENT_LOG,
                            viewEventLog.version,
                        ),
                        logData,
                    )
                ],
            )

            result = self._cognite_client.data_modeling.instances.apply(
                [rawEventNode, rawEventLogNode], replace=False
            )

            request["status"] = "created"
            request["externalId"] = rawEventExternalId
            if requestReferenceId:
                request["requestReferenceId"] = requestReferenceId

        except Exception as e:
            request["status"] = "failed"
            request["detail"] = str(e)
            request["externalId"] = None

        return request
