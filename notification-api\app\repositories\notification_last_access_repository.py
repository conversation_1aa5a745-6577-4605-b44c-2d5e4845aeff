from datetime import datetime

from cognite.client import CogniteClient
from cognite.client.data_classes.data_modeling import (
    NodeApply,
    NodeOrEdgeData,
    ViewId,
    Node,
)
from cognite.client.data_classes.data_modeling.query import (
    Query,
    NodeResultSetExpression,
    Select,
    SourceSelector,
)
from cognite.client.data_classes.filters import And, Equals, HasData
from pytz import timezone

import app.core as core
import app.models as models
import app.utils as Utils
from app.core.env_variables import EnvVariables
from app.core.graphql_client import GraphQLClient
from app.core.cache_global import get_cache_instance

ENTITY = "NotificationLastAccess"
class NotificationLastAccessRepository:
    def __init__(
        self,
        cognite_client: CogniteClient,
        graphql_client: GraphQLClient,
        env_variables: EnvVariables,
    ):
        self._cognite_client = cognite_client
        self._graphql_client = graphql_client
        self._fdm_model_space = env_variables.cognite.fdm_model_space
        self._ntf_instances_space = env_variables.spaces.ntf_instance_space

    def save(self, user_external_id: str):

        _cache = get_cache_instance()
        cognite_views = _cache.get("cognite_views")[self._fdm_model_space]

        view = Utils.cognite.find_view_by_external_id(cognite_views, ENTITY)

        request: models.NotificationLastAccessModel = (
            models.NotificationLastAccessModel(userExternalId=user_external_id)
        )

        request.user = models.RelationModel(
            externalId=user_external_id, space=core.env.spaces.um_instance_space
        )

        current_time = datetime.now()
        gmt_timezone = timezone("GMT")
        gmt_time = current_time.astimezone(gmt_timezone).strftime("%Y-%m-%dT%H:%M:%SZ")
        request.date = gmt_time

        last_access_external_id = request.generate_external_id()

        del request.userExternalId

        last_access_node = NodeApply(
            self._ntf_instances_space,
            last_access_external_id,
            sources=[
                NodeOrEdgeData(
                    ViewId(self._fdm_model_space, ENTITY, view.version),
                    request.model_dump(),
                )
            ],
        )

        self._cognite_client.data_modeling.instances.apply(
            nodes=last_access_node,
        )

    def get_last_access_date(self, user_external_id: str):
        _cache = get_cache_instance()
        cognite_views = _cache.get("cognite_views")[self._fdm_model_space]

        view = Utils.cognite.find_view_by_external_id(cognite_views, ENTITY)
        
        last_access_external_id = models.NotificationLastAccessModel(
            userExternalId=user_external_id
        ).generate_external_id()
        response = self._cognite_client.data_modeling.instances.query(
            Query(
                with_={
                    "user_last_access": NodeResultSetExpression(
                        filter=And(
                            Equals(["node", "externalId"], last_access_external_id),
                            HasData(views=[view.as_id()]),
                        ),
                        limit=1,
                    )
                },
                select={
                    "user_last_access": Select(
                        sources=[
                            SourceSelector(source=view.as_id(), properties=["date"])
                        ]
                    )
                },
            )
        )
        nodes = response.get_nodes("user_last_access")
        if not nodes:
            return None

        node = nodes[0]
        if isinstance(node, Node):
            return node.properties.get(view.as_id(), {}).get("date")

        return None