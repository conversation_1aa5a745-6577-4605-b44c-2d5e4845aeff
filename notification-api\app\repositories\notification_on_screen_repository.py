from datetime import datetime, timezone
from typing import List, Any
from cognite.client import CogniteClient
from cognite.client.data_classes.data_modeling import (
    NodeApply,
    NodeOrEdgeData,
    ViewId,
    NodeId,
    PropertyId,
)
from cognite.client.data_classes.data_modeling.query import (
    Query,
    Select,
    EdgeResultSetExpression,
    NodeResultSetExpression,
    SourceSelector,
    QueryResult,
)
from cognite.client.data_classes.filters import And, Equals, Range, HasData, Or
import app.queries as queries
import app.utils as Utils
import app.core as core
from app.core.env_variables import EnvVariables
from app.core.graphql_client import GraphQLClient
from app.core.cache_global import get_cache_instance

ENTITY_ON_SCREEN = "NotificationOnScreen"
ENTITY_COMMENTS = "NotificationComment"


class NotificationOnScreenRepository:
    def __init__(
        self,
        cognite_client: CogniteClient,
        graphql_client: GraphQLClient,
        env_variables: EnvVariables,
    ):
        self._cognite_client = cognite_client
        self._graphql_client = graphql_client
        self._fdm_model_space = env_variables.cognite.fdm_model_space
        self._fdm_instances_space = env_variables.spaces.ntf_prot_instance_space
        self._um_instances_space = env_variables.spaces.um_instance_space

    def get_notification_on_screen_ids(
        self, user_external_id: str, last_visualized_date: str
    ) -> List[dict]:

        _cache = get_cache_instance()
        # GET ENTITY VIEW
        cognite_views = _cache.get("cognite_views")[self._fdm_model_space]

        notification_view = Utils.cognite.find_view_by_external_id(
            cognite_views, ENTITY_ON_SCREEN
        )

        notifications_response = self._cognite_client.data_modeling.instances.query(
            Query(
                with_={
                    "edges": EdgeResultSetExpression(
                        filter=And(
                            Equals(
                                ["edge", "type"],
                                {
                                    "externalId": "NotificationOnScreen.subscribers",
                                    "space": self._fdm_model_space,
                                },
                            ),
                            Equals(
                                ["edge", "endNode"],
                                {
                                    "externalId": user_external_id,
                                    "space": self._um_instances_space,
                                },
                            ),
                        ),
                        node_filter=Range(
                            notification_view.as_property_ref("createdAt"),
                            gt=last_visualized_date,
                        ),
                        direction="inwards",
                        max_distance=1,
                        limit=10_000,
                    )
                },
                select={"edges": Select()},
            )
        )
        notifications = notifications_response.get_edges("edges")
        return [{"externalId": n.external_id} for n in notifications]

    def get_chat(self, externalId) -> List[Any]:
        query = (
            queries.notification_on_screen_queries.get_notification_on_screen_with_chat
        )
        query = query.replace("$space", self._fdm_instances_space).replace(
            "$externalId", externalId
        )
        return self._graphql_client.query(
            query,
            "getNotificationOnScreenById",
        )

    def create_chat(self, externalId, user_external_id: str, comment: str) -> bool:

        try:
            _cache = get_cache_instance()
            # GET ENTITY VIEW
            cognite_views = _cache.get("cognite_views")[self._fdm_model_space]

            entity_versions_comment_view = Utils.cognite.find_view_by_external_id(
                cognite_views, ENTITY_COMMENTS
            )
            entity_versions_comment = entity_versions_comment_view.version

            entity_versions_on_screen_view = Utils.cognite.find_view_by_external_id(
                cognite_views, ENTITY_ON_SCREEN
            )
            entity_versions_on_screen = entity_versions_on_screen_view.version

            comment_external_id = str(Utils.generateExternalId("NTFOSC"))
            comment = {
                "user": {
                    "externalId": user_external_id,
                    "space": self._um_instances_space,
                },
                "comment": comment,
            }

            # ADD COMMENTED
            comment_nodes = NodeApply(
                self._fdm_instances_space,
                comment_external_id,
                sources=[
                    NodeOrEdgeData(
                        ViewId(
                            self._fdm_model_space,
                            ENTITY_COMMENTS,
                            entity_versions_comment,
                        ),
                        Utils.DotDict(comment),
                    )
                ],
            )
            self._cognite_client.data_modeling.instances.apply(
                nodes=comment_nodes,
            )

            # UPDATE NOTIFICATION ON SCREEN
            notification_on_screen = {
                "externalId": externalId,
                "space": self._fdm_instances_space,
            }
            on_screen_nodes = NodeApply(
                self._fdm_instances_space,
                externalId,
                sources=[
                    NodeOrEdgeData(
                        ViewId(
                            self._fdm_model_space,
                            ENTITY_ON_SCREEN,
                            entity_versions_on_screen,
                        ),
                        Utils.DotDict(notification_on_screen),
                    )
                ],
            )

            comment = {
                "externalId": comment_external_id,
                "space": self._fdm_instances_space,
            }

            Utils.cognite.createRelationship(
                [Utils.DotDict(comment)],
                on_screen_nodes,
                entity_versions_on_screen,
                self._graphql_client,
                self._cognite_client,
                self._fdm_model_space,
                self._fdm_instances_space,
                "comments",
                False,
            )
            return True
        except Exception as e:
            print(e)

    def get_page_info(self, filter) -> List[Any]:
        return self._graphql_client.queryPageInfo(
            queries.notification_on_screen_queries.get_page_info,
            "listNotificationOnScreen",
            filter,
        )

    def get_notification_on_screen_info(self, filter) -> List[Any]:
        filter["filter"]["and"].append({"space": {"eq": self._fdm_instances_space}})
        return self._graphql_client.query(
            queries.notification_on_screen_queries.list_notification_on_screen_info,
            "listNotificationOnScreen",
            filter,
        )

    def get_notification_on_screen_ids_by_filter(self, filter) -> List[Any]:
        filter["filter"]["and"].append({"space": {"eq": self._fdm_instances_space}})
        return self._graphql_client.query(
            queries.notification_on_screen_queries.list_notification_on_screen_ids_by_filter,
            "listNotificationOnScreen",
            filter,
        )

    def get_sites_applications_notification_types(self, filter) -> List[Any]:
        return self._graphql_client.query(
            queries.notification_on_screen_queries.list_sites_applications_notification_types,
            "listNotificationOnScreen",
            filter,
        )

    def get_by_user_and_filter(self, filter: Any) -> List[Any]:

        results = self._graphql_client.query(
            queries.notification_on_screen_queries.list_by_user_and_filter,
            "listNotificationUser",
            filter,
        )
        items = [result.get("notificationsOnScreen") for result in results]
        if len(items[0].get("items")) > 0:
            return items[0].get("items")

        return None

    def find_notification_comment(self, filter) -> List[Any]:
        return self._graphql_client.query(
            queries.notification_on_screen_queries.find_notification_comment_by_id,
            "listNotificationComment",
            filter,
        )

    def delete_notification_comment(self, external_id: str):
        notificationComment = NodeId(
            space=self._fdm_instances_space, external_id=external_id
        )
        self._cognite_client.data_modeling.instances.delete(nodes=notificationComment)

    def transform_date(self, date: str):
        timestamp = date
        timestamp_in_seconds = timestamp / 1000
        dt = datetime.fromtimestamp(timestamp_in_seconds, tz=timezone.utc)
        return dt.strftime("%Y-%m-%dT%H:%M:%S.%f")[:-3] + "Z"

    def list_notification_on_screen_by_user_and_period(
        self, user_id, start_date, end_date
    ):

        _cache = get_cache_instance()

        cognite_views = _cache.get("cognite_views")

        notification_views = cognite_views[core.env.cognite.fdm_model_space]

        notification_user_view_id = Utils.cognite.find_view_by_external_id(
            notification_views, core.env.cognite_entities.notification_user
        ).as_id()

        notification_on_screen_view_id = Utils.cognite.find_view_by_external_id(
            notification_views, core.env.cognite_entities.notification_on_screen
        ).as_id()
        template_view_id = Utils.cognite.find_view_by_external_id(
            notification_views, core.env.cognite_entities.notification_template
        ).as_id()

        notification_type_view_id = Utils.cognite.find_view_by_external_id(
            notification_views, core.env.cognite_entities.notification_type
        ).as_id()

        application_view_id = Utils.cognite.find_view_by_external_id(
            cognite_views[core.env.spaces.um_model_space],
            core.env.cognite_entities.application,
        ).as_id()

        notification_severity_view_id = Utils.cognite.find_view_by_external_id(
            notification_views,
            core.env.cognite_entities.notification_severity,
        ).as_id()
        notification_comment_view_id = Utils.cognite.find_view_by_external_id(
            notification_views, core.env.cognite_entities.notification_comment
        ).as_id()

        reporting_site_view_id = Utils.cognite.find_view_by_external_id(
            cognite_views[core.env.spaces.asset_hierarcy_model_space],
            core.env.cognite_entities.reporting_site,
        ).as_id()

        notification_types_cursor = None
        comments_edges_cursor = None
        notification_details_cursor = None
        comments_cursor = None
        templates_cursor = None
        notifications_on_screen_edges_cursor = None
        users_cursor = None
        reporting_sites_cursor = None
        applications_cursor = None
        severities_cursor = None

        has_cursor = True
        response_query = None
        response = []
        fetch_limit = 10_000

        views = {
            "notification_user": notification_user_view_id,
            "notification_on_screen": notification_on_screen_view_id,
            "template": template_view_id,
            "notification_type": notification_type_view_id,
            "application": application_view_id,
            "notification_severity": notification_severity_view_id,
            "notification_comment": notification_comment_view_id,
            "reporting_site": reporting_site_view_id,
        }

        user_filter_conditions = []

        user_filter_conditions.append(Equals(["node", "externalId"], user_id))
        user_filter_conditions.append(
            Equals(["node", "space"], core.env.spaces.um_instance_space)
        )

        if isinstance(start_date, str):
            start_date = datetime.fromisoformat(start_date.replace("Z", "+00:00"))
        if isinstance(end_date, str):
            end_date = datetime.fromisoformat(end_date.replace("Z", "+00:00"))

        format_date = "%Y-%m-%dT%H:%M:%S"

        notification_filter_conditions = And(
            Or(
                And(
                    Range(
                        notification_on_screen_view_id.as_property_ref("createdAt"),
                        gte=start_date.strftime(format_date),
                    ),
                    Range(
                        notification_on_screen_view_id.as_property_ref("createdAt"),
                        lte=end_date.strftime(format_date),
                    ),
                ),
                And(
                    Range(
                        notification_on_screen_view_id.as_property_ref("newUpdateTime"),
                        gte=start_date.strftime(format_date),
                    ),
                    Range(
                        notification_on_screen_view_id.as_property_ref("newUpdateTime"),
                        lte=end_date.strftime(format_date),
                    ),
                ),
            ),
        )

        query = Query(
            with_={
                "users": NodeResultSetExpression(
                    filter=(
                        And(*user_filter_conditions) if user_filter_conditions else None
                    ),
                    chain_to="destination",
                    direction="outwards",
                    limit=fetch_limit,
                ),
                "notifications_on_screen_edges": EdgeResultSetExpression(
                    from_="users",
                    limit=fetch_limit,
                    direction="inwards",
                    chain_to="destination",
                    filter=Equals(
                        ["edge", "type"],
                        {
                            "space": core.env.cognite.fdm_model_space,
                            "externalId": "NotificationOnScreen.subscribers",
                        },
                    ),
                    node_filter=notification_filter_conditions,
                ),
                "notification_details": NodeResultSetExpression(
                    from_="notifications_on_screen_edges", limit=fetch_limit
                ),
                "templates": NodeResultSetExpression(
                    from_="notification_details",
                    limit=fetch_limit,
                    chain_to="destination",
                    direction="outwards",
                    through=PropertyId(notification_on_screen_view_id, "template"),
                ),
                "notification_types": NodeResultSetExpression(
                    from_="templates",
                    limit=fetch_limit,
                    chain_to="destination",
                    direction="outwards",
                    through=PropertyId(template_view_id, "notificationType"),
                ),
                "applications": NodeResultSetExpression(
                    from_="notification_types",
                    limit=fetch_limit,
                    chain_to="destination",
                    direction="outwards",
                    through=PropertyId(notification_type_view_id, "application"),
                ),
                "severities": NodeResultSetExpression(
                    from_="notification_details",
                    limit=fetch_limit,
                    chain_to="destination",
                    direction="outwards",
                    through=PropertyId(notification_on_screen_view_id, "severity"),
                ),
                "comments_edges": EdgeResultSetExpression(
                    from_="notification_details",
                    limit=fetch_limit,
                    direction="outwards",
                    chain_to="destination",
                    filter=Equals(
                        ["edge", "type"],
                        {
                            "space": core.env.cognite.fdm_model_space,
                            "externalId": "NotificationOnScreen.comments",
                        },
                    ),
                    node_filter=HasData(
                        views=[
                            notification_comment_view_id,
                        ]
                    ),
                ),
                "comments": NodeResultSetExpression(
                    from_="comments_edges", limit=fetch_limit
                ),
                "reporting_sites": NodeResultSetExpression(
                    from_="notification_details",
                    limit=fetch_limit,
                    chain_to="destination",
                    direction="outwards",
                    through=PropertyId(notification_on_screen_view_id, "reportingSite"),
                ),
            },
            select={
                "users": Select(
                    [SourceSelector(notification_user_view_id, ["*"])],
                    limit=fetch_limit,
                ),
                "notification_details": Select(
                    [SourceSelector(notification_on_screen_view_id, ["*"])],
                    limit=fetch_limit,
                ),
                "templates": Select(
                    [SourceSelector(template_view_id, ["*"])], limit=fetch_limit
                ),
                "notification_types": Select(
                    [SourceSelector(notification_type_view_id, ["*"])],
                    limit=fetch_limit,
                ),
                "applications": Select(
                    [SourceSelector(application_view_id, ["*"])], limit=fetch_limit
                ),
                "severities": Select(
                    [SourceSelector(notification_severity_view_id, ["*"])],
                    limit=fetch_limit,
                ),
                "comments_edges": Select(limit=fetch_limit),
                "comments": Select(
                    [SourceSelector(notification_comment_view_id, ["*"])],
                    limit=fetch_limit,
                ),
                "reporting_sites": Select(
                    [SourceSelector(reporting_site_view_id, ["*"])], limit=fetch_limit
                ),
                "notifications_on_screen_edges": Select(limit=fetch_limit),
            },
            cursors={
                "users": users_cursor,
                "notifications_on_screen_edges": notifications_on_screen_edges_cursor,
                "notification_details": notification_details_cursor,
                "templates": templates_cursor,
                "notification_types": notification_types_cursor,
                "applications": applications_cursor,
                "severities": severities_cursor,
                "comments_edges": comments_edges_cursor,
                "comments": comments_cursor,
                "reporting_sites": reporting_sites_cursor,
            },
        )

        def format_result(query_result: QueryResult, views: dict[str, ViewId]):
            formatted_items = []

            ntf_users = query_result.get("users", []).dump()
            ntf_on_screen = query_result.get("notification_details", []).dump()
            ntf_on_screen_template = query_result.get("templates", []).dump()
            ntf_on_screen_notification_types = query_result.get(
                "notification_types", []
            ).dump()
            ntf_on_screen_applications = query_result.get("applications", []).dump()
            ntf_on_screen_severities = query_result.get("severities", []).dump()
            ntf_on_screen_comments = query_result.get("comments", []).dump()
            ntf_on_screen_reporting_sites = query_result.get(
                "reporting_sites", []
            ).dump()

            def group_edges_by_end_node(result_edge):
                """Groups edges by their end node."""
                grouped_edges = {}
                for edge in result_edge:
                    start_node = edge["startNode"]["externalId"]
                    end_node = edge["endNode"]["externalId"]

                    if end_node not in grouped_edges:
                        grouped_edges[end_node] = []

                    grouped_edges[end_node].append(start_node)
                return grouped_edges

            def group_edges_by_start_node(result_edge):
                """Groups edges by their start node."""
                grouped_edges = {}
                for edge in result_edge:
                    start_node = edge["startNode"]["externalId"]
                    end_node = edge["endNode"]["externalId"]

                    if start_node not in grouped_edges:
                        grouped_edges[start_node] = []

                    grouped_edges[start_node].append(end_node)
                return grouped_edges

            ntf_on_screen_edges = group_edges_by_end_node(
                query_result.get("notifications_on_screen_edges", []).dump()
            )

            comment_edges = group_edges_by_start_node(
                query_result.get("comments_edges", []).dump()
            )

            for user in ntf_users:
                user_notifications = []

                ntf_on_screen_ids_from_edges = ntf_on_screen_edges.get(
                    user.get("externalId"), []
                )

                filtered_ntf_on_screen = [
                    ntf
                    for ntf in ntf_on_screen
                    if ntf.get("externalId") in ntf_on_screen_ids_from_edges
                ]

                for notification in filtered_ntf_on_screen:

                    ntf_on_screen_view_id = views["notification_on_screen"]

                    ntf_on_screen_properties = (
                        notification.get("properties", {})
                        .get(ntf_on_screen_view_id.space, {})
                        .get(
                            f"{ntf_on_screen_view_id.external_id}/{ntf_on_screen_view_id.version}",
                            {},
                        )
                    )

                    template = None
                    template_properties = None
                    for template_item in ntf_on_screen_template:
                        if template_item.get(
                            "externalId"
                        ) == ntf_on_screen_properties.get("template", {}).get(
                            "externalId"
                        ):
                            template = template_item
                            break

                    if template:
                        template_view_id = views["template"]
                        template_properties = (
                            template.get("properties", {})
                            .get(template_view_id.space, {})
                            .get(
                                f"{template_view_id.external_id}/{template_view_id.version}",
                                {},
                            )
                        )

                    notification_type = None
                    if template:
                        for type_item in ntf_on_screen_notification_types:
                            if type_item.get("externalId") == template_properties.get(
                                "notificationType", {}
                            ).get("externalId"):
                                type_view_id = views["notification_type"]
                                notification_type_properties = (
                                    type_item.get("properties", {})
                                    .get(type_view_id.space, {})
                                    .get(
                                        f"{type_view_id.external_id}/{type_view_id.version}",
                                        {},
                                    )
                                )
                                notification_type = {
                                    "externalId": type_item.get("externalId"),
                                    "space": type_item.get("space"),
                                    "name": notification_type_properties.get("name"),
                                    "description": (
                                        notification_type_properties.get("description")
                                        if notification_type_properties
                                        else None
                                    ),
                                    "application": notification_type_properties.get(
                                        "application", {}
                                    ),
                                }
                                break

                    application = None
                    if notification_type:
                        for app_item in ntf_on_screen_applications:
                            if app_item.get("externalId") == notification_type.get(
                                "application", {}
                            ).get("externalId"):
                                app_view_id = views["application"]
                                application_properties = (
                                    app_item.get("properties", {})
                                    .get(app_view_id.space, {})
                                    .get(
                                        f"{app_view_id.external_id}/{app_view_id.version}",
                                        {},
                                    )
                                )
                                application = {
                                    "externalId": app_item.get("externalId"),
                                    "space": app_item.get("space"),
                                    "name": application_properties.get("name"),
                                    "description": (
                                        application_properties.get("description")
                                        if application_properties
                                        else None
                                    ),
                                }
                                break

                    severity = None
                    for severity_item in ntf_on_screen_severities:
                        if severity_item.get("externalId") == ntf_on_screen_properties.get(
                            "severity", {}
                        ).get("externalId"):
                            severity_view_id = views["notification_severity"]
                            severity_properties = (
                                severity_item.get("properties", {})
                                .get(severity_view_id.space, {})
                                .get(
                                    f"{severity_view_id.external_id}/{severity_view_id.version}",
                                    {},
                                )
                            )
                            severity = {
                                "externalId": severity_item.get("externalId"),
                                "space": severity_item.get("space"),
                                "name": severity_properties.get("name"),
                                "description": (
                                    severity_properties.get("description")
                                    if severity_properties
                                    else None
                                ),
                            }
                            break

                    comment_items = []
                    comment_ids_from_edges = comment_edges.get(
                        notification.get("externalId"), []
                    )
                    filtered_ntf_on_screen_comments = [
                        comment
                        for comment in ntf_on_screen_comments
                        if comment.get("externalId") in comment_ids_from_edges
                    ]
                    for comment in filtered_ntf_on_screen_comments:

                        comment_view_id = views["notification_comment"]
                        comment_properties = (
                            comment.get("properties", {})
                            .get(comment_view_id.space, {})
                            .get(
                                f"{comment_view_id.external_id}/{comment_view_id.version}",
                                {},
                            )
                        )

                        comment_items.append(
                            {
                                "externalId": comment.get("externalId"),
                                "space": comment.get("space"),
                                "comment": comment_properties.get("comment"),
                                "createdTime": (
                                    self.transform_date(comment.get("createdTime"))
                                    if comment.get("createdTime")
                                    else None
                                ),
                                "user": {
                                    "externalId": (
                                        comment_properties.get("user", {}).get(
                                            "externalId"
                                        )
                                        if comment_properties.get("user")
                                        else None
                                    ),
                                    "space": (
                                        comment_properties.get("user", {}).get("space")
                                        if comment_properties.get("user")
                                        else None
                                    ),
                                },
                            }
                        )

                    reporting_site = None
                    for site_item in ntf_on_screen_reporting_sites:
                        if site_item.get("externalId") == ntf_on_screen_properties.get(
                            "reportingSite", {}
                        ).get("externalId"):
                            reporting_site_view_id = views["reporting_site"]
                            reporting_site_properties = (
                                site_item.get("properties", {})
                                .get(reporting_site_view_id.space, {})
                                .get(
                                    f"{reporting_site_view_id.external_id}/{reporting_site_view_id.version}",
                                    {},
                                )
                            )
                            reporting_site = {
                                "externalId": site_item.get("externalId"),
                                "space": site_item.get("space"),
                                "name": reporting_site_properties.get("name"),
                                "description": (
                                    reporting_site_properties.get("description")
                                    if reporting_site_properties
                                    else None
                                ),
                            }
                            break

                    notification_obj = {
                        "externalId": notification.get("externalId"),
                        "space": notification.get("space"),
                        "text": ntf_on_screen_properties.get("text"),
                        "createdTime": (
                            self.transform_date(notification.get("createdTime"))
                            if notification.get("createdTime")
                            else None
                        ),
                        "createdAt": ntf_on_screen_properties.get("createdAt"),
                        "newUpdateTime": ntf_on_screen_properties.get("newUpdateTime"),
                        "template": {
                            "externalId": (
                                template.get("externalId") if template else None
                            ),
                            "space": template.get("space") if template else None,
                            "notificationType": {
                                "externalId": (
                                    notification_type.get("externalId")
                                    if notification_type
                                    else None
                                ),
                                "space": (
                                    notification_type.get("space")
                                    if notification_type
                                    else None
                                ),
                                "name": (
                                    notification_type.get("name")
                                    if notification_type
                                    else None
                                ),
                                "description": (
                                    notification_type.get("description")
                                    if notification_type
                                    else None
                                ),
                                "application": {
                                    "externalId": (
                                        application.get("externalId")
                                        if application
                                        else None
                                    ),
                                    "space": (
                                        application.get("space")
                                        if application
                                        else None
                                    ),
                                    "name": (
                                        application.get("name") if application else None
                                    ),
                                    "description": (
                                        application.get("description")
                                        if application
                                        else None
                                    ),
                                },
                            },
                        },
                        "severity": {
                            "externalId": (
                                severity.get("externalId") if severity else None
                            ),
                            "space": severity.get("space") if severity else None,
                            "name": severity.get("name") if severity else None,
                            "description": (
                                severity.get("description") if severity else None
                            ),
                        },
                        "comments": {"items": comment_items},
                    }

                    if reporting_site:
                        notification_obj["reportingSite"] = {
                            "externalId": reporting_site.get("externalId"),
                            "space": reporting_site.get("space"),
                            "name": reporting_site.get("name"),
                            "description": reporting_site.get("description"),
                        }

                    user_notifications.append(notification_obj)

                formatted_items.append(
                    {
                        "externalId": user.get("externalId"),
                        "space": user.get("space"),
                        "notificationsOnScreen": {"items": user_notifications},
                    }
                )

            return formatted_items

        while has_cursor:
            response_query = self._cognite_client.data_modeling.instances.query(query)

            formatted_result = format_result(response_query, views)

            if len(formatted_result):
                response.extend(formatted_result)

            if response_query:
                has_cursor = False
                cursor_keys = [
                    "users",
                    "notifications_on_screen_edges",
                    "notification_details",
                    "templates",
                    "notification_types",
                    "applications",
                    "severities",
                    "comments_edges",
                    "comments",
                    "reporting_sites",
                ]

                for key in cursor_keys:
                    if (
                        key in response_query.cursors
                        and len(response_query[key]) == fetch_limit
                    ):
                        query.cursors[key] = response_query.cursors[key]
                        has_cursor = True

        return response

    def get_notification_on_screen_by_id(self, external_id: str):

        _cache = get_cache_instance()

        cognite_views = _cache.get("cognite_views")
        notification_views = cognite_views[core.env.cognite.fdm_model_space]
        notification_on_screen_view_id = Utils.cognite.find_view_by_external_id(
            notification_views, core.env.cognite_entities.notification_on_screen
        ).as_id()
        template_view_id = Utils.cognite.find_view_by_external_id(
            notification_views, core.env.cognite_entities.notification_template
        ).as_id()
        notification_type_view_id = Utils.cognite.find_view_by_external_id(
            notification_views, core.env.cognite_entities.notification_type
        ).as_id()
        application_view_id = Utils.cognite.find_view_by_external_id(
            cognite_views[core.env.spaces.um_model_space],
            core.env.cognite_entities.application,
        ).as_id()
        notification_severity_view_id = Utils.cognite.find_view_by_external_id(
            notification_views, core.env.cognite_entities.notification_severity
        ).as_id()
        notification_comment_view_id = Utils.cognite.find_view_by_external_id(
            notification_views, core.env.cognite_entities.notification_comment
        ).as_id()
        notification_user_view_id = Utils.cognite.find_view_by_external_id(
            notification_views, core.env.cognite_entities.notification_user
        ).as_id()
        reporting_site_view_id = Utils.cognite.find_view_by_external_id(
            cognite_views[core.env.spaces.asset_hierarcy_model_space],
            core.env.cognite_entities.reporting_site,
        ).as_id()
        notification_event_view_id = Utils.cognite.find_view_by_external_id(
            notification_views, core.env.cognite_entities.notification_event
        ).as_id()
        notification_application_group_view_id = Utils.cognite.find_view_by_external_id(
            notification_views, core.env.cognite_entities.notification_application_group
        ).as_id()
        notification_user_role_site_view_id = Utils.cognite.find_view_by_external_id(
            notification_views, core.env.cognite_entities.notification_user_role_site
        ).as_id()
        role_view_id = Utils.cognite.find_view_by_external_id(
            cognite_views[core.env.spaces.um_model_space],
            core.env.cognite_entities.role,
        ).as_id()

        comments_cursor = None
        event_application_groups_edges_cursor = None
        event_application_groups_cursor = None
        comment_users_cursor = None
        templates_cursor = None
        event_roles_cursor = None
        event_users_cursor = None
        role_details_cursor = None
        notification_cursor = None
        event_roles_edges_cursor = None
        reporting_sites_cursor = None
        events_cursor = None
        applications_cursor = None
        severities_cursor = None

        has_cursor = True
        response_query = None
        response = []
        fetch_limit = 10_000

        views = {
            "notification_on_screen": notification_on_screen_view_id,
            "template": template_view_id,
            "notification_type": notification_type_view_id,
            "application": application_view_id,
            "notification_severity": notification_severity_view_id,
            "notification_comment": notification_comment_view_id,
            "notification_user": notification_user_view_id,
            "reporting_site": reporting_site_view_id,
            "notification_event": notification_event_view_id,
            "notification_application_group": notification_application_group_view_id,
            "notification_user_role_site": notification_user_role_site_view_id,
            "role": role_view_id,
        }

        notification_filter = And(
            Equals(["node", "externalId"], external_id),
            Equals(["node", "space"], core.env.spaces.ntf_prot_instance_space),
        )

        query = Query(
            with_={
                "notification": NodeResultSetExpression(
                    filter=notification_filter,
                    limit=fetch_limit,
                ),
                "templates": NodeResultSetExpression(
                    from_="notification",
                    limit=fetch_limit,
                    chain_to="destination",
                    direction="outwards",
                    through=PropertyId(notification_on_screen_view_id, "template"),
                ),
                "notification_types": NodeResultSetExpression(
                    from_="templates",
                    limit=fetch_limit,
                    chain_to="destination",
                    direction="outwards",
                    through=PropertyId(template_view_id, "notificationType"),
                ),
                "applications": NodeResultSetExpression(
                    from_="notification_types",
                    limit=fetch_limit,
                    chain_to="destination",
                    direction="outwards",
                    through=PropertyId(notification_type_view_id, "application"),
                ),
                "severities": NodeResultSetExpression(
                    from_="notification",
                    limit=fetch_limit,
                    chain_to="destination",
                    direction="outwards",
                    through=PropertyId(notification_on_screen_view_id, "severity"),
                ),
                "reporting_sites": NodeResultSetExpression(
                    from_="notification",
                    limit=fetch_limit,
                    chain_to="destination",
                    direction="outwards",
                    through=PropertyId(notification_on_screen_view_id, "reportingSite"),
                ),
                "comments_edges": EdgeResultSetExpression(
                    from_="notification",
                    limit=fetch_limit,
                    direction="outwards",
                    chain_to="destination",
                    filter=Equals(
                        ["edge", "type"],
                        {
                            "space": core.env.cognite.fdm_model_space,
                            "externalId": "NotificationOnScreen.comments",
                        },
                    ),
                    node_filter=HasData(views=[notification_comment_view_id]),
                ),
                "comments": NodeResultSetExpression(
                    from_="comments_edges", limit=fetch_limit
                ),
                "comment_users": NodeResultSetExpression(
                    from_="comments",
                    limit=fetch_limit,
                    chain_to="destination",
                    direction="outwards",
                    through=PropertyId(notification_comment_view_id, "user"),
                ),
                "events": NodeResultSetExpression(
                    from_="notification",
                    limit=fetch_limit,
                    chain_to="destination",
                    direction="outwards",
                    through=PropertyId(notification_on_screen_view_id, "event"),
                ),
                "event_users_edges": EdgeResultSetExpression(
                    from_="events",
                    limit=fetch_limit,
                    direction="outwards",
                    chain_to="destination",
                    filter=Equals(
                        ["edge", "type"],
                        {
                            "space": core.env.cognite.fdm_model_space,
                            "externalId": "NotificationEvent.users",
                        },
                    ),
                    node_filter=HasData(views=[notification_user_view_id]),
                ),
                "event_users": NodeResultSetExpression(
                    from_="event_users_edges", limit=fetch_limit
                ),
                "event_roles_edges": EdgeResultSetExpression(
                    from_="events",
                    limit=fetch_limit,
                    direction="outwards",
                    chain_to="destination",
                    filter=Equals(
                        ["edge", "type"],
                        {
                            "space": core.env.cognite.fdm_model_space,
                            "externalId": "NotificationEvent.roles",
                        },
                    ),
                    node_filter=HasData(views=[notification_user_role_site_view_id]),
                ),
                "event_roles": NodeResultSetExpression(
                    from_="event_roles_edges", limit=fetch_limit
                ),
                "role_details": NodeResultSetExpression(
                    from_="event_roles",
                    limit=fetch_limit,
                    chain_to="destination",
                    direction="outwards",
                    through=PropertyId(notification_user_role_site_view_id, "role"),
                ),
                "event_application_groups_edges": EdgeResultSetExpression(
                    from_="events",
                    limit=fetch_limit,
                    direction="outwards",
                    chain_to="destination",
                    filter=Equals(
                        ["edge", "type"],
                        {
                            "space": core.env.cognite.fdm_model_space,
                            "externalId": "NotificationEvent.applicationGroups",
                        },
                    ),
                    node_filter=HasData(views=[notification_application_group_view_id]),
                ),
                "event_application_groups": NodeResultSetExpression(
                    from_="event_application_groups_edges", limit=fetch_limit
                ),
            },
            select={
                "notification": Select(
                    [SourceSelector(notification_on_screen_view_id, ["*"])],
                    limit=fetch_limit,
                ),
                "templates": Select(
                    [SourceSelector(template_view_id, ["*"])], limit=fetch_limit
                ),
                "notification_types": Select(
                    [SourceSelector(notification_type_view_id, ["*"])],
                    limit=fetch_limit,
                ),
                "applications": Select(
                    [SourceSelector(application_view_id, ["*"])], limit=fetch_limit
                ),
                "severities": Select(
                    [SourceSelector(notification_severity_view_id, ["*"])],
                    limit=fetch_limit,
                ),
                "reporting_sites": Select(
                    [SourceSelector(reporting_site_view_id, ["*"])], limit=fetch_limit
                ),
                "comments_edges": Select(limit=fetch_limit),
                "comments": Select(
                    [SourceSelector(notification_comment_view_id, ["*"])],
                    limit=fetch_limit,
                ),
                "comment_users": Select(
                    [SourceSelector(notification_user_view_id, ["*"])],
                    limit=fetch_limit,
                ),
                "events": Select(
                    [SourceSelector(notification_event_view_id, ["*"])],
                    limit=fetch_limit,
                ),
                "event_users_edges": Select(limit=fetch_limit),
                "event_users": Select(
                    [SourceSelector(notification_user_view_id, ["*"])],
                    limit=fetch_limit,
                ),
                "event_roles_edges": Select(limit=fetch_limit),
                "event_roles": Select(
                    [SourceSelector(notification_user_role_site_view_id, ["*"])],
                    limit=fetch_limit,
                ),
                "role_details": Select(
                    [SourceSelector(role_view_id, ["*"])], limit=fetch_limit
                ),
                "event_application_groups_edges": Select(limit=fetch_limit),
                "event_application_groups": Select(
                    [SourceSelector(notification_application_group_view_id, ["*"])],
                    limit=fetch_limit,
                ),
            },
            cursors={
                "comments": comments_cursor,
                "event_application_groups_edges": event_application_groups_edges_cursor,
                "event_application_groups": event_application_groups_cursor,
                "comment_users": comment_users_cursor,
                "templates": templates_cursor,
                "event_roles": event_roles_cursor,
                "event_users": event_users_cursor,
                "role_details": role_details_cursor,
                "notification": notification_cursor,
                "event_roles_edges": event_roles_edges_cursor,
                "reporting_sites": reporting_sites_cursor,
                "events": events_cursor,
                "applications": applications_cursor,
                "severities": severities_cursor,
            },
        )

        def format_notification_by_id_results(
            query_result: QueryResult, views: dict[str, ViewId]
        ):
            notifications = query_result.get("notification", []).dump()
            templates = query_result.get("templates", []).dump()
            notification_types = query_result.get("notification_types", []).dump()
            applications = query_result.get("applications", []).dump()
            severities = query_result.get("severities", []).dump()
            reporting_sites = query_result.get("reporting_sites", []).dump()
            comments = query_result.get("comments", []).dump()
            comment_users = query_result.get("comment_users", []).dump()
            events = query_result.get("events", []).dump()
            event_users = query_result.get("event_users", []).dump()
            event_roles = query_result.get("event_roles", []).dump()
            role_details = query_result.get("role_details", []).dump()
            event_application_groups = query_result.get(
                "event_application_groups", []
            ).dump()

            def group_edges_by_start_node(result_edge):
                grouped_edges = {}
                for edge in result_edge:
                    start_node = edge["startNode"]["externalId"]
                    end_node = edge["endNode"]["externalId"]

                    if start_node not in grouped_edges:
                        grouped_edges[start_node] = []
                    grouped_edges[start_node].append(end_node)
                return grouped_edges

            comment_edges = group_edges_by_start_node(
                query_result.get("comments_edges", []).dump()
            )
            event_users_edges = group_edges_by_start_node(
                query_result.get("event_users_edges", []).dump()
            )
            event_roles_edges = group_edges_by_start_node(
                query_result.get("event_roles_edges", []).dump()
            )
            event_application_groups_edges = group_edges_by_start_node(
                query_result.get("event_application_groups_edges", []).dump()
            )

            formatted_items = []

            for notification in notifications:
                notification_view_id = views["notification_on_screen"]
                notification_properties = (
                    notification.get("properties", {})
                    .get(notification_view_id.space, {})
                    .get(
                        f"{notification_view_id.external_id}/{notification_view_id.version}",
                        {},
                    )
                )

                template = None
                template_properties = None
                for temp_item in templates:
                    if temp_item.get("externalId") == notification_properties.get(
                        "template", {}
                    ).get("externalId"):
                        template = temp_item
                        template_view_id = views["template"]
                        template_properties = (
                            template.get("properties", {})
                            .get(template_view_id.space, {})
                            .get(
                                f"{template_view_id.external_id}/{template_view_id.version}",
                                {},
                            )
                        )
                        break

                notification_type = None
                notification_type_properties = None
                if template_properties:
                    for type_item in notification_types:
                        if type_item.get("externalId") == template_properties.get(
                            "notificationType", {}
                        ).get("externalId"):
                            notification_type = type_item
                            type_view_id = views["notification_type"]
                            notification_type_properties = (
                                type_item.get("properties", {})
                                .get(type_view_id.space, {})
                                .get(
                                    f"{type_view_id.external_id}/{type_view_id.version}",
                                    {},
                                )
                            )
                            break

                application = None
                application_properties = None
                if notification_type_properties:
                    for app_item in applications:
                        if app_item.get(
                            "externalId"
                        ) == notification_type_properties.get("application", {}).get(
                            "externalId"
                        ):
                            application = app_item
                            app_view_id = views["application"]
                            application_properties = (
                                app_item.get("properties", {})
                                .get(app_view_id.space, {})
                                .get(
                                    f"{app_view_id.external_id}/{app_view_id.version}",
                                    {},
                                )
                            )
                            break

                severity = None
                severity_properties = None
                for sev_item in severities:
                    if sev_item.get("externalId") == notification_properties.get(
                        "severity", {}
                    ).get("externalId"):
                        severity = sev_item
                        sev_view_id = views["notification_severity"]
                        severity_properties = (
                            sev_item.get("properties", {})
                            .get(sev_view_id.space, {})
                            .get(f"{sev_view_id.external_id}/{sev_view_id.version}", {})
                        )
                        break

                reporting_site = None
                reporting_site_properties = None
                for site_item in reporting_sites:
                    if site_item.get("externalId") == notification_properties.get(
                        "reportingSite", {}
                    ).get("externalId"):
                        reporting_site = site_item
                        site_view_id = views["reporting_site"]
                        reporting_site_properties = (
                            site_item.get("properties", {})
                            .get(site_view_id.space, {})
                            .get(
                                f"{site_view_id.external_id}/{site_view_id.version}", {}
                            )
                        )
                        break

                comment_items = []
                comment_ids_from_edges = comment_edges.get(
                    notification.get("externalId"), []
                )

                for comment in comments:
                    if comment.get("externalId") in comment_ids_from_edges:
                        comment_view_id = views["notification_comment"]
                        comment_properties = (
                            comment.get("properties", {})
                            .get(comment_view_id.space, {})
                            .get(
                                f"{comment_view_id.external_id}/{comment_view_id.version}",
                                {},
                            )
                        )

                        comment_user = None
                        comment_user_properties = None
                        for user_item in comment_users:
                            if user_item.get("externalId") == comment_properties.get(
                                "user", {}
                            ).get("externalId"):
                                comment_user = user_item
                                user_view_id = views["notification_user"]
                                comment_user_properties = (
                                    user_item.get("properties", {})
                                    .get(user_view_id.space, {})
                                    .get(
                                        f"{user_view_id.external_id}/{user_view_id.version}",
                                        {},
                                    )
                                )
                                break

                        comment_items.append(
                            {
                                "comment": comment_properties.get("comment"),
                                "externalId": comment.get("externalId"),
                                "space": comment.get("space"),
                                "createdTime": (
                                    self.transform_date(comment.get("createdTime"))
                                    if comment.get("createdTime")
                                    else None
                                ),
                                "user": (
                                    {
                                        "email": (
                                            comment_user_properties.get("email")
                                            if comment_user_properties
                                            else None
                                        ),
                                        "externalId": (
                                            comment_user.get("externalId")
                                            if comment_user
                                            else None
                                        ),
                                        "firstName": (
                                            comment_user_properties.get("firstName")
                                            if comment_user_properties
                                            else None
                                        ),
                                        "lastName": (
                                            comment_user_properties.get("lastName")
                                            if comment_user_properties
                                            else None
                                        ),
                                        "displayName": (
                                            comment_user_properties.get("displayName")
                                            if comment_user_properties
                                            else None
                                        ),
                                        "space": (
                                            comment_user.get("space")
                                            if comment_user
                                            else None
                                        ),
                                    }
                                    if comment_user
                                    else None
                                ),
                            }
                        )

                event = None
                event_properties = None
                if events:
                    event = events[0]
                    event_view_id = views["notification_event"]
                    event_properties = (
                        event.get("properties", {})
                        .get(event_view_id.space, {})
                        .get(f"{event_view_id.external_id}/{event_view_id.version}", {})
                    )

                event_user_items = []
                if event:
                    event_user_ids = event_users_edges.get(event.get("externalId"), [])
                    for user_item in event_users:
                        if user_item.get("externalId") in event_user_ids:
                            user_view_id = views["notification_user"]
                            user_properties = (
                                user_item.get("properties", {})
                                .get(user_view_id.space, {})
                                .get(
                                    f"{user_view_id.external_id}/{user_view_id.version}",
                                    {},
                                )
                            )
                            event_user_items.append(
                                {
                                    "externalId": user_item.get("externalId"),
                                    "space": user_item.get("space"),
                                    "email": (
                                        user_properties.get("email")
                                        if user_properties
                                        else None
                                    ),
                                }
                            )

                event_role_items = []
                if event:
                    event_role_ids = event_roles_edges.get(event.get("externalId"), [])
                    for role_item in event_roles:
                        if role_item.get("externalId") in event_role_ids:
                            role_detail = None
                            role_detail_properties = None
                            role_view_id = views["notification_user_role_site"]
                            role_properties = (
                                role_item.get("properties", {})
                                .get(role_view_id.space, {})
                                .get(
                                    f"{role_view_id.external_id}/{role_view_id.version}",
                                    {},
                                )
                            )

                            for detail_item in role_details:
                                if detail_item.get("externalId") == role_properties.get(
                                    "role", {}
                                ).get("externalId"):
                                    role_detail = detail_item
                                    detail_view_id = views["role"]
                                    role_detail_properties = (
                                        detail_item.get("properties", {})
                                        .get(detail_view_id.space, {})
                                        .get(
                                            f"{detail_view_id.external_id}/{detail_view_id.version}",
                                            {},
                                        )
                                    )
                                    break

                            event_role_items.append(
                                {
                                    "externalId": role_item.get("externalId"),
                                    "role": (
                                        {
                                            "externalId": (
                                                role_detail.get("externalId")
                                                if role_detail
                                                else None
                                            ),
                                            "name": (
                                                role_detail_properties.get("name")
                                                if role_detail_properties
                                                else None
                                            ),
                                        }
                                        if role_detail
                                        else None
                                    ),
                                }
                            )

                event_app_group_items = []
                if event:
                    event_app_group_ids = event_application_groups_edges.get(
                        event.get("externalId"), []
                    )
                    for group_item in event_application_groups:
                        if group_item.get("externalId") in event_app_group_ids:
                            group_view_id = views["notification_application_group"]
                            group_properties = (
                                group_item.get("properties", {})
                                .get(group_view_id.space, {})
                                .get(
                                    f"{group_view_id.external_id}/{group_view_id.version}",
                                    {},
                                )
                            )
                            event_app_group_items.append(
                                {
                                    "externalId": group_item.get("externalId"),
                                    "name": (
                                        group_properties.get("name")
                                        if group_properties
                                        else None
                                    ),
                                    "description": (
                                        group_properties.get("description")
                                        if group_properties
                                        else None
                                    ),
                                }
                            )

                notification_obj = {
                    "createdTime": (
                        self.transform_date(notification.get("createdTime"))
                        if notification.get("createdTime")
                        else None
                    ),
                    "createdAt": notification_properties.get("createdAt"),
                    "externalId": notification.get("externalId"),
                    "space": notification.get("space"),
                    "text": notification_properties.get("text"),
                    "reportingSite": (
                        {
                            "name": (
                                reporting_site_properties.get("name")
                                if reporting_site_properties
                                else None
                            ),
                        }
                        if reporting_site
                        else None
                    ),
                    "template": (
                        {
                            "notificationType": (
                                {
                                    "name": (
                                        notification_type_properties.get("name")
                                        if notification_type_properties
                                        else None
                                    ),
                                    "description": (
                                        notification_type_properties.get("description")
                                        if notification_type_properties
                                        else None
                                    ),
                                    "application": (
                                        {
                                            "name": (
                                                application_properties.get("name")
                                                if application_properties
                                                else None
                                            ),
                                            "azureAppId": (
                                                application_properties.get("azureAppId")
                                                if application_properties
                                                else None
                                            ),
                                        }
                                        if application
                                        else None
                                    ),
                                }
                                if notification_type
                                else None
                            ),
                        }
                        if template
                        else None
                    ),
                    "comments": {"items": comment_items},
                    "severity": (
                        {
                            "externalId": (
                                severity.get("externalId") if severity else None
                            ),
                            "description": (
                                severity_properties.get("description")
                                if severity_properties
                                else None
                            ),
                        }
                        if severity
                        else None
                    ),
                    "event": (
                        {
                            "properties": (
                                event_properties.get("properties")
                                if event_properties
                                else None
                            ),
                            "users": {"items": event_user_items},
                            "roles": {"items": event_role_items},
                            "applicationGroups": {"items": event_app_group_items},
                            "externalUsers": (
                                event_properties.get("externalUsers")
                                if event_properties
                                else None
                            ),
                        }
                        if event
                        else None
                    ),
                }

                formatted_items.append(notification_obj)

            return formatted_items

        while has_cursor:
            response_query = self._cognite_client.data_modeling.instances.query(query)

            formatted_result = format_notification_by_id_results(response_query, views)

            if len(formatted_result):
                response.extend(formatted_result)

            if response_query:
                has_cursor = False

                cursor_keys = [
                    "comments",
                    "event_application_groups_edges",
                    "event_application_groups",
                    "comment_users",
                    "templates",
                    "event_roles",
                    "event_users",
                    "role_details",
                    "notification",
                    "event_roles_edges",
                    "reporting_sites",
                    "events",
                    "applications",
                    "severities",
                ]

                for key in cursor_keys:
                    if (
                        key in response_query.cursors
                        and len(response_query[key]) == fetch_limit
                    ):
                        query.cursors[key] = response_query.cursors[key]
                        has_cursor = True

        return response
