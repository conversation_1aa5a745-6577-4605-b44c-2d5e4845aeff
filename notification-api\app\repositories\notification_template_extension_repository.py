from typing import List, Any, Optional, TypeVar
from cognite.client import CogniteClient
from cognite.client.data_classes.data_modeling import (
    NodeApply,
    NodeOrEdgeData,
    ViewId,
    NodeId,
)
from app.core.env_variables import EnvVariables
from app.core.graphql_client import GraphQLClient
import app.core as core
import app.models as models
import app.queries as queries
import math as math
import app.utils as Utils
from app.core.cache_global import get_cache_instance

ENTITY = "NotificationTemplateExtension"
T = TypeVar("T")


class NotificationTemplateExtensionRepository:
    def __init__(
        self,
        cognite_client: CogniteClient,
        graphql_client: GraphQLClient,
        env_variables: EnvVariables,
    ):
        self._cognite_client = cognite_client
        self._graphql_client = graphql_client
        self._fdm_model_space = env_variables.cognite.fdm_model_space
        self._fdm_instances_space = env_variables.spaces.ntf_instance_space

    def find_by_filter(self, filter: Any):
        items = self._graphql_client.query(
            queries.notification_template_extensions.list_extensions,
            "listNotificationTemplateExtension",
            filter,
        )
        return items

    def save(self, requests: models.NotificationTemplateExtensionModel):
        # GET ENTITY VIEW
        _cache = get_cache_instance()
        cognite_views = _cache.get("cognite_views")[self._fdm_model_space]
        
        entity_view = Utils.cognite.find_view_by_external_id(
            cognite_views, ENTITY
        )
        entity_versions = entity_view.version  # GET ENTITY VERSION

        # PREPARE RELASHIONSHIP
        channels = requests.channels
        del requests.channels

        # CREATE TEMPLATE EXTENSION
        templateExtensionExternalId = requests.externalId
        del requests.externalId
        del requests.space
        # del requests.template

        templateExtensionNodes = NodeApply(
            self._fdm_instances_space,
            templateExtensionExternalId,
            sources=[
                NodeOrEdgeData(
                    ViewId(self._fdm_model_space, ENTITY, entity_versions),
                    requests.dict(),
                )
            ],
        )
        self._cognite_client.data_modeling.instances.apply(
            nodes=templateExtensionNodes,
            replace=True,
        )

        # CREATE TEMPLATERELATION SHIP WITH CHANNELS
        Utils.cognite.createRelationship(
            channels,
            templateExtensionNodes,
            entity_versions,
            self._graphql_client,
            self._cognite_client,
            self._fdm_model_space,
            self._fdm_instances_space,
            "channels",
            True,
        )

        return requests

    def findFirstTemplateExtension(
        self,
        ownerUser: str,
        template: Optional[models.NotificationTemplateModel] = None,
    ):
        if template is None:
            return None
        else:
            filter = {
                "filter": {
                    "and": [
                        {"owner": {"externalId": {"eq": ownerUser}}},
                        {
                            "template": {
                                "and": [
                                    {"externalId": {"eq": template.externalId}},
                                    {
                                        "space": {
                                            "eq": core.env.spaces.ntf_instance_space
                                        }
                                    },
                                ]
                            }
                        },
                    ]
                }
            }

        result = Utils.cognite.getDefaultList(
            self._graphql_client,
            "NotificationTemplateExtension",
            "externalId",
            filter,
        )
        if len(result) == 0:
            return None

        return result[0]

    def find_relation_model_by_filter(self, filter: Any = None) -> List[Any]:
        items = self._graphql_client.query(
            queries.notification_template_extensions.list_relation_model,
            "listNotificationTemplateExtension",
            filter,
        )
        return items

    def delete(self, external_id: str, space: str):
        templateNode = NodeId(space=space, external_id=external_id)
        self._cognite_client.data_modeling.instances.delete(nodes=templateNode)
