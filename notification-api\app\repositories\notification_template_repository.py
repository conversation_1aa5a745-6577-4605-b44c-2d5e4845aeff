from datetime import datetime, timezone
import json
from typing import List, Any, Optional, TypeVar
from cognite.client import CogniteClient
from cognite.client.data_classes.data_modeling import (
    NodeApply,
    NodeOrEdgeData,
    ViewId,
    NodeId,
    PropertyId,
)
from cognite.client.data_classes.data_modeling.query import (
    Query,
    Select,
    NodeResultSetExpression,
    EdgeResultSetExpression,
    SourceSelector,
)
from cognite.client.data_classes.filters import Equals, HasData, And
from app.core.env_variables import EnvVariables
from app.core.graphql_client import GraphQLClient
import app.core as core
import app.models as models
import app.queries as queries
import math as math
import app.utils as Utils
from app.core.cache_global import get_cache_instance
from app.core.settings import Settings

T = TypeVar("T")


class NotificationTemplateRepository:
    def __init__(
        self,
        cognite_client: CogniteClient,
        graphql_client: GraphQLClient,
        env_variables: EnvVariables,
    ):
        self._cognite_client = cognite_client
        self._graphql_client = graphql_client
        self._fdm_model_space = env_variables.cognite.fdm_model_space
        self._fdm_instances_space = env_variables.spaces.ntf_instance_space

        settings = Settings()
        self._data_model_id = settings.data_model_id

    def get_by_filter_paginated(self, filter: Any = None) -> List[Any]:
        query_response = self._cognite_client.data_modeling.graphql.query(
            self._data_model_id,
            query=queries.notification_templates.notification_template_list,
            variables=filter,
        )
    
        return query_response.get("listNotificationTemplate", {}).get("items", [])


    def find_notification_basic_info(self, filter: Any = None) -> List[Any]:
        items = self._cognite_client.data_modeling.graphql.query(
            self._data_model_id,
            query=queries.notification_templates.notification_template_basic_info,
            variables=filter,
        )

        return items.get("listNotificationTemplate", {}).get("items", [])

    def save(self, requests: models.NotificationTemplateCreateModel):
        # GET ENTITY VIEW
        _cache = get_cache_instance()
        cognite_views = _cache.get("cognite_views")[self._fdm_model_space]

        view = Utils.cognite.find_view_by_external_id(
            cognite_views, core.env.cognite_entities.notification_template
        )

        # PREPARE RELASHIONSHIP
        channels = requests.channels
        subscribedUsers = [
            models.RelationModel(
                externalId=user, space=core.env.spaces.um_instance_space
            )
            for user in requests.subscribedUsers
        ]
        subscribedUserRoles = [
            models.RelationModel(
                externalId=user, space=core.env.spaces.um_instance_space
            )
            for user in requests.subscribedUserRoles
        ]
        subscribedApplicationGroups = [
            models.RelationModel(
                externalId=group, space=core.env.spaces.ntf_instance_space
            )
            for group in requests.subscribedApplicationGroups
        ]
        blocklist = [
            models.RelationModel(
                externalId=user, space=core.env.spaces.um_instance_space
            )
            for user in requests.blocklist or []
        ]
        blocklist_roles = [
            models.RelationModel(
                externalId=role, space=core.env.spaces.um_instance_space
            )
            for role in requests.blocklistRoles or []
        ]

        requests.creator.space = core.env.spaces.um_instance_space
        requests.notificationType.space = core.env.spaces.ntf_instance_space
        requests.severity.space = core.env.spaces.ntf_instance_space

        notificationTemplateExtensions = requests.notificationTemplateExtensions

        del requests.channels
        del requests.subscribedUsers
        del requests.subscribedUserRoles
        del requests.subscribedApplicationGroups
        del requests.notificationTemplateExtensions
        del requests.emailRequester
        del requests.blocklist
        del requests.blocklistRoles

        entity_versions = view.version  # GET ENTITY VERSION
        # CREATE TEMPLATE
        isUpdate = True if requests.externalId is not None else False
        templateExternalId = (
            Utils.generateExternalId("NTFTMP")
            if requests.externalId is None
            else requests.externalId
        )

        del requests.externalId
        del requests.code

        # Convert requests to a dictionary and add the subject
        request_dict = requests.dict()
        request_dict["subject"] = requests.subject

        templateNodes = NodeApply(
            self._fdm_instances_space,
            templateExternalId,
            sources=[
                NodeOrEdgeData(
                    ViewId(
                        self._fdm_model_space,
                        core.env.cognite_entities.notification_template,
                        entity_versions,
                    ),
                    requests.dict(),
                )
            ],
        )
        self._cognite_client.data_modeling.instances.apply(
            nodes=templateNodes,
            replace=isUpdate,
        )

        # CREATE TEMPLATERELATION SHIP WITH CHANNELS
        Utils.cognite.createRelationship(
            channels,
            templateNodes,
            entity_versions,
            self._graphql_client,
            self._cognite_client,
            self._fdm_model_space,
            self._fdm_instances_space,
            "channels",
            isUpdate,
        )

        # CREATE TEMPLATERELATION SHIP WITH SUBSCRIBE USERS
        Utils.cognite.createRelationship(
            subscribedUsers,
            templateNodes,
            entity_versions,
            self._graphql_client,
            self._cognite_client,
            self._fdm_model_space,
            self._fdm_instances_space,
            "subscribedUsers",
            isUpdate,
        )

        # CREATE TEMPLATERELATION SHIP WITH SUBSCRIBE ROLES
        Utils.cognite.createRelationship(
            subscribedUserRoles,
            templateNodes,
            entity_versions,
            self._graphql_client,
            self._cognite_client,
            self._fdm_model_space,
            self._fdm_instances_space,
            "subscribedRoles",
            isUpdate,
        )

        # CREATE TEMPLATERELATION SHIP WITH SUBSCRIBE APPLICATION GROUPS
        Utils.cognite.createRelationship(
            subscribedApplicationGroups,
            templateNodes,
            entity_versions,
            self._graphql_client,
            self._cognite_client,
            self._fdm_model_space,
            self._fdm_instances_space,
            "subscribedApplicationGroups",
            isUpdate,
        )

        # CREATE TEMPLATERELATION SHIP WITH BLOCKLIST
        Utils.cognite.createRelationship(
            blocklist,
            templateNodes,
            entity_versions,
            self._graphql_client,
            self._cognite_client,
            self._fdm_model_space,
            self._fdm_instances_space,
            "blocklist",
            isUpdate,
        )

        # CREATE TEMPLATERELATION SHIP WITH BLOCKLIST ROLES
        Utils.cognite.createRelationship(
            blocklist_roles,
            templateNodes,
            entity_versions,
            self._graphql_client,
            self._cognite_client,
            self._fdm_model_space,
            self._fdm_instances_space,
            "blocklistRoles",
            isUpdate,
        )

        return requests

    def find_by_application_groups(self, filter: Any = None) -> List[Any]:
        return self._graphql_client.query(
            queries.notification_templates.notification_template_list_by_application_groups,
            "listNotificationApplicationGroup",
            filter,
        )

    def delete(self, external_id: str, space: str):
        templateNode = NodeId(space=space, external_id=external_id)
        self._cognite_client.data_modeling.instances.delete(nodes=templateNode)

    def find_by_id_with_role_info(self, external_id: str):
        return self.find_template_info(external_id)

    def find_template_info(self, external_id: str):

        _cache = get_cache_instance()

        cognite_views = _cache.get("cognite_views")

        notification_views = cognite_views[core.env.cognite.fdm_model_space]

        user_management_views = cognite_views[core.env.spaces.um_model_space]

        templates_view = Utils.cognite.find_view_by_external_id(
            notification_views,
            core.env.cognite_entities.notification_template,
        ).version

        notification_user_view = Utils.cognite.find_view_by_external_id(
            notification_views,
            core.env.cognite_entities.notification_user,
        )

        notification_channel_view = Utils.cognite.find_view_by_external_id(
            notification_views,
            core.env.cognite_entities.notification_channel,
        ).version

        notification_application_group_view = Utils.cognite.find_view_by_external_id(
            notification_views,
            core.env.cognite_entities.notification_application_group,
        ).version

        user_view = Utils.cognite.find_view_by_external_id(
            user_management_views,
            core.env.cognite_entities.user,
        )

        notification_user_role_site_view = Utils.cognite.find_view_by_external_id(
            notification_views,
            core.env.cognite_entities.notification_user_role_site,
        ).version

        user_complement_view = Utils.cognite.find_view_by_external_id(
            user_management_views,
            core.env.cognite_entities.user_complement,
        ).version

        user_azure_attribute_view = Utils.cognite.find_view_by_external_id(
            user_management_views,
            core.env.cognite_entities.user_azure_attribute,
        ).version

        notification_severity_view = Utils.cognite.find_view_by_external_id(
            notification_views,
            core.env.cognite_entities.notification_severity,
        ).version

        notification_type_view = Utils.cognite.find_view_by_external_id(
            notification_views,
            core.env.cognite_entities.notification_type,
        ).version

        application_view = Utils.cognite.find_view_by_external_id(
            user_management_views,
            core.env.cognite_entities.application,
        ).version

        reporting_site_view = Utils.cognite.find_view_by_external_id(
            cognite_views[core.env.spaces.asset_hierarcy_model_space],
            core.env.cognite_entities.reporting_site,
        ).version

        role_view = Utils.cognite.find_view_by_external_id(
            user_management_views,
            core.env.cognite_entities.role,
        ).version

        views = {
            "template": templates_view,
            "notification_user": notification_user_view.version,
            "notification_channel": notification_channel_view,
            "notification_application_group": notification_application_group_view,
            "user": user_view.version,
            "notification_user_role_site": notification_user_role_site_view,
            "user_complement": user_complement_view,
            "user_azure_attribute": user_azure_attribute_view,
            "notification_severity": notification_severity_view,
            "notification_type": notification_type_view,
            "application": application_view,
            "reporting_site": reporting_site_view,
            "role": role_view,
        }

        users_application_groups_in_template_cursor = None
        users_in_application_groups_cursor = None
        users_in_subscribed_users_template_cursor = None
        subscribed_user_in_template_cursor = None
        users_in_blocklist_template_cursor = None
        blocklist_in_template_cursor = None
        blocklist_application_groups_in_template_cursor = None
        users_in_blocklist_application_groups_cursor = None
        template_cursor = None
        user_complement_in_subscribed_roles_in_template_cursor = None
        user_complement_in_blocklist_roles_in_template_cursor = None
        has_cursor = True
        response_query = None

        query = Query(
            with_={
                # Retrieving the template
                "template": NodeResultSetExpression(
                    filter=Equals(["node", "externalId"], external_id),
                    limit=1,
                    chain_to="destination",
                    direction="outwards",
                ),
                # getting the user info of creator
                "creator": NodeResultSetExpression(
                    from_="template",
                    limit=1,
                    chain_to="destination",
                    direction="outwards",
                    through=PropertyId(
                        ViewId(
                            core.env.cognite.fdm_model_space,
                            core.env.cognite_entities.notification_template,
                            templates_view,
                        ),
                        "creator",
                    ),
                ),
                # getting the severity info
                "severity": NodeResultSetExpression(
                    from_="template",
                    limit=1,
                    chain_to="destination",
                    direction="outwards",
                    through=PropertyId(
                        ViewId(
                            core.env.cognite.fdm_model_space,
                            core.env.cognite_entities.notification_template,
                            templates_view,
                        ),
                        "severity",
                    ),
                ),
                # getting the notification type info
                "notification_type": NodeResultSetExpression(
                    from_="template",
                    limit=1,
                    chain_to="destination",
                    direction="outwards",
                    through=PropertyId(
                        ViewId(
                            core.env.cognite.fdm_model_space,
                            core.env.cognite_entities.notification_template,
                            templates_view,
                        ),
                        "notificationType",
                    ),
                ),
                # getting the application info
                "application": NodeResultSetExpression(
                    from_="notification_type",
                    limit=1,
                    chain_to="destination",
                    direction="outwards",
                    through=PropertyId(
                        ViewId(
                            core.env.cognite.fdm_model_space,
                            core.env.cognite_entities.notification_type,
                            notification_type_view,
                        ),
                        "application",
                    ),
                ),
                # getting the edge of subscribed user
                "subscribed_users_in_template": EdgeResultSetExpression(
                    from_="template",
                    limit=10000,
                    direction="outwards",
                    chain_to="destination",
                    filter=Equals(
                        ["edge", "type"],
                        {
                            "space": core.env.cognite.fdm_model_space,
                            "externalId": "NotificationTemplate.subscribedUsers",
                        },
                    ),
                    node_filter=HasData(
                        views=[
                            ViewId(
                                core.env.cognite.fdm_model_space,
                                core.env.cognite_entities.notification_user,
                                notification_user_view.version,
                            )
                        ]
                    ),
                ),
                # getting the users in the subscribed user
                "users_in_subscribed_users_template": NodeResultSetExpression(
                    from_="subscribed_users_in_template",
                    filter=Equals(
                        notification_user_view.as_property_ref("deleted"), False
                    ),
                    limit=10000,
                ),
                # getting the edge of blocklist
                "blocklist_in_template": EdgeResultSetExpression(
                    from_="template",
                    limit=10000,
                    direction="outwards",
                    chain_to="destination",
                    filter=Equals(
                        ["edge", "type"],
                        {
                            "space": core.env.cognite.fdm_model_space,
                            "externalId": "NotificationTemplate.blocklist",
                        },
                    ),
                    node_filter=HasData(
                        views=[
                            ViewId(
                                core.env.cognite.fdm_model_space,
                                core.env.cognite_entities.notification_user,
                                notification_user_view.version,
                            )
                        ]
                    ),
                ),
                # getting the users in the blocklist
                "users_in_blocklist_template": NodeResultSetExpression(
                    from_="blocklist_in_template",
                    filter=Equals(
                        notification_user_view.as_property_ref("deleted"), False
                    ),
                    limit=10000,
                ),
                # getting the edge of the channels
                "channels_in_template": EdgeResultSetExpression(
                    from_="template",
                    limit=3,
                    direction="outwards",
                    chain_to="destination",
                    filter=Equals(
                        ["edge", "type"],
                        {
                            "space": core.env.cognite.fdm_model_space,
                            "externalId": "NotificationTemplate.channels",
                        },
                    ),
                    node_filter=HasData(
                        views=[
                            ViewId(
                                core.env.cognite.fdm_model_space,
                                core.env.cognite_entities.notification_channel,
                                notification_channel_view,
                            )
                        ]
                    ),
                ),
                # getting the edge of subscribed channels in the template
                "subscribed_channels_in_template": NodeResultSetExpression(
                    from_="channels_in_template", limit=3
                ),
                # getting the application groups
                "application_groups_in_template": EdgeResultSetExpression(
                    from_="template",
                    limit=10000,
                    direction="outwards",
                    chain_to="destination",
                    filter=Equals(
                        ["edge", "type"],
                        {
                            "space": core.env.cognite.fdm_model_space,
                            "externalId": "NotificationTemplate.subscribedApplicationGroups",
                        },
                    ),
                    node_filter=HasData(
                        views=[
                            ViewId(
                                core.env.cognite.fdm_model_space,
                                core.env.cognite_entities.notification_application_group,
                                notification_application_group_view,
                            )
                        ]
                    ),
                ),
                # getting the users in the application groups
                "groups_in_application_groups_in_template": NodeResultSetExpression(
                    from_="application_groups_in_template", limit=10000
                ),
                # getting the users in the application groups
                "users_application_groups_in_template": EdgeResultSetExpression(
                    from_="application_groups_in_template",
                    limit=10000,
                    direction="outwards",
                    chain_to="destination",
                    filter=Equals(
                        ["edge", "type"],
                        {
                            "space": core.env.cognite.fdm_model_space,
                            "externalId": "NotificationApplicationGroup.users",
                        },
                    ),
                    node_filter=HasData(
                        views=[
                            ViewId(
                                core.env.cognite.fdm_model_space,
                                core.env.cognite_entities.notification_user,
                                notification_user_view.version,
                            )
                        ]
                    ),
                ),
                # getting the users in the application groups
                "users_in_application_groups": NodeResultSetExpression(
                    from_="users_application_groups_in_template",
                    filter=Equals(user_view.as_property_ref("deleted"), False),
                    limit=10000,
                ),
                # getting the blocklist in the application groups
                "blocklist_application_groups_in_template": EdgeResultSetExpression(
                    from_="application_groups_in_template",
                    limit=10000,
                    direction="outwards",
                    chain_to="destination",
                    filter=Equals(
                        ["edge", "type"],
                        {
                            "space": core.env.cognite.fdm_model_space,
                            "externalId": "NotificationApplicationGroup.blocklist",
                        },
                    ),
                    node_filter=HasData(
                        views=[
                            ViewId(
                                core.env.cognite.fdm_model_space,
                                core.env.cognite_entities.notification_user,
                                notification_user_view.version,
                            )
                        ]
                    ),
                ),
                # getting the roles users in the application groups
                "user_roles_application_groups_in_template": EdgeResultSetExpression(
                    from_="application_groups_in_template",
                    limit=10000,
                    direction="outwards",
                    chain_to="destination",
                    filter=Equals(
                        ["edge", "type"],
                        {
                            "space": core.env.cognite.fdm_model_space,
                            "externalId": "NotificationApplicationGroup.usersRoles",
                        },
                    ),
                    node_filter=HasData(
                        views=[
                            ViewId(
                                core.env.cognite.fdm_model_space,
                                core.env.cognite_entities.notification_user_role_site,
                                notification_user_role_site_view,
                            )
                        ]
                    ),
                ),
                # getting the blocklist roles in the application groups
                "blocklist_roles_application_groups_in_template": EdgeResultSetExpression(
                    from_="application_groups_in_template",
                    limit=10000,
                    direction="outwards",
                    chain_to="destination",
                    filter=Equals(
                        ["edge", "type"],
                        {
                            "space": core.env.cognite.fdm_model_space,
                            "externalId": "NotificationApplicationGroup.blocklistRoles",
                        },
                    ),
                    node_filter=HasData(
                        views=[
                            ViewId(
                                core.env.cognite.fdm_model_space,
                                core.env.cognite_entities.notification_user_role_site,
                                notification_user_role_site_view,
                            )
                        ]
                    ),
                ),
                # getting the users in the blocklist in the application groups
                "users_in_blocklist_application_groups": NodeResultSetExpression(
                    from_="blocklist_application_groups_in_template",
                    filter=Equals(
                        notification_user_view.as_property_ref("deleted"), False
                    ),
                    limit=10000,
                ),
                # getting the edge of subscribed roles
                "subscribed_roles_in_template": EdgeResultSetExpression(
                    from_="template",
                    limit=10000,
                    direction="outwards",
                    chain_to="destination",
                    filter=Equals(
                        ["edge", "type"],
                        {
                            "space": core.env.cognite.fdm_model_space,
                            "externalId": "NotificationTemplate.subscribedRoles",
                        },
                    ),
                    node_filter=HasData(
                        views=[
                            ViewId(
                                core.env.cognite.fdm_model_space,
                                core.env.cognite_entities.notification_user_role_site,
                                notification_user_role_site_view,
                            )
                        ]
                    ),
                ),
                # getting the edge of blocklist roles
                "blocklist_roles_in_template": EdgeResultSetExpression(
                    from_="template",
                    limit=10000,
                    direction="outwards",
                    chain_to="destination",
                    filter=Equals(
                        ["edge", "type"],
                        {
                            "space": core.env.cognite.fdm_model_space,
                            "externalId": "NotificationTemplate.blocklistRoles",
                        },
                    ),
                    node_filter=HasData(
                        views=[
                            ViewId(
                                core.env.cognite.fdm_model_space,
                                core.env.cognite_entities.notification_user_role_site,
                                notification_user_role_site_view,
                            )
                        ]
                    ),
                ),
                # getting the roles in the subscribed roles
                "roles_in_template": NodeResultSetExpression(
                    from_="subscribed_roles_in_template", limit=10000
                ),
                # getting the roles in the blocklist roles
                "roles_in_template_blocklist": NodeResultSetExpression(
                    from_="blocklist_roles_in_template", limit=10000
                ),
                # getting the reporting sites from the roles
                "reporting_sites_from_roles_in_template": NodeResultSetExpression(
                    from_="roles_in_template",
                    limit=10000,
                    chain_to="destination",
                    direction="outwards",
                    through=PropertyId(
                        ViewId(
                            core.env.cognite.fdm_model_space,
                            core.env.cognite_entities.notification_user_role_site,
                            notification_user_role_site_view,
                        ),
                        "reportingSite",
                    ),
                ),
                # getting the reporting sites from the blocklist roles
                "reporting_sites_from_roles_in_template_blocklist": NodeResultSetExpression(
                    from_="roles_in_template_blocklist",
                    limit=10000,
                    chain_to="destination",
                    direction="outwards",
                    through=PropertyId(
                        ViewId(
                            core.env.cognite.fdm_model_space,
                            core.env.cognite_entities.notification_user_role_site,
                            notification_user_role_site_view,
                        ),
                        "reportingSite",
                    ),
                ),
                # getting the reporting sites from roles in template
                "role_from_roles_in_template": NodeResultSetExpression(
                    from_="roles_in_template",
                    limit=10000,
                    chain_to="destination",
                    direction="outwards",
                    through=PropertyId(
                        ViewId(
                            core.env.cognite.fdm_model_space,
                            core.env.cognite_entities.notification_user_role_site,
                            notification_user_role_site_view,
                        ),
                        "role",
                    ),
                ),
                # getting the reporting sites from roles in blocklist
                "role_from_roles_in_template_blocklist": NodeResultSetExpression(
                    from_="roles_in_template_blocklist",
                    limit=10000,
                    chain_to="destination",
                    direction="outwards",
                    through=PropertyId(
                        ViewId(
                            core.env.cognite.fdm_model_space,
                            core.env.cognite_entities.notification_user_role_site,
                            notification_user_role_site_view,
                        ),
                        "role",
                    ),
                ),
                # getting the users complements from the roles
                "user_complement_in_subscribed_roles_in_template": EdgeResultSetExpression(
                    from_="subscribed_roles_in_template",
                    limit=10000,
                    direction="inwards",
                    chain_to="destination",
                    filter=Equals(
                        ["edge", "type"],
                        {
                            "space": core.env.spaces.um_model_space,
                            "externalId": "UserComplement.userRoleSite",
                        },
                    ),
                    node_filter=HasData(
                        views=[
                            ViewId(
                                core.env.spaces.um_model_space,
                                core.env.cognite_entities.user_complement,
                                user_complement_view,
                            )
                        ]
                    ),
                ),
                # getting the users complements from the blocklist roles
                "user_complement_in_blocklist_roles_in_template": EdgeResultSetExpression(
                    from_="blocklist_roles_in_template",
                    limit=10000,
                    direction="inwards",
                    chain_to="destination",
                    filter=Equals(
                        ["edge", "type"],
                        {
                            "space": core.env.spaces.um_model_space,
                            "externalId": "UserComplement.userRoleSite",
                        },
                    ),
                    node_filter=HasData(
                        views=[
                            ViewId(
                                core.env.spaces.um_model_space,
                                core.env.cognite_entities.user_complement,
                                user_complement_view,
                            )
                        ]
                    ),
                ),
                # getting the user azure attribute from users complements from the roles
                "user_complement_in_subscribed_roles": NodeResultSetExpression(
                    from_="user_complement_in_subscribed_roles_in_template",
                    limit=10000,
                ),
                # getting the user azure attribute from users complements from the blocklist roles
                "user_complement_in_blocklist_roles": NodeResultSetExpression(
                    from_="user_complement_in_blocklist_roles_in_template",
                    limit=10000,
                ),
                # getting the user azure attribute from users complements from the roles
                "user_azure_attribute_in_subscribed_roles_in_template": NodeResultSetExpression(
                    from_="user_complement_in_subscribed_roles",
                    limit=10000,
                    chain_to="destination",
                    direction="outwards",
                    through=PropertyId(
                        ViewId(
                            core.env.spaces.um_model_space,
                            core.env.cognite_entities.user_complement,
                            user_complement_view,
                        ),
                        "userAzureAttribute",
                    ),
                ),
                # getting the user azure attribute from users complements from the blocklist roles
                "user_azure_attribute_in_blocklist_roles_in_template": NodeResultSetExpression(
                    from_="user_complement_in_blocklist_roles",
                    limit=10000,
                    chain_to="destination",
                    direction="outwards",
                    through=PropertyId(
                        ViewId(
                            core.env.spaces.um_model_space,
                            core.env.cognite_entities.user_complement,
                            user_complement_view,
                        ),
                        "userAzureAttribute",
                    ),
                ),
                # getting the user info from user azure attribute from users complements from the roles
                "user_in_subscribed_roles": NodeResultSetExpression(
                    from_="user_azure_attribute_in_subscribed_roles_in_template",
                    limit=10000,
                    chain_to="destination",
                    direction="outwards",
                    through=PropertyId(
                        ViewId(
                            core.env.spaces.um_model_space,
                            core.env.cognite_entities.user_azure_attribute,
                            user_azure_attribute_view,
                        ),
                        "user",
                    ),
                    filter=Equals(user_view.as_property_ref("deleted"), False),
                ),
                # getting the user info from user azure attribute from users complements from the blocklist roles
                "user_in_blocklist_roles": NodeResultSetExpression(
                    from_="user_azure_attribute_in_blocklist_roles_in_template",
                    limit=10000,
                    chain_to="destination",
                    direction="outwards",
                    through=PropertyId(
                        ViewId(
                            core.env.spaces.um_model_space,
                            core.env.cognite_entities.user_azure_attribute,
                            user_azure_attribute_view,
                        ),
                        "user",
                    ),
                    filter=Equals(user_view.as_property_ref("deleted"), False),
                ),
            },
            select={
                "template": Select(
                    [
                        SourceSelector(
                            ViewId(
                                core.env.cognite.fdm_model_space,
                                core.env.cognite_entities.notification_template,
                                templates_view,
                            ),
                            ["*"],
                        )
                    ],
                    limit=1,
                ),
                "subscribed_users_in_template": Select(limit=10000),
                "users_in_subscribed_users_template": Select(
                    [
                        SourceSelector(
                            ViewId(
                                core.env.cognite.fdm_model_space,
                                core.env.cognite_entities.notification_user,
                                notification_user_view.version,
                            ),
                            ["*"],
                        )
                    ],
                    limit=10000,
                ),
                "blocklist_in_template": Select(limit=10000),
                "users_in_blocklist_template": Select(
                    [
                        SourceSelector(
                            ViewId(
                                core.env.cognite.fdm_model_space,
                                core.env.cognite_entities.notification_user,
                                notification_user_view.version,
                            ),
                            ["*"],
                        )
                    ],
                    limit=10000,
                ),
                "channels_in_template": Select(limit=3),
                "subscribed_channels_in_template": Select(
                    [
                        SourceSelector(
                            ViewId(
                                core.env.cognite.fdm_model_space,
                                core.env.cognite_entities.notification_channel,
                                notification_channel_view,
                            ),
                            ["*"],
                        )
                    ],
                    limit=3,
                ),
                "application_groups_in_template": Select(limit=10000),
                "groups_in_application_groups_in_template": Select(
                    [
                        SourceSelector(
                            ViewId(
                                core.env.cognite.fdm_model_space,
                                core.env.cognite_entities.notification_application_group,
                                notification_application_group_view,
                            ),
                            ["*"],
                        )
                    ],
                    limit=10000,
                ),
                "users_application_groups_in_template": Select(limit=10000),
                "users_in_application_groups": Select(
                    [
                        SourceSelector(
                            ViewId(
                                core.env.cognite.fdm_model_space,
                                core.env.cognite_entities.notification_user,
                                notification_user_view.version,
                            ),
                            ["*"],
                        )
                    ],
                    limit=10000,
                ),
                "blocklist_application_groups_in_template": Select(limit=10000),
                "user_roles_application_groups_in_template": Select(limit=10000),
                "blocklist_roles_application_groups_in_template": Select(limit=10000),
                "users_in_blocklist_application_groups": Select(
                    [
                        SourceSelector(
                            ViewId(
                                core.env.cognite.fdm_model_space,
                                core.env.cognite_entities.notification_user,
                                notification_user_view.version,
                            ),
                            ["*"],
                        )
                    ],
                    limit=10000,
                ),
                "subscribed_roles_in_template": Select(limit=10000),
                "blocklist_roles_in_template": Select(limit=10000),
                "roles_in_template": Select(
                    [
                        SourceSelector(
                            ViewId(
                                core.env.cognite.fdm_model_space,
                                core.env.cognite_entities.notification_user_role_site,
                                notification_user_role_site_view,
                            ),
                            ["*"],
                        )
                    ],
                    limit=10000,
                ),
                "roles_in_template_blocklist": Select(
                    [
                        SourceSelector(
                            ViewId(
                                core.env.cognite.fdm_model_space,
                                core.env.cognite_entities.notification_user_role_site,
                                notification_user_role_site_view,
                            ),
                            ["*"],
                        )
                    ],
                    limit=10000,
                ),
                "user_complement_in_subscribed_roles_in_template": Select(limit=10000),
                "user_complement_in_blocklist_roles_in_template": Select(limit=10000),
                "user_complement_in_subscribed_roles": Select(
                    [
                        SourceSelector(
                            ViewId(
                                core.env.spaces.um_model_space,
                                core.env.cognite_entities.user_complement,
                                user_complement_view,
                            ),
                            ["searchTags", "userAzureAttribute"],
                        )
                    ],
                    limit=10000,
                ),
                "user_complement_in_blocklist_roles": Select(
                    [
                        SourceSelector(
                            ViewId(
                                core.env.spaces.um_model_space,
                                core.env.cognite_entities.user_complement,
                                user_complement_view,
                            ),
                            ["searchTags", "userAzureAttribute"],
                        )
                    ],
                    limit=10000,
                ),
                "user_azure_attribute_in_subscribed_roles_in_template": Select(
                    [
                        SourceSelector(
                            ViewId(
                                core.env.spaces.um_model_space,
                                core.env.cognite_entities.user_azure_attribute,
                                user_azure_attribute_view,
                            ),
                            ["user"],
                        )
                    ],
                    limit=10000,
                ),
                "user_azure_attribute_in_blocklist_roles_in_template": Select(
                    [
                        SourceSelector(
                            ViewId(
                                core.env.spaces.um_model_space,
                                core.env.cognite_entities.user_azure_attribute,
                                user_azure_attribute_view,
                            ),
                            ["user"],
                        )
                    ],
                    limit=10000,
                ),
                "user_in_subscribed_roles": Select(
                    [
                        SourceSelector(
                            ViewId(
                                core.env.spaces.um_model_space,
                                core.env.cognite_entities.user,
                                user_view.version,
                            ),
                            ["email", "firstName", "lastName"],
                        )
                    ],
                    limit=10000,
                ),
                "user_in_blocklist_roles": Select(
                    [
                        SourceSelector(
                            ViewId(
                                core.env.spaces.um_model_space,
                                core.env.cognite_entities.user,
                                user_view.version,
                            ),
                            ["email", "firstName", "lastName"],
                        )
                    ],
                    limit=10000,
                ),
                "creator": Select(
                    [
                        SourceSelector(
                            ViewId(
                                core.env.cognite.fdm_model_space,
                                core.env.cognite_entities.notification_user,
                                notification_user_view.version,
                            ),
                            ["firstName", "lastName"],
                        )
                    ],
                    limit=1,
                ),
                "severity": Select(
                    [
                        SourceSelector(
                            ViewId(
                                core.env.cognite.fdm_model_space,
                                core.env.cognite_entities.notification_severity,
                                notification_severity_view,
                            ),
                            ["*"],
                        )
                    ],
                    limit=1,
                ),
                "notification_type": Select(
                    [
                        SourceSelector(
                            ViewId(
                                core.env.cognite.fdm_model_space,
                                core.env.cognite_entities.notification_type,
                                notification_type_view,
                            ),
                            ["name", "application", "entityType", "properties"],
                        )
                    ],
                    limit=1,
                ),
                "application": Select(
                    [
                        SourceSelector(
                            ViewId(
                                core.env.spaces.um_model_space,
                                core.env.cognite_entities.application,
                                application_view,
                            ),
                            ["alias"],
                        )
                    ],
                    limit=1,
                ),
                "reporting_sites_from_roles_in_template": Select(
                    [
                        SourceSelector(
                            ViewId(
                                core.env.spaces.asset_hierarcy_model_space,
                                core.env.cognite_entities.reporting_site,
                                reporting_site_view,
                            ),
                            ["*"],
                        )
                    ],
                    limit=10000,
                ),
                "reporting_sites_from_roles_in_template_blocklist": Select(
                    [
                        SourceSelector(
                            ViewId(
                                core.env.spaces.asset_hierarcy_model_space,
                                core.env.cognite_entities.reporting_site,
                                reporting_site_view,
                            ),
                            ["*"],
                        )
                    ],
                    limit=10000,
                ),
                "role_from_roles_in_template": Select(
                    [
                        SourceSelector(
                            ViewId(
                                core.env.spaces.um_model_space,
                                core.env.cognite_entities.role,
                                role_view,
                            ),
                            ["*"],
                        )
                    ],
                    limit=10000,
                ),
                "role_from_roles_in_template_blocklist": Select(
                    [
                        SourceSelector(
                            ViewId(
                                core.env.spaces.um_model_space,
                                core.env.cognite_entities.role,
                                role_view,
                            ),
                            ["*"],
                        )
                    ],
                    limit=10000,
                ),
            },
            cursors={
                "users_in_subscribed_users_template": users_in_subscribed_users_template_cursor,
                "subscribed_users_in_template": subscribed_user_in_template_cursor,
                "template": template_cursor,
                "users_in_application_groups": users_in_application_groups_cursor,
                "users_application_groups_in_template": users_application_groups_in_template_cursor,
                "user_complement_in_subscribed_roles_in_template": user_complement_in_subscribed_roles_in_template_cursor,
                "user_complement_in_blocklist_roles_in_template": user_complement_in_blocklist_roles_in_template_cursor,
                "users_in_blocklist_template": users_in_blocklist_template_cursor,
                "blocklist_in_template": blocklist_in_template_cursor,
                "users_in_blocklist_application_groups": users_in_blocklist_application_groups_cursor,
                "blocklist_application_groups_in_template": blocklist_application_groups_in_template_cursor,
            },
        )

        while has_cursor:
            response_query = self._cognite_client.data_modeling.instances.query(query)
            template = self.format_template_response(response_query, views)

            if response_query:
                has_cursor = False
                cursor_keys = [
                    "subscribed_users_in_template",
                    "users_in_subscribed_users_template",
                    "users_in_application_groups",
                    "users_application_groups_in_template",
                    "user_complement_in_subscribed_roles_in_template",
                    "user_complement_in_blocklist_roles_in_template",
                    "users_in_blocklist_template",
                    "blocklist_in_template",
                    "users_in_blocklist_application_groups",
                    "blocklist_application_groups_in_template",
                ]

                for key in cursor_keys:
                    if (
                        key in response_query.cursors
                        and len(response_query[key]) == 10000
                    ):
                        query.cursors[key] = response_query.cursors[key]
                        has_cursor = True
            else:
                has_cursor = False

        return [template]

    def format_template_response(self, response, views: dict):
        template_result = response["template"].dump()[0]
        template_properties = (
            template_result.get("properties", {})
            .get(core.env.cognite.fdm_model_space, {})
            .get(
                f"{core.env.cognite_entities.notification_template}/{views['template']}",
                {},
            )
        )

        # define channels object
        channels_result = response["subscribed_channels_in_template"].dump()
        channels = [
            {
                "externalId": channel.get("externalId", ""),
                "space": channel.get("space", ""),
                "name": channel.get("properties", {})
                .get(core.env.cognite.fdm_model_space, {})
                .get(
                    f"{core.env.cognite_entities.notification_channel}/{views['notification_channel']}",
                    {},
                )
                .get("name", ""),
                "description": channel.get("properties", {})
                .get(core.env.cognite.fdm_model_space, {})
                .get(
                    f"{core.env.cognite_entities.notification_channel}/{views['notification_channel']}",
                    {},
                )
                .get("description", ""),
            }
            for channel in channels_result
        ]

        # define severity object
        severity_result = response["severity"].dump()[0]
        severity_properties = (
            severity_result.get("properties", {})
            .get(core.env.cognite.fdm_model_space, {})
            .get(
                f"{core.env.cognite_entities.notification_severity}/{views['notification_severity']}",
                {},
            )
        )
        severity = {
            "externalId": severity_result.get("externalId", ""),
            "space": severity_result.get("space", ""),
            "name": severity_properties.get("name", ""),
            "description": severity_properties.get("description", ""),
        }

        # define notification type name
        notification_type_result = response["notification_type"].dump()[0]
        notification_type_properties = (
            notification_type_result.get("properties", {})
            .get(core.env.cognite.fdm_model_space, {})
            .get(
                f"{core.env.cognite_entities.notification_type}/{views['notification_type']}",
                {},
            )
        )
        # define the application name and externalId
        application_result = response["application"].dump()[0]
        application_properties = application_properties = (
            application_result.get("properties", {})
            .get(core.env.spaces.um_model_space, {})
            .get(f"{core.env.cognite_entities.application}/{views['application']}", {})
        )
        # define the editedBy
        creator_result = response["creator"].dump()[0]
        creator_properties = (
            creator_result.get("properties", {})
            .get(core.env.cognite.fdm_model_space, {})
            .get(
                f"{core.env.cognite_entities.notification_user}/{views['notification_user']}",
                {},
            )
        )
        edited_by = f"{creator_properties.get('lastName', '')}, {creator_properties.get('firstName', '')}"

        # define the editedAt
        last_updated_time = self.transform_date(template_result.get("lastUpdatedTime"))

        # define subject
        subject = template_properties.get("subject", None)

        # define subscribed users
        subscribed_users_result = response["users_in_subscribed_users_template"].dump()
        subscribed_users_list = [
            {
                "externalId": user.get("externalId", ""),
                "email": user.get("properties", {})
                .get(core.env.cognite.fdm_model_space, {})
                .get(
                    f"{core.env.cognite_entities.notification_user}/{views['notification_user']}",
                    {},
                )
                .get("email", ""),
                "firstName": user.get("properties", {})
                .get(core.env.cognite.fdm_model_space, {})
                .get(
                    f"{core.env.cognite_entities.notification_user}/{views['notification_user']}",
                    {},
                )
                .get("firstName", ""),
                "lastName": user.get("properties", {})
                .get(core.env.cognite.fdm_model_space, {})
                .get(
                    f"{core.env.cognite_entities.notification_user}/{views['notification_user']}",
                    {},
                )
                .get("lastName", ""),
            }
            for user in subscribed_users_result
        ]

        # define blocklist
        blocklist_result = response["users_in_blocklist_template"].dump()
        blocklist = [
            {
                "externalId": user.get("externalId", ""),
                "email": user.get("properties", {})
                .get(core.env.cognite.fdm_model_space, {})
                .get(
                    f"{core.env.cognite_entities.notification_user}/{views['notification_user']}",
                    {},
                )
                .get("email", ""),
            }
            for user in blocklist_result
        ]
        # defined subscribed roles
        subscribed_roles_result = response["roles_in_template"].dump()
        reporting_sites_from_subscribed_roles = response[
            "reporting_sites_from_roles_in_template"
        ].dump()
        role_from_subscribed_roles = response["role_from_roles_in_template"].dump()
        user_complement_from_role = response[
            "user_complement_in_subscribed_roles"
        ].dump()
        user_azure_attribute_from_role = response[
            "user_azure_attribute_in_subscribed_roles_in_template"
        ].dump()
        users_in_subscrubed_roles = response["user_in_subscribed_roles"].dump()
        subscribed_roles = []
        for role in subscribed_roles_result:
            properties = (
                role.get("properties", {})
                .get(core.env.cognite.fdm_model_space, {})
                .get(
                    f"{core.env.cognite_entities.notification_user_role_site}/{views['notification_user_role_site']}",
                    {},
                )
            )

            # Buscar site de reporting relacionado ao role
            reporting_site = properties.get("reportingSite", {}).get("externalId", "")
            filter_site = [
                item
                for item in reporting_sites_from_subscribed_roles
                if item["externalId"] == reporting_site
            ][0]
            filter_site_properties = (
                filter_site.get("properties", {})
                .get(core.env.spaces.asset_hierarcy_model_space, {})
                .get(
                    f"{core.env.cognite_entities.reporting_site}/{views['reporting_site']}",
                    {},
                )
            )
            site_code = filter_site_properties.get("siteCode", "")
            site = {
                "externalId": filter_site.get("externalId", ""),
                "space": filter_site.get("space", ""),
                "name": filter_site_properties.get("name", ""),
                "siteCode": site_code,
            }

            # Buscar role associado ao site
            role_externalId = properties.get("role", {}).get("externalId", "")
            filter_role = [
                item
                for item in role_from_subscribed_roles
                if item["externalId"] == role_externalId
            ][0]
            filter_role_properties = (
                filter_role.get("properties", {})
                .get(core.env.spaces.um_model_space, {})
                .get(f"{core.env.cognite_entities.role}/{views['role']}", {})
            )
            role_name = f"({site_code}) {filter_role_properties.get('name', '')}"

            # Buscar complementos de usuários associados ao role
            filter_users = [
                user
                for user in user_complement_from_role
                if role_externalId
                in user.get("properties", {})
                .get(core.env.spaces.um_model_space, {})
                .get(
                    f"{core.env.cognite_entities.user_complement}/{views['user_complement']}",
                    {},
                )
                .get("searchTags", [])
            ]
            users_complement = [
                user.get("properties", {})
                .get(core.env.spaces.um_model_space, {})
                .get(
                    f"{core.env.cognite_entities.user_complement}/{views['user_complement']}",
                    {},
                )
                .get("userAzureAttribute", {})
                .get("externalId", "")
                for user in filter_users
            ]
            user_azure_attribute = [
                user.get("properties", {})
                .get(core.env.spaces.um_model_space, {})
                .get(
                    f"{core.env.cognite_entities.user_azure_attribute}/{views['user_azure_attribute']}",
                    {},
                )
                .get("user", {})
                .get("externalId", "")
                for user in user_azure_attribute_from_role
                if user["externalId"] in users_complement
            ]
            users_in_role = [
                {
                    "email": user.get("properties", {})
                    .get(core.env.spaces.um_model_space, {})
                    .get(f"{core.env.cognite_entities.user}/{views['user']}", {})
                    .get("email", ""),
                    "externalId": user.get("externalId", ""),
                    "firstName": user.get("properties", {})
                    .get(core.env.spaces.um_model_space, {})
                    .get(f"{core.env.cognite_entities.user}/{views['user']}", {})
                    .get("firstName", ""),
                    "lastName": user.get("properties", {})
                    .get(core.env.spaces.um_model_space, {})
                    .get(f"{core.env.cognite_entities.user}/{views['user']}", {})
                    .get("lastName", ""),
                }
                for user in users_in_subscrubed_roles
                if user["externalId"] in user_azure_attribute
            ]

            subscribed_roles.append(
                {
                    "externalId": role.get("externalId", ""),
                    "space": role.get("space", ""),
                    "name": role_name,
                    "site": site,
                    "users": sorted(users_in_role, key=lambda x: x["email"].lower()),
                }
            )

        # defined blocklist roles
        blocklist_roles_result = response["roles_in_template_blocklist"].dump()
        reporting_sites_from_blocklist_roles = response[
            "reporting_sites_from_roles_in_template_blocklist"
        ].dump()
        role_from_blocklist_roles = response[
            "role_from_roles_in_template_blocklist"
        ].dump()
        user_complement_from_blocklist_role = response[
            "user_complement_in_blocklist_roles"
        ].dump()
        user_azure_attribute_from_role_blocklist = response[
            "user_azure_attribute_in_blocklist_roles_in_template"
        ].dump()
        users_in_blocklist_roles = response["user_in_blocklist_roles"].dump()
        blocklist_roles = []
        for role in blocklist_roles_result:
            properties = (
                role.get("properties", {})
                .get(core.env.cognite.fdm_model_space, {})
                .get(
                    f"{core.env.cognite_entities.notification_user_role_site}/{views['notification_user_role_site']}",
                    {},
                )
            )

            # Buscar site de reporting relacionado ao role
            reporting_site = properties.get("reportingSite", {}).get("externalId", "")
            filter_site = [
                item
                for item in reporting_sites_from_blocklist_roles
                if item["externalId"] == reporting_site
            ][0]
            filter_site_properties = (
                filter_site.get("properties", {})
                .get(core.env.spaces.asset_hierarcy_model_space, {})
                .get(
                    f"{core.env.cognite_entities.reporting_site}/{views['reporting_site']}",
                    {},
                )
            )
            site_code = filter_site_properties.get("siteCode", "")
            site = {
                "externalId": filter_site.get("externalId", ""),
                "space": filter_site.get("space", ""),
                "name": filter_site_properties.get("name", ""),
                "siteCode": site_code,
            }

            # Buscar role associado ao site
            role_externalId = properties.get("role", {}).get("externalId", "")
            filter_role = [
                item
                for item in role_from_blocklist_roles
                if item["externalId"] == role_externalId
            ][0]
            filter_role_properties = (
                filter_role.get("properties", {})
                .get(core.env.spaces.um_model_space, {})
                .get(f"{core.env.cognite_entities.role}/{views['role']}", {})
            )
            role_name = f"({site_code}) {filter_role_properties.get('name', '')}"

            # Buscar complementos de usuários associados ao role
            filter_users = [
                user
                for user in user_complement_from_blocklist_role
                if role_externalId
                in user.get("properties", {})
                .get(core.env.spaces.um_model_space, {})
                .get(
                    f"{core.env.cognite_entities.user_complement}/{views['user_complement']}",
                    {},
                )
                .get("searchTags", [])
            ]
            users_complement = [
                user.get("properties", {})
                .get(core.env.spaces.um_model_space, {})
                .get(
                    f"{core.env.cognite_entities.user_complement}/{views['user_complement']}",
                    {},
                )
                .get("userAzureAttribute", {})
                .get("externalId", "")
                for user in filter_users
            ]
            user_azure_attribute = [
                user.get("properties", {})
                .get(core.env.spaces.um_model_space, {})
                .get(
                    f"{core.env.cognite_entities.user_azure_attribute}/{views['user_azure_attribute']}",
                    {},
                )
                .get("user", {})
                .get("externalId", "")
                for user in user_azure_attribute_from_role_blocklist
                if user["externalId"] in users_complement
            ]
            users_in_role = [
                {
                    "email": user.get("properties", {})
                    .get(core.env.spaces.um_model_space, {})
                    .get(f"{core.env.cognite_entities.user}/{views['user']}", {})
                    .get("email", ""),
                    "externalId": user.get("externalId", ""),
                    "firstName": user.get("properties", {})
                    .get(core.env.spaces.um_model_space, {})
                    .get(f"{core.env.cognite_entities.user}/{views['user']}", {})
                    .get("firstName", ""),
                    "lastName": user.get("properties", {})
                    .get(core.env.spaces.um_model_space, {})
                    .get(f"{core.env.cognite_entities.user}/{views['user']}", {})
                    .get("lastName", ""),
                }
                for user in users_in_blocklist_roles
                if user["externalId"] in user_azure_attribute
            ]
            blocklist_roles.append(
                {
                    "externalId": role.get("externalId", ""),
                    "space": role.get("space", ""),
                    "name": role_name,
                    "site": site,
                    "users": sorted(users_in_role, key=lambda x: x["email"].lower()),
                }
            )

        # define applicationGroups
        application_groups_result = response[
            "groups_in_application_groups_in_template"
        ].dump()
        users_edge_application_group_result = response[
            "users_application_groups_in_template"
        ].dump()
        users_in_application_group_result = response[
            "users_in_application_groups"
        ].dump()
        blocklist_edge_application_group_result = response[
            "blocklist_application_groups_in_template"
        ].dump()
        users_in_blocklist_application_group_result = response[
            "users_in_blocklist_application_groups"
        ].dump()
        users_roles_edge_application_group_result = response[
            "user_roles_application_groups_in_template"
        ].dump()
        blocklist_roles_edge_application_group_result = response[
            "blocklist_roles_application_groups_in_template"
        ].dump()

        grouped_edges = {}
        blocklist_edges = {}
        users_roles_application_group_edges = {}
        blocklist_roles_application_group_edges = {}

        for edge in users_edge_application_group_result:
            start_node = edge["startNode"]["externalId"]
            end_node = edge["endNode"]["externalId"]

            if start_node not in grouped_edges:
                grouped_edges[start_node] = []

            grouped_edges[start_node].append(end_node)
        for edge in blocklist_edge_application_group_result:
            start_node = edge["startNode"]["externalId"]
            end_node = edge["endNode"]["externalId"]

            if start_node not in blocklist_edges:
                blocklist_edges[start_node] = []

            blocklist_edges[start_node].append(end_node)
        for edge in users_roles_edge_application_group_result:
            start_node = edge["startNode"]["externalId"]
            end_node = edge["endNode"]["externalId"]

            if start_node not in users_roles_application_group_edges:
                users_roles_application_group_edges[start_node] = []

            users_roles_application_group_edges[start_node].append(end_node)
        for edge in blocklist_roles_edge_application_group_result:
            start_node = edge["startNode"]["externalId"]
            end_node = edge["endNode"]["externalId"]

            if start_node not in blocklist_roles_application_group_edges:
                blocklist_roles_application_group_edges[start_node] = []

            blocklist_roles_application_group_edges[start_node].append(end_node)

        application_groups = []
        for application in application_groups_result:
            users_in_edge = grouped_edges.get(application.get("externalId", ""), [])

            blocklist_in_edge = blocklist_edges.get(
                application.get("externalId", ""), []
            )
            filtered_users = [
                user
                for user in users_in_application_group_result
                if user["externalId"] in users_in_edge
            ]
            filtered_users_blocklist = (
                [
                    user
                    for user in users_in_blocklist_application_group_result
                    if user["externalId"] in blocklist_in_edge
                ]
                if len(blocklist_in_edge) > 0
                else []
            )
            users_in_nft_application_group = [
                {
                    "email": user.get("properties", {})
                    .get(core.env.cognite.fdm_model_space, {})
                    .get(
                        f"{core.env.cognite_entities.notification_user}/{views['notification_user']}",
                        {},
                    )
                    .get("email", ""),
                    "externalId": user.get("externalId", ""),
                    "firstName": user.get("properties", {})
                    .get(core.env.cognite.fdm_model_space, {})
                    .get(
                        f"{core.env.cognite_entities.notification_user}/{views['notification_user']}",
                        {},
                    )
                    .get("firstName", ""),
                    "lastName": user.get("properties", {})
                    .get(core.env.cognite.fdm_model_space, {})
                    .get(
                        f"{core.env.cognite_entities.notification_user}/{views['notification_user']}",
                        {},
                    )
                    .get("lastName", ""),
                }
                for user in filtered_users
            ]
            blocklist_in_nft_application_group = (
                [
                    {
                        "email": user.get("properties", {})
                        .get(core.env.cognite.fdm_model_space, {})
                        .get(
                            f"{core.env.cognite_entities.notification_user}/{views['notification_user']}",
                            {},
                        )
                        .get("email", ""),
                        "externalId": user.get("externalId", ""),
                        "firstName": user.get("properties", {})
                        .get(core.env.cognite.fdm_model_space, {})
                        .get(
                            f"{core.env.cognite_entities.notification_user}/{views['notification_user']}",
                            {},
                        )
                        .get("firstName", ""),
                        "lastName": user.get("properties", {})
                        .get(core.env.cognite.fdm_model_space, {})
                        .get(
                            f"{core.env.cognite_entities.notification_user}/{views['notification_user']}",
                            {},
                        )
                        .get("lastName", ""),
                    }
                    for user in filtered_users_blocklist
                ]
                if len(filtered_users_blocklist) > 0
                else []
            )
            users_roles_in_nft_application_group = [
                {
                    "externalId": role,
                    "name": "",
                    "space": "",
                    "users": [],
                }
                for role in users_roles_application_group_edges.get(
                    application.get("externalId", ""), []
                )
            ]
            blocklist_roles_in_nft_application_group = [
                {
                    "externalId": role,
                    "name": "",
                    "space": "",
                    "users": [],
                }
                for role in blocklist_roles_application_group_edges.get(
                    application.get("externalId", ""), []
                )
            ]

            application_groups.append(
                {
                    "externalId": application.get("externalId", ""),
                    "space": application.get("space", ""),
                    "name": application.get("properties", {})
                    .get(core.env.cognite.fdm_model_space, {})
                    .get(
                        f"{core.env.cognite_entities.notification_application_group}/{views['notification_application_group']}",
                        {},
                    )
                    .get("name", ""),
                    "description": application.get("properties", {})
                    .get(core.env.cognite.fdm_model_space, {})
                    .get(
                        f"{core.env.cognite_entities.notification_application_group}/{views['notification_application_group']}",
                        {},
                    )
                    .get("description", ""),
                    "application": {
                        "externalId": application.get("properties", {})
                        .get(core.env.cognite.fdm_model_space, {})
                        .get(
                            f"{core.env.cognite_entities.notification_application_group}/{views['notification_application_group']}",
                            {},
                        )
                        .get("application", "")
                        .get("externalId", ""),
                        "space": application.get("properties", {})
                        .get(core.env.cognite.fdm_model_space, {})
                        .get(
                            f"{core.env.cognite_entities.notification_application_group}/{views['notification_application_group']}",
                            {},
                        )
                        .get("application", "")
                        .get("space", ""),
                    },
                    "externalUsers": application.get("properties", {})
                    .get(core.env.cognite.fdm_model_space, {})
                    .get(
                        f"{core.env.cognite_entities.notification_application_group}/{views['notification_application_group']}",
                        {},
                    )
                    .get("externalUsers", ""),
                    "users": users_in_nft_application_group,
                    "blocklist": blocklist_in_nft_application_group,
                    "usersRoles": users_roles_in_nft_application_group,
                    "blocklistRoles": blocklist_roles_in_nft_application_group,
                }
            )

        return {
            "name": template_properties.get("name", ""),
            "creator": template_properties.get("creator", {}),
            "conditionalExpression": template_properties.get(
                "conditionalExpression", ""
            ),
            "customChannelEnabled": template_properties.get(
                "customChannelEnabled", False
            ),
            "customFrequencyEnabled": template_properties.get(
                "customFrequencyEnabled", False
            ),
            "externalId": template_result.get("externalId", ""),
            "space": template_result.get("space", ""),
            "text": template_properties.get("text"),
            "frequencyCronExpression": template_properties.get(
                "frequencyCronExpression", None
            ),
            "subscribedExternalUsers": template_properties.get(
                "subscribedExternalUsers", []
            ),
            "notificationType": template_properties.get("notificationType", {}).get(
                "externalId", ""
            ),
            "adminLevel": template_properties.get("adminLevel", False),
            "deleted": template_properties.get("deleted", False),
            "allUsers": template_properties.get("allUsers", False),
            "externalUsers": template_properties.get("externalUsers", False),
            "channels": channels,
            "severity": severity,
            "notificationTypeName": notification_type_properties.get("name", ""),
            "notificationTypeEntity": notification_type_properties.get(
                "entityType", ""
            ),
            "application": application_result.get("externalId", ""),
            "applicationName": application_properties.get("alias", ""),
            "editedBy": edited_by,
            "editedAt": last_updated_time,
            "subject": subject,
            "subscribedUsers": subscribed_users_list,
            "subscribedUserRoles": subscribed_roles,
            "blocklistRoles": blocklist_roles,
            "blocklist": blocklist,
            "subscribedApplicationGroups": application_groups,
            "notificationTypeProperties": notification_type_properties.get(
                "properties", []
            ),
        }

    def transform_date(self, date: str):
        timestamp = date
        timestamp_in_seconds = timestamp / 1000
        dt = datetime.fromtimestamp(timestamp_in_seconds, tz=timezone.utc)
        return dt.strftime("%Y-%m-%dT%H:%M:%S.%f")[:-3] + "Z"

    def find_templates_by_filters(self, params, user):
        if params["is_admin_edition"] == True:
            variables = self.mount_query_filter(params)
            
            if params['q'] and params['q'] != '':
                list_name = "searchNotificationTemplate"
                query_response = self._cognite_client.data_modeling.graphql.query(
                    self._data_model_id,
                    query=queries.notification_templates.notification_template_search,
                    variables={
                        "query": params['q'],
                        "filter": variables["templates_filter"]
                    },
                )
            else:
                list_name = "listNotificationTemplate"
                query_response = self._cognite_client.data_modeling.graphql.query(
                    self._data_model_id,
                    query=queries.notification_templates.notification_template_list,
                    variables=variables,
                )

            items = query_response[list_name]['items']
        else:
            variables = self.mount_query_filter(params)

            query_response = self._cognite_client.data_modeling.graphql.query(
                self._data_model_id,
                query=queries.notification_templates.notification_template_list_by_users,
                variables={
                    "user_filter": {"displayName": {"eq": user.get("name")}},
                    "templates_filter": {
                        "or": [
                            {**variables["templates_filter"]},
                            {"and": [
                                {"creator": {"displayName": {"eq": user.get("name")}}},
                                {**variables["templates_filter"]}
                            ]}
                        ]
                    },
                    "after": variables["after"],
                    "pageSize": variables["pageSize"]
                }
            )

            notification_user_response = query_response.get("listNotificationUser", {}).get("items", [])
            templates_list = notification_user_response[0].get("templates", {}).get("items", []) if len(notification_user_response) > 0 else []

            if params['q'] and params['q'] != '':
                list_name = "searchNotificationTemplate"
                search_external_ids = self.get_externalIds(templates_list)

                query_response = self._cognite_client.data_modeling.graphql.query(
                    self._data_model_id,
                    query=queries.notification_templates.notification_template_search,
                    variables={
                        "query": params['q'],
                        "filter": {
                            "and": [
                                {**variables["templates_filter"]},
                                {"externalId": {"in": search_external_ids}}
                            ]
                        }
                    },
                )

                my_templates_result = query_response[list_name]['items']
            else:
                list_name = "listNotificationUser"
                my_templates_result = templates_list

            result_templates_ids = self.get_externalIds(my_templates_result)

            templates_extensions = self._cognite_client.data_modeling.graphql.query(
                self._data_model_id,
                query=queries.notification_template_extensions.list_extensions,
                variables={
                    "filter": { "template": {"externalId": {"in": result_templates_ids}}}
                }
            )

            templates_extensions_items = templates_extensions.get("listNotificationTemplateExtension", {}).get("items", [])

            templates_with_blocklist = self.get_blocklist_users(my_templates_result)
            templates_user_is_assigned = [
                item
                for item in templates_with_blocklist
                if user.get("preferred_username") not in (item.get("blocklistUsers") or [])
            ]
            
            items = self.replace_channels(templates_user_is_assigned, templates_extensions_items)

        result = self.filter_query_results(items, params)

        return {
            "templates": result,
            "end_cursor": query_response[list_name]['pageInfo']['endCursor'],
            "has_next_page": query_response[list_name]['pageInfo']['hasNextPage']
        }

    def mount_query_filter(self, params):
        templates_filter = [
            {
                "or": [
                    {"deleted": {"eq": False}},
                    {"deleted": {"isNull": True}},
                ]
            }
        ]
        notification_type_filter = []

        if params["is_admin_edition"]:
            templates_filter.append({ "adminLevel": { "eq": params["is_admin_edition"] }})

        if params["notification_type"] and params["notification_type"] != 'undefined' and params['q'] == '':
            notification_type_filter.append({
               "externalId": {"eq": params["notification_type"]}
            })

        if params["application_id"] and params['q'] == '':
            notification_type_filter.append({
                "application": {"externalId": {"in": params["application_id"].split(',')}}
            })

        if len(notification_type_filter) > 0:
            templates_filter.append({
                "notificationType": {"and": notification_type_filter}
            })

        return {
            "after": params["cursor"] if params["cursor"] else None,
            "pageSize": params["page_size"],
            "templates_filter": {"and": templates_filter},
        }
    
    def filter_query_results(self, items, params):
        filtered = items

        if params["application_id"]:
            filtered = list(filter(
                lambda el: el['notificationType']['application']['externalId'] in params["application_id"], filtered
            ))

        if params["notification_type"] and params["notification_type"] != 'undefined':
            filtered = list(filter(lambda el: el['notificationType']['externalId'] == params["notification_type"], filtered))

        if params['channels'] and params["channels"] != ['']:
            filtered = list(filter(
                lambda item: any(
                    canal.get("externalId") in params["channels"][0].split(',')
                    for canal in item.get("channels", {}).get("items", [])
                    if isinstance(canal, dict)
                ),
                filtered
            ))

        if params['subscribers_ids'] and params['subscribers_ids'] != ['']:
            separate_subscribers = self.get_subscribed_separated(params['subscribers_ids'])

            if len(separate_subscribers['emails']) > 0:
                filtered = list(filter(
                    lambda item: any(
                        user.get("externalId") in separate_subscribers['emails']
                        for user in item.get("subscribedUsers", {}).get("items", [])
                        if isinstance(user, dict)
                    ),
                    filtered
                ))

            if len(separate_subscribers['appgrps']) > 0:
                filtered = list(filter(
                    lambda item: any(
                        group.get("externalId") in separate_subscribers['appgrps']
                        for group in item.get("subscribedApplicationGroups", {}).get("items", [])
                        if isinstance(group, dict)
                    ),
                    filtered
                ))

            if len(separate_subscribers['userroles']) > 0:
                filtered = list(filter(
                    lambda item: any(
                        role.get("externalId") in separate_subscribers['userroles']
                        for role in item.get("subscribedRoles", {}).get("items", [])
                        if isinstance(role, dict)
                    ),
                    filtered
                ))

        return filtered

    def get_subscribed_separated(self, params):
        subscribers = params[0].split(',')

        emails = [i for i in subscribers if '@' in i]
        appgrps = [i for i in subscribers if i.startswith('APPGRP')]
        userroles = [i for i in subscribers if i.startswith('UserRoleSite')]

        return {
            'emails': emails,
            'appgrps': appgrps,
            'userroles': userroles
        }
    
    def get_externalIds(self, items):
        return [
            item.get("externalId")
            for item in items
        ]
    
    def replace_channels(self, templates, templates_extensions):
        for instance in templates_extensions:
            template_id = instance["template"]["externalId"]

            for template in templates:
                if template["externalId"] == template_id:
                    template["channels"] = instance.get("channels", {})

        return templates
    
    def get_blocklist_users(self, templates):
        for item in templates:
            blocklistUsers = []
            if len(item.get("blocklist").get("items")) > 0:
                blocklist = item.get("blocklist").get("items")

                blocklist_users = [user.get("externalId") for user in blocklist]

                blocklistUsers.extend(blocklist_users)

            if len(item.get("blocklistRoles").get("items")) > 0:
                blocklist = item.get("blocklistRoles").get("items")

                for role in blocklist:
                    blocklist_users = [
                        user.get("userAzureAttribute").get("user").get("externalId")
                        for user in role.get("usersComplements").get("items")
                    ]

                    blocklistUsers.extend(blocklist_users)

            if len(item.get("subscribedApplicationGroups").get("items")) > 0:
                application_group = item.get("subscribedApplicationGroups").get("items")

                for group in application_group:
                    if len(group.get("blocklist").get("items")) > 0:
                        blocklist_users = [
                            user.get("externalId")
                            for user in group.get("blocklist").get("items")
                        ]

                        blocklistUsers.extend(blocklist_users)

                    if len(group.get("blocklistRoles").get("items")) > 0:
                        for role in group.get("blocklistRoles").get("items"):
                            blocklist_users = [
                                user.get("userAzureAttribute")
                                .get("user")
                                .get("externalId")
                                for user in role.get("usersComplements").get("items")
                            ]

                            blocklistUsers.extend(blocklist_users)

            item["blocklistUsers"] = blocklistUsers

        return templates