from typing import List, Any, Optional
from cognite.client import CogniteClient
from app.core.env_variables import EnvVariables
from app.core.graphql_client import GraphQLClient
import app.core as core

ENTITY = "NotificationType"


class NotificationTypeRepository:
    def __init__(
        self,
        cognite_client: CogniteClient,
        graphql_client: GraphQLClient,
        env_variables: EnvVariables,
    ):
        self._cognite_client = cognite_client
        self._graphql_client = graphql_client
        self._fdm_model_space = env_variables.cognite.fdm_model_space
        self._fdm_instances_space = env_variables.spaces.ntf_instance_space

    def find_by_filter(self, filter: Any = None) -> List[Any]:
        items = self._graphql_client.query(
            core.queries.notification_types.notification_type_list,
            "listNotificationType",
            filter,
        )
        return items

    def find_type_in_templates(self, filter: Any = None) -> List[Any]:
        results = self._graphql_client.query_unlimited(
            core.queries.notification_types.list_type_in_templates,
            "listNotificationTemplate",
            filter,
        )
        items = [
            result.get("notificationType").get("externalId")
            for result in results
            if result.get("notificationType") is not None
        ]
        return items
