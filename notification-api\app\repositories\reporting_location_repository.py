from typing import Annotated
from cognite.client import CogniteClient
from app.core.env_variables import EnvVariables
from app.core.graphql_client import GraphQLClient
from app.core.settings import Settings
import app.core as core
import app.utils as Utils
from fastapi import Depends
import app.models as models
from cognite.client.data_classes.data_modeling import (
    ViewId,
)
from cognite.client.data_classes.data_modeling.query import (
    Query,
    Select,
    NodeResultSetExpression,
    SourceSelector,
    InstanceSort,
)
from cognite.client.data_classes.filters import Equals, HasData, And, In
from app.core.cache_global import get_cache_instance


class ReportingLocationRepository:
    def __init__(
        self,
        cognite_client: CogniteClient,
        graphql_client: GraphQLClient,
        env_variables: EnvVariables,
    ):
        settings = Settings()
        self._data_model_id = settings.data_model_id
        self._cognite_client = cognite_client
        self._graphql_client = graphql_client
        self.env_variables = env_variables

    def find_by_unit_filter(
        self,
        params: Annotated[
            dict, Depends(models.reporting_location_model.common_request_params)
        ],
    ):

        _cache = get_cache_instance()
        cognite_views = _cache.get("cognite_views")

        reporting_location_view = Utils.cognite.find_view_by_external_id(
            cognite_views[core.env.spaces.asset_hierarcy_model_space],
            core.env.cognite_entities.reporting_location,
        )

        reporting_location_cursor = None
        has_cursor = True
        response_query = None
        response = []

        views = {
            "reporting_location": reporting_location_view.version,
        }
        units = [
            {
                "externalId": unit,
                "space": core.env.spaces.assethierarcy_instace_space,
            }
            for unit in params.get("units")
        ]

        query = Query(
            with_={
                "reporting_location": NodeResultSetExpression(
                    filter=And(
                        Equals(reporting_location_view.as_property_ref("isActive"), True),
                        In(reporting_location_view.as_property_ref("reportingUnit"), units)
                    ),
                    limit=10000,
                    chain_to="destination",
                    direction="outwards",
                ),
            },
            select={
                "reporting_location": Select(
                    [
                        SourceSelector(
                            ViewId(
                                core.env.spaces.asset_hierarcy_model_space,
                                core.env.cognite_entities.reporting_location,
                                reporting_location_view.version,
                            ),
                            ["name", "description", "isActive", "reportingUnit"],
                        )
                    ],
                    sort=[InstanceSort(reporting_location_view.as_property_ref("name"))],
                    limit=10000,
                ),
            },
            cursors={
                "reporting_location": reporting_location_cursor,
            },
        )
 
        while has_cursor:
            response_query = self._cognite_client.data_modeling.instances.query(query)
            reportingLocations = self.format_response(response_query, views)

            if len(reportingLocations) > 0:
                response.extend(reportingLocations)

            if response_query:
                has_cursor = False
                cursor_keys = [
                    "reporting_location",
                ]

                for key in cursor_keys:
                    if key in response_query.cursors and len(response_query[key]) == 10000:
                        query.cursors[key] = response_query.cursors[key]
                        has_cursor = True
            else:
                has_cursor = False 

        return response

    def format_response(self, response, views):
        reporting_location_result = response["reporting_location"].dump()

        reporting_location = [
            {
                'externalId': location.get("externalId", ""),
                'space': location.get("space", ""),
                'name': location.get("properties", {}).get(
                        core.env.spaces.asset_hierarcy_model_space, {}
                    ).get(f"{core.env.cognite_entities.reporting_location}/{views['reporting_location']}", {}).get("name", ""),
                'description': location.get("properties", {}).get(
                        core.env.spaces.asset_hierarcy_model_space, {}
                    ).get(f"{core.env.cognite_entities.reporting_location}/{views['reporting_location']}", {}).get("description", ""),
            }
            for location in reporting_location_result
        ]

        return reporting_location