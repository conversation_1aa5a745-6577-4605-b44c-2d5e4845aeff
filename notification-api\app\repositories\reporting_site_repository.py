from cognite.client import Cognite<PERSON>lient
from app.core.env_variables import EnvVariables
from app.core.graphql_client import GraphQLClient
from app.core.settings import Settings
import app.core as core
from typing import Optional
from app.core.cache_global import get_cache_instance


class ReportingSiteRepository:
    def __init__(
        self,
        cognite_client: CogniteClient,
        graphql_client: GraphQLClient,
        env_variables: EnvVariables,
    ):
        settings = Settings()
        self._data_model_id = settings.data_model_id
        self._cognite_client = cognite_client
        self._graphql_client = graphql_client
        self.env_variables = env_variables

    def find(self, type_of_filter: str, id: Optional[str] = None):

        _cache = get_cache_instance()
        _cache_reporting_sites = _cache.get("reporting_sites")

        return (
            _cache_reporting_sites
            if type_of_filter == "findAll"
            else [site for site in _cache_reporting_sites if site["externalId"] == id]
        )

    def format_response(self, response, views):
        reporting_site_result = response["reporting_site"].dump()
        reporting_units_result = response["reporting_units"].dump()
        reporting_units_infos_result = response["reporting_units_infos"].dump()
        reporting_sites = []

        grouped_reporting_units_by_site = {}
        for item in reporting_units_result:
            start_node = item["startNode"]["externalId"]
            end_node = item["endNode"]["externalId"]

            if start_node not in grouped_reporting_units_by_site:
                grouped_reporting_units_by_site[start_node] = []

            grouped_reporting_units_by_site[start_node].append(end_node)

        for site in reporting_site_result:
            site_id = site.get("externalId", "")
            site_properties = (
                site.get("properties", {})
                .get(core.env.spaces.asset_hierarcy_model_space, {})
                .get(
                    f"{core.env.cognite_entities.reporting_site}/{views['reporting_site']}",
                    {},
                )
            )
            reporting_units_ids = grouped_reporting_units_by_site.get(site_id, [])
            filtered_units = [
                unit
                for unit in reporting_units_infos_result
                if unit["externalId"] in reporting_units_ids
            ]

            reporting_units = [
                {
                    "externalId": unit.get("externalId", ""),
                    "space": unit.get("space", ""),
                    "name": unit.get("properties", {})
                    .get(core.env.spaces.asset_hierarcy_model_space, {})
                    .get(
                        f"{core.env.cognite_entities.reporting_unit}/{views['reporting_unit']}",
                        {},
                    )
                    .get("name", ""),
                    "description": unit.get("properties", {})
                    .get(core.env.spaces.asset_hierarcy_model_space, {})
                    .get(
                        f"{core.env.cognite_entities.reporting_unit}/{views['reporting_unit']}",
                        {},
                    )
                    .get("description", ""),
                }
                for unit in filtered_units
                if len(filtered_units) > 0
            ]

            reporting_sites.append(
                {
                    "externalId": site_id,
                    "space": site.get("space", ""),
                    "name": site_properties.get("name", ""),
                    "siteCode": site_properties.get("siteCode", ""),
                    "reportingUnits": reporting_units,
                }
            )

        return reporting_sites
