from typing import List, Any
from cognite.client import CogniteClient
from app.core.env_variables import EnvVariables
from app.core.graphql_client import GraphQLClient
import app.queries as queries
from cognite.client.data_classes.data_modeling import (
    ViewId,
    PropertyId,
)
from cognite.client.data_classes.data_modeling.query import (
    Query,
    Select,
    NodeResultSetExpression,
    EdgeResultSetExpression,
    SourceSelector,
)
from cognite.client.data_classes.filters import Equals, HasData, And, Or, In
import app.utils as Utils
import app.core as core
from app.core.cache_global import get_cache_instance


class RoleRepository:
    def __init__(
        self,
        cognite_client: CogniteClient,
        graphql_client: GraphQLClient,
        env_variables: EnvVariables,
    ):
        self._cognite_client = cognite_client
        self._graphql_client = graphql_client
        self.env_variables = env_variables

    def find_by_user(self, filter: Any):
        results = self._graphql_client.query(
            queries.roles.role_by_user, "listUserComplement", filter
        )

        result = []

        items = [result.get("userRoleSite") for result in results]
        if len(items[0].get("items")) > 0:
            result_items = items[0].get("items")
            result = [item.get("externalId") for item in result_items]

        return result

    def find_role_name_and_application(
        self, term: str, application_external_id: str, limit: int = 10
    ) -> List[Any]:
        lowercase_term = term.lower()
        separate_term = lowercase_term.split()
        capitalized_term = [word.capitalize() for word in separate_term]
        result_string = " ".join(capitalized_term)

        _cache = get_cache_instance()
        cognite_views = _cache.get("cognite_views")

        um_views = cognite_views[core.env.spaces.um_model_space]

        notification_user_role_site_view = Utils.cognite.find_view_by_external_id(
            cognite_views[core.env.cognite.fdm_model_space],
            core.env.cognite_entities.notification_user_role_site,
        )

        role_view = Utils.cognite.find_view_by_external_id(
            um_views,
            core.env.cognite_entities.role,
        )

        user_complement_view = Utils.cognite.find_view_by_external_id(
            um_views,
            core.env.cognite_entities.user_complement,
        ).version

        user_azure_attribute_view = Utils.cognite.find_view_by_external_id(
            um_views,
            core.env.cognite_entities.user_azure_attribute,
        ).version

        user_view = Utils.cognite.find_view_by_external_id(
            um_views,
            core.env.cognite_entities.user,
        )

        reporting_site_view = Utils.cognite.find_view_by_external_id(
            cognite_views[core.env.spaces.asset_hierarcy_model_space],
            core.env.cognite_entities.reporting_site,
        )

        views = {
            "notification_user_role_site": notification_user_role_site_view.version,
            "role": role_view.version,
            "user_complement": user_complement_view,
            "user_azure_attribute": user_azure_attribute_view,
            "user": user_view.version,
            "reporting_site": reporting_site_view.version,
        }

        roles_cursor = None
        role_site_from_roles_cursor = None
        user_complement_in_role_site_cursor = None
        user_complement_in_roles_cursor = None
        user_azure_attribute_in_roles_cursor = None
        user_in_roles_cursor = None
        reporting_site_info_cursor = None
        has_cursor = True
        response_query = None
        roles_results_accumulated = []

        result_string_lower = result_string.lower()

        results_from_search = self._cognite_client.data_modeling.instances.search(
            view=ViewId(
                space=core.env.spaces.um_model_space,
                external_id=core.env.cognite_entities.role,
                version=role_view.version,
            ),
            properties=["name"],
            limit=1000,
            query=result_string,
        )
        
        if not results_from_search:
            return []
        
        roles = Utils.cognite.from_node_list(results_from_search)

        roles_filtered = [
            role.get("externalId")
            for role in roles
            if result_string_lower in role["name"].lower()
        ]

        if not roles_filtered:
            return []
        
        query = Query(
            with_={
                "roles": NodeResultSetExpression(
                    filter=And(
                        Or(
                            And(
                                Equals(
                                    role_view.as_property_ref("roleCategory"),
                                    {
                                        "externalId": "RoleApplication",
                                        "space": core.env.spaces.um_instance_space,
                                    },
                                ),
                                Equals(
                                    role_view.as_property_ref("application"),
                                    {
                                        "externalId": application_external_id,
                                        "space": core.env.spaces.um_instance_space,
                                    },
                                ),
                                In(["node", "externalId"], roles_filtered),
                            ),
                            And(
                                Equals(
                                    role_view.as_property_ref("roleCategory"),
                                    {
                                        "externalId": "RoleSite",
                                        "space": core.env.spaces.um_instance_space,
                                    },
                                ),
                                In(["node", "externalId"], roles_filtered),
                            ),
                        ),
                        Equals(["node", "space"], core.env.spaces.um_instance_space),
                    ),
                    limit=10000,
                    chain_to="destination",
                    direction="outwards",
                ),
                "role_site_from_roles": NodeResultSetExpression(
                    from_="roles",
                    limit=10000,
                    chain_to="destination",
                    direction="inwards",
                    through=PropertyId(
                        ViewId(
                            core.env.cognite.fdm_model_space,
                            core.env.cognite_entities.notification_user_role_site,
                            notification_user_role_site_view.version,
                        ),
                        "role",
                    ),
                ),
                # getting the site information from the roles_site
                "reporting_site_info": NodeResultSetExpression(
                    from_="role_site_from_roles",
                    limit=10000,
                    chain_to="destination",
                    direction="outwards",
                    through=PropertyId(
                        ViewId(
                            core.env.cognite.fdm_model_space,
                            core.env.cognite_entities.notification_user_role_site,
                            notification_user_role_site_view.version,
                        ),
                        "reportingSite",
                    ),
                ),
                # getting the users complements from the roles
                "user_complement_in_role_site": EdgeResultSetExpression(
                    from_="role_site_from_roles",
                    limit=10000,
                    direction="inwards",
                    chain_to="destination",
                    filter=Equals(
                        ["edge", "type"],
                        {
                            "space": core.env.spaces.um_model_space,
                            "externalId": "UserComplement.userRoleSite",
                        },
                    ),
                    node_filter=HasData(
                        views=[
                            ViewId(
                                core.env.spaces.um_model_space,
                                core.env.cognite_entities.user_complement,
                                user_complement_view,
                            )
                        ]
                    ),
                ),
                "user_complement_in_roles": NodeResultSetExpression(
                    from_="user_complement_in_role_site",
                    limit=10000,
                ),
                # getting the user azure attribute from users complements from the roles
                "user_azure_attribute_in_roles": NodeResultSetExpression(
                    from_="user_complement_in_roles",
                    limit=10000,
                    chain_to="destination",
                    direction="outwards",
                    through=PropertyId(
                        ViewId(
                            core.env.spaces.um_model_space,
                            core.env.cognite_entities.user_complement,
                            user_complement_view,
                        ),
                        "userAzureAttribute",
                    ),
                ),
                "user_in_roles": NodeResultSetExpression(
                    from_="user_azure_attribute_in_roles",
                    limit=10000,
                    chain_to="destination",
                    direction="outwards",
                    through=PropertyId(
                        ViewId(
                            core.env.spaces.um_model_space,
                            core.env.cognite_entities.user_azure_attribute,
                            user_azure_attribute_view,
                        ),
                        "user",
                    ),
                    filter=Equals(user_view.as_property_ref("deleted"), False),
                ),
            },
            select={
                "roles": Select(
                    [
                        SourceSelector(
                            ViewId(
                                core.env.spaces.um_model_space,
                                core.env.cognite_entities.role,
                                role_view.version,
                            ),
                            ["*"],
                        )
                    ],
                    limit=10000,
                ),
                "role_site_from_roles": Select(
                    [
                        SourceSelector(
                            ViewId(
                                core.env.cognite.fdm_model_space,
                                core.env.cognite_entities.notification_user_role_site,
                                notification_user_role_site_view.version,
                            ),
                            ["*"],
                        )
                    ],
                    limit=10000,
                ),
                "reporting_site_info": Select(
                    [
                        SourceSelector(
                            ViewId(
                                core.env.spaces.asset_hierarcy_model_space,
                                core.env.cognite_entities.reporting_site,
                                reporting_site_view.version,
                            ),
                            ["isActive", "name", "siteCode"],
                        )
                    ],
                    limit=10000,
                ),
                "user_complement_in_role_site": Select(limit=10000),
                "user_complement_in_roles": Select(
                    [
                        SourceSelector(
                            ViewId(
                                core.env.spaces.um_model_space,
                                core.env.cognite_entities.user_complement,
                                user_complement_view,
                            ),
                            ["searchTags", "userAzureAttribute"],
                        )
                    ],
                    limit=10000,
                ),
                "user_azure_attribute_in_roles": Select(
                    [
                        SourceSelector(
                            ViewId(
                                core.env.spaces.um_model_space,
                                core.env.cognite_entities.user_azure_attribute,
                                user_azure_attribute_view,
                            ),
                            ["user"],
                        )
                    ],
                    limit=10000,
                ),
                "user_in_roles": Select(
                    [
                        SourceSelector(
                            ViewId(
                                core.env.spaces.um_model_space,
                                core.env.cognite_entities.user,
                                user_view.version,
                            ),
                            ["deleted", "email", "firstName", "lastName"],
                        )
                    ],
                    limit=10000,
                ),
            },
            cursors={
                "roles": roles_cursor,
                "role_site_from_roles": role_site_from_roles_cursor,
                "user_complement_in_role_site": user_complement_in_role_site_cursor,
                "user_complement_in_roles": user_complement_in_roles_cursor,
                "user_azure_attribute_in_roles": user_azure_attribute_in_roles_cursor,
                "user_in_roles": user_in_roles_cursor,
                "reporting_site_info": reporting_site_info_cursor,
            },
        )

        while has_cursor and len(roles_filtered) > 0:
            response_query = self._cognite_client.data_modeling.instances.query(query)
            roles_result = self.format_query_response(response_query, views)
            if roles_result:
                roles_results_accumulated.extend(roles_result)

            if response_query:
                has_cursor = False
                cursor_keys = [
                    "roles",
                    "role_site_from_roles",
                    "user_complement_in_role_site",
                    "user_complement_in_roles",
                    "user_azure_attribute_in_roles",
                    "user_in_roles",
                    "reporting_site_info",
                ]

                for key in cursor_keys:
                    if (
                        key in response_query.cursors
                        and len(response_query[key]) == 10000
                    ):
                        query.cursors[key] = response_query.cursors[key]
                        has_cursor = True
            else:
                has_cursor = False

        return roles_results_accumulated

    def find_role_by_filter(self, filter) -> List[Any]:
        return self._graphql_client.query(
            core.queries.role_queries.SEARCH_ROLESITEUSERS_BY_NAME_AND_APP,
            "listNotificationUserRoleSite",
            filter,
        )
    
    def format_query_response(self, response, views: dict):

        roles_result = response["roles"].dump()
        role_site_from_roles_result = response["role_site_from_roles"].dump()
        user_complement_result = response["user_complement_in_roles"].dump()
        user_azure_attribute_result = response["user_azure_attribute_in_roles"].dump()
        user_in_roles = response["user_in_roles"].dump()
        reporting_site_result = response["reporting_site_info"].dump()
        subscribed_roles = []

        for role_site in role_site_from_roles_result:
            properties = (
                role_site.get("properties", {})
                .get(core.env.cognite.fdm_model_space, {})
                .get(
                    f"{core.env.cognite_entities.notification_user_role_site}/{views['notification_user_role_site']}",
                    {},
                )
            )
            reporting_site_id = properties.get("reportingSite", {}).get(
                "externalId", ""
            )

            role_external_id = properties.get("role", {}).get("externalId", "")
            filter_role = [
                item for item in roles_result if item["externalId"] == role_external_id
            ][0]

            filter_role_properties = (
                filter_role.get("properties", {})
                .get(core.env.spaces.um_model_space, {})
                .get(f"{core.env.cognite_entities.role}/{views['role']}", {})
            )

            filter_reporing_site = [
                site
                for site in reporting_site_result
                if reporting_site_id and site["externalId"] == reporting_site_id
            ]
            reporting_site_properties = (
                filter_reporing_site[0]
                .get("properties", {})
                .get(core.env.spaces.asset_hierarcy_model_space, {})
                .get(
                    f"{core.env.cognite_entities.reporting_site}/{views['reporting_site']}",
                    {},
                )
                if len(filter_reporing_site) > 0
                else {}
            )

            filter_users = [
                user
                for user in user_complement_result
                if role_site.get("externalId", {})
                in user.get("properties", {})
                .get(core.env.spaces.um_model_space, {})
                .get(
                    f"{core.env.cognite_entities.user_complement}/{views['user_complement']}",
                    {},
                )
                .get("searchTags", [])
            ]
            users_complement = [
                user.get("properties", {})
                .get(core.env.spaces.um_model_space, {})
                .get(
                    f"{core.env.cognite_entities.user_complement}/{views['user_complement']}",
                    {},
                )
                .get("userAzureAttribute", {})
                .get("externalId", "")
                for user in filter_users
            ]
            user_azure_attribute = [
                user.get("properties", {})
                .get(core.env.spaces.um_model_space, {})
                .get(
                    f"{core.env.cognite_entities.user_azure_attribute}/{views['user_azure_attribute']}",
                    {},
                )
                .get("user", {})
                .get("externalId", "")
                for user in user_azure_attribute_result
                if user["externalId"] in users_complement
            ]
            users_in_role = [
                {
                    "externalId": user.get("externalId", ""),
                    "email": user.get("properties", {})
                    .get(core.env.spaces.um_model_space, {})
                    .get(f"{core.env.cognite_entities.user}/{views['user']}", {})
                    .get("email", ""),
                    "firstName": user.get("properties", {})
                    .get(core.env.spaces.um_model_space, {})
                    .get(f"{core.env.cognite_entities.user}/{views['user']}", {})
                    .get("firstName", ""),
                    "lastName": user.get("properties", {})
                    .get(core.env.spaces.um_model_space, {})
                    .get(f"{core.env.cognite_entities.user}/{views['user']}", {})
                    .get("lastName", ""),
                }
                for user in user_in_roles
                if user["externalId"] in user_azure_attribute
            ]

            site = (
                {
                    "externalId": filter_reporing_site[0].get("externalId", ""),
                    "space": filter_reporing_site[0].get("space", ""),
                    "name": reporting_site_properties.get("name", ""),
                    "siteCode": reporting_site_properties.get("siteCode", ""),
                }
                if len(filter_reporing_site) > 0
                else {}
            )

            site_code = reporting_site_properties.get("siteCode", "")
            role_name = filter_role_properties.get("name", "")
            name_with_site_code = (
                f"({site_code}) {role_name}" if site_code else role_name
            )

            subscribed_roles.append(
                {
                    "externalId": role_site.get("externalId", ""),
                    "space": role_site.get("space", ""),
                    "name": name_with_site_code,
                    "users": sorted(
                        users_in_role, key=lambda x: x["externalId"].lower()
                    ),
                    "site": site,
                }
            )

        sorted_roles = []
        sorted_roles = sorted(subscribed_roles, key=lambda x: x["name"])

        return sorted_roles
