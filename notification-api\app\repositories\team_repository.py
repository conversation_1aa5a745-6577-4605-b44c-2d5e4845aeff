from typing import List, Any, Annotated
from cognite.client import CogniteClient
from app.core.env_variables import EnvVariables
from app.core.graphql_client import GraphQLClient
from app.core.settings import Settings
import app.queries as queries
from fastapi import Depends
import app.models as models
from app.core.cache_global import get_cache_instance


class TeamRepository:
    def __init__(
        self,
        cognite_client: CogniteClient,
        graphql_client: GraphQLClient,
        env_variables: EnvVariables,
    ):
        settings = Settings()
        self._data_model_id = settings.data_model_id
        self._cognite_client = cognite_client
        self._graphql_client = graphql_client
        self.env_variables = env_variables

    def find_by_filter(
        self, params: Annotated[dict, Depends(models.team_model.common_request_params)]
    ):
        _cache = get_cache_instance()
        _cache_teams = _cache.get("teams") or []

        def site_matches(team):
            site_id = params.get("site")
            return (
                site_id
                and team.get("reportingSite")
                and team["reportingSite"].get("externalId") == site_id
            )

        def units_match(team):
            units = params.get("units")
            if not units:
                return False
            reporting_units = team.get("reportingUnits", {}).get("items", [])
            return any(unit.get("externalId") in units for unit in reporting_units)

        result_teams = []
        for team in _cache_teams:
            if params.get("site") and not site_matches(team):
                continue
            if params.get("units") and not units_match(team):
                continue
            result_teams.append(team)

        return result_teams

    def build_filter_variables(
        self, params: Annotated[dict, Depends(models.team_model.common_request_params)]
    ):
        unitFilter = None
        filter = {"reportingSite": {"externalId": {"eq": params.get("site")}}}

        if len(params.get("units")) > 0:
            unitFilter = {"externalId": {"in": params.get("units")}}

        return {"filter": filter, "unitFilter": unitFilter}
