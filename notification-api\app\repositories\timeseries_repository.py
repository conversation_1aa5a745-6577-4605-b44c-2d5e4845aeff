import datetime
from typing import Any, List
from cognite.client import CogniteClient

class TimeseriesRepository:
    def __init__(
        self,
        cognite_client: CogniteClient
    ):
        self._cognite_client = cognite_client

    def find_timeserie_by_externalId(self, externalId: str):
        return self._cognite_client.time_series.retrieve(external_id=externalId)
    
    def find_asset_by_id(self, assetId: str):
        return self._cognite_client.assets.retrieve(id=assetId)
            
    def find_dataset_by_id(self, datasetId: str):
        return self._cognite_client.data_sets.retrieve(datasetId)
    
    def find_datapoints_by_filter(self, 
                                    timeseriesId: str, 
                                    granularity: str = '1h', 
                                    startPeriod: datetime = None, 
                                    endPeriod: datetime = None, 
                                    limit: int = 10000):
        return self._cognite_client.time_series.data.retrieve(
                aggregates=['average'],
                granularity=granularity,
                start=startPeriod,
                end=endPeriod,
                limit=limit,
                id=timeseriesId
            )
    
    def find_latest_datapoint(self, timeseriesId: str):
        return self._cognite_client.time_series.data.retrieve_latest(before='now', id=timeseriesId)