from typing import List, Any, Annotated
from cognite.client import Cognite<PERSON>lient
from fastapi import Depends
import app.models as models
import app.models.users_model as users_model
import app.queries as queries
from app.core.env_variables import EnvVariables
from app.core.graphql_client import GraphQLClient
from app.core.settings import Settings


class UserRepository:
    def __init__(
        self,
        cognite_client: CogniteClient,
        graphql_client: GraphQLClient,
        env_variables: EnvVariables,
    ):
        self._cognite_client = cognite_client
        self._graphql_client = graphql_client
        self._fdm_model_space = env_variables.cognite.fdm_model_space
        self._fdm_instances_space = env_variables.spaces.ntf_instance_space
        self.um_space = env_variables.spaces.um_instance_space

        settings = Settings()
        self._data_model_id = settings.data_model_id

    def find(self, _filter: Any) -> List[Any]:
        items = self._graphql_client.query_unlimited(
            queries.users.user_by_filter, "listUser", _filter
        )

        return items

    def find_by_email(self, email: str, limit: int = 10) -> List[Any]:

        filter_user = {
            "and": [
                {"active": {"eq": True}},
                {"isHidden": {"eq": False}},
                {
                    "or": [
                        {"deleted": {"eq": False}},
                        {"deleted": {"isNull": True}},
                    ]
                },
            ]
        }

        variables = {"query": email, "first": limit, "filter": filter_user}
        items = self._graphql_client.query(
            queries.users.search_user_by_email, "searchUser", variables
        )

        return items

    def find_by_tags(
        self,
        _filter: Annotated[
            dict, Depends(models.users_model.common_user_request_params)
        ],
    ):
        variables = self.build_filter_variables(_filter)
        items = self._cognite_client.data_modeling.graphql.query(
            self._data_model_id,
            query=queries.users.list_user_by_tags,
            variables=variables,
        )

        return items

    def build_filter_variables(
        self, _filter: Annotated[dict, Depends(users_model.common_user_request_params)]
    ):
        team_filters = None
        user_prefix = _filter.get("user")
        separate_user_prefix = user_prefix.split()
        capitalized_user_prefix = [word.capitalize() for word in separate_user_prefix]
        result_user_prefix = " ".join(capitalized_user_prefix)
        user_complement_filters = [
            {
                "or": [
                    {"userAzureAttribute": {"user": {"deleted": {"eq": False}}}},
                    {"userAzureAttribute": {"user": {"deleted": {"isNull": True}}}},
                ]
            },
            {
                "userAzureAttribute": {
                    "user": {
                        "and": [
                            {"active": {"eq": True}},
                            {"isHidden": {"eq": False}},
                            {
                                "or": [
                                    {"email": {"prefix": user_prefix}},
                                    {"firstName": {"prefix": result_user_prefix}},
                                ]
                            },
                        ]
                    }
                }
            },
        ]

        if _filter.get("searchTags"):
            user_complement_filters.append(
                {"searchTags": {"containsAll": _filter.get("searchTags")}}
            )

        if _filter.get("teams"):
            team_filters = {"externalId": {"in": _filter.get("teams")}}

        return {
            "after": _filter.get("cursor", ""),
            "pageSize": _filter.get("page_size", ""),
            "userComplementFilter": {"and": user_complement_filters},
            "teamFilter": team_filters,
        }
