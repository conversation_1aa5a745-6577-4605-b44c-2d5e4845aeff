from fastapi import APIRouter, Depends
from typing import List
import app.core as core
from app.core.authorization import JWT<PERSON>earer, get_user

router: APIRouter = APIRouter()


@router.post("")
def get_application(codes: core.models.ApplicationCodeModel, 
                    services: core._ServiceList = Depends(core.services),
                    token: JWTBearer = Depends(get_user)) -> List[core.models.ApplicationModel]:
    return services.application.find(codes)