from typing import Optional
from fastapi import APIRouter, Body, Depends
import app.utils as utils

router:APIRouter = APIRouter()

@router.post("/generate")
def genreate_cron_expression(schedule: utils.scheduleModel = Body(..., example={
    "for_test": "visit: https://ncrontab.swimburger.net/",
    "schedule_type": "minute",
    "time": "14:00",
    "end_time": "15:00",
    "interval": 15,
    "day_of_week": [
        "Monday",
        "Wednesday",
        "Friday"
    ],
    "day_of_month": "20",
    "months": [
        1,
        3,
        5,
        7,
        9,
        11
    ]},
)):
    return utils.cron.generate(schedule)

@router.get("/to_model")
def cron_to_model(cron_expression: str):
    return utils.cron.describe(cron_expression)

@router.get("/next_run")
def next_run(cron_expression: str, date_time: Optional[str] = None, qtd: Optional[int] = 1):
    return utils.cron.next(cron_expression, date_time, qtd)
