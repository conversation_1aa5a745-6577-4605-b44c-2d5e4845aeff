from typing import List, Optional
from fastapi import APIRouter, Depends
import app.core as core
from app.core.authorization import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, get_user

router: APIRouter = APIRouter()


@router.post("")
def save_notification_application_group(
    request: core.models.NotificationApplicationGroupCreateModel,
    services: core._ServiceList = Depends(core.services),
    user: JWTBearer = Depends(get_user),
):
    user_id = user.get("preferred_username")
    return services.notification_application_group.save(request, user_id)


@router.get("/{application}")
def get_notification_application_group(
    application: str,
    services: core._ServiceList = Depends(core.services),
):
    return services.notification_application_group.get_by_application(application)


@router.delete("/{external_id}")
def delete_notification_template(
    external_id: str,
    services: core._ServiceList = Depends(core.services),
):
    return services.notification_application_group.delete(external_id)
