from fastapi import APIRouter, Depends
from typing import List
import app.core as core
from app.core.authorization import JWT<PERSON>earer, get_user

router:APIRouter = APIRouter()

@router.get("")
def get_all_channels(services: core._ServiceList = Depends(core.services),
                     token: JWTBearer = Depends(get_user)) -> List[core.models.NotificationChannelModel]:
    return services.channels.findAll()