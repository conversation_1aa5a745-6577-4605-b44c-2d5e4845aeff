from typing import List, Optional
from fastapi import APIRouter, Depends
import app.core as core
from app.core.authorization import J<PERSON><PERSON><PERSON><PERSON><PERSON>, get_user

router: APIRouter = APIRouter()


@router.get("/{notification_type_external_id}/last/{count}")
def get_notification_event(
    notification_type_external_id: str,
    count: str,
    services: core._ServiceList = Depends(core.services),
    token: JWTBearer = Depends(get_user)
) -> List[core.models.NotificationEventModel]:
    response = services.notification_event.get_latest_events(notification_type_external_id)
    return response


@router.post("")
def save_notification_event(
    notification_event: List[core.models.NotificationRawEventModel],
    services: core._ServiceList = Depends(core.services),
    token: JWTBearer = Depends(get_user)
):
    hasNotificationReference = notification_event[0].notificationReference
    event_app_id = notification_event[0].appId
    app_id =  event_app_id if hasNotificationReference else token["appid"] if "appid" in token else token["aud"]
    return services.notification_event.save_list(app_id, notification_event)
