from typing import Optional
from fastapi import APIRouter, Depends
import app.core as core
from app.core.authorization import J<PERSON><PERSON><PERSON><PERSON><PERSON>, get_user

router: APIRouter = APIRouter()


@router.post("")
async def get_notification_on_screen(
    filter: core.models.NotificationOnScreenFilterModel,
    pagination: Optional[core.models.PaginationRequestModel] = None,
    services: core._ServiceList = Depends(core.services),
    user: JWTBearer = Depends(get_user),
):
    user_id = user.get("preferred_username")
    return await services.notification_on_screen_service.get_notification_on_screen_async(
        filter, pagination, user_id
    )


@router.get("/notification_not_visualized_amount")
async def get_notifications_not_visualized_amount(
    services: core._ServiceList = Depends(core.services),
    user: JWTBearer = Depends(get_user),
):
    user_id = user.get("preferred_username")
    return await services.notification_on_screen_service.get_notifications_not_visualized_amount_async(
        user_id
    )


@router.post("/notification_last_access")
def update_notification_last_access(
    services: core._ServiceList = Depends(core.services),
    user: JWTBearer = Depends(get_user),
):
    user_id = user.get("preferred_username")
    return services.notification_last_access_service.update_notification_last_access(
        user_id
    )


@router.get("/notification_last_access")
def get_notification_last_access(
    services: core._ServiceList = Depends(core.services),
    user: JWTBearer = Depends(get_user),
):
    user_id = user.get("preferred_username")
    return services.notification_on_screen_service.get_last_access(user_id)


@router.get("/chat/{external_id}")
async def get_chat_of_notification(
    external_id: str,
    services: core._ServiceList = Depends(core.services),
    user: JWTBearer = Depends(get_user),
):
    return await services.notification_on_screen_service.get_chat_async(external_id)


@router.post("/chat/{external_id}")
def create_chat_of_notification(
    external_id: str,
    comment: core.models.NotificationTextRequestModel,
    services: core._ServiceList = Depends(core.services),
    user: JWTBearer = Depends(get_user),
):
    user_id = user.get("preferred_username")
    return services.notification_on_screen_service.create_chat(
        external_id,
        user_id,
        comment.comment,
    )


@router.delete("/chat/{external_id}")
def delete_chat_of_notification(
    external_id: str,
    services: core._ServiceList = Depends(core.services),
    user: JWTBearer = Depends(get_user),
):
    user_id = user.get("preferred_username")
    return services.notification_on_screen_service.delete_chat(external_id, user_id)
