from typing import List, Optional, Annotated
from fastapi import APIRouter, Depends, Query
import app.core as core
from app.core.authorization import J<PERSON><PERSON><PERSON>ear<PERSON>, get_user

router: APIRouter = APIRouter()

@router.get("")
def get_templates(
    request: Annotated[dict, Depends(core.models.notification_template_model.common_template_request_params)],
    services: core._ServiceList = Depends(core.services),
    user: JWTBearer = Depends(get_user),
):
    result = services.notification_template.get_templates(request, user)
    return result

@router.get("/by_notification_application_group_id/{groupe_external_id}")
def get_templates_by_notification_application_group_id(
    groupe_external_id: str,
    services: core._ServiceList = Depends(core.services),
    user: JWTBearer = Depends(get_user),
) -> List[core.models.NotificationTemplateByApplicationGroupResponseModel]:
    return services.notification_template.get_by_notification_application_group_id(
        groupe_external_id
    )


@router.get("/{template_external_id}/{isAdminEdition}")
def get_template_by_external_id(
    template_external_id: str,
    isAdminEdition: bool = False,
    services: core._ServiceList = Depends(core.services),
    user: JWTBearer = Depends(get_user),
):
    user_id = user.get("preferred_username")
    return services.notification_template.find_by_id(
        template_external_id, user_id, isAdminEdition
    )


@router.post("")
def save_notification_templates(
    request: core.models.NotificationTemplateCreateRequestModel,
    services: core._ServiceList = Depends(core.services),
    user: JWTBearer = Depends(get_user),
):
    user_id = user.get("preferred_username")
    return services.notification_template.save(
        request.notificationTemplate, user_id, request.isAdminEdition
    )


@router.delete("/{template_external_id}/{isAdminEdition}")
def delete_notification_template(
    template_external_id: str,
    isAdminEdition: bool,
    services: core._ServiceList = Depends(core.services),
    user: JWTBearer = Depends(get_user),
):
    user_id = user.get("preferred_username")
    return services.notification_template.delete(
        template_external_id, isAdminEdition, user_id
    )


@router.get("/send_to")
def send_to_notification_type(
    notification_type_external_id: str | None = Query(
        alias="notificationTypeExternalId", default=None
    ),
    services: core._ServiceList = Depends(core.services),
    user: JWTBearer = Depends(get_user),
):
    return services.notification_template.get_send_to_by_notification_type(
        notification_type_external_id
    )
