from typing import List, Annotated
from fastapi import APIRouter, Depends
import app.core as core
from app.core.authorization import J<PERSON><PERSON><PERSON><PERSON><PERSON>, get_user
from fastapi import Depends

router:APIRouter = APIRouter()

@router.get("")
def get_notification_type_by_id(
    id: str,
    services: core._ServiceList = Depends(core.services),
) -> List[core.models.NotificationTypeModel]:
    
    filter_notification_type = {}
    filter_notification_type["filter"] = {
        "externalId": {"eq": id}
    }
    return services.notification_type.find_by_filter(filter_notification_type)