from typing import List, Annotated
from fastapi import APIRouter, Depends
import app.core as core
from app.core.authorization import J<PERSON><PERSON><PERSON><PERSON><PERSON>, get_user
from fastapi import Depends

router:APIRouter = APIRouter()

@router.get("")
def get_reporting_location_by_filter(
    request:  Annotated[dict, Depends(core.models.reporting_location_model.common_request_params)],
    services: core._ServiceList = Depends(core.services),
) -> List[core.models.ReportingLocationModel]:
    return services.reporting_location.find_by_filter(request)