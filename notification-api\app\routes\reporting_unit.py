from typing import List
from fastapi import APIRouter, Depends
import app.core as core
from app.core.authorization import JWTBearer, get_user

router:APIRouter = APIRouter()

@router.get("/{reporting_site}")
def get_reporting_unit_by_site(
    reporting_site: str,
    services: core._ServiceList = Depends(core.services),
) -> List[core.models.ReportingUnitModel]:
    return services.reporting_site.find_units_by_site(reporting_site)