from typing import List
from fastapi import APIRouter, Depends
import app.core as core
from app.core.authorization import JW<PERSON><PERSON>earer, get_user

router:APIRouter = APIRouter()

@router.get("/search/{application}/{term}")
def get_roles_by_application(
    application: str, term: str,
    services: core._ServiceList = Depends(core.services),
    token: JWTBearer = Depends(get_user)
) -> List[core.models.RoleModel]:
    return services.role.find_by_term_and_app(term, application)

@router.get("/bysite/{reporting_site}")
def get_roles_by_site(
    reporting_site: str,
    services: core._ServiceList = Depends(core.services),
    token: JWTBearer = Depends(get_user)
) -> List[core.models.RoleModel]:
    return services.role.find_by_reporting_site(reporting_site)

@router.get("/byapp/{application}")
def get_roles_by_application(
    application: str,
    services: core._ServiceList = Depends(core.services),
    token: JWTBearer = Depends(get_user)
) -> List[core.models.RoleModel]:
    return services.role.find_by_application(application)