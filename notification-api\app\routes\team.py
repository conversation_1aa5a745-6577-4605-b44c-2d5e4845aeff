from typing import List, Annotated
from fastapi import APIRouter, Depends
import app.core as core
from app.core.authorization import JWT<PERSON>earer, get_user

router:APIRouter = APIRouter()

@router.get("")
def get_team(
    request: Annotated[dict, Depends(core.models.team_model.common_request_params)],
    services: core._ServiceList = Depends(core.services),
) -> List[core.models.TeamModel]:
    return services.team.find_by_filter(request)