from typing import Optional
from fastapi import APIRouter, Depends
import app.core as core
from app.core.authorization import JW<PERSON><PERSON>ear<PERSON>, get_user

router: APIRouter = APIRouter()

@router.post("/{external_id}")
def get_by_id(
    external_id: str,
    filter: core.models.RequestTimeseriesFilter,
    services: core._ServiceList = Depends(core.services),
    user: JWTBearer = Depends(get_user)
) -> core.models.ResponseTimeseriesDetails:
    return services.timeseries_service.find_details_by_externalId(
        external_id, filter.startDate, filter.endDate
    )