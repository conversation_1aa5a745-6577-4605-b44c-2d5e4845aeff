from fastapi import APIRouter, Depends
import app.core as core
from app.core.authorization import J<PERSON><PERSON><PERSON><PERSON><PERSON>, get_user
from typing import Annotated, List

router:APIRouter = APIRouter()

@router.get("/search/{term}/{application}/{limit}")
def get_search_users_by_term(
    term: str, application: str, limit: int, 
    services: core._ServiceList = Depends(core.services),
    token: JWTBearer = Depends(get_user)
) -> core.models.ResponseSearchUsersTerm:
    return services.user.find_user_by_term_and_app(term, application, limit)

@router.get("/search")
def get_search_users(
    request: Annotated[dict, Depends(core.models.users_model.common_user_request_params)],
    services: core._ServiceList = Depends(core.services),
) -> List[core.models.UserModel]:
    return services.user.find_user_by_tags(request)