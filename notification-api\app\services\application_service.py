from operator import attrgetter
from typing import List
from fastapi import HTTPException
import app.repositories as repositories
import app.core as core


class ApplicationService:
    def __init__(
        self,
        repository: repositories.ApplicationRepository,
    ):
        self.repository = repository

    def find(self, request):
        try:
            applications: List[core.models.ApplicationModel] = []

            if len(request.codes) < 1:
                return applications

            # Get applications
            items = self.repository.find(request.codes)

            if len(items) > 0:
                applications = [
                    item for item in items
                    if len(item.get("notificationTypes", [])) > 0
                ]
            
            return applications
        except Exception as e:
            raise HTTPException(status_code=400, detail=str(e))

    def exists(self, filter):
        items = self.repository.find_external_ids(filter)
        return items
