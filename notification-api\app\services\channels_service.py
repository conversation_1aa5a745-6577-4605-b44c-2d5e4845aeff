from fastapi import Depends, HTTPException
import app.repositories as repositories
import app.core as core


class ChannelsService:
    def __init__(
        self,
        repository: repositories.ChannelsRepository,
    ):
        self.repository = repository

    def findAll(self):
        try:
            items = self.repository.findAll()
            if len(items) > 0:
                channels = [
                    core.models.NotificationChannelModel.mapFromResult(item)
                    for item in items
                    if item
                ]
            return channels

        except Exception as e:
            raise HTTPException(status_code=400, detail=str(e))
        
    def exists(self, external_id: str):

        items = self.repository.findAll()

        result = [item for item in items if item.get("externalId") == external_id]

        if len(result) == 0:
            return False

        return True