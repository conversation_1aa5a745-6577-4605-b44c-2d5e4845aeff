from typing import Any, List
import app.core as core
import app.repositories as repositories
import app.models as models
import math as math
from operator import attrgetter
import app.utils as Utils


class NotificationApplicationGroupService:
    def __init__(
        self,
        repository: repositories.NotificationApplicationGroupRepository,
    ):
        self.repository = repository

    def save(
        self,
        request: models.NotificationApplicationGroupCreateModel,
        user_id: str,
    ):

        if (
            request.users is not None
            and len(request.users) == 0
            and request.externalUsers is not None
            and len(request.externalUsers) == 0
            and request.blocklist is not None
            and len(request.blocklist) == 0
            and request.usersRoles is not None
            and len(request.usersRoles) == 0 
            and request.blocklistRoles is not None
            and len(request.blocklistRoles) == 0
        ):
            raise ValueError("The group must have at least one user, external user, blocklist, user role or blocklist role.")

        validation_errors = self.__validate_save(request)

        if validation_errors:
            raise ValueError(validation_errors)
        else:
            if (
                request.externalId is not None
                and request.externalId != ""
                and request.createdBy is None
            ):
                group_to_update = self.repository.get_by_filter(
                    request.externalId, "byExternalId"
                )
                if len(group_to_update) > 0 and group_to_update[0].get("createdBy").get("externalId") is not None:
                    request.createdBy = models.RelationModel(
                        space=core.env.spaces.um_instance_space,
                        externalId=group_to_update[0].get("createdBy").get("externalId"),
                    )

            return self.repository.save(request, user_id)

    def get_by_filter(self, term: str, application_id: str):
        items = self.repository.get_by_filter(application_id, "byApplication")
        filtered_data = [
            item for item in items if item["name"].lower().startswith(term.lower())
        ]

        return filtered_data

    def get_by_application(self, application: str):
        items = self.repository.get_by_filter(application, "byApplication")
        result = models.notification_application_group_model.parse_notification_application_groups(
            items
        )

        return result

    def delete(self, external_id: str):
        try:
            if Utils.list.is_null_or_empty(external_id):
                raise ValueError("The external_id is null or empty.")

            result = self.repository.get_by_filter(external_id, "byExternalId")
            if len(result) > 0:
                self.repository.delete_application_group(result[0].get("externalId"))

            return result
        except Exception as e:
            print(e)
        return result

    def __validate_save(
        self, request: models.NotificationApplicationGroupCreateModel = None
    ):
        errors = []

        self.__validate_name(request, errors)
        self.__validate_application(request, errors)
        self.__validate_users(request, errors)
        self.__validate_roles(request, errors)

        return errors

    def __validate_name(self, request, errors):
        if self.__validate_field(request, "name", errors):
            already_exists = self.repository.get_by_filter(
                request.externalId, "validExits", request.name
            )

            if len(already_exists) > 0:
                errors.append("Application Group name already exists")

    def __validate_users(self, request, errors):
        if hasattr(request, "users") and len(request.users) > 0:
            query_data = core.services().user.find_by_ids(request.users)
            if len(query_data) != len(request.users):
                errors.append("Check the users list.")

    def __validate_application(self, request, errors):
        if self.__validate_field(request, "application", errors):
            application_filter = {"filter": {"externalId": {"eq": request.application}}}

            if not core.services().application.exists(application_filter):
                errors.append("Application does not exist.")

    def __validate_roles(self, request, errors):
        if hasattr(request, "usersRoles") and len(request.usersRoles) > 0:
            query_data = core.services().role.find_by_external_ids(request.usersRoles)
            if len(query_data) != len(request.usersRoles):
                errors.append("Check the usersRoles list.")

        if hasattr(request, "blocklistRoles") and len(request.blocklistRoles) > 0:
            query_data_blocklist = core.services().role.find_by_external_ids(
                request.blocklistRoles
            )
            if len(query_data_blocklist) != len(request.blocklistRoles):
                errors.append("Check the blocklistRoles list.")

    def __validate_field(self, object, field_name, errors):
        if not hasattr(object, field_name):
            errors.append(
                f'The "{field_name}" field is required and must contain a valid value.'
            )
            return False
        return True
