from typing import Any, List
import app.core as core
import app.repositories as repositories
import app.models as models
import math as math


class NotificationEventService:
    def __init__(
        self,
        repository: repositories.NotificationEventRepository,
        application_repository: repositories.ApplicationRepository,
    ):
        self.repository = repository
        self.application_repository = application_repository

    def get_latest_events(self, notification_type_external_id: str):
        filterObj = {}
        filterObj["filter"] = {
            "and": [
                {
                    "notificationType": {
                        "externalId": {"eq": notification_type_external_id}
                    },
                },
                {"space": {"eq": core.env.spaces.ntf_prot_instance_space}},
            ]
        }
        filterObj["first"] = 1000

        return self.find_by_filter(filterObj)

    def find_by_filter(self, filter: Any = None) -> List[models.NotificationEventModel]:
        response: List[models.NotificationEventModel] = []
        data = self.repository.find_by_filter(filter)
        if len(data) > 0:
            response = [
                models.NotificationEventModel.mapFromResult(item)
                for item in data
                if item
            ]
        return response

    def save_list(
        self, app_id: str, notification_events: List[models.NotificationRawEventModel]
    ):
        results = []
        application_id = self.find_application(app_id)
        if application_id is None:
            raise Exception(f"Application {app_id} not found")

        for notification_event in notification_events:
            try:
                result = self.save(application_id, notification_event)
                results.append(result)
            except Exception as e:
                notification_event["status"] = "failed"
                notification_event["detail"] = str(e)
                notification_event["externalId"] = None
                results.append(notification_event)
        return results

    def save(
        self,
        application_id: str,
        notification_event: models.NotificationRawEventModel,
    ):
        raw_event = {
            "sourceApplication": {
                "externalId": application_id,
                "space": core.env.spaces.um_instance_space,
            },
            "notificationReference": (
                {
                    "externalId": notification_event.notificationReference,
                    "space": core.env.spaces.ntf_prot_instance_space,
                } if notification_event.notificationReference else None
            ),
            "sourceJson": notification_event.model_dump_json(exclude=["appId", "notificationReference"]),
        }
        return self.repository.save_raw_event(raw_event)

    def find_application(self, application_id: str):
        filterApplication = {}
        filterApplication["filter"] = {
            "and": [
                {"azureAppId": {"eq": application_id}},
                {"space": {"eq": core.env.spaces.um_instance_space}},
            ]
        }
        application_externalId = self.application_repository.find_external_ids(
            filterApplication
        )

        if len(application_externalId) >= 1:
            return application_externalId[0]["externalId"]

        return None