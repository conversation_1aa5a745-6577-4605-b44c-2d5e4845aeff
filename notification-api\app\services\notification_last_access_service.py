import app.repositories as repositories


class NotificationLastAccessService:
    def __init__(
        self,
        repository: repositories.NotificationLastAccessRepository,
    ):
        self.repository = repository

    def update_notification_last_access(self, user_external_id: str):
        return self.repository.save(user_external_id)

    def get_notification_last_access_date(self, user_external_id: str):
        return self.repository.get_last_access_date(user_external_id)
