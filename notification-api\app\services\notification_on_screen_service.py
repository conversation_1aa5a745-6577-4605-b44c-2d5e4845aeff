import math
from operator import attrgetter
from typing import List, Optional, Any

import app.models as models
import app.repositories as repositories
import app.utils as Utils
from app.core.async_utils import run_sync


class NotificationOnScreenService:
    def __init__(
        self,
        repository: repositories.NotificationOnScreenRepository,
        last_acess_repository: repositories.NotificationLastAccessRepository,
    ):
        self.repository = repository
        self.last_acess_repository = last_acess_repository

    async def get_notification_on_screen_async(
        self,
        filter: models.NotificationOnScreenFilterModel,
        pagination: Optional[models.PaginationRequestModel],
        user_id,
    ) -> models.NotificationOnScreenResponseModel:
        result_list: List[models.NotificationOnScreenModel] = []
        response = models.NotificationOnScreenResponseModel()
        filter_obj = {}
        filter_items = []
        filter_time = []
        created_at_items = []
        new_update_time_items = []

        try:
            # FILTER BY USER
            filter_obj["filter_user"] = {"externalId": {"eq": user_id}}

            # FILTER BY CREATED DATE
            if Utils.list.not_null_or_empty(filter.start_period):
                created_at_items.append({"createdAt": {"gte": filter.start_period}})

            if Utils.list.not_null_or_empty(filter.end_period):
                created_at_items.append({"createdAt": {"lte": filter.end_period}})

            # FILTER BY NEWUPDATETIME
            if Utils.list.not_null_or_empty(filter.start_period):
                new_update_time_items.append(
                    {"newUpdateTime": {"gte": filter.start_period}}
                )

            if Utils.list.not_null_or_empty(filter.end_period):
                new_update_time_items.append(
                    {"newUpdateTime": {"lte": filter.end_period}}
                )

            filter_time = {
                "or": [{"and": created_at_items}, {"and": new_update_time_items}]
            }

            filter_items.append(filter_time)
            # GET_FILTER_OPTIONS
            filter_obj["filter"] = {"and": filter_items}

            result = await run_sync(
                self.repository.list_notification_on_screen_by_user_and_period,
                user_id,
                filter.start_period,
                filter.end_period,
            )

            notifications_on_screen = next(
                (
                    item.get("notificationsOnScreen").get("items")
                    for item in result
                    if item.get("externalId") == user_id
                ),
                None,
            )

            if notifications_on_screen is not None and len(notifications_on_screen) > 0:
                response.sites = self.map_existing_sites_on_notifications(
                    notifications_on_screen
                )
                response.applications = self.map_existing_applications_on_notifications(
                    notifications_on_screen
                )
                response.notificationTypes = (
                    self.map_existing_notification_types_on_notifications(
                        notifications_on_screen
                    )
                )

            filtered_notifications_on_screen = notifications_on_screen

            # FILTER BY APPLICATION
            if Utils.list.not_null_or_empty(filter.application_external_id):
                filtered_notifications_on_screen = [
                    notification
                    for notification in filtered_notifications_on_screen
                    if notification.get("template", {})
                    .get("notificationType", {})
                    .get("application", {})
                    .get("externalId")
                    in [filter.application_external_id]
                ]

            # FILTER BY SEVERITY
            if (
                filter.severity_external_ids is not None
                and len(filter.severity_external_ids) > 0
            ):

                filtered_notifications_on_screen = [
                    notification
                    for notification in filtered_notifications_on_screen
                    if notification.get("severity", {}).get("externalId")
                    in filter.severity_external_ids
                ]

            # FILTER BY NOTIFICATION TYPE
            if Utils.list.not_null_or_empty(filter.notification_type_external_id):
                filtered_notifications_on_screen = [
                    notification
                    for notification in filtered_notifications_on_screen
                    if notification.get("template", {})
                    .get("notificationType", {})
                    .get("externalId")
                    in [filter.notification_type_external_id]
                ]

            # FILTER BY SITE
            if Utils.list.not_null_or_empty(filter.site_external_id):

                filtered_notifications_on_screen = [
                    notification
                    for notification in filtered_notifications_on_screen
                    if notification.get("reportingSite", {}).get("externalId")
                    in [filter.site_external_id]
                ]

            # FILTER BY SEARCH
            if Utils.list.not_null_or_empty(filter.search):

                filtered_notifications_on_screen = [
                    notification
                    for notification in filtered_notifications_on_screen
                    if notification.get("template", {})
                    .get("notificationType", {})
                    .get("name")
                    .startswith(filter.search)
                    or notification.get("template", {})
                    .get("notificationType", {})
                    .get("application", {})
                    .get("name")
                    .startswith(filter.search)
                    or notification.get("reportingSite", {})
                    .get("description", "")
                    .startswith(filter.search)
                    or notification.get("text", "").startswith(filter.search)
                ]

            notifications = filtered_notifications_on_screen

            if notifications is not None and len(notifications) > 0:
                list_notifications: List[models.NotificationOnScreenModel] = []

                for notification in notifications:
                    ntf = models.NotificationOnScreenModel.mapFromResult(notification)
                    if ntf is not None:
                        list_notifications.append(ntf)

                total_count = len(list_notifications)

                if total_count > 0:
                    # SORTING
                    if not filter.sort_column:
                        list_notifications.sort(
                            key=lambda x: (x.updateDate or x.date, x.date), reverse=True
                        )
                    else:
                        sort_key = list(filter.sort_column.keys())
                        sort_value = list(filter.sort_column.values())
                        if sort_value[0] == "ASC" or sort_value[0] == "asc":
                            list_notifications.sort(key=attrgetter(sort_key[0]))
                        else:
                            list_notifications.sort(
                                key=attrgetter(sort_key[0]), reverse=True
                            )

                    # PAGINATION
                    if pagination.page > 1:
                        param_first = (int)(
                            pagination.items_per_page * (pagination.page - 1)
                        )
                        param_limit = param_first + pagination.items_per_page
                        items = list_notifications[param_first:param_limit]

                    else:
                        param_first = int(pagination.items_per_page)
                        param_limit = param_first
                        items = list_notifications[0:param_limit]

                    # RESULT
                    if len(items) > 0:
                        result_list = items
                        response.notifications = result_list

                    response.totalItems = total_count

                    response.totalPages = math.ceil(
                        float(response.totalItems) / pagination.items_per_page
                    )

            return response

        except Exception as e:
            print(e)

    def map_ids(self, items: List[Any]) -> List[str]:
        return [item.get("externalId") for item in items]

    async def get_notifications_not_visualized_amount_async(
        self, user_external_id
    ) -> int:
        last_visualized_date = await run_sync(
            self.last_acess_repository.get_last_access_date, user_external_id
        )

        if Utils.list.not_null_or_empty(last_visualized_date):
            notifications = await run_sync(
                self.repository.get_notification_on_screen_ids,
                user_external_id,
                last_visualized_date,
            )

            if notifications is not None and len(notifications) > 0:
                return len(notifications)

        return 0

    def map_notification_on_screen_ids(self, items: List[Any]) -> List[Any]:
        response = []
        for item in items:
            for subItem in item.get("notificationsOnScreen").get("items"):
                response.append(subItem.get("externalId"))
        return response

    def handle_query_for_external_id(self, filter: any, response_set: set):
        response_for_external_id = (
            self.repository.get_notification_on_screen_ids_by_filter(filter)
        )
        if len(response_for_external_id) > 0:
            for item in response_for_external_id:
                response_set.add(item.get("externalId"))
        return len(response_for_external_id)

    def map_existing_sites_on_notifications(self, items: List[Any]) -> List[Any]:
        sites_dict = {}
        for item in items:
            if item.get("reportingSite"):
                currentSubItem = item.get("reportingSite")
                sites_dict[currentSubItem["externalId"]] = currentSubItem["name"]

        return sites_dict

    def map_existing_applications_on_notifications(self, items: List[Any]) -> List[Any]:
        applications_dict = {}
        for item in items:
            if item.get("template"):
                currentSubItem = item.get("template")
                if currentSubItem.get("notificationType"):
                    currentSubItem = currentSubItem.get("notificationType")
                    if currentSubItem.get("application"):
                        currentSubItem = currentSubItem.get("application")
                        applications_dict[currentSubItem["externalId"]] = (
                            currentSubItem["name"]
                        )

        return applications_dict

    def map_existing_notification_types_on_notifications(
        self, items: List[Any]
    ) -> List[Any]:
        notification_types_dict = {}
        for item in items:
            if item.get("template"):
                currentSubItem = item.get("template")
                if currentSubItem.get("notificationType"):
                    currentSubItem = currentSubItem.get("notificationType")
                    notification_types_dict[currentSubItem["externalId"]] = (
                        currentSubItem["name"]
                    )

        return notification_types_dict

    async def get_chat_async(self, external_id) -> List[Any]:

        chat_result = await run_sync(
            self.repository.get_notification_on_screen_by_id, external_id
        )
        
        if len(chat_result) > 0:
            try:
                data = models.NotificationOnScreenChatModel.mapFromResult(
                    chat_result[0]
                )
                return data
            except Exception as e:
                print(e)

    def create_chat(self, external_id: str, user_external_id: str, comment: str):
        chat_result = self.repository.create_chat(
            external_id, user_external_id, comment
        )
        return chat_result

    def delete_chat(self, external_id: str, user_external_id: str):
        try:
            if Utils.list.is_null_or_empty(external_id):
                raise ValueError("The external_id is null or empty.")

            filter = {
                "filter": {
                    "and": [
                        {"externalId": {"eq": external_id}},
                        {"user": {"externalId": {"eq": user_external_id}}},
                    ]
                }
            }
            result = self.repository.find_notification_comment(filter)
            if len(result) > 0:
                self.repository.delete_notification_comment(external_id)

            return result
        except Exception as e:
            print(e)

    def get_last_access(self, external_id: str):
        last_visualized_date = self.last_acess_repository.get_last_access_date(
            external_id
        )

        if Utils.list.not_null_or_empty(last_visualized_date):
            return last_visualized_date

        return 0
