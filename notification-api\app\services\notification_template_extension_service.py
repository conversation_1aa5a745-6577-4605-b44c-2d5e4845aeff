from typing import Any, List
import app.core as core
import app.repositories as repositories
import app.models as models
import math as math
import app.utils as Utils


class NotificationTemplateExtensionService:
    def __init__(
        self,
        repository: repositories.NotificationTemplateExtensionRepository,
    ):
        self.repository = repository

    def find_by_filter(
        self, filter: Any
    ) -> List[models.NotificationTemplateExtensionModel]:
        response: List[models.NotificationTemplateExtensionModel] = []

        items = self.repository.find_by_filter(filter)
        if len(items) > 0:
            response = [
                models.NotificationTemplateExtensionModel.mapFromResult(item)
                for item in items
            ]

        return response

    def save(self, template: models.NotificationTemplateCreateModel = None):
        template_extension = models.NotificationTemplateExtensionModel()
        filter_extension = {}
        filter_extension["filter"] = {
            "and": [
                {"template": {"externalId": {"eq": template.externalId}}},
                {"owner": {"externalId": {"eq": template.emailRequester}}},
            ]
        }

        data_extension = self.repository.find_relation_model_by_filter(filter_extension)

        if data_extension is not None and len(data_extension) > 0:
            template_extension.externalId = data_extension[0]["externalId"]
            template_extension.space = core.env.spaces.ntf_instance_space
        else:
            template_extension.externalId = Utils.generateExternalId('NTFTPLEXT')
            template_extension.space = core.env.spaces.ntf_instance_space

        template_extension.channels = template.channels
        template_extension.frequencyCronExpression = template.frequencyCronExpression
        template_extension.owner = models.RelationModel(
            externalId=template.emailRequester,
            space=core.env.spaces.um_instance_space,
        )
        template_extension.template = models.RelationModel(
            externalId=template.externalId,
            space=core.env.spaces.ntf_instance_space,
        )

        return self.repository.save(template_extension)

    def find_relation_model_by_filter(
        self, filter: Any = None
    ) -> List[models.RelationModel]:
        items = self.repository.find_relation_model_by_filter(filter)

        if len(items) > 0:
            return [models.RelationModel.mapFromResult(item) for item in items]

        return []

    def delete_by_template_id(self, template_id: str):
        filter_extension = {}
        filter_extension["filter"] = {
            "template": {"externalId": {"eq": template_id}}
        }
        extensions = core.services().notification_template_extension.find_relation_model_by_filter(
            filter_extension
        )
        if len(extensions) > 0:
            response = self.delete(
                extensions
            )
        
    def delete(self, extensions: List[models.RelationModel]):
        for item in extensions:
            self.repository.delete(external_id=item.externalId, space=item.space)
