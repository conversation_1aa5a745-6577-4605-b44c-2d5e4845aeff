from typing import Any, List
import app.core as core
import app.repositories as repositories
import app.models as models
import math as math
from operator import attrgetter
import app.utils as Utils
from cognite.client.data_classes.filters import Equals

from app.utils.json_utils import json_safe_loads


class NotificationTemplateService:
    def __init__(
        self,
        repository: repositories.NotificationTemplateRepository,
    ):
        self.repository = repository

    def find_notification_basic_info(self, filter: Any):
        templates = []
        data = self.repository.find_notification_basic_info(filter)
        if len(data) > 0:
            templates = [
                models.NotificationBasicInfoModel.map_from_result(item)
                for item in data
                if item
            ]
        return templates

    def find_by_notification_application_group_id(
        self, templates_ids: List[Any]
    ) -> List[models.NotificationTemplateByApplicationGroupResponseModel]:

        external_id_templates_filter = {
            "templates_filter": {
                "and": [
                    {
                        "or": [
                            {"deleted": {"eq": False}},
                            {"deleted": {"isNull": True}},
                        ]
                    },
                    {
                        "externalId": {
                            "in": [item.get("externalId") for item in templates_ids]
                        }
                    },
                ],
            }
        }

        templates: List[models.NotificationTemplateByApplicationGroupResponseModel] = []
        data = self.repository.find_notification_basic_info(external_id_templates_filter)
        if len(data) > 0:
            templates = [
                models.NotificationTemplateByApplicationGroupResponseModel.mapFromResult(
                    item
                )
                for item in data
                if item
            ]
        return templates

    def save(
        self,
        templates: models.NotificationTemplateCreateModel,
        user_id: str,
        isAdminEdition: bool = False,
        to_delete: bool = False,
    ):
        if to_delete:
            return self.repository.save(templates)

        conditionalExpression = json_safe_loads(
            templates.conditionalExpression, default=[]
        )
        siteConditionalId = next(
            (
                item["value"]
                for item in conditionalExpression
                if item.get("variable") == "site"
            ),
            None,
        )

        if siteConditionalId:
            validate_site = core.services().reporting_site.exists(siteConditionalId)
            if validate_site:
                templates.reportingSite = {
                    "externalId": siteConditionalId,
                    "space": core.env.spaces.assethierarcy_instace_space,
                }

        # TEMPLATE EDITION: Get creator of edited template
        if (
            not isAdminEdition
            and templates.creator is None
            and Utils.list.not_null_or_empty(templates.externalId)
        ):
            filter = {}
            filter["templates_filter"] = {"externalId": {"eq": templates.externalId}}
            template_to_update = self.find_notification_basic_info(filter)
            if template_to_update[0]:
                templates.creator = template_to_update[0].creator
        else:
            templates.creator = models.RelationModel(externalId=user_id)

        if templates.emailRequester is None:
            templates.emailRequester = user_id

        # SET FALSE FOR CUSTOM CHANNEL AND CUSTOM FREQUENCY IF USER NOT IS ADMIN LEVEL
        if not templates.adminLevel:
            templates.customChannelEnabled = False
            templates.customFrequencyEnabled = False
            if not templates.subscribedUsers:
                templates.subscribedUsers.append(
                    getattr(templates.creator, "externalId")
                )

        validation_errors = self.__validateSave(templates)
        if validation_errors:
            raise ValueError(validation_errors)
        else:
            if isAdminEdition and Utils.list.not_null_or_empty(templates.externalId):
                filter = {}
                filter["templates_filter"] = {"externalId": {"eq": templates.externalId}}
                template_to_update = self.find_notification_basic_info(filter)

                if (
                    not templates.customChannelEnabled
                    and template_to_update[0].customChannelEnabled
                ) or (
                    not templates.customFrequencyEnabled
                    and template_to_update[0].customFrequencyEnabled
                ):
                    core.services().notification_template_extension.delete_by_template_id(
                        template_to_update[0].externalId
                    )

            if isinstance(templates.frequencyCronExpression, Utils.scheduleModel):
                templates.frequencyCronExpression = Utils.cron.generate(
                    templates.frequencyCronExpression
                )["cron_expression"]

            if hasattr(templates, "subject") and templates.subject is not None:
                templates.subject = templates.subject
            else:
                templates.subject = ""

            if templates.adminLevel and not isAdminEdition:
                return core.services().notification_template_extension.save(templates)
            else:
                return self.repository.save(templates)

    def exists(
        self,
        templateName: str,
        notificationTypeExternalId: str,
        templateExternalId: str = None,
    ) -> bool:
        if templateExternalId is None:
            filter = {
                "filter": {
                    "and": [
                        {"deleted": {"eq": False}},
                        {"name": {"eq": templateName}},
                        {
                            "notificationType": {
                                "externalId": {"eq": notificationTypeExternalId}
                            }
                        },
                        {"space": {"eq": core.env.spaces.ntf_instance_space}},
                    ]
                }
            }
        else:
            filter = {
                "filter": {
                    "and": [
                        {"name": {"eq": templateName}},
                        {"deleted": {"eq": False}},
                        {
                            "notificationType": {
                                "externalId": {"eq": notificationTypeExternalId}
                            }
                        },
                        {"not": {"externalId": {"eq": templateExternalId}}},
                        {"space": {"eq": core.env.spaces.ntf_instance_space}},
                    ]
                }
            }

        result = Utils.cognite.getDefaultList(
            self.repository._graphql_client,
            "NotificationTemplate",
            "externalId",
            filter,
        )
        if len(result) == 0:
            return False

        return True

    def delete(self, external_id: str, isAdminEdition: bool, user_id: str):
        # TODO - Please, check if the user_id is the template's creator or extension's owner
        # GET TEMPLATE INFO
        if Utils.list.is_null_or_empty(external_id):
            raise ValueError(f'The external_id "{external_id}" is not valid.')

        filter = {}
        if isAdminEdition:
            filter["templates_filter"] = {"externalId": {"eq": external_id}}
        else:
            filter["templates_filter"] = {
                "and": [
                    {"externalId": {"eq": external_id}},
                    {"creator": {"externalId": {"eq": user_id}}},
                ]
            }
        template_query = self.repository.get_by_filter_paginated(filter)

        for item in template_query:
            if item:
                if "subscribedApplicationGroups" in item and "items" in item["subscribedApplicationGroups"]:
                    for app_group in item["subscribedApplicationGroups"]["items"]:
                        if "blocklistRoles" in app_group:
                            del app_group["blocklistRoles"]
                        if "usersRoles" in app_group:
                            del app_group["usersRoles"]
                        if "users" in app_group:
                            del app_group["users"]
                        if "blocklist" in app_group:
                            del app_group["blocklist"]                

        template = [
            models.NotificationTemplateModel.mapFromResult(item)
            for item in template_query
            if item
        ]
        if len(template) > 0:
            # UPDATE TEMPLATE TO SET DELETED FLAG - SOFT DELETE
            template_to_del: models.NotificationTemplateModel = template[0]
            template_to_del.deleted = True
            deleted_template = self.save(
                models.NotificationTemplateCreateModel.mapfromJson(template_to_del),
                user_id,
                True,
            )
            # DELETE TEMPLATE EXTENSIONS - HARD DELETE
            if deleted_template:
                filter_extension = {}
                filter_extension["filter"] = {
                    "template": {"externalId": {"eq": external_id}}
                }
                extensions = core.services().notification_template_extension.find_relation_model_by_filter(
                    filter_extension
                )
                if len(extensions) > 0:
                    core.services().notification_template_extension.delete(
                        extensions
                    )
        else:
            raise ValueError(f"Only Template's creator can delete the template.")

    def __validateSave(self, request: models.NotificationTemplateCreateModel = None):
        errors = []

        self.__validateFieldExists(request, "name", errors)
        self.__validateNotificationType(request, errors)
        self.__validateCreator(request, errors)
        self.__validateSeverity(request, errors)
        self.__validateText(request, errors)
        self.__validateChannels(request, errors)
        self.__validateCustomChannelEnabled(request, errors)
        if not request.allUsers:
            self.__validateSubscribedUsers(request, errors)
            self.__validateSubscribedUserRoles(request, errors)

        self.__validate_blocklist_users(request, errors)
        self.__validate_blocklist_roles(request, errors)

        return errors

    def __validateFieldExists(self, request, field_name, errors):
        if not hasattr(request, field_name):
            errors.append(f'The "{field_name}" field is required.')

    def __validateNotificationType(self, request, errors):
        if self.__validateField(request, "notificationType", errors):
            if self.__validateField(request.notificationType, "externalId", errors):
                if not core.services().notification_type.exists(
                    request.notificationType.externalId
                ):
                    errors.append("Notification type does not exist.")
                if self.exists(
                    request.name,
                    request.notificationType.externalId,
                    request.externalId,
                ):
                    errors.append(
                        "Template name already exists for the notification type."
                    )

    def __validateCreator(self, request, errors):
        if self.__validateField(request, "creator", errors):
            if self.__validateField(request.creator, "externalId", errors):
                if not core.services().user.exists(request.creator.externalId):
                    errors.append("Creator does not exist.")

    def __validateSeverity(self, request, errors):
        if self.__validateField(request, "severity", errors):
            if self.__validateField(request.severity, "externalId", errors):
                if not core.services().severities.exists(request.severity.externalId):
                    errors.append("Severity does not exist.")

    def __validateText(self, request, errors):
        if not hasattr(request, "text"):
            errors.append('The "text" field is required and cannot be empty.')

    def __validateChannels(self, request, errors):
        if hasattr(request, "channels"):
            if request.channels:
                for channel in request.channels:
                    if not hasattr(channel, "externalId"):
                        errors.append(
                            'Each item in "channels" must have an "externalId" attribute.'
                        )
                    else:
                        if not core.services().channels.exists(channel.externalId):
                            errors.append("Channel does not exist.")

    def __validateSubscribedUsers(self, request, errors):
        if hasattr(request, "subscribedUsers"):
            if len(request.subscribedUsers) > 0:
                queryData = core.services().user.find_by_ids(request.subscribedUsers)
                if len(queryData) != len(request.subscribedUsers):
                    errors.append("Check the subscribedUsers list.")

    def __validateSubscribedUserRoles(self, request, errors):
        if hasattr(request, "subscribedUserRoles"):
            if len(request.subscribedUserRoles) > 0:
                queryData = core.services().role.find_by_external_ids(
                    request.subscribedUserRoles
                )
                if len(queryData) != len(request.subscribedUserRoles):
                    errors.append("Check the subscribedUserRoles list.")

    def __validate_blocklist_users(self, request, errors):
        if hasattr(request, "blocklistUsers"):
            if len(request.blocklistUsers) > 0:
                query_data = core.services().user.find_by_ids(request.blocklistUsers)
                if len(query_data) != len(request.blocklistUsers):
                    errors.append("Check the blocklistUsers list.")

    def __validate_blocklist_roles(self, request, errors):
        if hasattr(request, "blocklistRoles"):
            if request.blocklistRoles is not None and len(request.blocklistRoles) > 0:
                query_data = core.services().role.find_by_external_ids(
                    request.blocklistRoles
                )
                if len(query_data) != len(request.blocklistRoles):
                    errors.append("Check the blocklistRoles list.")

    def __validateCustomChannelEnabled(self, request, errors):
        if not hasattr(request, "customChannelEnabled"):
            errors.append(
                'The "customChannelEnabled" field is required and must be a boolean value (true/false).'
            )

    def __validateField(self, object, field_name, errors):
        if not hasattr(object, field_name):
            errors.append(
                f'The "{field_name}" field is required and must contain a valid value.'
            )
            return False
        return True

    def map_template_ids(self, items: List[Any]) -> List[Any]:
        response = []
        for item in items:
            for subitem in item.get("templates").get("items"):
                response.append({"externalId": subitem.get("externalId")})
        return response

    def map_template_only_ids(self, items: List[Any], setToAdd):
        for item in items:
            for subitem in item.get("templates").get("items"):
                setToAdd.add(subitem.get("externalId"))

    def find_by_id(self, external_id: str, user_id: str, isAdminEdition: bool = False):
        response = self.repository.find_by_id_with_role_info(external_id)
        template: models.NotificationTemplateEditResponseModel = {}
        if len(response) > 0:
            template = models.NotificationTemplateEditResponseModel.mapFromResult(
                response[0]
            )
            if template.adminLevel and not isAdminEdition:
                filter_extension = {}
                filter_extension["filter"] = {
                    "and": [
                        {"template": {"externalId": {"eq": template.externalId}}},
                        {"owner": {"externalId": {"eq": user_id}}},
                    ]
                }
                temp_extension = (
                    core.services().notification_template_extension.find_by_filter(
                        filter_extension
                    )
                )
                if temp_extension is not None and len(temp_extension) > 0:
                    extension = (
                        models.NotificationTemplateExtensionEditModel.mapFromJson(
                            temp_extension[0]
                        )
                    )
                    template.channels = extension.channels
                    template.frequencyCronExpression = extension.frequencyCronExpression

        return template

    def get_send_to_by_notification_type(
        self, notification_type_external_id: str | None = None
    ):
        filter = (
            {
                "templates_filter": {
                    "notificationType": {
                        "externalId": {"eq": notification_type_external_id}
                    }
                }
            }
            if notification_type_external_id
            else None
        )

        templates_by_filter = self.repository.get_by_filter_paginated(filter)

        if not templates_by_filter:
            return []

        dict_send_to = {}

        for item in templates_by_filter:
            if item["allUsers"]:
                dict_send_to["allUsers"] = "All Users"
                continue

            # Process and collect subscribedUserRoles
            for user_role in item["subscribedRoles"]["items"]:
                if user_role["role"]:
                    dict_send_to[user_role["externalId"]] = (
                        f'({user_role["reportingSite"]["siteCode"]}) {user_role["role"]["name"]}'
                    )
            # Process and collect subscribedUsers
            for user in item["subscribedUsers"]["items"]:
                if user["firstName"] and user["lastName"]:
                    dict_send_to[user["externalId"]] = (
                        user["firstName"] + " " + user["lastName"]
                    )
                elif user["firstName"]:
                    dict_send_to[user["externalId"]] = user["firstName"]
                else:
                    dict_send_to[user["externalId"]] = user["externalId"]

            # Process and collect subscribedApplicationGroups
            for user_groups in item["subscribedApplicationGroups"]["items"]:
                dict_send_to[user_groups["externalId"]] = user_groups["name"]

        return dict(sorted(dict_send_to.items(), key=lambda x: x[1].lower()))

    def get_by_notification_application_group_id(self, groupe_external_id: str):

        templates = []

        filter_group_templates_id = {
            "filter": {"externalId": {"eq": groupe_external_id}}
        }

        response_by_application_groups = self.repository.find_by_application_groups(
            filter_group_templates_id
        )

        templates_ids = self.map_template_ids(response_by_application_groups)

        if len(templates_ids) > 0:
            templates = self.find_by_notification_application_group_id(templates_ids)

        return templates if templates else []

    def get_templates(self, request, user):
        templates = self.repository.find_templates_by_filters(request, user)

        result = [
            models.NotificationTemplateTableModel.mapFromResult(item)
            for item in templates["templates"]
            if item
        ]

        if request['sort_column'] and request['sort_column'] != {}:
            sort_key = list(request['sort_column'].keys())[0]
            reverse = list(request['sort_column'].values())[0].upper() == "DESC"
            result.sort(key=attrgetter(sort_key), reverse=reverse)

        return {
            "templates": result,
            "endCursor": templates['end_cursor'],
            "hasNextPage": templates['has_next_page']
        } 

