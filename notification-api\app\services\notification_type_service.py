from operator import attrgetter
from typing import List, Optional, Any
import app.repositories as repositories
import app.models as models
import app.core as core
import app.utils as Utils


class NotificationTypeService:
    def __init__(
        self,
        repository: repositories.NotificationTypeRepository,
    ):
        self.repository = repository

    def create(self, templates: models.application_model):
        print("Inside Service")
        return self.repository.create(templates)

    def find_by_application(self, application_ids: List[str]):
        notificationTypesModels: List[models.NotificationTypeModel] = []
        filter = {}
        if application_ids is not None and len(application_ids):
            filter["filter"] = {"application": {"externalId": {"in": application_ids}}}

            notification_types = self.repository.find_by_filter(filter)
            if notification_types is not None and len(notification_types) > 0:
                filter_template = {}
                filter_template["filter"] = {
                    "notificationType": {
                        "externalId": {
                            "in": [
                                item.get("externalId") for item in notification_types
                            ]
                        }
                    }
                }

                types_in_templates = set(
                    self.repository.find_type_in_templates(filter_template)
                )
                for type in notification_types:
                    type_model = models.NotificationTypeModel.mapFromResult(type)
                    type_model.hasTemplates = any(
                        type_model.externalId == id for id in types_in_templates
                    )

                    notificationTypesModels.append(type_model)

            notificationTypesModels.sort(key=attrgetter("name"))

        return notificationTypesModels

    def find_by_filter(self, filter: Any = None) -> List[models.NotificationTypeModel]:
        types: List[models.NotificationTypeModel] = []
        data = self.repository.find_by_filter(filter)
        if len(data) > 0:
            types = [
                models.NotificationTypeModel.mapFromResult(item)
                for item in data
                if item
            ]
        return types

    def exists(self, externalId: str) -> bool:
        result = Utils.cognite.getDefaultList(
            self.repository._graphql_client,
            "NotificationType",
            "externalId",
            {
                "filter": {
                    "and": [
                        {"externalId": {"eq": externalId}},
                        {"space": {"eq": core.env.spaces.ntf_instance_space}},
                    ]
                }
            },
        )

        if len(result) == 0:
            return False

        return True
