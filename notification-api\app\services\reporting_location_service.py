from typing import Any, List, Annotated
import app.repositories as repositories
import app.models as models
import app.utils as Utils
import app.core as core
from fastapi import Depends


class ReportingLocationService:
    def __init__(
        self,
        repository: repositories.ReportingLocationRepository,
    ):
        self.repository = repository
    
    def find_by_filter(self, params: Annotated[dict, Depends(models.reporting_location_model.common_request_params)]) -> List[models.ReportingLocationModel]:
        items = self.repository.find_by_unit_filter(params)

        return items