from typing import Any, List
import app.repositories as repositories
import app.models as models
import app.utils as Utils
import app.core as core


class ReportingSiteService:
    def __init__(
        self,
        repository: repositories.ReportingSiteRepository,
    ):
        self.repository = repository

    def find_all(self) -> List[models.ReportingSiteModel]:
        items = self.repository.find("findAll")

        return items
    
    def exists(self, external_id: str) -> bool:
        items = self.repository.find("byExternalId", external_id)

        if len(items) == 0:
            return False
        return True
    
    def find_units_by_site(self, external_id: str) -> List[models.ReportingUnitModel]:
        items = self.repository.find("byExternalId", external_id)
        result = models.reporting_unit_model.parse_units(items)

        return result