from typing import Any, List
import app.repositories as repositories
import app.models as models
import app.utils as Utils
import app.core as core


class RoleService:
    def __init__(
        self,
        repository: repositories.RoleRepository,
    ):
        self.repository = repository

    def find_by_term_and_app(self, term: str, applicationId: str, limit: int = 10) -> List[Any]:
        items = self.repository.find_role_name_and_application(term, applicationId, limit)

        result = models.role_model.parse_roles(items)

        return result
    
    def find_by_reporting_site(self, reporting_site: str) -> List[Any]:
        items = self.repository.find_role_by_filter(
            {
                "filter": {
                    "and": [
                        {"role": {"roleCategory": {"externalId": {"eq": "RoleSite"}}}},
                        {"reportingSite": {"externalId": {"eq": reporting_site}}}
                    ]
                }
            }
        )

        result = models.role_model.map_from_result(items)

        return result
    
    def find_by_application(self, application: str) -> List[Any]:
        items = self.repository.find_role_by_filter(
            {
                "filter": {
                    "role": {
                        "and": [
                            {"roleCategory": {"externalId": {"eq": "RoleApplication"}}}, 
                            {"application": {"externalId": {"eq": application}}}
                        ]
                    }
                }
            }
        )
        result = models.role_model.map_from_result(items)

        return result

    def find_by_external_ids(self, externalIds: List[str]) -> List[Any]:
        result = Utils.cognite.getDefaultList(
            self.repository._graphql_client,
            "NotificationUserRoleSite",
            "externalId",
            {
                "filter": {
                    "and": [
                        {"externalId": {"in": externalIds}},
                        {"space": {"eq": core.env.spaces.um_instance_space}},
                    ]
                }
            },
        )
        return result
    
    def find_by_user(self, user_id: str):
        filter = {}
        user_external_id = f"UserComplement_{user_id}"
        filter["filter"] = {"externalId":{"eq": user_external_id}}

        return self.repository.find_by_user(filter)
