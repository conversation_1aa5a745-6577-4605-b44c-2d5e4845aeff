from fastapi import Depends, HTTPException
import app.repositories as repositories
import app.core as core


class SeveritiesService:
    def __init__(
        self,
        repository: repositories.SeveritiesRepository,
    ):
        self.repository = repository

    def findAll(self):
        try:
            items = self.repository.findAll()
            if len(items) > 0:
                severities = [
                    core.models.NotificationSeverityModel.mapFromResult(item)
                    for item in items
                    if item
                ]

            order = {"LOW": 1, "MEDIUM": 2, "HIGH": 3}
            ordered_severities = sorted(severities, key=lambda x: order[x.name.upper()])
            return ordered_severities

        except Exception as e:
            raise HTTPException(status_code=400, detail=str(e))

    def exists(self, external_id: str):
        items = self.repository.findAll()

        result = [
            item
            for item in items
            if item.get("externalId") == external_id
        ]

        if len(result) == 0:
            return False

        return True
