from typing import Any, List, Annotated
import app.repositories as repositories
import app.models as models
import app.utils as Utils
import app.core as core
from fastapi import Depends


class TeamService:
    def __init__(
        self,
        repository: repositories.TeamRepository,
    ):
        self.repository = repository
    
    def find_by_filter(self, request: Annotated[dict, Depends(models.team_model.common_request_params)]) -> List[models.TeamModel]:
        items = self.repository.find_by_filter(request)
        result = models.team_model.parse_teams_from_filter(items, request)

        return result