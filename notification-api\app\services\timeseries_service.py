from typing import List
import app.repositories as repositories
from app.models.timeseries_model import ResponseTimeseriesDetails, DataPointModel
from app.utils.date_utils import DateUtils
from app.core.env_variables import EnvVariables

class TimeseriesService:
    def __init__(
        self,
        repository: repositories.TimeseriesRepository,
        env_variables: EnvVariables
    ):
        self.repository = repository
        self.env_variables = env_variables
    
    def find_details_by_externalId(self, externalId: str, startDateFilter: str, endDateFilter: str):
        timeserieDetails: ResponseTimeseriesDetails = None
        timeseriesItem = self.repository.find_timeserie_by_externalId(externalId)

        if timeseriesItem:
            timeserieDetails = ResponseTimeseriesDetails.mapFromResult(timeseriesItem)

        if timeseriesItem.asset_id:
            asset = self.repository.find_asset_by_id(timeseriesItem.asset_id)
            if asset:
                timeserieDetails.assetName = asset.name

            timeserieDetails.urlLinkedAssets = f"{self.env_variables.cognite.urlLinkedAssets}/{str(timeseriesItem.asset_id)}"

        if timeseriesItem.data_set_id:
            dataset = self.repository.find_dataset_by_id(timeseriesItem.data_set_id)
            if dataset:
                timeserieDetails.datasetName = dataset.name

            timeserieDetails.urlDataSetDetails = f"{self.env_variables.cognite.urlDataSetDetails}/{str(timeseriesItem.data_set_id)}"

        if timeseriesItem.id:
            # Energy Logic
            months = DateUtils.getMonthsQuantityBetweenTwoDates(startDateFilter, endDateFilter)
            granularity = '1h'
            if months >= 2:
                granularity = '1d'

            startPeriodUnix = DateUtils.convertFormattedTimeStampUTCToDatetime(startDateFilter)
            endPeriodUnix = DateUtils.convertFormattedTimeStampUTCToDatetime(endDateFilter)

            chartDatapoints = self.repository.find_datapoints_by_filter(
                timeseriesId=timeseriesItem.id,
                granularity=granularity,
                startPeriod=startPeriodUnix,
                endPeriod=endPeriodUnix
            )

            datapointsList: List[DataPointModel] = []

            if chartDatapoints and len(chartDatapoints) > 0:

                for datapointItem in chartDatapoints:
                    
                    newDatapoint: DataPointModel = DataPointModel()
                    newDatapoint.timestamp = DateUtils.convertUnixToFormattedTimeStampUTC(datapointItem.timestamp)
                    newDatapoint.value = datapointItem.value if datapointItem.value else datapointItem.average
                    
                    datapointsList.append(
                        newDatapoint
                    )

                latestDatapoint = self.repository.find_latest_datapoint(timeseriesItem.id)
                if latestDatapoint and len(latestDatapoint) > 0:
                    timeserieDetails.lastReading = DateUtils.getMinutesBetweenNowAndPastUnix(latestDatapoint[0].timestamp)
                
            if datapointsList and len(datapointsList) > 0:
                timeserieDetails.datapointsItems = datapointsList
            
            timeserieDetails.urlTimeSeriesDetails = f"{self.env_variables.cognite.urlTimeSeriesDetails}/{str(timeserieDetails.id)}"
        
        return timeserieDetails
