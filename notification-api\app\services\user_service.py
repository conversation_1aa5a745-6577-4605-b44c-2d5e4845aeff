from concurrent.futures import Thread<PERSON>oolExecutor
from typing import Any, List, Optional
import app.repositories as repositories
import app.models as models
import app.core as core
import app.utils as Utils
from typing import Annotated
from fastapi import Depends


class UserService:
    def __init__(
        self,
        repository: repositories.UserRepository,
    ):
        self.repository = repository

    def find_by_id(self, externalId: str) -> models.NotificationUserModel:
        user: models.NotificationUserModel = None
        filter = {}
        filter["filter"] = {"externalId": {"eq": externalId}}
        items = self.repository.find(filter)

        if items:
            user = [
                models.NotificationUserModel.mapFromResult(item)
                for item in items
                if item
            ][0]

        user.admin = any(role.name.lower() == "admin" for role in user.roles)

        return user

    def find_by_ids(self, externalId: List[str]) -> List[models.NotificationUserModel]:
        filter = {}
        list_users = []
        batch_size = 4000
        batch_filter = [
            externalId[i : i + batch_size]
            for i in range(0, len(externalId), batch_size)
        ]
        for batch_users in batch_filter:
            filter["filter"] = {
                "and": [
                    {"externalId": {"in": batch_users}},
                    {"space": {"eq": core.env.spaces.um_instance_space}},
                ]
            }
            items = self.repository.find(filter)
            users = []
            if items:
                users = [
                    models.NotificationUserModel.mapFromResult(item)
                    for item in items
                    if item
                ]
            list_users.extend(users)

        return list_users

    def distinct(self, list_items: List[Any]) -> List[Any]:
        unique_list_items = []
        for item in list_items:
            if item not in unique_list_items:
                unique_list_items.append(item)
        return unique_list_items

    def find_user_by_term_and_app(
        self, term: str, applicationId: str, limit: int = 10
    ) -> models.ResponseSearchUsersTerm:
        
        response = models.ResponseSearchUsersTerm
        services = core._ServiceList()

        with ThreadPoolExecutor(max_workers=3) as executor:
            future_roles = executor.submit(
                services.role.find_by_term_and_app, term, applicationId, limit
            )
            future_users = executor.submit(
                self.repository.find_by_email, term, limit
            )
            future_application_groups = executor.submit(
                services.notification_application_group.get_by_filter, term, applicationId
            )

            roles = future_roles.result()
            user_data = future_users.result()
            application_group_data = future_application_groups.result()

        users = models.users_model.parse_users(user_data)
        application_group = models.notification_application_group_model.parse_notification_application_groups(
            application_group_data
        )

        response.roles = roles
        response.users = users
        response.applicationGroups = application_group

        return response

    def exists(self, external_id: str):
        result = Utils.cognite.getDefaultList(
            self.repository._graphql_client,
            "NotificationUser",
            "externalId",
            {
                "filter": {
                    "and": [
                        {"externalId": {"eq": external_id}},
                        {"space": {"eq": core.env.spaces.um_instance_space}},
                    ]
                }
            },
        )
        if len(result) == 0:
            return False

        return True

    def find_by_filter(self, filter):
        items = self.repository.find(filter)
        return items

    def find_user_by_tags(
        self,
        params: Annotated[dict, Depends(models.users_model.common_user_request_params)],
    ) -> List[models.UserModel]:
        items = self.repository.find_by_tags(params)
        result = models.users_model.parse_users_from_filter(
            items.get("listUserComplement", "").get("items", []), params
        )

        return result
