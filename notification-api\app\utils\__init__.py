from .cognite_utils import Cognite as __cogniteUtils
from .list_utils import ListUtils as __listUtils
from .cron_expression_utils import CronExpression as __cronExpressionUtils
from .cron_expression_utils import ScheduleModel as __scheduleModel
from .date_utils import DateUtils as __dateUtils

from uuid import uuid4
from datetime import datetime

cognite = __cogniteUtils
list = __listUtils
cron = __cronExpressionUtils
scheduleModel = __scheduleModel
date = __dateUtils


def generateExternalId(table_code: str) -> str:
    now = datetime.now()
    timestamp_part = now.strftime("%Y%m%d%H%M%S")
    milliseconds = now.microsecond // 1000
    return f"{table_code}-{timestamp_part}{milliseconds:03d}"

def generateUUID():
    return str(uuid4())


class DotDict(dict):
    def __getattr__(self, item):
        try:
            return self[item]
        except KeyError:
            raise AttributeError(f"'DotDict' object has no attribute '{item}'")

    def __setattr__(self, key, value):
        self[key] = value
