from typing import List, Dict, Any, Optional, TypeVar
from cognite.client import CogniteClient
from cognite.client.data_classes.data_modeling import (
    NodeApply,
    EdgeApply,
    NodeList,
    Node,
    ViewId,
    View,
)
from app.core.graphql_client import GraphQLClient

T = TypeVar("T")


class Cognite:
    """
    [EN] Utility class for better implementation of Cognite methods.
    The purpose of this class is to allow developers to implement Cognite functionalities quickly and easily.

    [PT-BR] Classe de utilitarios para uma melhor implementação de metodos do cognite.
    O objetivo dessa classe é permitir ao desenvolvedor implementar funcionalidades do cognite de maneira rapida e simples.
    """

    @classmethod
    def getView(
        cls, cognite_client: CogniteClient, fdm_model_space: str, entityName: str
    ):
        """
        [EN] Method for retrieving the view of an entity.\n
        [PT-BR] Metodo para recuperar a view de uma entidade.
        """
        views = cognite_client.data_modeling.views.retrieve(
            (fdm_model_space, entityName), all_versions=False
        )
        view = views.get(external_id=entityName)
        if view is None:
            print("Could not retrieve the view info for the Entity {ENTITY}")
            return
        return view
    
    @classmethod
    def get_list_view(
        cls, cognite_client: CogniteClient, fdm_model_space: str, limit: Optional[int] = -1
    ):
        """
        [EN] Method for retrieving the view of an entity.\n
        [PT-BR] Metodo para recuperar a view de uma entidade.
        """
        views_list = cognite_client.data_modeling.views.list(space=fdm_model_space, limit=limit)
        
        if views_list is None:
            print("Could not retrieve the view info for the Entity {ENTITY}")
            return
        
        return views_list

    @classmethod
    def find_view_by_external_id(cls, views_list, external_id) -> View | None:
        return next(
            (view for view in views_list if view.external_id == external_id), None
        )

    @classmethod
    def createRelationship(
        cls,
        list_properties: List[Dict[str, Any]],
        sourceNodes: NodeApply,
        version: Optional[str],
        graphql_client: GraphQLClient,
        cognite_client: CogniteClient,
        fdm_model_space: str,
        fdm_instances_space: str,
        field: str,
        remove_previous_relationship: bool = False,
    ):
        """
        [EN] Method for creating relationships between entities.\n
        [PT-BR] Metodo para criar relacionamentos entre entidades.

        Args:
            list_properties: List of properties to be created.
            sourceNodes: NodeApply object that represents the source of the relationship.
            version: Version of the relationship.
            graphql_client: GraphQLClient object.
            cognite_client: CogniteClient object.
            fdm_model_space: Model space.
            fdm_instances_space: Instances space.
            field: Field Name of the relationship.
            remove_previous_relationship: If true, it will remove the previous relationship.
        """
        model_space = fdm_model_space
        instances_space = fdm_instances_space

        model_name = sourceNodes.sources[0].source.external_id
        relationshipTypeName = model_name + "." + field

        if remove_previous_relationship:
            edges = [""]
            while edges is not None and len(edges) > 0:
                edges = cls.getRelationshipEdges(
                    space=sourceNodes.space,
                    externalId=sourceNodes.external_id,
                    fieldName=field,
                    modelName=model_name,
                    graphql_client=graphql_client,
                )

                edge_tuples = [(edge["space"], edge["externalId"]) for edge in edges]
                if len(edge_tuples) > 0:
                    cognite_client.data_modeling.instances.delete(edges=edge_tuples)

        edges_to_apply = [
            EdgeApply(
                instances_space,
                f"{sourceNodes.external_id}-{payload.externalId}",
                type=(model_space, relationshipTypeName),
                start_node=(
                    instances_space,
                    sourceNodes.external_id,
                ),
                end_node=(
                    payload.space if payload.space is not None else instances_space,
                    payload.externalId,
                ),
                sources=(
                    sourceNodes.sources if version and "source" in payload else None
                ),
            )
            for payload in list_properties
        ]

        chunks = cls.__createDefaultPagination(edges_to_apply)

        for chunk in chunks:
            try:
                cognite_client.data_modeling.instances.apply(
                    edges=chunk,  # type: ignore
                    skip_on_version_conflict=False,
                    replace=True,
                    auto_create_direct_relations=True,
                    auto_create_end_nodes=False,
                    auto_create_start_nodes=False,
                )
            except Exception as e:
                print(e)

    @classmethod
    def __createDefaultPagination(cls, resources: list[T]) -> list[list[T]]:
        return [
            resources[1000 * i : 1000 * (i + 1)]
            for i in range(int(len(resources) / 1000) + 1)  # type: ignore
        ]

    @classmethod
    def getDefaultList(
        cls,
        graphql_client: GraphQLClient,
        modelName: str,
        fields: str,
        filter: Dict[str, Any] | None = None,
    ) -> List[Any]:
        """
        [EN] Method for retrieving a list of records from a model.\n
        [PT-BR] Metodo para recuperar uma lista de registros de uma model.
        """

        result = graphql_client.queryDefaultList(modelName, fields, filter)
        return result

    @classmethod
    def getRelationshipEdges(
        cls,
        graphql_client: GraphQLClient,
        modelName: str,
        space: str,
        externalId: str,
        fieldName: str,
    ) -> List[Any]:
        """
        [PT-BR] Metodo para recuperar o relacionamento de um campo especifico.
        [EN] Method for retrieving the relationship of a specific field.
        """

        result = graphql_client.getRelationshipEdges(
            modelName, space, externalId, fieldName
        )
        return result

    @classmethod
    def from_node_list(self, items: NodeList):
        data = []
        key: ViewId | None = None
        for item in items:
            if not isinstance(item, Node):
                continue
            if not key:
                key = next((key for key in item.properties.keys()), None)
                if not key:
                    continue
            entry = {str(k): v for k, v in item.properties.get(key, {}).items()}
            if not entry:
                continue
            entry["externalId"] = item.external_id
            entry["space"] = item.space

            data.append(entry)

        return data
