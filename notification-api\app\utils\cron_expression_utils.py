from enum import Enum
from typing import List, Dict, Any, Optional, TypeVar
from pydantic import BaseModel
from croniter import croniter
from datetime import datetime


T = TypeVar("T")


class ScheduleType(str, Enum):
    MINUTE = "minute"
    HOURLY = "hourly"
    WEEKLY = "weekly"
    MONTHLY = "monthly"


class ScheduleModel(BaseModel):
    schedule_type: ScheduleType
    time: Optional[str] = None
    end_time: Optional[str] = None
    interval: Optional[int] = None
    day_of_week: Optional[List[str]] = None
    day_of_month: Optional[str] = None
    months: Optional[List[int]] = None


days_of_week_to_cron = {
    "monday": "MON",
    "tuesday": "TUE",
    "wednesday": "WED",
    "thursday": "THU",
    "friday": "FRI",
    "saturday": "SAT",
    "sunday": "SUN",
}


class CronExpression:
    @classmethod
    def generate(cls, schedule: ScheduleModel):
        """Generate a cron expression based on the schedule type

        Example:
            #MINUTE
            {
                "schedule_type": "minute",
                "time": "14:00",
                "end_time": "15:00",
                "interval": 15
            }
            #HOURLY
            {
                "schedule_type": "hourly",
                "time": "00:42",
                "end_time": "19:45"
            }

            # WEEKLY
            {
                "schedule_type": "weekly",
                "time": "08:30",
                "day_of_week": ["Monday", "Wednesday", "Friday"]
            }

            # MONTHLY
            {
                "schedule_type": "monthly",
                "time": "10:00",
                "day_of_month": "15",
                "months": [1, 3, 5, 7, 9, 11]
            }

        """

        seconds, minutes, hours, day_of_month, months, day_of_week = (
            "0",
            "0",
            "*",
            "*",
            "*",
            "*",
        )

        if schedule.time:
            start_hour = schedule.time.split(":")[0]
            start_minutes = schedule.time.split(":")[1]
            if schedule.end_time:
                end_hour = schedule.end_time.split(":")[0]

                if int(start_hour) >= 24:
                    start_hour = 00
                if int(end_hour) >= 24:
                    end_hour = 00

                hours = f"{start_hour}-{end_hour}"

        if schedule.schedule_type.upper() == ScheduleType.MINUTE.upper():
            minutes = f"0/{schedule.interval}"

        if schedule.schedule_type.upper() == ScheduleType.HOURLY.upper():
            minutes = "0"
            hours = f"{hours}/{schedule.interval}"

        if schedule.schedule_type.upper() == ScheduleType.WEEKLY.upper():
            minutes = f"{start_minutes}"
            hours = f"{start_hour}"
            if int(hours) >= 24:
                hours = 00
            day_of_week = ",".join(
                days_of_week_to_cron[day.lower()] for day in schedule.day_of_week
            )

        if schedule.schedule_type.upper() == ScheduleType.MONTHLY.upper():
            day_of_month = f"{schedule.day_of_month}"
            months = ",".join(str(month) for month in schedule.months)

        return {
            "cron_expression": f"{seconds} {minutes} {hours} {day_of_month} {months} {day_of_week}"
        }

    @classmethod
    def describe(cls, cron_expression: str) -> ScheduleModel:
        def validate_cron_expression(cron_expression: str) -> list:
            if not cron_expression or not isinstance(cron_expression, str):
                raise ValueError("Cron expression must be a non-empty string")
            
            parts = cron_expression.strip().split()
            if len(parts) != 6:
                raise ValueError(f"Invalid cron expression: {cron_expression}")
            
            return parts

        def determine_schedule_type(parts: list) -> ScheduleType:
            _, minute, _, dom, _, dow = parts
            if dow != "*":
                return ScheduleType.WEEKLY
            elif dom != "*" and dom != "?":
                return ScheduleType.MONTHLY
            elif "/" not in minute:
                return ScheduleType.HOURLY
            return ScheduleType.MINUTE

        def parse_time_range(hour: str, minute: str) -> tuple:
            if "-" in hour:
                hour_start, hour_end = hour.split("-")
                hour_start = "24" if int(hour_start) == 0 else hour_start
                hour_end = "24" if int(hour_end) == 0 else hour_end
                return f"{hour_start}:{minute}", f"{hour_end}:{minute}"
            else:
                hour = "24" if int(hour) == 0 else hour
                return f"{hour}:{minute}", None

        def handle_weekly_schedule(schedule: ScheduleModel, parts: list):
            _, minute, hour, _, _, dow = parts
            schedule.schedule_type = ScheduleType.WEEKLY
            schedule.time, _ = parse_time_range(hour, minute)

        def handle_monthly_schedule(schedule: ScheduleModel, parts: list):
            _, _, _, dom, month, _ = parts
            schedule.schedule_type = ScheduleType.MONTHLY
            schedule.day_of_month = dom
            schedule.months = [int(m) for m in month.split(",")] if month != "*" else None

        def handle_minute_schedule(schedule: ScheduleModel, parts: list):
            _, minute, hour, _, _, _ = parts
            minute_split = minute.split("/")
            schedule.interval = int(minute_split[1]) if len(minute_split) > 1 else None
            minute_cron = minute_split[0]
            schedule.time, schedule.end_time = parse_time_range(hour, minute_cron)

        def handle_hourly_schedule(schedule: ScheduleModel, parts: list):
            _, minute, hour, _, _, _ = parts
            minute_cron = f"{minute}" if minute != "0" else "00"
            if "/" in hour:
                schedule.interval = int(hour.split("/")[1])
                hour = hour.split("/")[0]
            else:
                schedule.interval = 1
            schedule.time, schedule.end_time = parse_time_range(hour, minute_cron)

        parts = validate_cron_expression(cron_expression)
        schedule = ScheduleModel(schedule_type=ScheduleType.MINUTE)

        schedule.schedule_type = determine_schedule_type(parts)

        if schedule.schedule_type == ScheduleType.WEEKLY:
            handle_weekly_schedule(schedule, parts)
        elif schedule.schedule_type == ScheduleType.MONTHLY:
            handle_monthly_schedule(schedule, parts)
        elif schedule.schedule_type == ScheduleType.MINUTE:
            handle_minute_schedule(schedule, parts)
        elif schedule.schedule_type == ScheduleType.HOURLY:
            handle_hourly_schedule(schedule, parts)

        return schedule

    @classmethod
    def next(
        cls, cron_expression: str, date_time: Optional[str] = None, qtd: int = 1
    ) -> List[str]:
        try:
            cron_expression = cron_expression[1:]

            if date_time is None:
                date_time = datetime.now().strftime("%Y-%m-%dT%H:%M:%SZ")

            date_start_obj = datetime.strptime(date_time, "%Y-%m-%dT%H:%M:%SZ")

            iterador_cron = croniter(cron_expression, date_start_obj)

            next_dates = []
            for _ in range(qtd):
                next_date = iterador_cron.get_next(datetime)
                next_dates.append(next_date.strftime("%Y-%m-%dT%H:%M:%SZ"))

            return next_dates
        except Exception as e:
            raise print(str(e))
