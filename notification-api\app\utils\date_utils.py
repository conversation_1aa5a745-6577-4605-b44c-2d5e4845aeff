from typing import TypeVar
import datetime


T = TypeVar("T")

class DateUtils:
    """
     [EN] Utility class for date and time conversions with patterns.\n
     [PT-BR] Classe de utilitarios para conversão de datas e tempos com padrões.
    """

    def convertUnixToFormattedTimeStampUTC(milliseconds:int) -> datetime:
        """
            [EN] Function to convert milliseconds to valid Timestamp.\n
            [PT-BR] Função para converter milisegundos para um Timestamp válido.\n

            Converts from: "Number of milliseconds since 00:00:00 Thursday, 1 January 1970, Coordinated Universal Time (UTC), minus leap seconds"
        """
        if milliseconds and milliseconds > 0:
            convertedValue = datetime.datetime.fromtimestamp(milliseconds / 1000)

            return DateUtils.convertDatetimeToFriendlyFormat(convertedValue)
        
        return None
    
    def convertFormattedTimeStampUTCToDatetime(datetimeSource:str) -> datetime:
        """
            [EN] Function to convert String Timestamp to Unix Epoch format.\n
            [PT-BR] Função para converter String Timestamp para formato Unix Epoch.\n

            Converts to: "Number of milliseconds since 00:00:00 Thursday, 1 January 1970, Coordinated Universal Time (UTC), minus leap seconds"
        """
        date_format = datetime.datetime.strptime(datetimeSource, "%Y-%m-%dT%H:%M:%S")
        return date_format

    def convertDatetimeToCogniteFormat(datetimeToConvert:datetime):
        """
            [EN] Function to convert datetime to Cognite database format.\n
            [PT-BR] Função para converter datetime para o formato do database Cognite.\n
        """
        if datetimeToConvert:
            fmt = "%Y-%m-%dT%H:%M:%S"

            return datetimeToConvert.strftime(fmt)
        
        return None

    def convertDatetimeToFriendlyFormat(datetimeToConvert:datetime):
        """
            [EN] Function to convert datetime to a friendly format.\n
            [PT-BR] Função para converter datetime para um formato amigável (Mes/Dia/Ano Hora:Minuto AM/PM).\n
        """
        if datetimeToConvert:
            fmt = "%m/%d/%Y %H:%M %p"

            return datetimeToConvert.strftime(fmt)
        
        return None
    
    def getMinutesBetweenNowAndPastUnix(sourceDateMilliseconds:int) -> int:
        """
            [EN] Function to calculate a days amount between 'Now' and a past UNIX Timestamp (milliseconds).\n
            [PT-BR] Função para calcular a quantidade de dias entre 'Agora' e um UNIX Timestamp (milisegundos) no passado.\n
        """

        converted_d1 = datetime.datetime.fromtimestamp(round(sourceDateMilliseconds / 1000))
        current_time_utc = datetime.datetime.now()

        return int((current_time_utc - converted_d1).total_seconds() / 60)
    
    def getMonthsQuantityBetweenTwoDates(date1:str, date2:str) -> int:
        """
            [EN] Function to calculate months quantity between two string dates.\n
            [PT-BR] Função para calcular quantidade de meses entre duas datas em string.\n
        """

        start = datetime.datetime.strptime(date1, "%Y-%m-%dT%H:%M:%S")
        end = datetime.datetime.strptime(date2, "%Y-%m-%dT%H:%M:%S")

        return (end.year - start.year) * 12 + (end.month - start.month)