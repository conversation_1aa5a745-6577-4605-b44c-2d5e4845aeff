from typing import TypeVar

T = TypeVar("T")

class ListUtils:
    """
     [EN] Utility class for working better with lists.\n
     [PT-BR] Classe de utilitarios para trabalhar melhor com listas
    """
    
    def order(data, fieldsAndOrder):
        """
            [EN] Method for order data list.\n
            [PT-BR] Metodo para ordenar uma lista de dados.\n

            How to use:
                data = [
                    {"name": "A", "age": 20},
                    {"name": "B", "age": 30},
                    {"name": "C", "age": 25}
                ]
                fieldsAndOrder = [("name", "ASC"), ("age", "DESC")]
                data = Utils.cognite.order(data, fieldsAndOrder)
                print(data)
                [{'name': 'A', 'age': 20}, {'name': 'B', 'age': 30}, {'name': 'C', 'age': 25}]
        """
        def custom_key(item):
            key = []
            for field, order in fieldsAndOrder:
                value = item[field]
                if order == "DESC" and isinstance(value, (int, float)):
                    value = -value 
                key.append((value, -value if isinstance(value, (int, float)) else value))
            return key

        data = sorted(data, key=custom_key, reverse=True) 
        return data
    
    def is_null_or_empty(value: str):
        if value == None:
            return True
        elif value.isspace() or value == "":
            return True
        return False
    
    def not_null_or_empty(value: str):
        if value == None:
            return False
        elif value.isspace() or value == "":
            return False
        return True