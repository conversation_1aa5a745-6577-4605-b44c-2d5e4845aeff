import json
import os
import time
import traceback
from typing import Any, List
from settings.settings_class import Settings
from dotenv import load_dotenv
from core.cognite_client_factory import CogniteClientFactory
from core.gql_client_factory import GqlClientFactory
from core.graphql_client import GraphQLClient
import services
from models.common_basic_model import RelationModel
from models.notification_template_model import NotificationTemplateModel
from services.database_cache_service import DatabaseCacheService


class NoSecretsException(Exception):
    pass


class NoSettingsException(Exception):
    pass


def handle():
    start_time = time.time()  # Start timing
    print("*** Starting Function execution... ***")
    # load environment and create client
    try:
        # Load environment variables from .env file
        load_dotenv()
    except Exception as e:
        print("Error loading the environment. " + e)

    settings = Settings()
    cogniteClient = CogniteClientFactory.create(settings)
    gqlClient = GqlClientFactory.create(cogniteClient, settings)
    graphql_client = GraphQLClient(gqlClient)
    notification_types = []

    notificationEventService = services.NotificationEventService(
        cogniteClient, gqlClient, settings, graphql_client
    )

    notificationTemplateService = services.NotificationTemplateService(
        cogniteClient, gqlClient, settings, graphql_client
    )

    notificationDeliverableService = services.NotificationDeliverableService(
        cogniteClient, gqlClient, settings
    )

    notificationOnScreenService = services.NotificationOnScreenService(
        cogniteClient, gqlClient, settings, graphql_client
    )

    dataRawEventArray = notificationEventService.list_notification_raw_event_pending()

    db_cache = DatabaseCacheService(
        cogniteClient, gqlClient, settings, graphql_client
    ).initialize()

    validDataRawEvent = []

    for dataRawEvent in dataRawEventArray:
        if "rawEvent" in dataRawEvent and dataRawEvent["rawEvent"] is not None:
            rawEvent = dataRawEvent["rawEvent"]
            sourceJsonData = rawEvent["sourceJson"]

            if sourceJsonData.get("local-send"):
                print(f"Skipping local event: {dataRawEvent['externalId']}")
                continue

            validation_errors = notificationEventService.validateEvent(
                rawEvent["sourceApplication"], sourceJsonData, db_cache
            )

            if validation_errors:
                print(f"Error processing event: {validation_errors}")
                notificationEventService.stamp_raw_event_log_as_processed(
                    dataRawEvent["externalId"], db_cache, validation_errors
                )
            else: 
                validDataRawEvent.append(dataRawEvent)
                notification_type = notificationEventService.validate_or_create_notification_type(
                    rawEvent["sourceApplication"],
                    rawEvent["sourceJson"],
                    db_cache
                )

                if not any(nt["externalId"] == notification_type.externalId for nt in notification_types):
                    notification_types.append({
                        "externalId": notification_type.externalId,
                        "space": notification_type.space
                    })   

    templates = []
    extensionTemplates= []

    # Get All Templates by type
    if len(notification_types) > 0:
        templates = notificationTemplateService.find_all_by_type(
            notification_types,
            db_cache,
        )

    # Get All extensions by type (mounted as templates)
        extensionTemplates = notificationTemplateService.find_all_extension_details_by_type(
            notification_types
        ) 

    db_cache.cache["templates"] = templates
    db_cache.cache["extensionTemplates"] = extensionTemplates
    
    for dataRawEvent in validDataRawEvent:
        if "rawEvent" in dataRawEvent and dataRawEvent["rawEvent"] is not None:
            rawEvent = dataRawEvent["rawEvent"]
            sourceJsonData = rawEvent["sourceJson"]
            
            # Process each event
            try:
                process_errors = processEvents(
                    notificationEventService,
                    notificationTemplateService,
                    notificationDeliverableService,
                    notificationOnScreenService,
                    rawEvent,
                    db_cache,
                )

                if process_errors:
                    print(f"Error processing event: {process_errors}")

                notificationEventService.stamp_raw_event_log_as_processed(
                    dataRawEvent["externalId"], db_cache, process_errors
                )

            except Exception as e:
                print(f"Error processing event: {e}")
                print(f"Event data: {sourceJsonData}")
                notificationEventService.stamp_raw_event_log_as_processed(
                    dataRawEvent["externalId"], db_cache, [e]
                )
        else:
            notificationEventService.stamp_raw_event_log_as_processed(
                dataRawEvent["externalId"], db_cache, ["Notification Event is empty"]
            )

    print("*** Finishing Function execution... ***")
    end_time = time.time()  # End timing
    execution_time = end_time - start_time
    print(f"Function execution time: {execution_time:.2f} seconds")
    return


def processEvents(
    notificationEventService: services.NotificationEventService,
    notificationTemplateService: services.NotificationTemplateService,
    notificationDeliverableService: services.NotificationDeliverableService,
    notificationOnScreenService: services.NotificationOnScreenService,
    dataRawEvent: Any,
    db_cache: DatabaseCacheService,
) -> List[NotificationTemplateModel]:

    print("* Starting to generate Notifications...")

    boolAnyNotificationCreated = False
    errors = []

    commonEventData = notificationEventService.create(
        dataRawEvent["externalId"],
        dataRawEvent["sourceApplication"],
        dataRawEvent["sourceJson"],
        db_cache,
    )

    notificationReference = dataRawEvent["notificationReference"]

    sourceNotificationType = commonEventData[0]
    sourcePropertiesJSON = commonEventData[1]
    sourceSeverity = commonEventData[2]
    modelSite = commonEventData[3]
    eventAllRelatedUsers = commonEventData[4]
    eventExternalUsers = commonEventData[5]
    eventExternalId = commonEventData[6]
    hasEntitiesOnPayload = commonEventData[7]

    if(hasEntitiesOnPayload and len(eventAllRelatedUsers) == 0):
        print(
            "There are no related users to the event {}. Skipping...".format(
                eventExternalId
            )
        )

        errors.append(f'There are no related users to the event "{eventExternalId}".')
        return errors

    # Get All Templates by type
    templates = notificationTemplateService.find_by_type(
        sourceNotificationType.externalId,
        modelSite.externalId if modelSite else None,
        sourcePropertiesJSON,
        db_cache,
        notificationReference
    )

    # Get All extensions by type (mounted as templates)
    extensionTemplates = notificationTemplateService.find_extension_details_by_type(
        sourceNotificationType.externalId,
        modelSite.externalId if modelSite else None,
        sourcePropertiesJSON,
        db_cache,
        notificationReference,
    )

    # Remove unnecessary users from all templates,
    # because of Parent Linking, before processing them
    if extensionTemplates and len(extensionTemplates) > 0:
        for extensionTemplate in extensionTemplates:
            if extensionTemplate.basedOnExtension:
                parentTemplate = next(
                    filter(
                        lambda template: template.externalId
                        == extensionTemplate.externalId,
                        templates,
                    ),
                    None,
                )

                if parentTemplate and (
                    extensionTemplate.creator in parentTemplate.summarizedUsers
                ):
                    parentTemplate.summarizedUsers.remove(extensionTemplate.creator)

        templates.extend(extensionTemplates)

    if templates and len(templates) > 0:
        for template in templates:
            try:
                # Get Template severity as default if empty
                if not sourceSeverity:
                    severity = template.severity
                else:
                    severity = sourceSeverity

                # All Users template validation
                if template.allUsers:
                    if eventAllRelatedUsers is None or len(eventAllRelatedUsers) == 0:
                        template.summarizedUsers = []
                    else:
                        template.summarizedUsers = eventAllRelatedUsers

                # Remove from Users List of template the users not contained in the Json Source List
                if eventAllRelatedUsers and len(eventAllRelatedUsers) > 0:
                    cleanUsersList = [
                        user
                        for user in template.summarizedUsers
                        if user in eventAllRelatedUsers
                    ]
                    template.summarizedUsers = cleanUsersList

                # Remove from Users List of template the users contained in blocklist
                if (
                    template.summarizedBlockedUsers
                    and len(template.summarizedBlockedUsers) > 0
                ):
                    cleanUsersList = [
                        user
                        for user in template.summarizedUsers
                        if user not in template.summarizedBlockedUsers
                    ]
                    template.summarizedUsers = cleanUsersList

                # Set external users per event and template
                if (
                    template.externalUsers
                    and eventExternalUsers is not None
                    and len(eventExternalUsers) > 0
                ):
                    if template.summarizedExternalUsers is not None and len(
                        template.summarizedExternalUsers
                    ):
                        cleanExternalUserList = [
                            item
                            for item in template.summarizedExternalUsers
                            if item in eventExternalUsers
                        ]
                        template.summarizedExternalUsers = cleanExternalUserList
                    else:
                        template.summarizedExternalUsers = eventExternalUsers
                else:
                    template.summarizedExternalUsers = []

                # Create OnScreen objects by template
                ntfCreated = notificationOnScreenService.createByTemplate(
                    template,
                    sourcePropertiesJSON,
                    eventExternalId,
                    db_cache,
                    modelSite,
                    RelationModel.mapFromJson(severity),
                    notificationReference,
                )

                if ntfCreated:
                    # Create Deliverable objects by template
                    boolAnyNotificationCreated = notificationDeliverableService.create(
                        template,
                        severity,
                        eventExternalId,
                        db_cache,
                        notificationReference,
                        modelSite=modelSite,
                    )

            except Exception as e:
                tb = traceback.extract_tb(e.__traceback__)
                last_frame = tb[0]
                full_path = last_frame.filename
                filename = os.path.basename(full_path)
                function = last_frame.name
                line = last_frame.lineno
                print(f"EventId: {eventExternalId} - {type(e).__name__} in file '{filename}', function '{function}', line {line}: {e}")
                continue

    if boolAnyNotificationCreated:
        print("* Notifications created.")
    else:
        print("* No Notifications to be created.")
    return


# if __name__ == "__main__":
#     handle()
