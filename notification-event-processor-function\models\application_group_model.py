from pydantic import BaseModel
from typing import Any, List, Optional
import models as models


class ApplicationGroupModel(BaseModel):
    externalId: str
    name: str
    description: Optional[str] = None
    userIds: Optional[List[str]] = []
    users: Optional[Any] = []

    def mapFromResult(item: Any):
        return ApplicationGroupModel(
            externalId=item.get("externalId", ""),
            name=item.get("name", ""),
            description=item.get("description", ""),
            userIds=[
                subitem.get("externalId")
                for subitem in item.get("users", {}).get("items", [])
                if subitem
            ]
            if item.get("users")
            else [],
        )


def parse_application_groups(data: List[dict]) -> List[ApplicationGroupModel]:
    application_groups = []
    for group in data:
        users = [
            models.UserModel(**user) for user in group.get("users", {}).get("items", [])
        ]
        application_groups.append(
            ApplicationGroupModel(
                name=group["name"],
                externalId=group["externalId"],
                users=users,
                usersId=[user.externalId for user in users],
            )
        )
    return application_groups
