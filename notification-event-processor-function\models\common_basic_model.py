from typing import Any
from pydantic import BaseModel

class SingleBasicModel(BaseModel):
    externalId: str

class CommonBasicModel(BaseModel):
    externalId: str
    name: str
    description: str
    space: str

    def mapFromResult(item: Any):
        return CommonBasicModel(
            name=item.get("name", ""),
            description=item.get("description", "") if item.get("description", "") else "",
            externalId=item.get("externalId", ""),
            space=item.get("space", "")
        )
    
class RelationModel(BaseModel):
    space: str
    externalId: str

    def mapFromResult(item: Any):
        return RelationModel(
            externalId=item.get("externalId", "") if item else "",
            space=item.get("space", "") if item else ""
        )
    
    def mapFromJson(item: dict):
        return RelationModel(
            externalId=item.externalId if item else "",
            space=item.space if item else ""
        )