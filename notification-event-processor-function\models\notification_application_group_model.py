from typing import Any, List, Optional
from models.common_basic_model import CommonBasicModel, RelationModel
from pydantic import BaseModel


class NotificationApplicationGroupRolesUserModel(BaseModel):
    externalId: str
    space: str


class NotificationApplicationGroupRolesModel(BaseModel):
    externalId: str
    space: str
    users: List[NotificationApplicationGroupRolesUserModel]


class NotificationApplicationGroupModel(BaseModel):
    externalId: str
    name: str
    description: str
    space: str
    application: CommonBasicModel
    users: Optional[List[RelationModel]] = []
    blocklist: Optional[List[RelationModel]] = []
    externalUsers: Optional[List[str]] = []
    usersRoles: Optional[List[NotificationApplicationGroupRolesModel]] = []
    blocklistRoles: Optional[List[NotificationApplicationGroupRolesModel]] = []

    def mapFromResult(item: Any):
        return NotificationApplicationGroupModel(
            externalId=item.get("externalId", ""),
            name=item.get("name", ""),
            description=item.get("description", ""),
            space=item.get("space", ""),
            application=CommonBasicModel.mapFromResult(item.get("application", [])),
            users=(
                [
                    RelationModel.mapFromResult(subitem)
                    for subitem in item.get("users", [])
                    if subitem
                ]
                if item.get("users")
                else []
            ),
            blocklist=(
                [
                    RelationModel.mapFromResult(subitem)
                    for subitem in item.get("blocklist", [])
                    if subitem
                ]
                if item.get("blocklist")
                else []
            ),
            externalUsers=item.get("externalUsers", []),
            usersRoles=(
                [
                    NotificationApplicationGroupRolesModel(**subitem)
                    for subitem in item.get("usersRoles", [])
                    if subitem
                ]
                if item.get("usersRoles")
                else []
            ),
            blocklistRoles=(
                [
                    NotificationApplicationGroupRolesModel(**subitem)
                    for subitem in item.get("blocklistRoles", [])
                    if subitem
                ]
                if item.get("blocklistRoles")
                else []
            ),
        )
