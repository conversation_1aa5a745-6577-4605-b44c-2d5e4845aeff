from models.common_basic_model import RelationModel
from pydantic import BaseModel
from typing import List, Optional


class NotificationDeliverableCreateModel(BaseModel):

    subscribers: List[RelationModel] = None
    externalSubscribers: Optional[List[str]] = None
    template: RelationModel = None
    text: str = None
    channel: RelationModel = None
    scheduleDate: str = None
    reportingSite: Optional[RelationModel] = None
    severity: RelationModel = None
    event: Optional[RelationModel] = None
