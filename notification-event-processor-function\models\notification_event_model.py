from models.common_basic_model import RelationModel
from pydantic import BaseModel
from typing import Any, List, Optional


class NotificationEventModel(BaseModel):

    application: str
    description: str
    notificationType: str
    features: Optional[List[str]]
    roles: Optional[List[str]]
    applicationGroups: Optional[List[str]]
    users: Optional[List[str]]
    externalUsers: Optional[List[str]]
    severity: Optional[str]
    properties: str


class NotificationEventCreateModel(BaseModel):

    notificationType: RelationModel = None
    roles: Optional[List[RelationModel]] = None
    applicationGroups: Optional[List[RelationModel]] = None
    users: Optional[List[RelationModel]] = None
    externalUsers: Optional[List[str]] = None
    severity: RelationModel = None
    properties: Any = None
    rawEvent: RelationModel = None
    reportingSite: Optional[RelationModel] = None
