from typing import List, Optional

from models.common_basic_model import RelationModel
from pydantic import BaseModel, Field
from utils.datetime_utils import now_formatted


class NotificationOnScreenCreateModel(BaseModel):

    subscribers: Optional[List[RelationModel]] = None
    template: Optional[RelationModel] = None
    severity: Optional[RelationModel] = None
    text: str = None
    reportingSite: Optional[RelationModel] = None
    event: Optional[RelationModel] = None
    newUpdateTime: Optional[str] = None
    createdAt: Optional[str] = Field(default_factory=now_formatted)
 