from typing import Any, List
from pydantic import BaseModel
from models.common_basic_model import RelationModel
from models.notification_user_model import NotificationUserAttributeBasicModel


class NotificationRoleModel(BaseModel):
    name: str
    description: str
    externalId: str
    space: str
    usersComplements: List[RelationModel]
    # application:[RelationModel]

    def mapFromResult(item: Any):
        return NotificationRoleModel(
            name=item.get("role").get("name", "") if (item.get("role")) else "",
            description=item.get("role").get("description", "")
            if (item.get("role"))
            else "",
            externalId=item.get("role").get("externalId", "")
            if (item.get("role"))
            else "",
            space=item.get("role").get("space", "") if (item.get("role")) else "",
            usersComplements=[
                RelationModel.mapFromResult(
                    subitem.get("userAzureAttribute", None).get("user", None)
                )
                for subitem in item.get("role", {})
                .get("usersComplements", [])
                .get("items", [])
                if subitem and subitem.get("userAzureAttribute", None)
            ]
            if item.get("role") and item.get("role", {}).get("usersComplements", [])
            else [],
        )
