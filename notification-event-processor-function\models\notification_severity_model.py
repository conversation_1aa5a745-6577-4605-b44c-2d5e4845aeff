from typing import Any
from pydantic import BaseModel

class NotificationSeverityModel(BaseModel):
    externalId: str
    space:str
    name: str
    description: str

    def mapFromResult(item: Any):
        return NotificationSeverityModel(
            name=item.get("name", ""),
            description=item.get("description", ""),
            externalId=item.get("externalId", ""),
            space=item.get("space", ""),
        )