from pydantic import BaseModel
from typing import Any, List, Optional
from models.common_basic_model import RelationModel

class NotificationTemplateExtensionModel(BaseModel):
    externalId: str
    owner: RelationModel
    channels: Optional[List[RelationModel]]
    frequencyCronExpression: Optional[str]

    def mapFromResult(item: Any):
        return NotificationTemplateExtensionModel(
            externalId=item.get("externalId", ""),
            owner=RelationModel.mapFromResult(item.get("owner", None)),
            channels=[
                RelationModel.mapFromResult(subitem)
                for subitem in item.get("channels", {}).get("items", [])
                if subitem
            ]
            if item.get("channels")
            else [],
            frequencyCronExpression=item.get("frequencyCronExpression", ""),
        )
