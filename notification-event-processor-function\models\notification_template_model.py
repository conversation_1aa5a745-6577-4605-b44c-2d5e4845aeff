from typing import Any, List, Optional
from pydantic import BaseModel
from models.common_basic_model import RelationModel
from models.notification_type_model import NotificationTypeModel
from models.notification_severity_model import NotificationSeverityModel


class NotificationTemplateModel(BaseModel):

    externalId: Optional[str] = None
    name: str
    space: str
    creator: Optional[RelationModel] = None
    notificationType: NotificationTypeModel
    text: str
    severity: NotificationSeverityModel
    customFrequencyEnabled: bool = False
    customChannelEnabled: bool = False
    conditionalExpression: str
    adminLevel: Optional[bool]
    frequencyCronExpression: str
    channels: Optional[List[RelationModel]]
    subscribedUsers: Optional[List[RelationModel]] = None
    subscribedRoles: Optional[List[RelationModel]] = None
    subscribedApplicationGroups: Optional[List[RelationModel]] = None
    basedOnExtension: bool = False
    parentTemplateId: Optional[str] = None
    summarizedUsers: Optional[List[RelationModel]] = []
    allUsers: Optional[bool] = False
    externalUsers: Optional[bool] = False
    subscribedExternalUsers: Optional[List[str]] = None
    blocklist:  Optional[List[RelationModel]] = None
    summarizedBlockedUsers:  Optional[List[RelationModel]] = []
    summarizedExternalUsers: Optional[List[str]] = []
    blocklistRoles: Optional[List[RelationModel]] = None
    reportingSite: Optional[RelationModel] = None
    # applicationIconInfo: Optional[dict] = None

    def mapFromResult(item: Any):
        frequency = ""
        if item.get("frequencyCronExpression", "") is not None:
            frequency = item.get("frequencyCronExpression", "")

        return NotificationTemplateModel(
            name=item.get("name", ""),
            externalId=item.get("externalId", ""),
            space=item.get("space", ""),
            creator=RelationModel.mapFromResult(item.get("creator", None)),
            notificationType=NotificationTypeModel.mapFromResult(
                item.get("notificationType") if item.get("notificationType") else None
            ),
            text=item.get("text", ""),
            severity=NotificationSeverityModel.mapFromResult(
                item.get("severity") if item.get("severity") else None
            ),
            conditionalExpression=item.get("conditionalExpression", ""),
            adminLevel=item.get("adminLevel", False),
            frequencyCronExpression=frequency,
            channels=(
                [
                    RelationModel.mapFromResult(subitem)
                    for subitem in item.get("channels", [])
                    if subitem
                ]
                if item.get("channels")
                else []
            ),
            subscribedUsers=(
                [
                    RelationModel.mapFromResult(subitem)
                    for subitem in item.get("subscribedUsers", [])
                    if subitem
                ]
                if item.get("subscribedUsers")
                else []
            ),
            subscribedRoles=(
                [
                    RelationModel.mapFromResult(subitem)
                    for subitem in item.get("subscribedRoles", [])
                    if subitem
                ]
                if item.get("subscribedRoles")
                else []
            ),
            subscribedApplicationGroups=(
                [
                    RelationModel.mapFromResult(subitem)
                    for subitem in item.get("subscribedApplicationGroups", [])
                    if subitem
                ]
                if item.get("subscribedApplicationGroups")
                else []
            ),
            allUsers=item.get("allUsers"),
            externalUsers=item.get("externalUsers"),
            subscribedExternalUsers=item.get("subscribedExternalUsers", []),
            blocklist=(
                [
                    RelationModel.mapFromResult(subitem)
                    for subitem in item.get("blocklist", [])
                    if subitem
                ]
                if item.get("blocklist")
                else []
            ),
            blocklistRoles=(
                [
                    RelationModel.mapFromResult(subitem)
                    for subitem in item.get("blocklistRoles", [])
                    if subitem
                ]
                if item.get("blocklistRoles")
                else []
            ),
            reportingSite=item.get("reportingSite")
        )

    def mapFromExtensionResult(item: Any):
        template_frequency = item.get("template").get("frequencyCronExpression", "")
        template_frequency = "" if template_frequency is None else template_frequency
        frequency = item.get("frequencyCronExpression", "") if item.get("template").get("customFrequencyEnabled", False) else template_frequency

        return NotificationTemplateModel(
            basedOnExtension=True,
            name=item.get("template").get("name", ""),
            externalId=item.get("template").get("externalId", ""),
            space=item.get("template").get("space", ""),
            creator=RelationModel.mapFromResult(
                item.get("owner", None) if not item.get("owner") is None else None
            ),
            notificationType=NotificationTypeModel.mapFromResult(
                item.get("template").get("notificationType")
                if item.get("template").get("notificationType")
                else None
            ),
            text=item.get("template").get("text", ""),
            adminLevel=item.get("adminLevel", False),
            severity=NotificationSeverityModel.mapFromResult(
                item.get("template").get("severity")
                if item.get("template").get("severity")
                else None
            ),
            conditionalExpression=item.get("template").get("conditionalExpression", ""),
            customFrequencyEnabled=item.get("template").get(
                "customFrequencyEnabled", False
            ),
            customChannelEnabled=item.get("template").get(
                "customChannelEnabled", False
            ),
            frequencyCronExpression=frequency,
            reportingSite=item.get("template").get("reportingSite"),
            channels=(
                (
                    [
                        RelationModel.mapFromResult(subitem)
                        for subitem in item.get("channels", {}).get("items", [])
                        if subitem
                    ]
                    if item.get("channels")
                    else []
                )
                if item.get("template").get("customChannelEnabled", False)
                else (
                    [
                        RelationModel.mapFromResult(subitem)
                        for subitem in item.get("template")
                        .get("channels", {})
                        .get("items", [])
                        if subitem
                    ]
                    if item.get("template").get("channels")
                    else []
                )
            ),
        )
