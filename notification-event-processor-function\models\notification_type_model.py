from typing import Any, Optional, Dict, List
from pydantic import BaseModel
from models.notification_application_model import NotificationApplicationModel
from models.common_basic_model import RelationModel


class NotificationTypeModel(BaseModel):

    name: str
    description: str
    externalId: str
    space: str
    application: NotificationApplicationModel
    entityType: Optional[str] = ""
    properties: Optional[List[Dict[str, Any]]] = []

    def mapFromResult(item: Any):
        return NotificationTypeModel(
            name=item.get("name", ""),
            description=item.get("description", ""),
            externalId=item.get("externalId", ""),
            space=item.get("space", ""),
            application=(NotificationApplicationModel.mapFromResult(item.get("application")) 
                         if item.get("application") 
                         else None),
            entityType=(item.get("entityType", "") if item.get("entityType", "") else ""),
            properties=item.get("properties", [])
        )
    
class NotificationTypeCreateModel(BaseModel):
    code: str = "NTFTYP"
    name: str = ""
    description: str = ""
    externalId: str = ""
    application: RelationModel = None
    entityType: Optional[str] = ""
    properties: List[Dict[str, Any]] = []

    def __setattr__(self, key, value):
        if key == "code":
            raise AttributeError("Cannot modify CODE")
        super().__setattr__(key, value)

    def generate_external_id(self) -> str:
        result = (
            (self.code + "-" + self.name + "-" + self.application.externalId)
            .replace(" ", "-")
            .replace("(", "")
            .replace(")", "")
            .upper()
        )
        return result
    
    def format_name(self) -> str:
        result = (
            self.name
            .strip()
        )
        return result
    
    def format_description(self) -> str:
        result = (
            self.description
            .strip()
        )
        return result
    
    def format_entities(self) -> str:
        result = (
            self.entityType
            .strip()
        )
        return result