from typing import Any, List
from pydantic import BaseModel
from models.common_basic_model import RelationModel


class NotificationUserModel(BaseModel):
    externalId: str
    displayName: str
    firstName: str
    lastName: str
    email: str
    usersComplements:List[RelationModel]

    def mapFromResult(item: Any):
        return NotificationUserModel(
            displayName=item.get("displayName",""),
            firstName=item.get("firstName",""),
            lastName=item.get("lastName",""),
            description=item.get("description", ""),
            externalId=item.get("externalId", ""),
            usersComplements=[
                RelationModel.mapFromResult(subitem.get("usersComplements",None))
                for subitem in item.get("usersComplements", {}).get("items", [])
                if subitem
            ]
            if item.get("usersComplements")
            else []
        )
    
class NotificationUserAttributeBasicModel(BaseModel):
    userAzureAttribute:List[RelationModel]

    def mapFromResult(item: Any):
        return NotificationUserAttributeBasicModel(
            userAzureAttribute=[
                RelationModel.mapFromResult(subitem.get("userAzureAttribute",None))
                for subitem in item.get("userAzureAttribute", {}).get("items", [])
                if subitem
            ]
            if item.get("userAzureAttribute")
            else []
        )
    
class NotificationUserTemplateModel(BaseModel):
    userExternalId: str
    templateExternalId: str