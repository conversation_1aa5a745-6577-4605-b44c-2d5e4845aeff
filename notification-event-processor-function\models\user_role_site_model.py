from typing import Any, List, Optional
from pydantic import BaseModel
from models.common_basic_model import RelationModel


class UserRoleSiteModel(BaseModel):
    roleName: str
    roleDescription: Optional[str] = ""
    externalId: str
    space: str
    usersComplements: List[RelationModel]
    # application:[RelationModel]

    def mapFromResult(item: Any):
        return UserRoleSiteModel(
            roleName=item.get("roleName", ""),
            roleDescription=item.get("roleDescription", ""),
            externalId=item.get("externalId", ""),
            space=item.get("space", ""),
            usersComplements=[
                RelationModel.mapFromResult(subitem)
                for subitem in item.get("usersComplements", [])
                if subitem and subitem.get("externalId", None)
            ]
            if item.get("usersComplements", [])
            else []
        )