NOTIFICATION_CACHE_QUERY = """
query getBatchData {
  listNotificationApplication(first: 1000) {
    items {
      externalId
      space
      name
      alias
      description
      iconUrl
    }
  }
  listNotificationSeverity(first: 1000) {
    items {
      externalId
      space
      name
      description
    }
  }
}
"""

NOTIFICATION_TYPE_CACHE_QUERY = """
query getNotificationType($filter: _ListNotificationTypeFilter) {
  listNotificationType(
    filter: $filter
    first: 1000
  ) {
    items {
      name
      description
      externalId
      space
      application {
        name
        alias
        externalId
        description
      }
      entityType
      properties
    }
  }
}
"""
