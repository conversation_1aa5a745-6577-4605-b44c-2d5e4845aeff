
NOTIFICATION_RAW_EVENT_LOG_LIST = """
    query listRawEventLog($filter: _ListNotificationRawEventLogFilter) {
        listNotificationRawEventLog(filter: $filter, first: 1000) {
            items {
              externalId
              processedDate
              processResult
              rawEvent{
                externalId
                sourceApplication {
                    externalId
                    name
                    space
                }
                sourceJson
                space
                notificationReference {
                  externalId
                  space
                  template {
                    externalId
                  }
                }
              }
            }
        }
    }
"""
