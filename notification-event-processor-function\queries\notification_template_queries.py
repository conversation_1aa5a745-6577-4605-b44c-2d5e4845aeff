NOTIFICATION_TEMPLATE_BY_TYPE = """
    query GetNotificationTemplates($filter: _ListNotificationTemplateFilter) {
        listNotificationTemplate(filter: $filter) {
                items {
                externalId
                name
                space
                creator{
                    externalId
                    space
                }
                notificationType {
                    externalId
                    name
                    description
                    application {
                        externalId
                        name
                        alias
                        description
                        iconUrl
                    }
                    entityType
                }
                text
                severity {
                    externalId
                    space
                    name
                    description
                }
                conditionalExpression
                adminLevel
                channels {
                    items {
                        externalId
                        space
                    }
                }
                frequencyCronExpression
                subscribedUsers {
                    items {
                        externalId
                        space
                    }
                }
                subscribedRoles {
                    items {
                        externalId
                        space
                        role {
                            space
                            externalId
                            description
                        }
                    }
                }
                subscribedApplicationGroups {
                    items {
                        externalId
                        space
                        description
                        name
                    }
                }
                allUsers
                externalUsers
                subscribedExternalUsers
                blocklist {
                    items {
                        externalId
                        space
                    }
                }
            }
        }
    }
"""


NOTIFICATION_TEMPLATE_EXTENSION_BY_TYPE = """
    query GetNotificationTemplateExtensions($filter: _ListNotificationTemplateExtensionFilter) {
        listNotificationTemplateExtension(filter: $filter){
            items{
                externalId
                owner{
                    externalId
                    space
                }
                template{
                    externalId
                    space
                    name
                    customFrequencyEnabled
                    customChannelEnabled
                    notificationType {
                        externalId
                        name
                        description
                        application {
                            externalId
                            name
                            alias
                        }
                        entityType
                    }
                    text
                    severity {
                        externalId
                        space
                    }
                    conditionalExpression
                    adminLevel
                    channels {
                        items {
                            externalId
                            space
                        }
                    }
                    frequencyCronExpression
                    reportingSite {
                        externalId
                        space
                    }
                }
                channels {
                    items {
                        externalId
                        space
                    }
                }
                frequencyCronExpression
            }
        }
    }
"""
