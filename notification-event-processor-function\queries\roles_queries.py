SEARCH_ROLE_USERS_BY_ROLE_APP_SITE = """
    query getUserRoleSite_By_Site_and_App($filter: _ListNotificationUserRoleSiteFilter){
        listNotificationUserRoleSite(filter:$filter){
            items{
                externalId
                space
                usersComplements{
                    items{
                        userAzureAttribute{
                            user{
                                externalId
                                space
                            }
                        }
                    }
                }
                role{
                    externalId
                    name
                    description
                    space
                    application {
                        externalId
                    }
                    roleCategory {
                        externalId
                    }
                }
                reportingSite {
                    externalId
                    name
                    description
                    space
                }
            }
        }
    }
"""

LIST_USER_BY_FILTER = """
    query GetNotificationUserByFilter($filter: _ListNotificationUserFilter, $first: Int = 1000, $after: String) {
        listNotificationUser(filter: $filter, first: $first, after: $after) {
            items {
                externalId
                firstName
                lastName
                email
                active
            }
        }
    }
"""
