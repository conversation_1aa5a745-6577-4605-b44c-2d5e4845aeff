from typing import List
from gql import Client
import models
from settings.settings_class import Settings
from core.graphql_client import GraphQLClient
from cognite.client import CogniteClient
from cognite.client.data_classes.data_modeling import ViewId, PropertyId
from cognite.client.data_classes.data_modeling.query import (
    Query,
    Select,
    NodeResultSetExpression,
    EdgeResultSetExpression,
    SourceSelector,
)
from cognite.client.data_classes.filters import Equals, HasData, And, In
from services.database_cache_service import DatabaseCacheService

NOTIFICATION_APPLICATION_GROUP_ENTITY = "NotificationApplicationGroup"
NOTIFICATION_USER_ENTITY = "NotificationUser"
APPLICATION_ENTITY = "Application"
NOTIFICATION_USER_ROLE_ENTITY = "NotificationUserRoleSite"
USER_COMPLEMENT = "UserComplement"
USER_AZURE_ATTRIBUTE = "UserAzureAttribute"
USER = "User"


class NotificationApplicationGroupRepository:
    def __init__(
        self,
        gqlClient: Client,
        settings: Settings,
        graphql_client: GraphQLClient,
        cognite_client: CogniteClient,
    ):
        self.gqlClient = gqlClient
        self.settings = settings
        self._graphql_client = graphql_client
        self._cognite_client = cognite_client

    def find_view_by_external_id(self, views_list, external_id):
        return next(
            (view for view in views_list if view.external_id == external_id), None
        )

    def list_by_filter(
        self,
        application: str,
        applicationGroups: List[str],
        filterBy: str,
        db_cache: DatabaseCacheService,
    ) -> List[models.NotificationApplicationGroupModel]:
        items = []
        if application is None or len(applicationGroups) == 0:
            return items

        notification_views = db_cache.get(
            "cognite_views"
        )[self.settings.cognite_graphql_model_space]

        user_management_views = db_cache.get(
            "cognite_views"
        )[self.settings.um_model_space]

        notification_user_view = self.find_view_by_external_id(
            notification_views,
            NOTIFICATION_USER_ENTITY,
        )

        notification_application_group_view = self.find_view_by_external_id(
            notification_views,
            NOTIFICATION_APPLICATION_GROUP_ENTITY,
        )

        application_view = self.find_view_by_external_id(
            user_management_views,
            APPLICATION_ENTITY,
        ).version

        notification_user_role_site_view = self.find_view_by_external_id(
            notification_views,
            NOTIFICATION_USER_ROLE_ENTITY,
        ).version

        user_complement_view = self.find_view_by_external_id(
            user_management_views,
            USER_COMPLEMENT,
        ).version

        user_azure_attribute_view = self.find_view_by_external_id(
            user_management_views,
            USER_AZURE_ATTRIBUTE,
        ).version

        user_view = self.find_view_by_external_id(
            user_management_views,
            USER,
        )

        views = {
            "notification_user": notification_user_view.version,
            "notification_application_group": notification_application_group_view.version,
            "application": application_view,
            "notification_user_role_site": notification_user_role_site_view,
            "user_complement": user_complement_view,
            "user_azure_attribute": user_azure_attribute_view,
            "user": user_view,
        }

        application_groups_cursor = None
        users_in_blocklist_application_groups_cursor = None
        users_in_application_groups_cursor = None
        application_info_cursor = None
        user_complement_in_blocklist_roles_cursor = None
        user_complement_in_users_roles_cursor = None
        user_azure_attribute_in_blocklist_roles_in_application_groups_cursor = None
        user_azure_attribute_in_users_roles_in_application_groups_cursor = None
        roles_in_blocklist_application_groups_cursor = None
        roles_in_application_groups_cursor = None

        has_cursor = True
        response_query = None
        response = []
        items = []
        filter_variable = {
            "byName": In(
                notification_application_group_view.as_property_ref("name"),
                applicationGroups,
            ),
            "byExternalId": (
                In(
                    ["node", "externalId"],
                    [app.get("externalId") for app in applicationGroups],
                )
                if filterBy == "byExternalId"
                else {}
            ),
        }

        query = Query(
            with_={
                "application_groups": NodeResultSetExpression(
                    filter=And(
                        Equals(
                            notification_application_group_view.as_property_ref(
                                "application"
                            ),
                            {
                                "externalId": application,
                                "space": self.settings.um_instance_space,
                            },
                        ),
                        Equals(["node", "space"], self.settings.ntf_instance_space),
                        filter_variable.get(filterBy),
                    ),
                    limit=10000,
                    chain_to="destination",
                    direction="outwards",
                ),
                "subscribed_users_in_application_groups": EdgeResultSetExpression(
                    from_="application_groups",
                    limit=10000,
                    direction="outwards",
                    chain_to="destination",
                    filter=Equals(
                        ["edge", "type"],
                        {
                            "space": self.settings.cognite_graphql_model_space,
                            "externalId": "NotificationApplicationGroup.users",
                        },
                    ),
                    node_filter=HasData(
                        views=[
                            ViewId(
                                self.settings.cognite_graphql_model_space,
                                NOTIFICATION_USER_ENTITY,
                                notification_user_view.version,
                            )
                        ]
                    ),
                ),
                "users_in_application_groups": NodeResultSetExpression(
                    from_="subscribed_users_in_application_groups",
                    filter=Equals(
                        notification_user_view.as_property_ref("deleted"), False
                    ),
                    limit=10000,
                ),
                "blocklist_in_application_groups": EdgeResultSetExpression(
                    from_="application_groups",
                    limit=10000,
                    direction="outwards",
                    chain_to="destination",
                    filter=Equals(
                        ["edge", "type"],
                        {
                            "space": self.settings.cognite_graphql_model_space,
                            "externalId": "NotificationApplicationGroup.blocklist",
                        },
                    ),
                    node_filter=HasData(
                        views=[
                            ViewId(
                                self.settings.cognite_graphql_model_space,
                                NOTIFICATION_USER_ENTITY,
                                notification_user_view.version,
                            )
                        ]
                    ),
                ),
                "users_in_blocklist_application_groups": NodeResultSetExpression(
                    from_="blocklist_in_application_groups",
                    filter=Equals(
                        notification_user_view.as_property_ref("deleted"), False
                    ),
                    limit=10000,
                ),
                "application_info": NodeResultSetExpression(
                    from_="application_groups",
                    limit=10000,
                    chain_to="destination",
                    direction="outwards",
                    through=PropertyId(
                        ViewId(
                            self.settings.cognite_graphql_model_space,
                            NOTIFICATION_APPLICATION_GROUP_ENTITY,
                            notification_application_group_view.version,
                        ),
                        "application",
                    ),
                ),
                # getting the roles users in the application groups
                "user_roles_application_groups": EdgeResultSetExpression(
                    from_="application_groups",
                    limit=10000,
                    direction="outwards",
                    chain_to="destination",
                    filter=Equals(
                        ["edge", "type"],
                        {
                            "space": self.settings.cognite_graphql_model_space,
                            "externalId": "NotificationApplicationGroup.usersRoles",
                        },
                    ),
                    node_filter=HasData(
                        views=[
                            ViewId(
                                self.settings.cognite_graphql_model_space,
                                NOTIFICATION_USER_ROLE_ENTITY,
                                notification_user_role_site_view,
                            )
                        ]
                    ),
                ),
                # getting the blocklist roles in the application groups
                "blocklist_roles_application_groups": EdgeResultSetExpression(
                    from_="application_groups",
                    limit=10000,
                    direction="outwards",
                    chain_to="destination",
                    filter=Equals(
                        ["edge", "type"],
                        {
                            "space": self.settings.cognite_graphql_model_space,
                            "externalId": "NotificationApplicationGroup.blocklistRoles",
                        },
                    ),
                    node_filter=HasData(
                        views=[
                            ViewId(
                                self.settings.cognite_graphql_model_space,
                                NOTIFICATION_USER_ROLE_ENTITY,
                                notification_user_role_site_view,
                            )
                        ]
                    ),
                ),
                # getting the roles in the subscribed roles
                "roles_in_application_groups": NodeResultSetExpression(
                    from_="user_roles_application_groups", limit=10000
                ),
                # getting the roles in the blocklist roles
                "roles_in_blocklist_application_groups": NodeResultSetExpression(
                    from_="blocklist_roles_application_groups", limit=10000
                ),
                # getting the users complements from the roles
                "user_complement_in_users_roles_in_application_groups": EdgeResultSetExpression(
                    from_="user_roles_application_groups",
                    limit=10000,
                    direction="inwards",
                    chain_to="destination",
                    filter=Equals(
                        ["edge", "type"],
                        {
                            "space": self.settings.um_model_space,
                            "externalId": "UserComplement.userRoleSite",
                        },
                    ),
                    node_filter=HasData(
                        views=[
                            ViewId(
                                self.settings.um_model_space,
                                USER_COMPLEMENT,
                                user_complement_view,
                            )
                        ]
                    ),
                ),
                # getting the users complements from the blocklist roles
                "user_complement_in_blocklist_roles_in_application_groups": EdgeResultSetExpression(
                    from_="blocklist_roles_application_groups",
                    limit=10000,
                    direction="inwards",
                    chain_to="destination",
                    filter=Equals(
                        ["edge", "type"],
                        {
                            "space": self.settings.um_model_space,
                            "externalId": "UserComplement.userRoleSite",
                        },
                    ),
                    node_filter=HasData(
                        views=[
                            ViewId(
                                self.settings.um_model_space,
                                USER_COMPLEMENT,
                                user_complement_view,
                            )
                        ]
                    ),
                ),
                # getting the user azure attribute from users complements from the roles
                "user_complement_in_users_roles": NodeResultSetExpression(
                    from_="user_complement_in_users_roles_in_application_groups",
                    limit=10000,
                ),
                # getting the user azure attribute from users complements from the blocklist roles
                "user_complement_in_blocklist_roles": NodeResultSetExpression(
                    from_="user_complement_in_blocklist_roles_in_application_groups",
                    limit=10000,
                ),
                # getting the user azure attribute from users complements from the roles
                "user_azure_attribute_in_users_roles_in_application_groups": NodeResultSetExpression(
                    from_="user_complement_in_users_roles",
                    limit=10000,
                    chain_to="destination",
                    direction="outwards",
                    through=PropertyId(
                        ViewId(
                            self.settings.um_model_space,
                            USER_COMPLEMENT,
                            user_complement_view,
                        ),
                        "userAzureAttribute",
                    ),
                ),
                # getting the user azure attribute from users complements from the blocklist roles
                "user_azure_attribute_in_blocklist_roles_in_application_groups": NodeResultSetExpression(
                    from_="user_complement_in_blocklist_roles",
                    limit=10000,
                    chain_to="destination",
                    direction="outwards",
                    through=PropertyId(
                        ViewId(
                            self.settings.um_model_space,
                            USER_COMPLEMENT,
                            user_complement_view,
                        ),
                        "userAzureAttribute",
                    ),
                ),
            },
            select={
                "application_groups": Select(
                    [
                        SourceSelector(
                            ViewId(
                                self.settings.cognite_graphql_model_space,
                                NOTIFICATION_APPLICATION_GROUP_ENTITY,
                                notification_application_group_view.version,
                            ),
                            ["*"],
                        )
                    ],
                    limit=10000,
                ),
                "subscribed_users_in_application_groups": Select(limit=10000),
                "users_in_application_groups": Select(
                    [
                        SourceSelector(
                            ViewId(
                                self.settings.cognite_graphql_model_space,
                                NOTIFICATION_USER_ENTITY,
                                notification_user_view.version,
                            ),
                            ["firstName"],
                        )
                    ],
                    limit=10000,
                ),
                "blocklist_in_application_groups": Select(limit=10000),
                "users_in_blocklist_application_groups": Select(
                    [
                        SourceSelector(
                            ViewId(
                                self.settings.cognite_graphql_model_space,
                                NOTIFICATION_USER_ENTITY,
                                notification_user_view.version,
                            ),
                            ["firstName"],
                        )
                    ],
                    limit=10000,
                ),
                "application_info": Select(
                    [
                        SourceSelector(
                            ViewId(
                                self.settings.um_model_space,
                                APPLICATION_ENTITY,
                                application_view,
                            ),
                            ["name", "description"],
                        )
                    ],
                    limit=10000,
                ),
                "user_roles_application_groups": Select(limit=10000),
                "blocklist_roles_application_groups": Select(limit=10000),
                "roles_in_application_groups": Select(
                    [
                        SourceSelector(
                            ViewId(
                                self.settings.cognite_graphql_model_space,
                                NOTIFICATION_USER_ROLE_ENTITY,
                                notification_user_role_site_view,
                            ),
                            ["*"],
                        )
                    ],
                    limit=10000,
                ),
                "roles_in_blocklist_application_groups": Select(
                    [
                        SourceSelector(
                            ViewId(
                                self.settings.cognite_graphql_model_space,
                                NOTIFICATION_USER_ROLE_ENTITY,
                                notification_user_role_site_view,
                            ),
                            ["*"],
                        )
                    ],
                    limit=10000,
                ),
                "user_complement_in_users_roles_in_application_groups": Select(
                    limit=10000
                ),
                "user_complement_in_blocklist_roles_in_application_groups": Select(
                    limit=10000
                ),
                "user_complement_in_users_roles": Select(
                    [
                        SourceSelector(
                            ViewId(
                                self.settings.um_model_space,
                                USER_COMPLEMENT,
                                user_complement_view,
                            ),
                            ["searchTags", "userAzureAttribute"],
                        )
                    ],
                    limit=10000,
                ),
                "user_complement_in_blocklist_roles": Select(
                    [
                        SourceSelector(
                            ViewId(
                                self.settings.um_model_space,
                                USER_COMPLEMENT,
                                user_complement_view,
                            ),
                            ["searchTags", "userAzureAttribute"],
                        )
                    ],
                    limit=10000,
                ),
                "user_azure_attribute_in_users_roles_in_application_groups": Select(
                    [
                        SourceSelector(
                            ViewId(
                                self.settings.um_model_space,
                                USER_AZURE_ATTRIBUTE,
                                user_azure_attribute_view,
                            ),
                            ["user"],
                        )
                    ],
                    limit=10000,
                ),
                "user_azure_attribute_in_blocklist_roles_in_application_groups": Select(
                    [
                        SourceSelector(
                            ViewId(
                                self.settings.um_model_space,
                                USER_AZURE_ATTRIBUTE,
                                user_azure_attribute_view,
                            ),
                            ["user"],
                        )
                    ],
                    limit=10000,
                ),
            },
            cursors={
                "application_groups": application_groups_cursor,
                "users_in_blocklist_application_groups": users_in_blocklist_application_groups_cursor,
                "users_in_application_groups": users_in_application_groups_cursor,
                "application_info": application_info_cursor,
                "user_complement_in_blocklist_roles": user_complement_in_blocklist_roles_cursor,
                "user_complement_in_users_roles": user_complement_in_users_roles_cursor,
                "user_azure_attribute_in_blocklist_roles_in_application_groups": user_azure_attribute_in_blocklist_roles_in_application_groups_cursor,
                "user_azure_attribute_in_users_roles_in_application_groups": user_azure_attribute_in_users_roles_in_application_groups_cursor,
                "roles_in_blocklist_application_groups": roles_in_blocklist_application_groups_cursor,
                "roles_in_application_groups": roles_in_application_groups_cursor,
            },
        )

        while has_cursor:
            response_query = self._cognite_client.data_modeling.instances.query(query)
            application_groups = self.format_response(response_query, views)

            if len(application_groups) > 0:
                response.extend(application_groups)

            if response_query:
                has_cursor = False
                cursor_keys = [
                    "application_groups",
                    "users_in_blocklist_application_groups",
                    "users_in_application_groups",
                    "application_info",
                    "user_complement_in_blocklist_roles",
                    "user_complement_in_users_roles",
                    "user_azure_attribute_in_blocklist_roles_in_application_groups",
                    "user_azure_attribute_in_users_roles_in_application_groups",
                    "roles_in_blocklist_application_groups",
                    "roles_in_application_groups",
                ]

                for key in cursor_keys:
                    if (
                        key in response_query.cursors
                        and len(response_query[key]) == 10000
                    ):
                        query.cursors[key] = response_query.cursors[key]
                        has_cursor = True
            else:
                has_cursor = False

        if len(response) > 0:
            for item in response:
                items.append(
                    models.NotificationApplicationGroupModel.mapFromResult(item)
                )

        return items

    def format_response(self, response, views: dict):
        application_groups_result = response["application_groups"].dump()
        users_edge_application_group_result = response[
            "subscribed_users_in_application_groups"
        ].dump()
        users_in_application_group_result = response[
            "users_in_application_groups"
        ].dump()
        blocklist_edge_application_group_result = response[
            "blocklist_in_application_groups"
        ].dump()
        users_in_blocklist_application_group_result = response[
            "users_in_blocklist_application_groups"
        ].dump()

        users_roles_edge_application_group_result = response[
            "user_roles_application_groups"
        ].dump()
        blocklist_roles_edge_application_group_result = response[
            "blocklist_roles_application_groups"
        ].dump()

        application_info_response = response["application_info"].dump()

        users_roles = self._process_roles(
            response["roles_in_application_groups"].dump(),
            response["user_complement_in_users_roles"].dump(),
            response[
                "user_azure_attribute_in_users_roles_in_application_groups"
            ].dump(),
            views,
        )

        blocklist_roles = self._process_roles(
            response["roles_in_blocklist_application_groups"].dump(),
            response["user_complement_in_blocklist_roles"].dump(),
            response[
                "user_azure_attribute_in_blocklist_roles_in_application_groups"
            ].dump(),
            views,
        )

        grouped_edges = self._group_edges(users_edge_application_group_result)
        blocklist_edges = self._group_edges(blocklist_edge_application_group_result)
        users_roles_edges = self._group_edges(users_roles_edge_application_group_result)
        blocklist_roles_edges = self._group_edges(
            blocklist_roles_edge_application_group_result
        )

        application_groups = []
        for application in application_groups_result:
            users_in_edge = grouped_edges.get(application.get("externalId", ""), [])
            blocklist_in_edge = blocklist_edges.get(
                application.get("externalId", ""), []
            )
            users_roles_in_edge = users_roles_edges.get(
                application.get("externalId", ""), []
            )

            blocklist_roles_in_edge = blocklist_roles_edges.get(
                application.get("externalId", ""), []
            )

            filtered_users = (
                [
                    user
                    for user in users_in_application_group_result
                    if user["externalId"] in users_in_edge
                ]
                if len(users_in_edge) > 0
                else []
            )
            filtered_users_blocklist = (
                [
                    user
                    for user in users_in_blocklist_application_group_result
                    if user["externalId"] in blocklist_in_edge
                ]
                if len(blocklist_in_edge) > 0
                else []
            )
            filtered_users_roles = (
                [
                    role
                    for role in users_roles
                    if role["externalId"] in users_roles_in_edge
                ]
                if len(users_roles_in_edge) > 0
                else []
            )
            filtered_blocklist_roles = (
                [
                    role
                    for role in blocklist_roles
                    if role["externalId"] in blocklist_roles_in_edge
                ]
                if len(blocklist_roles_in_edge) > 0
                else []
            )
            application_properties = (
                application.get("properties", {})
                .get(self.settings.cognite_graphql_model_space, {})
                .get(
                    f"{NOTIFICATION_APPLICATION_GROUP_ENTITY}/{views['notification_application_group']}",
                    {},
                )
            )
            filter_application = [
                app
                for app in application_info_response
                if app["externalId"]
                == application_properties.get("application", {}).get("externalId", "")
            ]
            users = (
                [
                    {
                        "externalId": user.get("externalId", ""),
                        "space": user.get("space", ""),
                    }
                    for user in filtered_users
                ]
                if len(filtered_users) > 0
                else []
            )
            blocklist = (
                [
                    {
                        "externalId": user.get("externalId", ""),
                        "space": user.get("space", ""),
                    }
                    for user in filtered_users_blocklist
                ]
                if len(filtered_users_blocklist) > 0
                else []
            )
            application_groups.append(
                {
                    "externalId": application.get("externalId", ""),
                    "space": application.get("space", ""),
                    "name": application_properties.get("name", ""),
                    "description": application_properties.get("description", ""),
                    "application": {
                        "externalId": application_properties.get("application", {}).get(
                            "externalId", ""
                        ),
                        "space": application_properties.get("application", {}).get(
                            "space", ""
                        ),
                        "name": filter_application[0]
                        .get("properties", {})
                        .get(self.settings.um_model_space, {})
                        .get(f"{APPLICATION_ENTITY}/{views['application']}", {})
                        .get("name", ""),
                        "description": filter_application[0]
                        .get("properties", {})
                        .get(self.settings.um_model_space, {})
                        .get(f"{APPLICATION_ENTITY}/{views['application']}", {})
                        .get("description", ""),
                    },
                    "externalUsers": application_properties.get("externalUsers", []),
                    "users": users,
                    "blocklist": blocklist,
                    "usersRoles": filtered_users_roles,
                    "blocklistRoles": filtered_blocklist_roles,
                }
            )

        return application_groups

    def _group_edges(self, users_edge_application_group_result):
        """Groups edges by their start node."""
        grouped_edges = {}
        for edge in users_edge_application_group_result:
            start_node = edge["startNode"]["externalId"]
            end_node = edge["endNode"]["externalId"]

            if start_node not in grouped_edges:
                grouped_edges[start_node] = []

            grouped_edges[start_node].append(end_node)
        return grouped_edges

    def _get_user_list(
        self,
        role_external_id,
        user_complement_data,
        user_azure_data,
        views,
    ):
        """Get list of users associated with a role."""
        # Get user complements for the role

        filter_users = [
            user
            for user in user_complement_data
            if role_external_id
            in user.get("properties", {})
            .get(self.settings.um_model_space, {})
            .get(
                f"{USER_COMPLEMENT}/{views['user_complement']}",
                {},
            )
            .get("searchTags", [])
        ]

        # Get Azure attributes
        users_complement = [
            user.get("properties", {})
            .get(self.settings.um_model_space, {})
            .get(
                f"{USER_COMPLEMENT}/{views['user_complement']}",
                {},
            )
            .get("userAzureAttribute", {})
            .get("externalId", "")
            for user in filter_users
        ]

        user_azure_attribute = [
            user.get("properties", {})
            .get(self.settings.um_model_space, {})
            .get(
                f"{USER_AZURE_ATTRIBUTE}/{views['user_azure_attribute']}",
                {},
            )
            .get("user", {})
            for user in user_azure_data
            if user["externalId"] in users_complement
        ]

        return sorted(user_azure_attribute, key=lambda x: x["externalId"].lower())

    def _process_roles(
        self,
        roles_data,
        user_complement_data,
        user_azure_data,
        views,
    ):
        """Process roles and return formatted role information."""
        processed_roles = []

        for role in roles_data:
            # Get users
            users = self._get_user_list(
                role.get("externalId", ""),
                user_complement_data,
                user_azure_data,
                views,
            )

            processed_roles.append(
                {
                    "externalId": role.get("externalId", ""),
                    "space": role.get("space", ""),
                    "users": users,
                }
            )

        return processed_roles
