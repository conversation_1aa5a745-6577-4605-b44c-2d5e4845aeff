from uuid import uuid4
from gql import Client
from settings.settings_class import Settings
from cognite.client import CogniteClient
from cognite.client.data_classes.data_modeling import NodeApply, NodeOrEdgeData, ViewId
import utils as Utils
import models as models
from datetime import datetime
from services.database_cache_service import DatabaseCacheService

ENTITY = "NotificationDeliverable"


class NotificationDeliverableRepository:
    def __init__(
        self, cogniteClient: CogniteClient, gqlClient: Client, settings: Settings
    ):
        self.cogniteClient = cogniteClient
        self.gqlClient = gqlClient
        self.settings = settings

    def create(
        self,
        data: models.NotificationDeliverableCreateModel,
        db_cache: DatabaseCacheService,
    ):
        if (
            self.settings.environment_id == "DEV"
            or self.settings.environment_id == "QA"
        ):
            if data.subscribers is not None and len(data.subscribers) > 0:
                data.subscribers = (
                    Utils.subscribers_utils.set_allowed_subscriber_per_environment(
                        data.subscribers
                    )
                )
            if (
                data.externalSubscribers is not None
                and len(data.externalSubscribers) > 0
            ):
                data.externalSubscribers = Utils.subscribers_utils.set_allowed_external_subscriber_per_environment(
                    data.externalSubscribers
                )

            if (data.subscribers is None or len(data.subscribers) == 0) and (
                data.externalSubscribers is None or len(data.externalSubscribers) == 0
            ):
                return False

        # GET ENTITY VIEW
        view = Utils.cognite.find_view_by_external_id(
            db_cache.get("cognite_views")[self.settings.cognite_graphql_model_space],
            ENTITY,
        )

        users = data.subscribers
        del data.subscribers

        if not data.reportingSite:
            del data.reportingSite

        entity_versions = view.version  # GET ENTITY VERSION

        # CREATE DELIVERABLE
        eventExternalId = Utils.generate_external_id('NTFDLV')
        eventNodes = NodeApply(
            self.settings.ntf_prot_instance_space,
            eventExternalId,
            sources=[
                NodeOrEdgeData(
                    ViewId(
                        self.settings.cognite_graphql_model_space,
                        ENTITY,
                        entity_versions,
                    ),
                    data.model_dump(),
                )
            ],
        )
        self.cogniteClient.data_modeling.instances.apply(nodes=eventNodes)

        # CREATE DELIVERABLE RELATIONSHIP WITH SUBSCRIBERS
        Utils.cognite.createRelationship(
            users,
            eventNodes,
            entity_versions,
            self.gqlClient,
            self.cogniteClient,
            self.settings.cognite_graphql_model_space,
            self.settings.ntf_prot_instance_space,
            "subscribers",
        )

        return True