from typing import TypeVar
from uuid import uuid4
from gql import Client
from settings.settings_class import Settings
from cognite.client import CogniteClient
from cognite.client.data_classes.data_modeling import (
    NodeApply,
    NodeOrEdgeData,
    ViewId,
    NodeId,
)
import utils as Utils
import models as models
from datetime import datetime
from services.database_cache_service import DatabaseCacheService

ENTITY = "NotificationEvent"


class NotificationEventRepository:
    def __init__(
        self, cogniteClient: CogniteClient, gqlClient: Client, settings: Settings
    ):
        self.cogniteClient = cogniteClient
        self.gqlClient = gqlClient
        self.settings = settings

    def create(self, eventData: models.NotificationEventCreateModel, db_cache: DatabaseCacheService) -> str:
        # GET ENTITY VIEW        
        view = Utils.cognite.find_view_by_external_id(
            db_cache.get("cognite_views")[self.settings.cognite_graphql_model_space],
            ENTITY,
        )

        roles = eventData.roles
        applicationGroups = eventData.applicationGroups
        users = eventData.users

        del eventData.roles
        del eventData.applicationGroups
        del eventData.users
        if eventData.severity is None:
            del eventData.severity

        entity_versions = view.version  # GET ENTITY VERSION

        # CREATE EVENT
        eventExternalId = Utils.generate_external_id('NTFEVT')

        eventNodes = NodeApply(
            self.settings.ntf_prot_instance_space,
            eventExternalId,
            sources=[
                NodeOrEdgeData(
                    ViewId(
                        self.settings.cognite_graphql_model_space,
                        ENTITY,
                        entity_versions,
                    ),
                    eventData.dict(),
                )
            ],
        )
        self.cogniteClient.data_modeling.instances.apply(nodes=eventNodes)

        # CREATE EVENT RELATIONSHIP WITH ROLES
        Utils.cognite.createRelationship(
            roles,
            eventNodes,
            entity_versions,
            self.gqlClient,
            self.cogniteClient,
            self.settings.cognite_graphql_model_space,
            self.settings.ntf_prot_instance_space,
            "roles",
        )

        # CREATE EVENT RELATIONSHIP WITH APPLICATIONGROUP
        Utils.cognite.createRelationship(
            applicationGroups,
            eventNodes,
            entity_versions,
            self.gqlClient,
            self.cogniteClient,
            self.settings.cognite_graphql_model_space,
            self.settings.ntf_prot_instance_space,
            "applicationGroups",
        )

        # CREATE EVENT RELATIONSHIP WITH USER
        Utils.cognite.createRelationship(
            users,
            eventNodes,
            entity_versions,
            self.gqlClient,
            self.cogniteClient,
            self.settings.cognite_graphql_model_space,
            self.settings.ntf_prot_instance_space,
            "users",
        )

        return eventExternalId
