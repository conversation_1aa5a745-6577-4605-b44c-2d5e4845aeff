from typing import TypeVar, Optional
from uuid import uuid4
from gql import Client
from settings.settings_class import Settings
from cognite.client import CogniteClient
from cognite.client.data_classes.data_modeling import NodeApply, NodeOrEdgeData, ViewId, NodeId
from models.common_basic_model import RelationModel
from services.database_cache_service import DatabaseCacheService
import utils as Utils
import models as models

ENTITY = "NotificationOnScreen"


class NotificationOnScreenRepository:

    def __init__(
        self,
        cogniteClient: CogniteClient,
        gqlClient: Client,
        settings: Settings
    ):
        self.cogniteClient = cogniteClient
        self.gqlClient = gqlClient
        self.settings = settings

    def create(
        self,
        source_data: models.NotificationOnScreenCreateModel,
        db_cache: DatabaseCacheService,
        notification_reference: Optional[RelationModel] = None,
    ):

        if self.settings.environment_id == "DEV" or self.settings.environment_id == "QA":        
            source_data.subscribers = (Utils.subscribers_utils.
                                       set_allowed_subscriber_per_environment(source_data.subscribers))
            if source_data.subscribers is None or len(source_data.subscribers) == 0:
                return False
        
        # GET ENTITY VIEW
        view = Utils.cognite.find_view_by_external_id(
            db_cache.get("cognite_views")[self.settings.cognite_graphql_model_space],
            ENTITY,
        )

        users = source_data.subscribers
        del source_data.subscribers

        if not source_data.reportingSite:
            del source_data.reportingSite

        entity_versions = view.version  # GET ENTITY VERSION

        # CREATE ONSCREEN
        event_external_id = notification_reference.get("externalId", "") if notification_reference else Utils.generate_external_id('NTFOSC')
        event_nodes = NodeApply(
            self.settings.ntf_prot_instance_space,
            event_external_id,
            sources=[
                NodeOrEdgeData(
                    ViewId(self.settings.cognite_graphql_model_space, ENTITY, entity_versions),
                    source_data.model_dump()
                )
            ]
        )
        self.cogniteClient.data_modeling.instances.apply(nodes=event_nodes)

        # CREATE ONSCREEN RELATIONSHIP WITH SUBSCRIBERS
        Utils.cognite.createRelationship(
            users,
            event_nodes,
            entity_versions,
            self.gqlClient,
            self.cogniteClient,
            self.settings.cognite_graphql_model_space,
            self.settings.ntf_prot_instance_space,
            "subscribers"
        )

        return True