from typing import List
from uuid import uuid4
from gql import gql, Client
from settings.settings_class import Settings
from cognite.client import CogniteClient
from cognite.client.data_classes.data_modeling import NodeApply, NodeOrEdgeData, ViewId
import models as models
import queries.notification_event_queries as queries
from datetime import datetime, timedelta
import pytz
from models.common_basic_model import RelationModel
from models.notification_raw_event_log_model import NotificationRawEventLogModel
import utils as Utils
from services.database_cache_service import DatabaseCacheService

ENTITY_RAW_EVENT = "NotificationRawEvent"
ENTITY_RAW_EVENT_LOG = "NotificationRawEventLog"

class NotificationRawEventRepository:
    def __init__(
        self, cogniteClient: CogniteClient, gqlClient: Client, settings: Settings
    ):
        self.cogniteClient = cogniteClient
        self.gqlClient = gqlClient
        self.settings = settings


    def list_notification_raw_event_pending(self):
        print("Listing pending Raw Events")
        filter = {}
        filter["filter"] = {
            "and": [
                {"space": {"eq": self.settings.ntf_instance_space}},
                {"processedDate": {"isNull": "true"}},
                {"rawEvent": {"externalId": {"isNull": "false"} }},
                {"isProcessing": {"isNull": "true"}}
            ]
        }

        result = self.gqlClient.execute(
            gql(queries.NOTIFICATION_RAW_EVENT_LOG_LIST), filter
        )

        if len(result["listNotificationRawEventLog"]["items"]) > 0:
            self.stamp_raw_event_log_in_processing(result["listNotificationRawEventLog"]["items"])
            data = result["listNotificationRawEventLog"]["items"]
            return data
        print("No Raw Events to be processed")
        return []

    def stamp_raw_event_log_as_processed(
        self,
        logExternalId: str,
        db_cache: DatabaseCacheService,
        errors: List[str] = None,
    ):
        print("Stamping Raw Event as processed")

        # View for RawEventLog
        view_log = Utils.cognite.find_view_by_external_id(
            db_cache.get("cognite_views")[self.settings.cognite_graphql_model_space],
            ENTITY_RAW_EVENT_LOG,
        )

        if view_log is None:
            print(
                f"Could not retrieve the view info for the Entity {ENTITY_RAW_EVENT_LOG}"
            )
            return

        try:
            fmt = "%Y-%m-%dT%H:%M:%S"
            now = datetime.now(pytz.utc)
            formatted_now = now.strftime(fmt)

            eventLogData = {}
            eventLogData["processedDate"] = formatted_now
            eventLogData["isProcessing"] = False
            if errors:
                eventLogData["processResult"] = str(errors)
            
            logNode = NodeApply(
                self.settings.ntf_instance_space,
                logExternalId,
                sources=[
                    NodeOrEdgeData(
                        ViewId(
                            self.settings.cognite_graphql_model_space,
                            ENTITY_RAW_EVENT_LOG,
                            view_log.version
                        ),
                        eventLogData
                    )
                ]
            )

            self.cogniteClient.data_modeling.instances.apply(logNode)
        except Exception as e:
            print(e)
        return
    
    def stamp_raw_event_log_in_processing(self, items: str):
        print("Stamping Raw Events as processing")
        
        # View for RawEventLog
        viewLog = Utils.cognite.getView(
            self.cogniteClient,
            self.settings.cognite_graphql_model_space,
            ENTITY_RAW_EVENT_LOG,
        )
        if viewLog is None:
            print(f"Could not retrieve the view info for the Entity {ENTITY_RAW_EVENT_LOG}")
            return
        
        try:
            logNodes = []
            eventLogData = {}
            eventLogData["isProcessing"] = True

            for item in items:
                logNodes.append(
                    NodeApply(
                        self.settings.ntf_instance_space,
                        item.get("externalId"),
                        sources=[
                            NodeOrEdgeData(
                                ViewId(
                                    self.settings.cognite_graphql_model_space,
                                    ENTITY_RAW_EVENT_LOG,
                                    viewLog.version
                                ),
                                eventLogData
                            )
                        ]
                    )
                )

            self.cogniteClient.data_modeling.instances.apply(logNodes)
        except Exception as e:
            print(e)
        return
