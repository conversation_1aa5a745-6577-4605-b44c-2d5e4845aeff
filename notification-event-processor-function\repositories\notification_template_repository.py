from typing import List, Optional
from gql import Client, gql
from settings.settings_class import Settings
from models.notification_template_model import NotificationTemplateModel
from cognite.client import CogniteClient
from cognite.client.data_classes.data_modeling.query import (
    Query,
    Select,
    NodeResultSetExpression,
    EdgeResultSetExpression,
    SourceSelector,
)
from cognite.client.data_classes.data_modeling import (
    ViewId,
    PropertyId,
)
from cognite.client.data_classes.filters import Equals, HasData, And, Or, Not, Exists, In
import queries.notification_template_queries as queries
import utils as Utils
from models.common_basic_model import RelationModel
from services.database_cache_service import DatabaseCacheService

TEMPLATE_ENTITY = "NotificationTemplate"
USER_ENTITY = "NotificationUser"
SEVERITY_ENTITY = "NotificationSeverity"
NOTIFICATION_TYPE_ENTITY = "NotificationType"
APPLICATION_ENTITY = "Application"
CHANNELS_ENTITY = "NotificationChannel"
NOTIFICATION_USER_ENTITY = "NotificationUser"
NOTIFICATION_APPLICATION_GROUP_ENTITY = "NotificationApplicationGroup"
NOTIFICATION_USER_ROLE_SITE_ENTITY = "NotificationUserRoleSite"


class NotificationTemplateRepository:

    def __init__(
        self, cogniteClient: CogniteClient, gqlClient: Client, settings: Settings
    ):
        self.cogniteClient = cogniteClient
        self.gqlClient = gqlClient
        self.settings = settings

    def find_view_by_external_id(self, views_list, external_id):
        return next(
            (view for view in views_list if view.external_id == external_id), None
        )

    def find_by_type(
        self,
        notification_type_ids: List[str],
        db_cache: DatabaseCacheService,
    ) -> List[NotificationTemplateModel]:

        notification_views = db_cache.get("cognite_views")[
            self.settings.cognite_graphql_model_space
        ]

        templates_view = self.find_view_by_external_id(
            notification_views,
            TEMPLATE_ENTITY,
        )

        notification_severity_view = self.find_view_by_external_id(
            notification_views,
            SEVERITY_ENTITY,
        ).version

        notification_type_view = self.find_view_by_external_id(
            notification_views,
            NOTIFICATION_TYPE_ENTITY,
        ).version

        application_view = self.find_view_by_external_id(
            db_cache.get("cognite_views")[self.settings.um_model_space],
            APPLICATION_ENTITY
        ).version

        notification_channel_view = self.find_view_by_external_id(
            notification_views,
            CHANNELS_ENTITY,
        ).version

        notification_user_view = self.find_view_by_external_id(
            notification_views,
            NOTIFICATION_USER_ENTITY,
        )

        notification_application_group_view = self.find_view_by_external_id(
            notification_views,
            NOTIFICATION_APPLICATION_GROUP_ENTITY,
        ).version

        notification_user_role_site_view = self.find_view_by_external_id(
            notification_views,
            NOTIFICATION_USER_ROLE_SITE_ENTITY,
        ).version

        application_cursor = None
        application_groups_in_templates_cursor = None
        blocklist_in_templates_cursor = None
        channels_in_templates_cursor = None
        notification_type_cursor = None
        severities_cursor = None
        subscribed_roles_in_templates_cursor = None
        blocklists_roles_in_templates_cursor = None
        subscribed_user_in_templates_cursor = None
        templates_cursor = None
        users_in_blocklist_templates_cursor = None
        users_in_subscribed_users_templates_cursor = None
        has_cursor = True
        response_query = None
        response = []
        template_fetch_limit = 10000

        views = {
            "templates": templates_view.version,
            "notification_severity": notification_severity_view,
            "notification_type": notification_type_view,
            "application": application_view,
            "notification_channel": notification_channel_view,
            "notification_user": notification_user_view.version,
        }

        filter_variables = [
            Or(
                Equals(templates_view.as_property_ref("deleted"), False),
                Not(Exists(templates_view.as_property_ref("deleted"))),
            ),
            In(
                templates_view.as_property_ref("notificationType"),
                notification_type_ids,
            ),
            Equals(["node", "space"], self.settings.ntf_instance_space),
        ]

        query = Query(
            with_={
                # Retrieving the template
                "templates": NodeResultSetExpression(
                    filter=And(*filter_variables),
                    limit=template_fetch_limit,
                    chain_to="destination",
                    direction="outwards",
                ),
                # getting the severity info
                "severities": NodeResultSetExpression(
                    from_="templates",
                    limit=template_fetch_limit,
                    chain_to="destination",
                    direction="outwards",
                    through=PropertyId(
                        ViewId(
                            self.settings.cognite_graphql_model_space,
                            TEMPLATE_ENTITY,
                            templates_view.version,
                        ),
                        "severity",
                    ),
                ),
                # getting the notification type info
                "notification_type": NodeResultSetExpression(
                    from_="templates",
                    limit=template_fetch_limit,
                    chain_to="destination",
                    direction="outwards",
                    through=PropertyId(
                        ViewId(
                            self.settings.cognite_graphql_model_space,
                            TEMPLATE_ENTITY,
                            templates_view.version,
                        ),
                        "notificationType",
                    ),
                ),
                # getting the application info
                "application": NodeResultSetExpression(
                    from_="notification_type",
                    limit=template_fetch_limit,
                    chain_to="destination",
                    direction="outwards",
                    through=PropertyId(
                        ViewId(
                            self.settings.cognite_graphql_model_space,
                            NOTIFICATION_TYPE_ENTITY,
                            notification_type_view,
                        ),
                        "application",
                    ),
                ),
                # getting the edge of subscribed user
                "subscribed_users_in_templates": EdgeResultSetExpression(
                    from_="templates",
                    limit=template_fetch_limit,
                    direction="outwards",
                    chain_to="destination",
                    filter=Equals(
                        ["edge", "type"],
                        {
                            "space": self.settings.cognite_graphql_model_space,
                            "externalId": "NotificationTemplate.subscribedUsers",
                        },
                    ),
                    node_filter=HasData(
                        views=[
                            ViewId(
                                self.settings.cognite_graphql_model_space,
                                NOTIFICATION_USER_ENTITY,
                                notification_user_view.version,
                            )
                        ]
                    ),
                ),
                "users_in_subscribed_users_templates": NodeResultSetExpression(
                    from_="subscribed_users_in_templates",
                    filter=Equals(
                        notification_user_view.as_property_ref("deleted"), False
                    ),
                    limit=template_fetch_limit,
                ),
                # getting the edge of the channels
                "channels_in_templates": EdgeResultSetExpression(
                    from_="templates",
                    limit=template_fetch_limit,
                    direction="outwards",
                    chain_to="destination",
                    filter=Equals(
                        ["edge", "type"],
                        {
                            "space": self.settings.cognite_graphql_model_space,
                            "externalId": "NotificationTemplate.channels",
                        },
                    ),
                    node_filter=HasData(
                        views=[
                            ViewId(
                                self.settings.cognite_graphql_model_space,
                                CHANNELS_ENTITY,
                                notification_channel_view,
                            )
                        ]
                    ),
                ),
                # getting the edge of blocked users list
                "blocklist_in_templates": EdgeResultSetExpression(
                    from_="templates",
                    limit=template_fetch_limit,
                    direction="outwards",
                    chain_to="destination",
                    filter=Equals(
                        ["edge", "type"],
                        {
                            "space": self.settings.cognite_graphql_model_space,
                            "externalId": "NotificationTemplate.blocklist",
                        },
                    ),
                    node_filter=HasData(
                        views=[
                            ViewId(
                                self.settings.cognite_graphql_model_space,
                                NOTIFICATION_USER_ENTITY,
                                notification_user_view.version,
                            )
                        ]
                    ),
                ),
                "users_in_blocklist_templates": NodeResultSetExpression(
                    from_="blocklist_in_templates",
                    filter=Equals(
                        notification_user_view.as_property_ref("deleted"), False
                    ),
                    limit=template_fetch_limit,
                ),
                # getting the application groups
                "application_groups_in_templates": EdgeResultSetExpression(
                    from_="templates",
                    limit=template_fetch_limit,
                    direction="outwards",
                    chain_to="destination",
                    filter=Equals(
                        ["edge", "type"],
                        {
                            "space": self.settings.cognite_graphql_model_space,
                            "externalId": "NotificationTemplate.subscribedApplicationGroups",
                        },
                    ),
                    node_filter=HasData(
                        views=[
                            ViewId(
                                self.settings.cognite_graphql_model_space,
                                NOTIFICATION_APPLICATION_GROUP_ENTITY,
                                notification_application_group_view,
                            )
                        ]
                    ),
                ),
                # getting the edge of subscribed roles
                "subscribed_roles_in_templates": EdgeResultSetExpression(
                    from_="templates",
                    limit=template_fetch_limit,
                    direction="outwards",
                    chain_to="destination",
                    filter=Equals(
                        ["edge", "type"],
                        {
                            "space": self.settings.cognite_graphql_model_space,
                            "externalId": "NotificationTemplate.subscribedRoles",
                        },
                    ),
                    node_filter=HasData(
                        views=[
                            ViewId(
                                self.settings.cognite_graphql_model_space,
                                NOTIFICATION_USER_ROLE_SITE_ENTITY,
                                notification_user_role_site_view,
                            )
                        ]
                    ),
                ),
                # getting the edge of subscribed roles
                "blocklists_roles_in_templates": EdgeResultSetExpression(
                    from_="templates",
                    limit=template_fetch_limit,
                    direction="outwards",
                    chain_to="destination",
                    filter=Equals(
                        ["edge", "type"],
                        {
                            "space": self.settings.cognite_graphql_model_space,
                            "externalId": "NotificationTemplate.blocklistRoles",
                        },
                    ),
                    node_filter=HasData(
                        views=[
                            ViewId(
                                self.settings.cognite_graphql_model_space,
                                NOTIFICATION_USER_ROLE_SITE_ENTITY,
                                notification_user_role_site_view,
                            )
                        ]
                    ),
                ),
            },
            select={
                "templates": Select(
                    [
                        SourceSelector(
                            ViewId(
                                self.settings.cognite_graphql_model_space,
                                TEMPLATE_ENTITY,
                                templates_view.version,
                            ),
                            ["*"],
                        )
                    ],
                    limit=template_fetch_limit,
                ),
                "subscribed_users_in_templates": Select(limit=template_fetch_limit),
                "users_in_subscribed_users_templates": Select(
                    [
                        SourceSelector(
                            ViewId(
                                self.settings.cognite_graphql_model_space,
                                NOTIFICATION_USER_ENTITY,
                                notification_user_view.version,
                            ),
                            ["email"],
                        )
                    ],
                    limit=template_fetch_limit,
                ),
                "blocklist_in_templates": Select(limit=template_fetch_limit),
                "users_in_blocklist_templates": Select(
                    [
                        SourceSelector(
                            ViewId(
                                self.settings.cognite_graphql_model_space,
                                NOTIFICATION_USER_ENTITY,
                                notification_user_view.version,
                            ),
                            ["email"],
                        )
                    ],
                    limit=template_fetch_limit,
                ),
                "channels_in_templates": Select(limit=template_fetch_limit),
                "application_groups_in_templates": Select(limit=template_fetch_limit),
                "subscribed_roles_in_templates": Select(limit=template_fetch_limit),
                "blocklists_roles_in_templates": Select(limit=template_fetch_limit),
                "severities": Select(
                    [
                        SourceSelector(
                            ViewId(
                                self.settings.cognite_graphql_model_space,
                                SEVERITY_ENTITY,
                                notification_severity_view,
                            ),
                            ["*"],
                        )
                    ],
                    limit=template_fetch_limit,
                ),
                "notification_type": Select(
                    [
                        SourceSelector(
                            ViewId(
                                self.settings.cognite_graphql_model_space,
                                NOTIFICATION_TYPE_ENTITY,
                                notification_type_view,
                            ),
                            ["*"],
                        )
                    ],
                    limit=template_fetch_limit,
                ),
                "application": Select(
                    [
                        SourceSelector(
                            ViewId(
                                self.settings.um_model_space,
                                APPLICATION_ENTITY,
                                application_view,
                            ),
                            ["*"],
                        )
                    ],
                    limit=template_fetch_limit,
                ),
            },
            cursors={
                "application": application_cursor,
                "application_groups_in_templates": application_groups_in_templates_cursor,
                "blocklist_in_templates": blocklist_in_templates_cursor,
                "channels_in_templates": channels_in_templates_cursor,
                "notification_type": notification_type_cursor,
                "severities": severities_cursor,
                "subscribed_roles_in_templates": subscribed_roles_in_templates_cursor,
                "blocklists_roles_in_templates": blocklists_roles_in_templates_cursor,
                "subscribed_users_in_templates": subscribed_user_in_templates_cursor,
                "templates": templates_cursor,
                "users_in_blocklist_templates": users_in_blocklist_templates_cursor,
                "users_in_subscribed_users_templates": users_in_subscribed_users_templates_cursor,
            },
        )

        while has_cursor:
            response_query = self.cogniteClient.data_modeling.instances.query(query)
            template = self.format_template_response(response_query, views)

            if len(template) > 0:
                response.extend(template)

            if response_query:
                has_cursor = False
                cursor_keys = [
                    "users_in_subscribed_users_templates",
                    "subscribed_users_in_templates",
                    "application",
                    "application_groups_in_templates",
                    "blocklist_in_templates",
                    "channels_in_templates",
                    "notification_type",
                    "severities",
                    "subscribed_roles_in_templates",
                    "blocklists_roles_in_templates",
                    "templates",
                    "users_in_blocklist_templates",
                ]

                for key in cursor_keys:
                    if (
                        key in response_query.cursors
                        and len(response_query[key]) == template_fetch_limit
                    ):
                        query.cursors[key] = response_query.cursors[key]
                        has_cursor = True
            else:
                has_cursor = False

        items = []

        if len(response) > 0:
            for item in response:
                items.append(NotificationTemplateModel.mapFromResult(item))

        grouped_templates = self.group_templates(items)

        return grouped_templates

    def find_extension_details_by_type(
        self, notificationTypeIds: List[str]
    ) -> List[NotificationTemplateModel]:

        filter = {}
        filter["filter"] = {
            "and": [
                {
                    "template": {
                        "and": [
                            {"deleted": {"eq": False}},
                            {
                                "notificationType": {
                                    "application": {"externalId": {"isNull": False}}
                                }
                            },
                            {
                                "notificationType": {
                                    "externalId": {"in": [notification_type.get("externalId") for notification_type in notificationTypeIds]}
                                }
                            },
                        ]
                    }
                }
            ]
        }

        result = self.gqlClient.execute(
            gql(queries.NOTIFICATION_TEMPLATE_EXTENSION_BY_TYPE), filter
        )

        items = []
        if len(result["listNotificationTemplateExtension"]["items"]) > 0:
            for item in result["listNotificationTemplateExtension"]["items"]:
                items.append(NotificationTemplateModel.mapFromExtensionResult(item))

        return items

    def format_template_response(self, response, views: dict):
        templates_result = response["templates"].dump()
        severity_result = response["severities"].dump()
        notification_types_result = response["notification_type"].dump()
        applications_result = response["application"].dump()
        channels_result = response["channels_in_templates"].dump()
        subscribed_users_result = response["subscribed_users_in_templates"].dump()
        users_in_subscribed_users_result = response[
            "users_in_subscribed_users_templates"
        ].dump()
        blocklist_result = response["blocklist_in_templates"].dump()
        users_in_blocklist_result = response["users_in_blocklist_templates"].dump()
        application_groups_result = response["application_groups_in_templates"].dump()
        subscribed_roles_result = response["subscribed_roles_in_templates"].dump()
        blocklist_roles_result = response["blocklists_roles_in_templates"].dump()

        result = []

        grouped_channels_by_template = self._group_edges(channels_result, False)
        grouped_users_by_template = self._group_edges(subscribed_users_result, True)
        grouped_blocklist_by_template = self._group_edges(blocklist_result, True)
        grouped_application_groups_by_template = self._group_edges(
            application_groups_result, False
        )
        grouped_subscribed_roles_by_template = self._group_edges(
            subscribed_roles_result, False
        )
        grouped_blocklist_roles_by_template = self._group_edges(
            blocklist_roles_result, False
        )

        for template in templates_result:
            template_id = template.get("externalId", "")
            template_properties = (
                template.get("properties", {})
                .get(self.settings.cognite_graphql_model_space, {})
                .get(f"{TEMPLATE_ENTITY}/{views['templates']}", {})
            )

            template_severity_id = template_properties.get("severity", {}).get(
                "externalId", ""
            )
            template_notification_type_id = template_properties.get("notificationType", {}).get(
                "externalId", ""
            )
            users = grouped_users_by_template.get(template_id, [])
            blocklist = grouped_blocklist_by_template.get(template_id, [])
            filtered_users = [
                user
                for user in users_in_subscribed_users_result
                if user["externalId"] in users
            ]
            filtered_blocklist_users = [
                user
                for user in users_in_blocklist_result
                if user["externalId"] in blocklist
            ]
            filter_severity = [
                severity
                for severity in severity_result
                if severity["externalId"] == template_severity_id
            ][0]
            filter_notification_type = [
                notification_type
                for notification_type in notification_types_result
                if notification_type["externalId"] == template_notification_type_id
            ][0]
            filter_notification_type_properties = filter_notification_type.get("properties").get(self.settings.cognite_graphql_model_space, {}).get(f"{NOTIFICATION_TYPE_ENTITY}/{views['notification_type']}", {})

            template_application_id = filter_notification_type_properties.get("application", "").get("externalId", "")
            filter_application = [
                application
                for application in applications_result
                if application["externalId"] == template_application_id
            ][0]
            filter_application_properties = filter_application.get("properties").get(self.settings.um_model_space, {}).get(f"{APPLICATION_ENTITY}/{views['application']}", {})

            severity = {
                "externalId": filter_severity.get("externalId", ""),
                "space": filter_severity.get("space", ""),
                "name": filter_severity.get("properties", {})
                .get(self.settings.cognite_graphql_model_space, {})
                .get(f"{SEVERITY_ENTITY}/{views['notification_severity']}", {})
                .get("name", ""),
                "description": filter_severity.get("properties", {})
                .get(self.settings.cognite_graphql_model_space, {})
                .get(f"{SEVERITY_ENTITY}/{views['notification_severity']}", {})
                .get("description", ""),
            }

            notification_type = (
                {
                    "externalId": filter_notification_type.get("externalId", ""),
                    "space": filter_notification_type.get("space", ""),
                    "name": filter_notification_type_properties.get("name", ""),
                    "description": filter_notification_type_properties.get("description", ""),
                    "entityType": filter_notification_type_properties.get("entityType", ""),
                    "application": {
                        "externalId": filter_application.get("externalId", ""),
                        "space": filter_application.get("space", ""),
                        "name": filter_application_properties.get("name", ""),
                        "alias": filter_application_properties.get("alias", ""),
                        "description": filter_application_properties.get("description", ""),
                        "iconUrl": filter_application_properties.get("iconUrl", ""),
                    },
                }
                if len(filter_notification_type) > 0 and len(filter_application_properties) > 0
                else {}
            )

            subscribed_users_list = [
                {
                    "externalId": user.get("externalId", ""),
                    "space": user.get("space", ""),
                }
                for user in filtered_users
                if len(filtered_users) > 0
            ]

            blocklist = [
                {
                    "externalId": user.get("externalId", ""),
                    "space": user.get("space", ""),
                }
                for user in filtered_blocklist_users
                if len(filtered_blocklist_users) > 0
            ]

            result.append(
                {
                    "name": template_properties.get("name", ""),
                    "creator": template_properties.get("creator", {}),
                    "conditionalExpression": template_properties.get(
                        "conditionalExpression", ""
                    ),
                    "customChannelEnabled": template_properties.get(
                        "customChannelEnabled", False
                    ),
                    "customFrequencyEnabled": template_properties.get(
                        "customFrequencyEnabled", False
                    ),
                    "externalId": template_id,
                    "space": template.get("space", ""),
                    "text": template_properties.get("text"),
                    "frequencyCronExpression": template_properties.get(
                        "frequencyCronExpression", None
                    ),
                    "subscribedExternalUsers": template_properties.get(
                        "subscribedExternalUsers", []
                    ),
                    "adminLevel": template_properties.get("adminLevel", False),
                    "deleted": template_properties.get("deleted", False),
                    "allUsers": template_properties.get("allUsers", False),
                    "externalUsers": template_properties.get("externalUsers", False),
                    "channels": (
                        grouped_channels_by_template.get(template_id, [])
                        if grouped_channels_by_template.get(template_id, [])
                        else []
                    ),
                    "severity": severity,
                    "notificationType": notification_type,
                    "subscribedUsers": subscribed_users_list,
                    "blocklist": blocklist,
                    "subscribedRoles": (
                        grouped_subscribed_roles_by_template.get(template_id, [])
                        if grouped_subscribed_roles_by_template.get(template_id, [])
                        else []
                    ),
                    "blocklistRoles": (
                        grouped_blocklist_roles_by_template.get(template_id, [])
                        if grouped_blocklist_roles_by_template.get(template_id, [])
                        else []
                    ),
                    "subscribedApplicationGroups": (
                        grouped_application_groups_by_template.get(template_id, [])
                        if grouped_application_groups_by_template.get(template_id, [])
                        else []
                    ),
                    "reportingSite": (
                        {
                            "space": template_properties.get("reportingSite", {}).get("space", ""),
                            "externalId": template_properties.get("reportingSite", {}).get("externalId", ""),
                        } if template_properties.get("reportingSite")
                        else None
                    )
                }
            )

        return result

    def group_templates(self, items):
        groupedTemplatesDict = {}

        for templateModel in items:
            if templateModel.externalId not in groupedTemplatesDict:
                groupedTemplatesDict[templateModel.externalId] = templateModel
            else:
                groupedTemplate = groupedTemplatesDict[templateModel.externalId]
                groupedTemplate.subscribedUsers.extend(templateModel.subscribedUsers)

        # Convert the dictionary back to a list
        groupedTemplates = list(groupedTemplatesDict.values())
        return groupedTemplates

    def _group_edges(
        self, users_edge_application_group_result, end_node_use_external_id: bool
    ):
        """Groups edges by their start node."""
        grouped_edges = {}
        for edge in users_edge_application_group_result:
            start_node = edge["startNode"]["externalId"]

            if end_node_use_external_id:
                end_node = edge["endNode"]["externalId"]
            else:
                end_node = edge["endNode"]

            if start_node not in grouped_edges:
                grouped_edges[start_node] = []

            grouped_edges[start_node].append(end_node)

        return grouped_edges
