from typing import Any
from gql import Client, gql
from models.notification_type_model import (
    NotificationTypeModel,
    NotificationTypeCreateModel,
)
from settings.settings_class import Settings
from cognite.client import CogniteClient
from cognite.client.data_classes.data_modeling import NodeApply, NodeOrEdgeData, ViewId
import utils as Utils
from models.notification_event_model import NotificationEventModel
from services.database_cache_service import DatabaseCacheService
import queries.notification_cache_queries as queries_notification_cache_queries

ENTITY = "NotificationType"

class NotificationTypeRepository:

    def __init__(
        self,
        cogniteClient: CogniteClient,
        gqlClient: Client,
        settings:Settings
    ):
        self.cogniteClient = cogniteClient
        self.gqlClient = gqlClient
        self.settings = settings

    def create(
        self, request: NotificationTypeCreateModel, db_cache: DatabaseCacheService
    ):

        # GET ENTITY VIEW
        view = Utils.cognite.find_view_by_external_id(
            db_cache.get("cognite_views")[self.settings.cognite_graphql_model_space],
            ENTITY,
        )

        entity_versions = view.version

        eventExternalId = request.generate_external_id()
        request.name = request.format_name()
        request.description = request.format_description()
        request.entityType = request.format_entities() if request.entityType else ""

        del request.externalId
        del request.code

        # CREATE EVENT
        eventNodes = NodeApply(
            self.settings.ntf_instance_space,
            eventExternalId,
            sources=[
                NodeOrEdgeData(
                    ViewId(self.settings.cognite_graphql_model_space, ENTITY, entity_versions),
                    request.dict()
                )
            ]
        )
        self.cogniteClient.data_modeling.instances.apply(nodes=eventNodes)

        return eventExternalId

    def validate_notification_type_changes(
        self,
        eventSource: NotificationEventModel,
        notificationType: NotificationTypeModel,
    ):
        new_changes = False
        properties_changes = self.validate_properties_changes(
            eventSource["properties"], notificationType.properties
        )

        new_changes = (
            True
            if eventSource["description"] != notificationType.description
            or properties_changes
            else False
        )

        if eventSource.get("entityType") is not None:
            new_changes = (
                True
                if eventSource["entityType"] != notificationType.entityType
                else False
            )

        return new_changes

    def validate_properties_changes(self, eventProperties, notificationTypeProperties):
        keys_to_compare = ["name"]

        event_keys = [
            {key: obj[key] for key in keys_to_compare} for obj in eventProperties
        ]
        data_keys = (
            [
                {key: obj[key] for key in keys_to_compare}
                for obj in notificationTypeProperties
            ]
            if notificationTypeProperties is not None
            else []
        )

        event_keys_sorted = {tuple(d.items()) for d in event_keys}
        data_keys_sorted = {tuple(d.items()) for d in data_keys}

        return not event_keys_sorted.issubset(data_keys_sorted)

    def format_properties_json(self, eventProperties, notificationType):
        combined = (
            eventProperties + notificationType.properties
            if notificationType is not None and notificationType.properties is not None
            else eventProperties
        )
        unique_items = {item["name"]: item for item in combined}.values()
        properties_list = list(unique_items)
        result = []
        for obj in properties_list:
            value = obj.get("value", None)
            result.append(
                {
                    "name": obj["name"],
                    "type": obj["type"],
                    "value": value,
                    "isNumeric": self.is_number(value),
                }
            )
        return result

    def is_number(self, value):
        if isinstance(value, (int, float)):
            return True

        elif isinstance(value, str) and value.replace(".", "", 1).isdigit():
            return True

        return False
    
    def get_by_id(self, external_id):
        filter = {}
        filter["filter"] = {"externalId": {"eq": external_id}}

        result = self.gqlClient.execute(
            gql(queries_notification_cache_queries.NOTIFICATION_TYPE_CACHE_QUERY), filter
        )

        notification_type = result["listNotificationType"]["items"][0]

        if not notification_type:
            return None

        return notification_type
