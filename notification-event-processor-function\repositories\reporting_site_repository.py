import queries.reporting_site_queries as queries
from gql import Client, gql
from settings.settings_class import Settings

class ReportingSiteRepository:

    def __init__(
        self,
        gqlClient: Client,
        settings: Settings,
    ):
        self.gqlClient = gqlClient
        self.settings = settings
        
    def findById(self, description: str):
        filter = {}
        filter["filter"] = {
            "and":[
                    {"externalId": {"eq": description}}, 
                    {"space": {"eq": self.settings.assethierarchy_instance_space}}
                ]
            }
        
        result = self.gqlClient.execute(
            gql(queries.SITE_FIND_BY_NAME),
            filter)
        
        if (len(result["listReportingSite"]["items"]) > 0):
            return result["listReportingSite"]["items"][0]
        
        return None
