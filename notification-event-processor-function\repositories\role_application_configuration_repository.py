from typing import Any, List
from gql import gql, Client
import queries.roles_queries as queries
import models.notification_role_model as models
from settings.settings_class import Settings
from core.graphql_client import GraphQLClient


class RoleApplicationConfigurationRepository:
    def __init__(
        self,
        gqlClient: Client,
        settings: Settings,
        graphql_client: GraphQLClient,
    ):
        self.gqlClient = gqlClient
        self.settings = settings
        self._graphql_client = graphql_client
