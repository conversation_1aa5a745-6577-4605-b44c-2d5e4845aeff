from typing import Any, List, Optional
from gql import gql, Client
import queries.roles_queries as queries
from models.user_role_site_model import UserRoleSiteModel
from models.common_basic_model import RelationModel
from settings.settings_class import Settings
from core.graphql_client import GraphQL<PERSON>lient
from cognite.client import CogniteClient
import utils as Utils
from cognite.client.data_classes.data_modeling.query import (
    Query,
    Select,
    NodeResultSetExpression,
    EdgeResultSetExpression,
    SourceSelector,
)
from cognite.client.data_classes.data_modeling import (
    ViewId,
    PropertyId,
)
from cognite.client.data_classes.filters import Equals, HasData, In, And
from cognite.client.data_classes.data_modeling.ids import ViewId
from services.database_cache_service import DatabaseCacheService

NOTIFICATION_USER_ROLE_SITE_ENTITY = "NotificationUserRoleSite"
ROLE_ENTITY = "Role"
USER_COMPLEMENT_ENTITY = "UserComplement"
USER_AZURE_ATTRIBUTE_ENTITY = "UserAzureAttribute"
USER_ENTITY = "User"
UMG_MODEL_SPACE = "UMG-COR-ALL-DMD"


class UserRoleSiteRepository:
    def __init__(
        self,
        gqlClient: Client,
        settings: Settings,
        graphql_client: GraphQLClient,
        cogniteClient: CogniteClient,
    ):
        self.gqlClient = gqlClient
        self.settings = settings
        self._graphql_client = graphql_client
        self.cogniteClient = cogniteClient

    def find_view_by_external_id(self, views_list, external_id):
        return next(
            (view for view in views_list if view.external_id == external_id), None
        )

    def list_users_by_role_app_site(
        self,
        role_list: List[str],
        db_cache: DatabaseCacheService,
        event_service: Optional[bool] = False,
    ) -> List[UserRoleSiteModel]:

        roles = []

        if event_service:
            for role in role_list:
                roles.append("UserRoleSite_" + role)
        else:
            roles = role_list

        result = self.find_unlimited_template_subscribed_roles(roles, db_cache)
        items = []
        if len(result) > 0:
            for item in result:
                items.append(UserRoleSiteModel.mapFromResult(item))

        return items

    def find(self, filter: Any) -> List[Any]:
        items = self._graphql_client.query_unlimited(
            queries.LIST_USER_BY_FILTER, "listNotificationUser", filter
        )

        return items

    def find_unlimited_template_subscribed_roles(
        self, roles: List[str], db_cache: DatabaseCacheService
    ):
        notification_user_role_site_view = Utils.cognite.find_view_by_external_id(
            db_cache.get("cognite_views")[self.settings.cognite_graphql_model_space],
            NOTIFICATION_USER_ROLE_SITE_ENTITY,
        )

        user_management_views = db_cache.get("cognite_views")[UMG_MODEL_SPACE]

        role_view = self.find_view_by_external_id(
            user_management_views,
            ROLE_ENTITY,
        ).version

        user_complement_view = self.find_view_by_external_id(
            user_management_views,
            USER_COMPLEMENT_ENTITY,
        ).version

        user_azure_attribute_view = self.find_view_by_external_id(
            user_management_views,
            USER_AZURE_ATTRIBUTE_ENTITY,
        ).version

        user_view = self.find_view_by_external_id(
            user_management_views,
            USER_ENTITY,
        )

        views = {
            "notification_user_role_site": notification_user_role_site_view.version,
            "role": role_view,
            "user_complement": user_complement_view,
            "user_azure_attribute": user_azure_attribute_view,
        }

        user_azure_attribute_in_roles_from_event_cursor = None
        user_complement_in_roles_from_event_cursor = None
        user_complement_in_subscribed_roles_cursor = None
        role_from_roles_cursor = None
        roles_cursor = None
        has_cursor = True
        response_query = None
        roles_results_accumulated = []

        query = Query(
            with_={
                "roles": NodeResultSetExpression(
                    filter=And(
                        Equals(["node", "space"], self.settings.um_instance_space),
                        In(["node", "externalId"], roles),
                    ),
                    limit=10000,
                    chain_to="destination",
                    direction="outwards",
                ),
                "role_from_roles": NodeResultSetExpression(
                    from_="roles",
                    limit=10000,
                    chain_to="destination",
                    direction="outwards",
                    through=PropertyId(
                        ViewId(
                            self.settings.cognite_graphql_model_space,
                            NOTIFICATION_USER_ROLE_SITE_ENTITY,
                            notification_user_role_site_view.version,
                        ),
                        "role",
                    ),
                ),
                # getting the users complements from the roles
                "user_complement_in_roles_from_event": EdgeResultSetExpression(
                    from_="roles",
                    limit=10000,
                    direction="inwards",
                    chain_to="destination",
                    filter=Equals(
                        ["edge", "type"],
                        {
                            "space": UMG_MODEL_SPACE,
                            "externalId": "UserComplement.userRoleSite",
                        },
                    ),
                    node_filter=HasData(
                        views=[
                            ViewId(
                                UMG_MODEL_SPACE,
                                USER_COMPLEMENT_ENTITY,
                                user_complement_view,
                            )
                        ]
                    ),
                ),
                "user_complement_in_subscribed_roles": NodeResultSetExpression(
                    from_="user_complement_in_roles_from_event",
                    limit=10000,
                ),
                # getting the user azure attribute from users complements from the roles
                "user_azure_attribute_in_roles_from_event": NodeResultSetExpression(
                    from_="user_complement_in_subscribed_roles",
                    limit=10000,
                    chain_to="destination",
                    direction="outwards",
                    through=PropertyId(
                        ViewId(
                            UMG_MODEL_SPACE,
                            USER_COMPLEMENT_ENTITY,
                            user_complement_view,
                        ),
                        "userAzureAttribute",
                    ),
                ),
                "user_in_subscribed_roles": NodeResultSetExpression(
                    from_="user_azure_attribute_in_roles_from_event",
                    limit=10000,
                    chain_to="destination",
                    direction="outwards",
                    through=PropertyId(
                        ViewId(
                            UMG_MODEL_SPACE,
                            USER_AZURE_ATTRIBUTE_ENTITY,
                            user_azure_attribute_view,
                        ),
                        "user",
                    ),
                    filter=Equals(user_view.as_property_ref("deleted"), False),
                ),
            },
            select={
                "roles": Select(
                    [
                        SourceSelector(
                            ViewId(
                                self.settings.cognite_graphql_model_space,
                                NOTIFICATION_USER_ROLE_SITE_ENTITY,
                                notification_user_role_site_view.version,
                            ),
                            ["*"],
                        )
                    ],
                    limit=10000,
                ),
                "role_from_roles": Select(
                    [
                        SourceSelector(
                            ViewId(
                                UMG_MODEL_SPACE,
                                ROLE_ENTITY,
                                role_view,
                            ),
                            ["*"],
                        )
                    ],
                    limit=10000,
                ),
                "user_complement_in_roles_from_event": Select(limit=10000),
                "user_complement_in_subscribed_roles": Select(
                    [
                        SourceSelector(
                            ViewId(
                                UMG_MODEL_SPACE,
                                USER_COMPLEMENT_ENTITY,
                                user_complement_view,
                            ),
                            ["searchTags", "userAzureAttribute"],
                        )
                    ],
                    limit=10000,
                ),
                "user_azure_attribute_in_roles_from_event": Select(
                    [
                        SourceSelector(
                            ViewId(
                                UMG_MODEL_SPACE,
                                USER_AZURE_ATTRIBUTE_ENTITY,
                                user_azure_attribute_view,
                            ),
                            ["user"],
                        )
                    ],
                    limit=10000,
                ),
                "user_in_subscribed_roles": Select(
                    [
                        SourceSelector(
                            ViewId(
                                UMG_MODEL_SPACE,
                                USER_ENTITY,
                                user_view.version,
                            ),
                            ["deleted"],
                        )
                    ],
                    limit=10000,
                ),
            },
            cursors={
                "roles": roles_cursor,
                "role_from_roles": role_from_roles_cursor,
                "user_complement_in_roles_from_event": user_complement_in_roles_from_event_cursor,
                "user_complement_in_subscribed_roles": user_complement_in_subscribed_roles_cursor,
                "user_azure_attribute_in_roles_from_event": user_azure_attribute_in_roles_from_event_cursor,
            },
        )

        while has_cursor:
            response_query = self.cogniteClient.data_modeling.instances.query(query)
            roles_result = self.format_query_response(response_query, views)

            if roles_result:
                roles_results_accumulated.extend(roles_result)

            if response_query:
                has_cursor = False
                cursor_keys = [
                    "roles",
                    "role_from_roles",
                    "user_complement_in_subscribed_roles",
                    "user_complement_in_roles_from_event",
                    "user_azure_attribute_in_roles_from_event",
                ]

                for key in cursor_keys:
                    if (
                        key in response_query.cursors
                        and len(response_query[key]) == 10000
                    ):
                        query.cursors[key] = response_query.cursors[key]
                        has_cursor = True
            else:
                has_cursor = False

        return roles_results_accumulated

    def format_query_response(self, response, views: dict):
        roles_result = response["roles"].dump()
        role_from_roles = response["role_from_roles"].dump()
        user_complement_result = response["user_complement_in_subscribed_roles"].dump()
        user_azure_attribute_result = response[
            "user_azure_attribute_in_roles_from_event"
        ].dump()
        user_in_roles = response["user_in_subscribed_roles"].dump()
        subscribed_roles = []

        for role in roles_result:
            properties = (
                role.get("properties", {})
                .get(self.settings.cognite_graphql_model_space, {})
                .get(
                    f"{NOTIFICATION_USER_ROLE_SITE_ENTITY}/{views['notification_user_role_site']}",
                    {},
                )
            )

            role_externalId = properties.get("role", {}).get("externalId", "")
            filter_role = [
                item
                for item in role_from_roles
                if item["externalId"] == role_externalId
            ][0]
            filter_role_properties = (
                filter_role.get("properties", {})
                .get(UMG_MODEL_SPACE, {})
                .get(f"{ROLE_ENTITY}/{views['role']}", {})
            )

            filter_users = [
                user
                for user in user_complement_result
                if role.get("externalId", "")
                in user.get("properties", {})
                .get(UMG_MODEL_SPACE, {})
                .get(f"{USER_COMPLEMENT_ENTITY}/{views['user_complement']}", {})
                .get("searchTags", [])
            ]
            users_complement = [
                user.get("properties", {})
                .get(UMG_MODEL_SPACE, {})
                .get(f"{USER_COMPLEMENT_ENTITY}/{views['user_complement']}", {})
                .get("userAzureAttribute", {})
                .get("externalId", "")
                for user in filter_users
            ]
            user_azure_attribute = [
                user.get("properties", {})
                .get(UMG_MODEL_SPACE, {})
                .get(
                    f"{USER_AZURE_ATTRIBUTE_ENTITY}/{views['user_azure_attribute']}", {}
                )
                .get("user", {})
                .get("externalId", "")
                for user in user_azure_attribute_result
                if user["externalId"] in users_complement
            ]
            users_in_role = [
                {
                    "externalId": user.get("externalId", ""),
                    "space": user.get("space", ""),
                }
                for user in user_in_roles
                if user["externalId"] in user_azure_attribute
            ]

            subscribed_roles.append(
                {
                    "externalId": role.get("externalId", ""),
                    "space": role.get("space", ""),
                    "roleName": filter_role_properties.get("name", ""),
                    "roleDescription": filter_role_properties.get("description", None),
                    "usersComplements": sorted(
                        users_in_role, key=lambda x: x["externalId"].lower()
                    ),
                }
            )

        return subscribed_roles
