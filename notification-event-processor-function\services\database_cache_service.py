from typing import Any, Dict, Optional
from cognite.client import Cognite<PERSON>lient
from gql import Client, gql
from settings.settings_class import Settings
from core.graphql_client import GraphQLClient
import queries.notification_cache_queries as queries_notification_cache_queries


class DatabaseCacheService:
    def __init__(
        self,
        cognite_client: CogniteClient,
        gql_client: Client,
        settings: Settings,
        graphql_client: GraphQLClient,
    ):
        self.cache: Dict[str, Any] = {}

        self.cognite_client = cognite_client
        self.gql_client = gql_client
        self.settings = settings
        self.graphql_client = graphql_client

    def initialize(self):

        self.cache["cognite_views"] = self.__views_cognite()

        result = self.gql_client.execute(
            gql(queries_notification_cache_queries.NOTIFICATION_CACHE_QUERY)
        )

        self.cache["applications"] = self.__fetch_applications(result)
        self.cache["severity"] = self.__fetch_severity(result)
        self.cache["notification_type"] = self.__fetch_notification_type()
        self.cache["application_groups"] = { }
        self.cache["roles"] = { }

        return self

    def get(self, key: str) -> Any:

        if key in self.cache:
            return self.cache[key]

        data = self._fetch_from_db(key)
        self.cache[key] = data
        return data

    def set(self, key: str, value: Any) -> None:

        self.cache[key] = value

    def invalidate(self, key: str) -> None:

        if key in self.cache:
            del self.cache[key]

    def clear(self) -> "DatabaseCacheService":

        self.cache.clear()
        return self
    
    def update_in_list_by_external_id(self, key: str, new_item: dict[str, Any]) -> None:
        items = self.cache.get(key, [])

        if not isinstance(items, list):
            raise ValueError(f"Cache entry for key '{key}' is not a list.")

        for i, item in enumerate(items):
            if item.get("externalId") == new_item.get("externalId"):
                items[i] = {**item, **new_item}
                self.cache[key] = items
                return

        raise ValueError(f"No item with externalId '{new_item.get('externalId')}' found in '{key}'.")

    def _fetch_from_db(self, key: str) -> Any:

        if key == "notification_type":
            return self.__fetch_notification_type()

        return None

    def __views_cognite(self):

        ntf_cor_views = self.cognite_client.data_modeling.views.list(
            space=self.settings.cognite_graphql_model_space,
            limit=1000,
        )

        um_cor_views = self.cognite_client.data_modeling.views.list(
            space=self.settings.um_model_space,
            limit=1000,
        )

        cognite_views = {
            self.settings.cognite_graphql_model_space: ntf_cor_views.data,
            self.settings.um_model_space: um_cor_views.data,
        }

        return cognite_views

    def __fetch_applications(self, result: Dict[str, Any]):
        list_notification_application = result["listNotificationApplication"]["items"]

        if not list_notification_application:
            return []

        return list_notification_application

    def __fetch_severity(self, result: Dict[str, Any]):

        list_notification_severity = result["listNotificationSeverity"]["items"]

        if not list_notification_severity:
            return []

        return list_notification_severity

    def __fetch_notification_type(self):
        filter = {}
        filter["filter"] = {"and": [
            {"application": {"externalId": {"isNull": False}}}, 
            {"space": {"eq": "NTF-COR-ALL-DAT"}}
        ]}
        result = self.gql_client.execute(
            gql(queries_notification_cache_queries.NOTIFICATION_TYPE_CACHE_QUERY), filter
        )

        list_notification_type = result["listNotificationType"]["items"]

        if not list_notification_type:
            return []

        return list_notification_type
