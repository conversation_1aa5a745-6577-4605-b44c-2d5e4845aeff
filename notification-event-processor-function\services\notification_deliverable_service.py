import json
from settings.settings_class import Settings
from cognite.client import Cognite<PERSON>lient
from gql import Client
import croniter
from datetime import datetime, timezone
from models.common_basic_model import CommonBasicModel, RelationModel
from models.notification_template_model import NotificationTemplateModel
from models.notification_deliverable_model import NotificationDeliverableCreateModel
from models.notification_application_model import NotificationApplicationModel
from models.notification_severity_model import NotificationSeverityModel
import repositories
from jinja2 import Environment, FileSystemLoader
from pathlib import Path
import utils
from services.database_cache_service import DatabaseCacheService


class NotificationDeliverableService:
    def __init__(
        self, cogniteClient: CogniteClient, gqlClient: Client, settings: Settings
    ):
        self.cogniteClient = cogniteClient
        self.gqlClient = gqlClient
        self.settings = settings
        self.notification_deliverable_repository = (
            repositories.NotificationDeliverableRepository(
                cogniteClient, gqlClient, settings
            )
        )

    def create(
        self,
        template: NotificationTemplateModel,
        severity: NotificationSeverityModel,
        eventExternalId: str,
        db_cache: DatabaseCacheService,
        notificationReference: RelationModel = None,
        modelSite: CommonBasicModel = None,
    ):
        print(
            "*** Starting NotificationDeliverable processing for template {}... ***".format(
                template.externalId
            )
        )

        try:
            if not template.channels or len(template.channels) == 0:
                print(
                    "There are no channels available for template {}. Skipping...".format(
                        template.externalId
                    )
                )
                return

            if (
                not template.summarizedUsers or len(template.summarizedUsers) == 0
            ) and (
                not template.summarizedExternalUsers
                or len(template.summarizedExternalUsers) == 0
            ):
                print("There are no subscribers for template. Skipping...")
                return

            nextScheduleDate = self.getNextScheduleDate(
                template.frequencyCronExpression, notificationReference
            )
            template.severity = severity

            reportingSiteRelationObject = (
                None
                if not modelSite
                else RelationModel(
                    externalId=modelSite.externalId, space=modelSite.space
                )
            )
            templateRelationObject = RelationModel(
                externalId=template.externalId, space=template.space
            )
            eventRelationObject = RelationModel(
                externalId=eventExternalId, space=self.settings.ntf_prot_instance_space
            )

            template_channels = (
                [obj for obj in template.channels if obj.externalId == "NTFCHN-EMAIL"]
                if notificationReference
                else template.channels
            )

            for channel in template_channels:
                deliverableCreateModel = NotificationDeliverableCreateModel()
                deliverableCreateModel.subscribers = template.summarizedUsers
                deliverableCreateModel.channel = channel
                deliverableCreateModel.severity = severity
                deliverableCreateModel.reportingSite = reportingSiteRelationObject
                deliverableCreateModel.template = templateRelationObject
                deliverableCreateModel.event = eventRelationObject
                deliverableCreateModel.scheduleDate = nextScheduleDate
                deliverableCreateModel.externalSubscribers = (
                    template.summarizedExternalUsers
                    if (channel.externalId == self.settings.channel_email_external_id)
                    else None
                )

                if (
                    channel.externalId == self.settings.channel_email_external_id
                    or channel.externalId == self.settings.channel_teams_external_id
                ):
                    # if template.notificationType.application.iconUrl:
                    #     template.applicationIconInfo = self.organize_icon(template.notificationType.application.iconUrl)
                    env = self.getHtmlPath()
                    message = self.setHtmlMessage(
                        template, modelSite, env, notificationReference
                    )
                    deliverableCreateModel.text = message
                elif channel.externalId == self.settings.channel_sms_external_id:
                    publish_date = datetime.now(timezone.utc).strftime(
                        "%m/%d/%Y %I:%M %p (UTC)"
                    )
                    deliverableCreateModel.text = f"{template.text}-{publish_date}"
                else:
                    deliverableCreateModel.text = template.text

                creation_response = self.notification_deliverable_repository.create(
                    deliverableCreateModel, db_cache
                )

            if creation_response:
                print(
                    """Finishing NotificationDeliverable processing for Template '{}'. 
                    {} subscribers to be notified.""".format(
                        template.externalId,
                        str(
                            len(template.summarizedUsers)
                            + (
                                len(template.summarizedExternalUsers)
                                if template.summarizedExternalUsers
                                else 0
                            )
                        ),
                    )
                )
            else:
                print(
                    f"There are no allowed subscribers for {self.settings.environment_id} environment."
                )

            return True
        except Exception as e:
            print(
                "Error during creation of NotificationDeliverable for Template '{}'. Skipping...".format(
                    template.externalId
                )
            )
            print("Details: {}".format(str(e)))

    def getNextScheduleDate(
        self, frequencyCronExpression: str, notificationReference: RelationModel = None
    ) -> datetime.timestamp:
        if frequencyCronExpression and frequencyCronExpression != "":
            cron = croniter.croniter(frequencyCronExpression, datetime.now())
            return (
                datetime.now().strftime("%Y-%m-%dT%H:%M:%SZ")
                if notificationReference
                else cron.get_next(datetime).strftime("%Y-%m-%dT%H:%M:%SZ")
            )
        else:
            return datetime.now().strftime("%Y-%m-%dT%H:%M:%SZ")

    def getHtmlPath(self):
        actual_path = Path(__file__)
        parent_path = actual_path.parent.parent
        env = Environment(loader=FileSystemLoader("%s/templates" % parent_path))
        return env

    def setHtmlMessage(
        self,
        template: NotificationTemplateModel,
        modelSite: CommonBasicModel,
        env: Environment,
        notificationReference: RelationModel = None,
    ):
        item = (
            env.get_template("chat-template.html")
            if notificationReference
            else env.get_template("default-template.html")
        )
        json_template = json.loads(json.dumps(template, cls=self.SimpleObject))
        data = self.setJsonProperties(json_template, modelSite, template)
        output = item.render(data=data)
        return output

    def setJsonProperties(
        self, json, modelSite: CommonBasicModel, template: NotificationTemplateModel
    ):
        json["text"] = self.formatString(json["text"])
        json["site"] = modelSite.description if modelSite else None
        json["publish_date"] = datetime.now(timezone.utc).strftime(
            "%m/%d/%Y %I:%M %p (UTC Time)"
        )
        json["iconText"] = self.setApplicationIcon(
            template.notificationType.application
        )

        return json

    def formatString(self, htmlString: str):
        format = "<br />".join(htmlString.split("\n"))
        if htmlString.count("http") > 0:
            format = utils.text_utils.formatLink(format)
        return format

    def setApplicationIcon(self, application: NotificationApplicationModel):
        namesArray = application.alias.split(" ")
        firstLetter = ""
        secondLetter = ""

        if len(namesArray) > 0:
            firstLetter = str(namesArray[0])[0]
            if len(namesArray) > 1 and len(namesArray[1]) > 0:
                secondLetter = str(namesArray[1])[0]
            else:
                secondLetter = str(namesArray[0])[1]

        return str(firstLetter + secondLetter).upper()

    class SimpleObject(json.JSONEncoder):
        def default(self, obj):
            if hasattr(obj, "__dict__"):
                return {
                    key: value
                    for key, value in obj.__dict__.items()
                    if not key.startswith("_")
                }
            return super().default(obj)

    # def organize_icon(self, icon: str):
    #     string_arrray = re.findall(r'[A-Z][a-z]*', icon)
    #     icon_types = ["Outlined", "Rounded", "Sharp", "Two", "Tone"]
    #     remove_icon_types = (
    #         palavra for palavra in string_arrray if palavra not in icon_types
    #     )
    #     get_icon_types = (
    #         palavra for palavra in string_arrray if palavra in icon_types
    #     )

    #     icon = '_'.join(palavra.lower() for palavra in remove_icon_types)
    #     icon_type = '-'.join(palavra.lower() for palavra in get_icon_types)
    #     icon_class = 'material-icons-' + icon_type if len(icon_type) > 0 else 'material-icons'

    #     return {
    #         'class': icon_class,
    #         'icon': icon,
    #     }
