import json
from typing import Any, List
from gql import Client
from settings.settings_class import Settings
from cognite.client import CogniteClient
from models.notification_event_model import (
    NotificationEventModel,
    NotificationEventCreateModel,
)
from models.notification_type_model import NotificationTypeCreateModel
from models.notification_event_model_validate import ValidateNotificationRawEventModel
from models.common_basic_model import CommonBasicModel, RelationModel
from models.notification_severity_model import NotificationSeverityModel
import repositories
from core.graphql_client import GraphQLClient
import models as models
from services.database_cache_service import DatabaseCacheService
from models.notification_type_model import NotificationTypeModel
from types import SimpleNamespace


class NotificationEventService:
    def __init__(
        self,
        cogniteClient: CogniteClient,
        gqlClient: Client,
        settings: Settings,
        graphql_client: GraphQLClient,
    ):
        self.cogniteClient = cogniteClient
        self.gqlClient = gqlClient
        self.settings = settings
        self._graphql_client = graphql_client

        self.notification_template_repository = (
            repositories.NotificationTemplateRepository(
                cogniteClient, gqlClient, settings
            )
        )
        self.notification_event_raw_repository = (
            repositories.NotificationRawEventRepository(
                cogniteClient, gqlClient, settings
            )
        )
        self.notification_application_group_repository = (
            repositories.NotificationApplicationGroupRepository(
                gqlClient, settings, graphql_client, cogniteClient
            )
        )
        self.notification_on_screen_repository = (
            repositories.NotificationOnScreenRepository(
                cogniteClient, gqlClient, settings
            )
        )
        self.reporting_site_repository = repositories.ReportingSiteRepository(
            gqlClient, settings
        )
        self.notification_type_repository = repositories.NotificationTypeRepository(
            cogniteClient, gqlClient, settings
        )
        self.notification_event_repository = repositories.NotificationEventRepository(
            cogniteClient, gqlClient, settings
        )
        self.user_role_repository = repositories.UserRoleSiteRepository(
            gqlClient, settings, graphql_client, cogniteClient
        )

    def list_notification_raw_event_pending(self):
        return (
            self.notification_event_raw_repository.list_notification_raw_event_pending()
        )

    def stamp_raw_event_log_as_processed(
        self,
        eventLog_external_id: str,
        db_cache: DatabaseCacheService,
        erros: List[str] = None,
    ):
        self.notification_event_raw_repository.stamp_raw_event_log_as_processed(
            eventLog_external_id, db_cache, erros
        )

    def create(
        self,
        raw_external_id: str,
        sourceApplication: Any,
        eventSource: NotificationEventModel,
        db_cache: DatabaseCacheService,
    ):
        print("* Starting NotificationEvent creation...")

        properties_json = eventSource["properties"]
        eventCreateModel = NotificationEventCreateModel()
        eventCreateModel.applicationGroups = []
        eventCreateModel.roles = []
        eventCreateModel.users = []
        eventCreateModel.externalUsers = []
        eventCreateModel.properties = json.loads(
            '{ "properties": ' + json.dumps(properties_json) + "}"
        )

        allRelatedUsersList = []
        externalUsersList = []
        entitiesOnPayload = False

        umSpace = self.settings.um_instance_space

        # GET RAW EVENT
        eventCreateModel.rawEvent = RelationModel(
            space=self.settings.ntf_prot_instance_space, externalId=raw_external_id
        )

        # GET REPORTING SITE
        site = self.getSiteFromProperties(properties_json)
        modelSite = None
        if site:
            modelSite = CommonBasicModel.mapFromResult(site)
            eventCreateModel.reportingSite = RelationModel(
                space=site.get("space"), externalId=site.get("externalId")
            )

        # GET APPLICATION EXTERNALID BY NAME
        if sourceApplication is None:
            raise Exception(
                "Application '"
                + eventSource["application"]
                + "' not found. Aborting execution."
            )

        notificationType = self._find_and_map_notification_type(
            sourceApplication, eventSource, db_cache
        )

        eventCreateModel.notificationType = RelationModel(
            space=notificationType.space, externalId=notificationType.externalId
        )

        # GET SEVERITY EXTERNALID BY NAME
        severity: NotificationSeverityModel = None
        if "severity" in eventSource and eventSource["severity"]:
            severity = (
                NotificationSeverityModel.mapFromResult(matched)
                if (
                    matched := next(
                        (
                            item
                            for item in db_cache.get("severity")
                            if item.get("description") == eventSource.get("severity")
                        ),
                        None,
                    )
                )
                else None
            )

            eventCreateModel.severity = severity

        # CREATING RELATIONSHIPS TO THE NOTIFICATIONEVENT RECORD
        if eventSource.get("applicationGroups", None):
            applicationGroups = []
            cache_groups = db_cache.get("application_groups")

            groups_in_cache, missing_groups = self.get_existing_and_non_existing_groups_in_cache(cache_groups, eventSource.get("applicationGroups"))
            applicationGroups.extend(groups_in_cache.values())

            if len(missing_groups) > 0:
                applicationGroups.extend(
                    self.notification_application_group_repository.list_by_filter(
                        sourceApplication["externalId"],
                        missing_groups,
                        "byName",
                        db_cache,
                    )
                )

            # Get All Application Groups externalIds and save to NotificationEvent object
            for item in applicationGroups:
                if len(missing_groups) > 0 and item.name in missing_groups:
                    db_cache.cache["application_groups"][item.externalId] = item

                eventCreateModel.applicationGroups.append(
                    RelationModel(space=item.space, externalId=item.externalId)
                )

                # Save USERS to global list
                if item.users:
                    for user in item.users:
                        if user not in allRelatedUsersList:
                            allRelatedUsersList.append(
                                RelationModel(
                                    space=user.space, externalId=user.externalId
                                )
                            )

                if item.usersRoles:
                    # Save USERS ROLES to global list
                    for role in item.usersRoles:
                        for user in role.users:
                            if user not in allRelatedUsersList:
                                allRelatedUsersList.append(
                                    RelationModel(
                                        space=user.space, externalId=user.externalId
                                    )
                                )

                if item.blocklist:
                    # Save BLOCKED USERS to global list
                    for user in item.blocklist:
                        if user not in allRelatedUsersList:
                            allRelatedUsersList.append(
                                RelationModel(
                                    space=user.space, externalId=user.externalId
                                )
                            )

                if item.blocklistRoles:
                    # Save BLOCKED USERS ROLES to global list
                    for role in item.blocklistRoles:
                        for user in role.users:
                            if user not in allRelatedUsersList:
                                allRelatedUsersList.append(
                                    RelationModel(
                                        space=user.space, externalId=user.externalId
                                    )
                                )

                if item.externalUsers:
                    # Save EXTERNAL USERS to global list
                    for user in item.externalUsers:
                       if user not in externalUsersList:
                            externalUsersList.append(user)

        # Get All Roles related to the Application and its Users, and save to Event and global list
        if eventSource.get("roles", None):
            cache_roles = db_cache.get("roles")

            roles_id = ['UserRoleSite_' + role for role in eventSource.get("roles")]

            roles = [cache_roles[eid] for eid in roles_id if eid in cache_roles]
            missing_roles_ids = [eid for eid in roles_id if eid not in cache_roles]

            if len(missing_roles_ids) > 0:
                roles.extend(
                    self.user_role_repository.list_users_by_role_app_site(
                        roles_id,
                        db_cache,
                    )
                )

            for item in roles:
                if len(missing_roles_ids) > 0 and item.externalId in missing_roles_ids:
                    db_cache.cache["roles"][role.externalId] = role

                eventCreateModel.roles.append(
                    RelationModel(space=item.space, externalId=item.externalId)
                )

                # Save USERS to global list
                if item.usersComplements:
                    for user in item.usersComplements:
                        if user not in allRelatedUsersList:
                            allRelatedUsersList.append(
                                RelationModel(
                                    space=user.space, externalId=user.externalId
                                )
                            )

        # Get All Users externalIds and save to Event and to global List
        if eventSource.get("users", None):
            filter = {}
            filter["filter"] = {
                "and": [
                    {
                        "or": [
                            {"email": {"in": eventSource.get("users")}},
                            {"externalId": {"in": eventSource.get("users")}},
                        ]
                    },
                    {"space": {"eq": self.settings.um_instance_space}},
                ]
            }
            um_users = self.user_role_repository.find(filter)
            for user in um_users:
                userMapped = RelationModel(space=umSpace, externalId=user["externalId"])
                if userMapped not in eventCreateModel.users:
                    eventCreateModel.users.append(userMapped)

                if userMapped not in allRelatedUsersList:
                    allRelatedUsersList.append(userMapped)

        # Get external users emails and save to Event and to global List
        if eventSource.get("externalUsers", None):
            external_users = eventSource.get("externalUsers")
            for user in external_users:
                eventCreateModel.externalUsers.append(user)
                if user not in externalUsersList:
                    externalUsersList.append(user)

        if len(eventCreateModel.roles) > 0 or len(eventCreateModel.applicationGroups) > 0 or len(eventCreateModel.users) > 0: 
            entitiesOnPayload = True

        eventExternalId = self.notification_event_repository.create(
            eventCreateModel, db_cache
        )

        print("* NotificationEvent created successfully.")

        return (
            eventCreateModel.notificationType,
            eventCreateModel.properties,
            severity if severity else None,
            modelSite,
            allRelatedUsersList,
            externalUsersList,
            eventExternalId,
            entitiesOnPayload,
        )

    def _find_and_map_notification_type(
        self,
        sourceApplication: Any,
        eventSource: NotificationEventModel,
        db_cache: DatabaseCacheService,
    ):

        notificationType = next(
            (
                item
                for item in db_cache.get("notification_type")
                if item["name"] == eventSource["notificationType"]
                and item["application"]["externalId"] == sourceApplication["externalId"]
            ),
            None,
        )

        if notificationType:
            return NotificationTypeModel.mapFromResult(notificationType)

        return None

    def getSiteFromProperties(self, sourcePropertiesJSON: dict):
        # Attempt to get reportingSite details based on name
        siteNode = next((item.get("value") for item in sourcePropertiesJSON if isinstance(item, dict) and item.get('name') == 'site'), None)
        if siteNode:
            return self.reporting_site_repository.findById(siteNode)

        return None

    def validateEvent(
        self,
        sourceApplication: Any,
        request: ValidateNotificationRawEventModel,
        db_cache: DatabaseCacheService,
    ):
        errors = []
        app_id = []
        self.__validateApplication(sourceApplication, errors, app_id, db_cache)
        self.__validateNotificationType(request, errors)
        self.__validateDescription(request, errors)
        self.__validateSeverity(request, errors, db_cache)

        if len(app_id) > 0:
            self.__validateRoles(request, errors, app_id[0], db_cache)
            self.__validateGroups(request, errors, app_id[0], db_cache)
            self.__validateUsers(request, errors, app_id[0])

        users = request.get("users")
        roles = request.get("roles")
        groups = request.get("applicationGroups")
        if not users and not roles and not groups:
            errors.append({
                "field": "notificationTarget",
                "message": "The event must contain at least one target: user, group, or role."
            })

        self.__validateProperties(request, errors)

        return errors

    def __validateApplication(
        self, request, errors, app_id, db_cache: DatabaseCacheService
    ):
        field_name = "application"
        if not request:
            errors.append(f'The "{field_name}" is required.')
        else:
            application = next(
                (
                    item
                    for item in db_cache.get("applications")
                    if item["externalId"] == request["externalId"]
                ),
                None,
            )

            if not application:
                errors.append(
                    f'The "{field_name}" ("{request[field_name]}") does not exist.'
                )
                return

            app_id.append(application["externalId"])

    def __validateNotificationType(self, request, errors):
        field_name = "notificationType"
        self.__validateFieldExists(request, field_name, errors)

    def __validateDescription(self, request, errors):
        field_name = "description"
        self.__validateFieldExists(request, field_name, errors)

    def __validateSeverity(self, request, errors, db_cache: DatabaseCacheService):
        field_name = "severity"
        if hasattr(request, field_name) and getattr(request, field_name):
            severity = next(
                (
                    item
                    for item in db_cache.get("severities")
                    if item["description"] == request[field_name]
                ),
                None,
            )

            if not severity:
                errors.append(
                    f'The "{field_name}" ("{request[field_name]}") does not exist.'
                )

    def __validateRoles(self, request, errors, app_id, db_cache: DatabaseCacheService):
        field_name = "roles"
        role_sufix = "UserRoleSite_"
        if field_name in request and (len(request[field_name]) > 0) and app_id:
            cache_roles = db_cache.get("roles")

            roles_id = [role_sufix + role for role in request[field_name]]

            roles = [cache_roles[eid] for eid in roles_id if eid in cache_roles]
            missing_roles_ids = [eid for eid in roles_id if eid not in cache_roles]

            
            if len(missing_roles_ids) > 0:
                roles.extend(
                    self.user_role_repository.list_users_by_role_app_site(
                        role_list=request[field_name],
                        db_cache=db_cache,
                        event_service=True,
                    )
                )

            for role in roles:
                if len(missing_roles_ids) > 0 and role.externalId in missing_roles_ids:
                    db_cache.cache["roles"][role.externalId] = role

            if len(roles) < len(request[field_name]):
                if len(roles) > 0:
                    request_roles = request[field_name]
                    request_roles_with_prefix = [role_sufix + role for role in request_roles if role_sufix not in role]
                    um_roles_id = [x.externalId for x in roles]
                    no_match_roles = self.__returnNotMatches(request_roles_with_prefix, um_roles_id)
                    errors.append(
                        f'There are non-existent "{field_name}": {no_match_roles}'
                    )
                else:
                    errors.append(
                        f'There are non-existent "{field_name}": {request[field_name]}'
                    )

    def __validateGroups(self, request, errors, app_id, db_cache):
        field_name = "applicationGroups"
        if field_name in request and len(request[field_name]) > 0 and app_id:
            applicationGroups = []
            cache_groups = db_cache.get("application_groups")

            groups_in_cache, missing_groups = self.get_existing_and_non_existing_groups_in_cache(cache_groups, request[field_name])
            applicationGroups.extend(groups_in_cache.values())

            if len(missing_groups) > 0:
                applicationGroups.extend(
                    self.notification_application_group_repository.list_by_filter(
                        app_id,
                        missing_groups,
                        "byName",
                        db_cache,
                    )
                )

            # Get All Application Groups externalIds and save to NotificationEvent object
            for item in applicationGroups:
                if len(missing_groups) > 0 and item.name in missing_groups:
                    db_cache.cache["application_groups"][item.externalId] = item

            if len(applicationGroups) < len(request[field_name]):
                if len(applicationGroups) > 0:
                    a = request[field_name]
                    b = [x.name for x in applicationGroups]
                    no_match_groups = self.__returnNotMatches(a, b)
                    errors.append(
                        f'There are non-existent "{field_name}": {no_match_groups}'
                    )
                else:
                    errors.append(
                        f'There are non-existent "{field_name}": {request[field_name]}'
                    )

    def __validateUsers(self, request, errors, app_id):
        field_name = "users"
        if field_name in request and len(request[field_name]) > 0:
            filter = {}
            filter["filter"] = {
                "and": [
                    {
                        "or": [
                            {"email": {"in": request[field_name]}},
                            {"externalId": {"in": request[field_name]}},
                        ]
                    },
                    {"space": {"eq": self.settings.um_instance_space}},
                ]
            }
            um_users = self.user_role_repository.find(filter)
            if len(um_users) < len(request[field_name]):
                if len(um_users) > 0:
                    a = request[field_name]
                    b = [x["email"] for x in um_users]
                    no_match_users = self.__returnNotMatches(a, b)
                    errors.append(
                        f'There are non-existent "{field_name}": {no_match_users}'
                    )
                else:
                    errors.append(
                        f'There are non-existent "{field_name}": {request[field_name]}'
                    )

    def __validateProperties(self, request, errors):
        field_name = "properties"
        self.__validateFieldExists(request, field_name, errors)

    def __validateFieldExists(self, request, field_name: str, errors) -> bool:
        if field_name not in request:
            errors.append(f'The "{field_name}" field is required.')
            return False
        else:
            if type(request[field_name]) is str:
                attr = request[field_name]
                if attr.isspace() or attr is None or attr == "":
                    errors.append(f'The "{field_name}" field is required.')
                    return False

            if type(request[field_name]) is list:
                if len(request[field_name]) == 0:
                    errors.append(f'The "{field_name}" field is required.')
                    return False

        return True

    def __returnNotMatches(self, a, b):
        return [[x for x in a if x not in b]]

    def get_existing_and_non_existing_groups_in_cache(self, cache_groups, groups_names):
        name_to_external_id = {
            group.name: external_id
            for external_id, group in cache_groups.items()
        }

        groups_found = {}
        groups_not_found = []

        for name in groups_names:
            if name in name_to_external_id:
                external_id = name_to_external_id[name]
                groups_found[external_id] = cache_groups[external_id]
            else:
                groups_not_found.append(name)

        return groups_found, groups_not_found
    
    def validate_or_create_notification_type(
            self,         
            sourceApplication: Any,
            eventSource: NotificationEventModel,
            db_cache: DatabaseCacheService
        ):
        new_changes = False
        cached_notification_types = db_cache.get("notification_type")
        properties_json = eventSource["properties"]
        # GET NOTIFICATION TYPE BY NAME AND APP ID
        notificationType = self._find_and_map_notification_type(
            sourceApplication, eventSource, db_cache
        )

        if notificationType:
            new_changes = (
                self.notification_type_repository.validate_notification_type_changes(
                    eventSource, notificationType
                )
            )

        if (
            notificationType is None
            or notificationType.externalId is None
            or new_changes
        ):
            new_properties = self.notification_type_repository.format_properties_json(
                properties_json, notificationType
            )
            newNotificationType = NotificationTypeCreateModel(
                name=eventSource["notificationType"],
                description=eventSource["description"],
                application=RelationModel(
                    space=sourceApplication["space"],
                    externalId=sourceApplication["externalId"],
                ),
                entityType=(
                    eventSource["entityType"] if eventSource.get("entityType") else ""
                ),
                properties=new_properties,
            )

            create_or_update_notification_type_id = self.notification_type_repository.create(newNotificationType, db_cache)

            notificationType = self.notification_type_repository.get_by_id(create_or_update_notification_type_id)

            if new_changes:
                db_cache.update_in_list_by_external_id("notification_type", notificationType)
            else:
                cached_notification_types.append(notificationType)
                db_cache.set("notification_type", cached_notification_types)
                
            if notificationType is None or notificationType["externalId"] is None:
                raise Exception(
                    "Notification Type '"
                    + eventSource["notificationType"]
                    + "' not found. Aborting execution."
                )
            
        return self.dict_to_obj(notificationType)
    
    def dict_to_obj(self, d):
        if isinstance(d, dict):
            return SimpleNamespace(**{k: self.dict_to_obj(v) for k, v in d.items()})
        elif isinstance(d, list):
            return [self.dict_to_obj(i) for i in d]
        else:
            return d