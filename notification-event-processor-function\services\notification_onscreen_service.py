import html
from typing import Any, List

from cognite.client import Cognite<PERSON>lient
from gql import Client

import repositories
import services
import utils
from core.graphql_client import GraphQLClient
from models.common_basic_model import RelationModel
from models.notification_onscreen_model import NotificationOnScreenCreateModel
from models.notification_template_model import NotificationTemplateModel
from settings.settings_class import Settings
from utils.datetime_utils import now_formatted
from services.database_cache_service import DatabaseCacheService


class NotificationOnScreenService:
    def __init__(
        self,
        cogniteClient: CogniteClient,
        gqlClient: Client,
        settings: Settings,
        graphql_client: GraphQLClient,
    ):
        self.cogniteClient = cogniteClient
        self.gqlClient = gqlClient
        self.settings = settings
        self._graphql_client = graphql_client

        self.notification_on_screen_repository = (
            repositories.NotificationOnScreenRepository(
                cogniteClient, gqlClient, settings
            )
        )
        self.reporting_site_repository = repositories.ReportingSiteRepository(
            gqlClient, settings
        )
        self.notification_application_group_repository = (
            repositories.NotificationApplicationGroupRepository(
                gqlClient, settings, graphql_client, cogniteClient
            )
        )
        self.role_application_configuration_repository = (
            repositories.RoleApplicationConfigurationRepository(
                gqlClient, settings, graphql_client
            )
        )
        self.notification_deliverable_service = services.NotificationDeliverableService(
            cogniteClient, gqlClient, settings
        )

    def createByTemplate(
        self,
        template: NotificationTemplateModel,
        sourcePropertiesJSON: Any,
        eventExternalId: str,
        db_cache: DatabaseCacheService,
        reportingSite: RelationModel = None,
        sourceSeverity: RelationModel = None,
        notificationReference: RelationModel = None,
    ) -> List[RelationModel]:
        try:

            print(
                "* Starting NotificationOnScreen processing for Template '{}'...".format(
                    template.externalId
                )
            )

            defaultValidationMessage = "Template {} does not have {}. Skipping..."
            returnEmpty = None

            if (
                not template.summarizedUsers or len(template.summarizedUsers) == 0
            ) and (
                not template.summarizedExternalUsers
                or len(template.summarizedExternalUsers) == 0
            ):
                print("There are no subscribers for template. Skipping...")
                return

            #### Required Fields Validation
            if not template.text or template.text == "":
                print(
                    defaultValidationMessage.format(template.externalId, "valid Text")
                )
                return returnEmpty

            if (
                not template.notificationType
                or not template.notificationType.externalId
            ):
                print(
                    defaultValidationMessage.format(
                        template.externalId, "valid Notification Type"
                    )
                )
                return returnEmpty
            ### Required Fields Validation

            if (
                type(sourcePropertiesJSON) is dict
                and sourcePropertiesJSON["properties"]
            ):
                # If text contains any replaceable field ({{fieldName}}
                # replace by real values from source Json
                onScreenText = template.text

                if "{{" in template.text and "}}" in template.text:
                    for field in sourcePropertiesJSON["properties"]:
                        result = self.replaceTemplateField(
                            field["name"], field["value"], field["type"], template.text, onScreenText
                        )
                        template.text = result["text"]
                        onScreenText = result["textOnScreen"]

                #Clickable link in template text
                if template.text.count("http") > 0:
                    onScreenText = utils.text_utils.formatLink(onScreenText)

            onScreenCreateObject = NotificationOnScreenCreateModel()

            onScreenCreateObject.reportingSite = reportingSite
            onScreenCreateObject.subscribers = template.summarizedUsers
            onScreenCreateObject.template = RelationModel(
                space=template.space, externalId=template.externalId
            )
            onScreenCreateObject.severity = sourceSeverity
            onScreenCreateObject.text = onScreenText
            onScreenCreateObject.event = RelationModel(
                space=self.settings.ntf_prot_instance_space, externalId=eventExternalId
            )

            onScreenCreateObject.newUpdateTime = now_formatted() if notificationReference else None

            creation_response = self.notification_on_screen_repository.create(
                onScreenCreateObject, db_cache, notificationReference
            )

            if creation_response:
                print(
                    "* NotificationOnScreen processed for Template '{}'. {} subscribers to be notified.".format(
                        template.externalId, str(len(template.summarizedUsers))
                    )
                )
                return True
            else:
                print(f"There are no allowed subscribers for {self.settings.environment_id} environment.")

        except Exception as e:
            print(
                "Error during creation of NotificationOnScreen for Template '{}'. Skipping...".format(
                    template.externalId
                )
            )
            print("Details: {}".format(str(e)))
            raise e

    def replaceTemplateField(
        self, fieldName: str, fieldValue: Any, fieldType: str, text: str, textOnScreen: str
    ) -> dict[str, str]:
        
        result = {
                "text": text,
                "textOnScreen": textOnScreen
            }

        if fieldName and fieldValue and fieldType and text:
            prefix = str("{{")
            suffix = str("}}")
            fieldTypeName = fieldType.lower()
            fieldName = fieldName.lower()
            
            if not fieldName.lower() in text.lower():
                return result

            value = ""
            valueOnScreen = ""

            try:
                if fieldTypeName == "number":
                    value = str(int(fieldValue))
                    valueOnScreen = str(int(fieldValue))
                elif fieldTypeName == "decimal":
                    value = str(float(fieldValue))
                    valueOnScreen = str(float(fieldValue))
                elif fieldTypeName == "text":
                    value = str(fieldValue)
                    valueOnScreen = str(fieldValue)
                elif fieldTypeName == "html":
                    htmlDecode = str(html.unescape(fieldValue))
                    signalizedHtml = f"#htmlmark{htmlDecode}#htmlmark"
                    value = "".join(htmlDecode.split("\n"))
                    valueOnScreen = "".join(signalizedHtml.split("\n"))
                else:
                    return result
            except:
                return result

            searchFromIndex = 0
            for i in range(1, text.count(prefix) + 1):
                tagStartIndex = text.index(prefix, searchFromIndex)
                tagEndIndex = text.index(suffix, searchFromIndex) + 2

                nestedFieldName = text[tagStartIndex + 2 : tagEndIndex - 2]
                if nestedFieldName.lower() == fieldName.lower():
                    result["text"] = text.replace(text[tagStartIndex:tagEndIndex], value)
                    
                searchFromIndex = text.index(suffix, searchFromIndex) + 2


            #Replacement only to onScreen Text
            searchFromIndexOnScreen = 0
            for i in range(1, textOnScreen.count(prefix) + 1):
                tagStartIndex = textOnScreen.index(prefix, searchFromIndexOnScreen)
                tagEndIndex = textOnScreen.index(suffix, searchFromIndexOnScreen) + 2

                nestedFieldName = textOnScreen[tagStartIndex + 2 : tagEndIndex - 2]
                if nestedFieldName.lower() == fieldName.lower():
                    result["textOnScreen"] = textOnScreen.replace(textOnScreen[tagStartIndex:tagEndIndex], valueOnScreen)

                    #Treatment to set tags/timeseries as clickable on OnScreen property ONLY
                    if nestedFieldName.lower() == "tag" or nestedFieldName.lower() == "timeseries":
                        tagFormatted = f'<span class="clickable-tag" data-eid="{fieldValue}">{fieldValue}</span>'
                        result["textOnScreen"] = textOnScreen.replace(textOnScreen[tagStartIndex:tagEndIndex], tagFormatted)

                searchFromIndexOnScreen = textOnScreen.index(suffix, searchFromIndexOnScreen) + 2

        return result