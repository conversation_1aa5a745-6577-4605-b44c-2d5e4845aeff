import json
import copy
from settings.settings_class import Settings
from cognite.client import Cognite<PERSON>lient
from gql import Client
from core.graphql_client import GraphQLClient
import repositories
from typing import List, Optional
from models.notification_template_model import NotificationTemplateModel
from models.common_basic_model import RelationModel
from models.notification_onscreen_model import NotificationOnScreenCreateModel
from services.database_cache_service import DatabaseCacheService
from cognite.client import CogniteClient


class NotificationTemplateService:
    def __init__(
        self,
        cogniteClient: CogniteClient,
        gqlClient: Client,
        settings: Settings,
        graphql_client: GraphQLClient,
    ):
        self.cogniteClient = cogniteClient
        self.gqlClient = gqlClient
        self.settings = settings
        self._graphql_client = graphql_client

        self.notification_template_repository = (
            repositories.NotificationTemplateRepository(
                cogniteClient, gqlClient, settings
            )
        )
        self.notification_application_group_repository = (
            repositories.NotificationApplicationGroupRepository(
                gqlClient, settings, graphql_client, cogniteClient
            )
        )
        self.user_role_repository = repositories.UserRoleSiteRepository(
            gqlClient, settings, graphql_client, cogniteClient
        )

    def find_all_by_type(
        self,
        notification_type_ids: List[str],
        db_cache: DatabaseCacheService
    ) -> List[NotificationTemplateModel]:
        templates = self.notification_template_repository.find_by_type(
            notification_type_ids, db_cache
        )

        return templates

    def find_by_type(
        self,
        notification_type_id: str,
        reporting_site_id: str,
        source_properties_json,
        db_cache: DatabaseCacheService,
        notification_reference: Optional[NotificationOnScreenCreateModel],
    ) -> List[NotificationTemplateModel]:
        templates_from_cache = copy.deepcopy(db_cache.get("templates"))
        filtered_templates = []
        
        if notification_reference:
            notification_reference_template = notification_reference.get("template", {}).get("externalId", "")

            filtered_templates = [
                template 
                for template in templates_from_cache
                if template.externalId == notification_reference_template
            ]

        else:
            templates = [
                template 
                for template in templates_from_cache
                if template.notificationType.externalId == notification_type_id
            ]

            if reporting_site_id is not None:
                filtered_templates = [
                    template
                    for template in templates
                    if template.reportingSite and template.reportingSite.externalId == reporting_site_id or template.reportingSite is None 
                ]
            else:
                filtered_templates = [
                    template
                    for template in templates
                    if template.reportingSite is None
                ]
        
        templates_result = []
        if filtered_templates:
            valid_templates = self.conditionals_match(filtered_templates, source_properties_json)
            for template in valid_templates:
                if template.allUsers is False:
                    self.get_summarized_user_list(template, db_cache)

            templates_result.extend(valid_templates)

        return templates_result

    def find_all_extension_details_by_type(
        self,
        notification_type_ids: List[str],
    ) -> List[NotificationTemplateModel]:
        extensionTemplates = (
            self.notification_template_repository.find_extension_details_by_type(
                notification_type_ids
            )
        )

        return extensionTemplates

    def find_extension_details_by_type(
        self,
        notification_type_id: str,
        reporting_site_id: str,
        source_properties_json,
        db_cache: DatabaseCacheService,
        notification_reference: Optional[NotificationOnScreenCreateModel],
    ) -> List[NotificationTemplateModel]:
        
        templates_from_cache = db_cache.get("extensionTemplates")
        filtered_templates = []

        if notification_reference:
            notification_reference_template = notification_reference.get("template", {}).get("externalId", "")

            filtered_templates = [
                template 
                for template in templates_from_cache
                if template.externalId == notification_reference_template
            ]

        else:
            extensionTemplates = [
                template 
                for template in templates_from_cache
                if template.notificationType.externalId == notification_type_id
            ]

            if reporting_site_id is not None:
                filtered_templates = [
                    template
                    for template in extensionTemplates
                    if template.reportingSite and template.reportingSite.externalId == reporting_site_id or template.reportingSite is None 
                ]
            else:
                filtered_templates = [
                    template
                    for template in extensionTemplates
                    if template.reportingSite is None
                ]

        templates_result = []
        if filtered_templates:
            valid_templates = self.conditionals_match(filtered_templates, source_properties_json)
            for template in valid_templates:
                if template.creator not in template.summarizedUsers:
                    template.summarizedUsers.append(template.creator)

            templates_result.extend(valid_templates)

        return templates_result

    def get_summarized_user_list(
        self,
        template: NotificationTemplateModel,
        db_cache: DatabaseCacheService,
    ):
        template_users_list = []
        template_block_users_list = []
        template_external_users_list = []

        if (
            template.subscribedApplicationGroups
            and len(template.subscribedApplicationGroups) > 0
        ):
            cache_groups = db_cache.get("application_groups")

            application_groups_ids = [app.externalId for app in template.subscribedApplicationGroups]

            application_groups = [cache_groups[eid] for eid in application_groups_ids if eid in cache_groups]
            missing_groups_ids = [eid for eid in application_groups_ids if eid not in cache_groups]

            if len(missing_groups_ids) > 0:
                subscribed_groups = [
                    {"externalId": group} for group in missing_groups_ids
                ]
                application_groups.extend(
                    self.notification_application_group_repository.list_by_filter(
                        template.notificationType.application.externalId,
                        subscribed_groups,
                        "byExternalId",
                        db_cache,
                    )
                )

            # Get All Application Groups externalIds and save to NotificationEvent object
            for group in application_groups:
                if len(missing_groups_ids) > 0 and group.externalId in missing_groups_ids:
                    db_cache.cache["application_groups"][group.externalId] = group

                if group.users:
                    # Save USERS to global list
                    for user in group.users:
                        template_users_list.append(
                            RelationModel(space=user.space, externalId=user.externalId)
                        )

                if group.usersRoles:
                    # Save USERS ROLES to global list
                    for role in group.usersRoles:
                        for user in role.users:
                            template_users_list.append(
                                RelationModel(
                                    space=user.space, externalId=user.externalId
                                )
                            )

                if group.blocklist:
                    # Save BLOCKED USERS to global list
                    for user in group.blocklist:
                        template_block_users_list.append(
                            RelationModel(space=user.space, externalId=user.externalId)
                        )

                if group.blocklistRoles:
                    # Save BLOCKED USERS ROLES to global list
                    for role in group.blocklistRoles:
                        for user in role.users:
                            template_block_users_list.append(
                                RelationModel(
                                    space=user.space, externalId=user.externalId
                                )
                            )

                if group.externalUsers:
                    # Save EXTERNAL USERS to global list
                    for user in group.externalUsers:
                        template_external_users_list.append(user)

        # Get All Roles related to the Application and its Users, and save to Event and global list
        if template.subscribedRoles and len(template.subscribedRoles) > 0:

            cache_roles = db_cache.get("roles")

            roles_id = [role.externalId for role in template.subscribedRoles]

            roles = [cache_roles[eid] for eid in roles_id if eid in cache_roles]
            missing_roles_ids = [eid for eid in roles_id if eid not in cache_roles]


            if len(missing_roles_ids) > 0:
                roles.extend(
                    self.user_role_repository.list_users_by_role_app_site(
                        missing_roles_ids,
                        db_cache,
                    )
                )

            for role in roles:
                if len(missing_roles_ids) > 0 and role.externalId in missing_roles_ids:
                    db_cache.cache["roles"][role.externalId] = role
                # Save USERS to global list
                if role.usersComplements:
                    for user in role.usersComplements:
                        template_users_list.append(
                            RelationModel(space=user.space, externalId=user.externalId)
                        )

        if template.blocklistRoles and len(template.blocklistRoles) > 0:

            cache_roles = db_cache.get("roles")

            roles_id = [role.externalId for role in template.blocklistRoles]

            roles = [cache_roles[eid] for eid in roles_id if eid in cache_roles]
            missing_roles_ids = [eid for eid in roles_id if eid not in cache_roles]


            if len(missing_roles_ids) > 0:
                roles.extend(
                    self.user_role_repository.list_users_by_role_app_site(
                        missing_roles_ids,
                        db_cache,
                    )
                )

            for role in roles:
                if len(missing_roles_ids) > 0 and role.externalId in missing_roles_ids:
                    db_cache.cache["roles"][role.externalId] = role

                # Save BLOCKED USERS to global list
                if role.usersComplements:
                    for user in role.usersComplements:
                        template_block_users_list.append(
                            RelationModel(space=user.space, externalId=user.externalId)
                        )

        # Get All Users externalIds and save to Event and to global List
        if template.subscribedUsers and len(template.subscribedUsers) > 0:
            for user in template.subscribedUsers:
                user_mapped = RelationModel(
                    space=self.settings.um_instance_space,
                    externalId=user.externalId,
                )

                template_users_list.append(user_mapped)

        # Get All Blocked Users externalIds and save to global list
        if template.blocklist and len(template.blocklist) > 0:
            for user in template.blocklist:
                user_mapped = RelationModel(
                    space=self.settings.um_instance_space,
                    externalId=user.externalId,
                )

                template_block_users_list.append(user_mapped)

        # Get External Users and save to global list
        if template.externalUsers and len(template.subscribedExternalUsers) > 0:
            for user in template.subscribedExternalUsers:
                template_external_users_list.append(user)

        # IF the template does not have any users, ignore it.
        if (
            template_users_list
            and len(template_users_list) > 0
            and not template.allUsers
        ):
            # Removing duplicated users
            clean_users_list = list(
                {user.externalId: user for user in template_users_list}.values()
            )
            template_users_list = clean_users_list

        # IF the template does not have any blocked users, ignore it.
        if template_block_users_list and len(template_block_users_list) > 0:
            # Removing duplicated users
            clean_blocked_users_list = []

            for user in template_block_users_list:
                if user not in clean_blocked_users_list:
                    clean_blocked_users_list.append(user)

            template_block_users_list = clean_blocked_users_list

        # IF the template does not have any external users, ignore it.
        if template_external_users_list and len(template_external_users_list) > 0:
            # Removing duplicated users
            clean_external_users_list = []

            for user in template_external_users_list:
                if user not in clean_external_users_list:
                    clean_external_users_list.append(user)

            template_external_users_list = clean_external_users_list

        template.summarizedUsers = template_users_list
        template.summarizedBlockedUsers = template_block_users_list
        template.summarizedExternalUsers = template_external_users_list

    def conditionals_match(self, templates, source_properties_json):
        valid_templates = []
        for template in templates:
            if (
                template.conditionalExpression
                and template.conditionalExpression != "[]"
            ):
                template_conditionals = self.validateJson(
                    template.conditionalExpression
                )
                event_data = self.convertToDict(
                    source_properties_json.get("properties", [])
                )

                expression = self._build_conditional_expression(
                    template_conditionals, event_data
                )

                validation_result = eval(expression)

                if validation_result == False:
                    print("Conditionals do not match")
                else:
                    valid_templates.append(template)
            else:
                valid_templates.append(template)

        return valid_templates

    def _evaluate_single_condition(self, variable_value, operator, value):
        # Define valid operators and their corresponding operations
        operators = {
            "=": lambda x, y: x == y,
            "<": lambda x, y: x < y,
            ">": lambda x, y: x > y,
            "≠": lambda x, y: x != y,
        }

        # Get the variable value, return False if not found
        var_value = variable_value
        if var_value is None:
            return False

        # Check if operator is valid
        if operator not in operators:
            return False

        return operators[operator](var_value, value)

    def _build_conditional_expression(self, conditionals, event_data):

        expressions = ""

        for condition in conditionals:

            variable = condition["variable"]
            operator = condition["operator"]
            conjunction = condition["conjunction"]
            variable_value = event_data.get(variable)

            condition_value = (
                float(condition["value"])
                if condition["isNumeric"]
                else condition["value"]
            )

            result = self._evaluate_single_condition(
                variable_value, operator, condition_value
            )

            if expressions:
                expressions += f" {conjunction.lower()} "
            expressions += str(result)

        return expressions

    def validateJson(self, jsonObj):
        try:
            return json.loads(jsonObj)
        except ValueError as e:
            return None

    def convertToDict(self, data_list):
        return {item["name"].lower(): item["value"] for item in data_list}
