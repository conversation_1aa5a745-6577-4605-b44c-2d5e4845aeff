from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    cognite_data_set_id: str
    cognite_base_uri: str
    cognite_project: str
    cognite_client_name: str
    cognite_graphql_uri: str
    cognite_graphql_model_space: str
    assethierarchy_instance_space: str
    um_instance_space: str
    um_model_space: str
    ntf_instance_space: str
    ntf_prot_instance_space: str
    auth_scopes: str
    auth_client_id: str
    auth_tenant_id: str
    auth_secret: str
    auth_token_uri: str
    channel_email_external_id: str
    channel_sms_external_id: str
    channel_teams_external_id: str
    environment_id: str
