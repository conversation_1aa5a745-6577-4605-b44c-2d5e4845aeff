<!DOCTYPE html>
<html>
  <div style="max-width: 700px; overflow: auto">
    <body style="background-color: grey">
      <div
        style="
          display: flex;
          flex-direction: column;
          padding: 20px 20px 5px 20px;
        "
      >
        <div
          style="
            display: flex;
            justify-content: space-between;
            align-items: center;
          "
        >
          <div style="display: flex; align-items: center; column-gap: 15px">
            <div
              style="
                background-color: #083d5b;
                border-radius: 50%;
                width: 35px;
                height: 35px;
                line-height: 35px;
                overflow: hidden;
                position: relative;
                color: white;
                align-items: center;
                text-align: center;
              "
            >
              NTF
            </div>
            <div style="display: flex; flex-direction: column">
              <div style="display: flex; align-items: center">
                <div>
                  <strong style="align-items: left; font-size: 15px"
                    >Notifications</strong
                  >
                </div>
              </div>
            </div>
          </div>
          <div style="display: flex; align-items: center">
            {% if data.severity.description == 'High' %}
            <div
              style="
                width: 15px;
                height: 15px;
                border-radius: 50%;
                background-color: #ef5350;
                margin: 7px;
              "
            ></div>
            {% endif %} {% if data.severity.description == 'Medium' %}
            <div
              style="
                width: 15px;
                height: 15px;
                border-radius: 50%;
                background-color: #ff9800;
                margin: 7px;
              "
            ></div>
            {% endif %} {% if data.severity.description == 'Low' %}
            <div
              style="
                width: 15px;
                height: 15px;
                border-radius: 50%;
                background-color: #8ce3b0;
                margin: 7px;
              "
            ></div>
            {% endif %}
            <div>
              <span style="align-items: left; font-size: 12px"
                >{{data.severity.description}}</span
              >
            </div>
          </div>
        </div>
        <div>
          <span style="font-size: 11px; color: lightslategray"
            >Published {{data.publishDate}}</span
          >
        </div>
      </div>
      <hr />
      <div style="padding: 20px; display: flex; flex-direction: column">
        <div>
          <span
            style="
              display: flex;
              text-align: justify-all;
              align-items: center;
              font-size: 14px;
              margin-bottom: 10px;
            "
          >
            New chat message in notification from {{data.notificationType.application.name}}. Please go to the notifications portal to see the message.
          </span>
        </div>
        <div>
          <strong
            style="text-align: justify-all; align-items: left; font-size: 15px"
            >{{data.notificationType.name}}</strong
          >
        </div>
        <div>
          <span
            style="
              text-align: justify-all;
              align-items: center;
              font-size: 13px;
            "
          >
            {{data.text}}
          </span>
        </div>
      </div>
      <hr style="border: 1px solid #083d5b" />
    </body>
  </div>
</html>
