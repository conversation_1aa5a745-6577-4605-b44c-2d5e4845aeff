import json

class ConditionalsUtils():
        @classmethod
        def getMatchConditions(self, sourceValues: dict, conditions: dict) -> bool:
            try:
                if (sourceValues and len(sourceValues) > 0 and 
                    conditions and len(conditions) > 0):

                    conditions = json.loads(conditions)

                    evalExpr = ""
                    
                    for condition in conditions:

                        variable = str(condition["variable"]).lower()
                        sourceProperty = next((item for item in sourceValues 
                                            if str(item["name"]).lower() == variable), None)
                        if sourceProperty is None:
                            continue
                        
                        sourceValue = sourceProperty["value"]
                        sourceType = sourceProperty["type"]
                        if sourceType == "number":
                            sourceValue = float(sourceValue)
                        elif sourceType == "text":
                            sourceValue = "'" + sourceValue + "'"

                        operator = condition["operator"]
                        if operator == "=":
                            operator = "=="
                        
                        isNumeric = bool(condition["isNumeric"])
                        if isNumeric:
                            conditionValue = float(condition["value"])
                        else:
                            conditionValue = "'" + str(condition["value"]) + "'"
                    
                        conjunction = str(condition["conjunction"]).lower()

                        evalExpr += " {} {} {} {}".format(sourceValue, operator, conditionValue, conjunction)
                        
                    if evalExpr.endswith("and"):
                        evalExpr = evalExpr.rsplit('and', 1)[0]
                    elif evalExpr.endswith("or"):
                        evalExpr = evalExpr.rsplit('or', 1)[0]

                    if evalExpr == "":
                        return False

                    return eval(evalExpr)

                return False
            except:
                return None