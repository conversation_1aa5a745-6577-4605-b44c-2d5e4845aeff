class TextUtils:
    @classmethod
    def formatLink(self, htmlString: str):
        searchFromIndex = 0
        limit = len(htmlString)

        for i in range(1, htmlString.count("http") + 1):
            tagStartIndex = htmlString.index("http", searchFromIndex)
            if htmlString.count("\n", tagStartIndex):
                suffix = "\n"
                tagEndIndex = htmlString.index(suffix, tagStartIndex)
            elif htmlString.count("<br />", tagStartIndex):
                suffix = "<br />"
                tagEndIndex = htmlString.index(suffix, tagStartIndex)
            elif htmlString.count(" ", tagStartIndex):
                suffix = " "
                tagEndIndex = htmlString.index(suffix, tagStartIndex)
            else:
                tagEndIndex = limit

            nestedFieldName = htmlString[tagStartIndex : tagEndIndex]

            value = f"<a href='{nestedFieldName}' target='_blank'>{nestedFieldName}</a>"
            result = htmlString.replace(htmlString[tagStartIndex:tagEndIndex], value)
            if tagEndIndex < limit:
                searchFromIndex = htmlString.index(suffix, searchFromIndex, limit)
        
        return result