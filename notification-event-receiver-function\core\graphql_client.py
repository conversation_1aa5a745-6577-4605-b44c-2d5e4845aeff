from typing import Any, Dict, List, Optional
from gql import Client, gql
from graphql import DocumentNode
from retry import retry


class GraphQLClient:
    def __init__(self, client: Client):
        self.client = client

    def query(
        self, query: str, list_name: str, variable_values: Dict[str, Any] | None = None
    ) -> List[Any]:
        result = self._execute(gql(query), variable_values=variable_values)
        return result[list_name]["items"]

    def execute(self, query: str) -> None:
        self._execute(gql(query))

    @retry(tries=4, backoff=2, delay=1)
    def _execute(self, document: DocumentNode, variable_values: Optional[Dict[str, Any]]):
        return self.client.execute(document, variable_values=variable_values)