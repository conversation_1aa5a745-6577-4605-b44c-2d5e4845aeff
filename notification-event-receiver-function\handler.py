from settings.settings_class import Settings
from dotenv import load_dotenv
from core.cognite_client_factory import CogniteClientFactory
from core.gql_client_factory import GqlClientFactory
from core.graphql_client import GraphQLClient
import services


class NoSecretsException(Exception):
    pass


class NoSettingsException(Exception):
    pass


def handle(data):
    print("*** Starting Function execution... ***")
    
    print("*** Log payload ***")
    print(data)
    
    # load environment and create client
    try:
        # Load environment variables from .env file
        load_dotenv()
    except Exception as e:
        print("Error loading the environment. " + e)

    appId = ""
    if data["appId"]:
        appId = data["appId"]
        print("Source Client Id: " + appId)
    else:
        raise Exception(
            "The source JSON must contain the property 'appId' with the source's clientId."
        )

    # Taking array from the single element, because Cognite Function does not accept JSON array as parameter
    elementsArray = []
    if hasattr(data, "items") and getattr(data, "items"):
        elementsArray = data["items"]
    else:
        raise Exception(
            "The source JSON must contain the elements array contained in a property called 'items'."
        )

    # Create Client after initial validations
    settings = Settings()
    cogniteClient = CogniteClientFactory.create(settings)
    gqlClient = GqlClientFactory.create(cogniteClient, settings)
    graphql_client = GraphQLClient(gqlClient)

    # Create the NotificationRawEvent object and return values
    notificationRawEventService = services.NotificationRawEventService(
        cogniteClient, gqlClient, settings, graphql_client
    )

    dataRawEventArray = notificationRawEventService.save_list(appId, elementsArray)

    if not dataRawEventArray:
        print(f"No event created")
    else: 
        print(f"{len(dataRawEventArray)} event(s) created")

    print("*** Finishing Function execution... ***")

    return dataRawEventArray


# if __name__ == "__main__":

# #     # #WITH ROLE + USER
#     jsonData = {
#         "appId": "",
#         "items": [
#             {
#             "request-reference-id": "123",
#             "notificationType": "Boiller Efficiency",
#             "description": "Efficiency of these or that boillers.",
#             "applicationGroups": [
#             ],
#             "users": [
#                 "<EMAIL>",
#                 "<EMAIL>"
#             ],
#             "roles": [
#             ],
#             "severity": "High",
#             "properties": [
#                 {
#                 "name": "EquipmentName",
#                 "value": "AB001",
#                 "type": "text"
#                 },
#                 {
#                 "name": "TAG",
#                 "value": "XXXX",
#                 "type": "text"
#                 },
#                 {
#                 "name": "Efficiency",
#                 "value": 80,
#                 "type": "number"
#                 },
#                 {
#                 "name": "UOM",
#                 "value": "MMBTUD",
#                 "type": "text"
#                 },
#                 {
#                 "name": "Site",
#                 "value": "Beijing, China",
#                 "type": "text"
#                 }
#             ]
#             },
#             {
#             "request-reference-id": "234",
#             "notificationType": "Boiller Efficiency",
#             "description": "Efficiency of these or that boillers.",
#             "applicationGroups": [
#             ],
#             "users": [
#                 "<EMAIL>"
#             ],
#             "roles": [
#             ],
#             "severity": "High",
#             "properties": [
#                 {
#                 "name": "EquipmentName",
#                 "value": "AB001",
#                 "type": "text"
#                 },
#                 {
#                 "name": "TAG",
#                 "value": "XXXX",
#                 "type": "text"
#                 },
#                 {
#                 "name": "Efficiency",
#                 "value": 80,
#                 "type": "number"
#                 }
#             ]
#             }
#         ]
#     }

# jsonData = {
#     "appId": "9a42fc66-6786-408f-b1e6-4cbb1070f285",
#     "items": [
#         {
#             "notificationType": "CKM-Test-ExternalUsers",
#             "description": "Notification type to test external users feature",
#             "users":[
#                 "<EMAIL>",
#                 "<EMAIL>"
#             ],
#             "externalUsers": [
#                 "<EMAIL>",
#                 "<EMAIL>",
#             ],
#             "properties": [
#                 {"value": "test_val", "name": "testName", "type": "text"}
#             ],
#         }
#     ],
# }

# handle(data=jsonData)

