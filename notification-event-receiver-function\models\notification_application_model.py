from typing import Any, Optional
from pydantic import BaseModel

class NotificationApplicationModel(BaseModel):
    externalId: str
    description: str
    name: str
    space: str
    iconUrl: Optional[str] = None

    def mapFromResult(item: Any):
        return NotificationApplicationModel(
            name=item.get("name", ""),
            description=item.get("description", "") if item.get("description") else "",
            externalId=item.get("externalId", ""),
            space=item.get("space", ""),
            iconUrl=item.get("iconUrl", None)
        )
