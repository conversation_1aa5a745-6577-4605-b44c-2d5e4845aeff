from pydantic import BaseModel
from typing import Any, List, Optional, Dict
import models as models


class ValidateNotificationRawEventModel(BaseModel):
    application: str
    notificationType: str
    description: str
    severity: Optional[str] = None
    roles: Optional[List[str]] = []
    applicationGroups: Optional[List[str]] = []
    users: Optional[List[str]] = []
    properties: List[Dict[str, Any]]