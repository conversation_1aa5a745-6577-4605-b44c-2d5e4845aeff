from gql import Client
import queries.notification_application_queries as queries
from settings.settings_class import Settings
from core.graphql_client import GraphQLClient

class NotificationApplicationRepository:
    def __init__(
        self,
        gqlClient: Client,
        settings: Settings,
        graphql_client: GraphQLClient,
    ):
        self.gqlClient = gqlClient
        self.settings = settings
        self._graphql_client = graphql_client


    def find_external_ids(self, filter):
        return self._graphql_client.query(
            queries.APPLICATION_EXTERNAL_ID_LIST, "listApplication", filter
        )
