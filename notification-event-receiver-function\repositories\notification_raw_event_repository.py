import json
from uuid import uuid4
from gql import Client
from settings.settings_class import Settings
from cognite.client import CogniteClient
from cognite.client.data_classes.data_modeling import (
    NodeApply,
    NodeOrEdgeData,
    ViewId
)
import utils as Utils
import models as models

ENTITY = "NotificationRawEvent"
ENTITY_RAW_EVENT_LOG = "NotificationRawEventLog"

class NotificationRawEventRepository:
    def __init__(
        self, cogniteClient: CogniteClient, gqlClient: Client, settings: Settings
    ):
        self.cogniteClient = cogniteClient
        self.gqlClient = gqlClient
        self.settings = settings

    def save_raw_event(self, request):
        try:
            # GET ENTITY VIEW
            view = Utils.cognite.getView(
                self.cogniteClient, self.settings.cognite_graphql_model_space, ENTITY
            )
            request["sourceJson"] = json.loads(request["sourceJson"])
            externalId = Utils.generate_external_id('NTFRAWEVT')
            rawEventNode = NodeApply(
                self.settings.ntf_prot_instance_space,
                externalId,
                sources=[
                    NodeOrEdgeData(
                        ViewId(self.settings.cognite_graphql_model_space, ENTITY, view.version),
                        request
                    )
                ]
            )

            viewEventLog = Utils.cognite.getView(
                self.cogniteClient, self.settings.cognite_graphql_model_space, ENTITY_RAW_EVENT_LOG
            )
            logData = {}
            logData["rawEvent"] = {"externalId": externalId, "space": self.settings.ntf_prot_instance_space}
            externalIdLog = Utils.generate_external_id('NTFRAWEVTLOG')
            rawEventLogNode = NodeApply(
                self.settings.ntf_instance_space,
                externalIdLog,
                sources=[
                    NodeOrEdgeData(
                        ViewId(
                            self.settings.cognite_graphql_model_space,
                            ENTITY_RAW_EVENT_LOG,
                            viewEventLog.version
                        ),
                        logData
                    )
                ]
            )

            self.cogniteClient.data_modeling.instances.apply(
                [rawEventNode, rawEventLogNode],
                replace=False
            )

            request["status"] = "created"
            request["externalId"] = externalId

        except Exception as e:
            request["status"] = "failed"
            request["detail"] = str(e)
            request["externalId"] = None

        return request
