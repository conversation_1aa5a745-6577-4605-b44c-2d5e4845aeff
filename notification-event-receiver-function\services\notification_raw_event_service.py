import json
from typing import Any, List
from gql import Client
from settings.settings_class import Settings
from cognite.client import CogniteClient
from models import NotificationRawEventModel
import repositories
from core.graphql_client import GraphQLClient


class NotificationRawEventService:
    def __init__(
        self,
        cogniteClient: CogniteClient,
        gqlClient: Client,
        settings: Settings,
        graphql_client: GraphQLClient
    ):
        self.cogniteClient = cogniteClient
        self.gqlClient = gqlClient
        self.settings = settings
        self._graphql_client = graphql_client

        self.notification_application_repository = (
            repositories.NotificationApplicationRepository(
                gqlClient, settings, graphql_client
            )
        )
        self.notification_raw_event_repository = repositories.NotificationRawEventRepository(
            cogniteClient, gqlClient, settings
        )

    def save_list(
        self, app_id: str, notification_events: List[NotificationRawEventModel]
    ):
        results = []
        application_id = self.find_application(app_id)
        if application_id is None:
            raise Exception(f"Application {app_id} not found")

        for notification_event in notification_events:
            try:
                result = self.save(application_id, notification_event)
                results.append(result)
            except Exception as e:
                notification_event["status"] = "failed"
                notification_event["detail"] = str(e)
                notification_event["externalId"] = None
                results.append(notification_event)
        return results

    def save(
        self,
        application_id: str,
        notification_event: NotificationRawEventModel,
    ):
        reportingSiteId = next((item['value'] for item in notification_event['properties'] if item['name'] == 'site'), None)
        raw_event = {
            "sourceApplication": {
                "externalId": application_id,
                "space": self.settings.um_instance_space,
            },
            "sourceJson": json.dumps(notification_event),
            "reportingSite": {
                "externalId": reportingSiteId,
                "space": self.settings.assethierarchy_instance_space
            } if reportingSiteId else None
        }
        return self.notification_raw_event_repository.save_raw_event(raw_event)

    def find_application(self, azureAppId: str):
        filterApplication = {}
        filterApplication["filter"] = {
            "and": [
                {"azureAppId": {"eq": azureAppId}},
                {"space": {"eq": self.settings.um_instance_space}},
            ]
        }
        application_externalId = self.notification_application_repository.find_external_ids(
            filterApplication
        )
        if len(application_externalId) >= 1:
            return application_externalId[0]["externalId"]
        
        return None

    
    