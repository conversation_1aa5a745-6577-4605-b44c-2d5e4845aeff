from cognite.client import Client<PERSON>onfig, CogniteClient
from cognite.client.credentials import Cred<PERSON><PERSON>rovider, OAuthClientCredentials, Token

from core.env_variables import EnvVariables


class CogniteClientFactory:
    @staticmethod
    def _create_credentials(env_variables: EnvVariables) -> CredentialProvider:
        auth_variables = env_variables.auth

        if (
            auth_variables.token_override
        ):
            return Token(lambda: auth_variables.token_override)
        
        return OAuthClientCredentials(
            token_url=auth_variables.token_uri,
            client_id=auth_variables.client_id,
            client_secret=auth_variables.secret,
            scopes=auth_variables.scopes,
        )

    @staticmethod
    def _create_client_config(env_variables: EnvVariables) -> ClientConfig:
        cognite_variables = env_variables.cognite
        return ClientConfig(
            client_name=cognite_variables.client_name,
            project=env_variables.cognite.project,
            credentials=CogniteClientFactory._create_credentials(env_variables),
            base_url=cognite_variables.base_uri,
        )

    @staticmethod
    def create(
        env_variables: EnvVariables,
    ) -> CogniteClient:
        return CogniteClient(
            config=CogniteClientFactory._create_client_config(env_variables)
        )
