import os
from pathlib import Path
from tempfile import TemporaryDirectory
from typing import cast
from zipfile import ZipFile
from cognite.client import CogniteClient

def _sanitize_filename(filename: str) -> str:
    # Forwardslash, '/', is not allowed in file names:
    return filename.replace("/", "-")

def zip_and_upload_folder(cognite_client: CogniteClient, data_set_id: int, folder: str, name: str, external_id: str | None = None) -> int:
        name = _sanitize_filename(name)
        current_dir = os.getcwd()
        os.chdir(folder)
        try:
            with TemporaryDirectory() as tmpdir:
                zip_path = Path(tmpdir, "function.zip")
                with ZipFile(zip_path, "w") as zf:
                    for root, dirs, files in os.walk("."):
                        zf.write(root)

                        for filename in files:
                            zf.write(Path(root, filename))

                overwrite = True if external_id else False
                file = cognite_client.files.upload_bytes(
                    zip_path.read_bytes(), name=f"{name}.zip", external_id=external_id, overwrite=overwrite, data_set_id=data_set_id
                )
                return cast(int, file.id)
        finally:
            os.chdir(current_dir)