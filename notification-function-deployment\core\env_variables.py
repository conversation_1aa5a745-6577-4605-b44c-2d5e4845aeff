import os
from typing import List, Optional

from pydantic import Field
from pydantic_settings import BaseSettings, SettingsConfigDict

from core.models import CogniteProjects

python_env = os.getenv("PYTHON_ENV") or "dev"


class AuthVariables(BaseSettings):
    model_config = SettingsConfigDict(
        env_file=(".env", ".env." + python_env), env_prefix="auth_", extra="ignore"
    )

    client_id: str = Field(alias="function_auth_client_id")
    tenant_id: str
    secret: str = Field(alias="function_auth_secret")
    scopes_str: str = Field(alias="auth_scopes")
    token_uri: str
    token_override: str = ""

    @property
    def scopes(self) -> List[str]:
        return self.scopes_str.split(" ")


class CogniteVariables(BaseSettings):
    model_config = SettingsConfigDict(
        env_file=(".env", ".env." + python_env), env_prefix="cognite_", extra="ignore"
    )
    base_uri: str
    client_name: str = Field(alias="function_cognite_client_name")
    data_set_id: int
    project: CogniteProjects


class EnvVariables:
    def __init__(self) -> None:
        self.auth = AuthVariables()  # type: ignore
        self.cognite = CogniteVariables()  # type: ignore
