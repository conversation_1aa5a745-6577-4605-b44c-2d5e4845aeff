[{"functionName": "ntf-event-processor", "path": ["notification-event-processor-function"], "runtime": "py310", "description": "Function used by Notification Solution. Receives events from Notification API, transform them, and generate notifications to be sent later.", "parameters": [{"scheduleName": "ntf-event-processor-schedule", "cronn": "* * * * *"}]}, {"functionName": "ntf-message-dispatcher", "path": ["notification-message-dispatcher-function"], "runtime": "py310", "description": "Function used by Notification solution. Reads messages generated by ntf-event-processor, and delivery them to the users.", "parameters": [{"scheduleName": "ntf-message-dispatcher-schedule", "cronn": "* * * * *"}]}, {"functionName": "ntf-event-receiver", "path": ["notification-event-receiver-function"], "runtime": "py310", "description": "Function used to receive Event Messages from functions from other Apps, due to restrictions on call the Notifications API. Expected to be replaced by an Azure Function;", "parameters": []}, {"functionName": "ntf-infield-monitor", "path": ["notification-infield-monitor-function"], "runtime": "py310", "description": "Function implemented to monitor and retrieve new instances from specific types on 'ApmAppData' data model, based on time trigger.", "parameters": [{"scheduleName": "ntf-infield-monitor-schedule", "cronn": "*/5 * * * *"}]}]