import os
from dotenv import load_dotenv

common_variables = [
    "COGNITE_BASE_URI", 
    "COGNITE_PROJECT", 
    "COGNITE_CLIENT_NAME", 
    "COGNITE_GRAPHQL_URI", 
    "COGNITE_GRAPHQL_MODEL_SPACE", 
    "COGNITE_DATA_SET_ID", 
    "AUTH_CLIENT_ID", 
    "AUTH_TENANT_ID", 
    "AUTH_SECRET", 
    "AUTH_SCOPES",  
    "AUTH_TOKEN_URI", 
    "UM_INSTANCE_SPACE",
    "NTF_INSTANCE_SPACE",
    "NTF_PROT_INSTANCE_SPACE",
    "CHANNEL_EMAIL_EXTERNAL_ID", 
    "CHANNEL_SMS_EXTERNAL_ID", 
    "CHANNEL_TEAMS_EXTERNAL_ID"
]

functions_variables = {
    "ntf-event-processor": ["ASSETHIERARCHY_INSTANCE_SPACE", "ENVIRONMENT_ID", "UM_MODEL_SPACE"],
    "ntf-message-dispatcher": [ 
                               "NEXT_MINUTES_TO_CONSIDER_PENDING", 
                               "PREVIOUS_MINUTES_TO_CONSIDER_PENDING",
                               "LOGIC_APP_URL",
                               "SMS_CONNECTION_URL",
                               "SMS_FROM_NUMBER"],
    "ntf-event-receiver": ["ASSETHIERARCHY_INSTANCE_SPACE"],
    "ntf-infield-monitor":["COGNITE_GRAPHQL_APMAPPDATA_URI",
                            "APMAPPDATA_DATAMODEL_VERSION",
                            "NEXT_MINUTES_TO_CONSIDER_PENDING", 
                            "PREVIOUS_MINUTES_TO_CONSIDER_PENDING"]
}


def load_variables(function_name: str, function_location: str):
    load_dotenv()
    if function_name not in functions_variables:
        raise Exception(f"No configuration was found for function {function_name}.")
    
    processed_variables = {}
    function_variables: list[str] = functions_variables[function_name]

    for var in common_variables + function_variables:
        value = os.getenv(var)
        if value is None:
            raise Exception(
                f"Key {var} not present in the Environment Variables")
        processed_variables[var] = value

    return processed_variables