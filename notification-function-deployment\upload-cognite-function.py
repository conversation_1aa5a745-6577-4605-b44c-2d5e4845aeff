import logging
import sys
from cognite.client import CogniteClient
import time
import os
from typing import List
from core.settings import load_variables
from core.env_variables import EnvVariables
from core.FunctionConfiguration import FunctionConfiguration
from core.cognite_client_factory import CogniteClientFactory
from pydantic import TypeAdapter
from core.cognite_utils import zip_and_upload_folder

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

current_path = os.path.dirname(__file__)

logger.info("Loading environment variables")
env_variables = EnvVariables()

logger.info("Creating Cognite Client")
client: CogniteClient = CogniteClientFactory.create(
    env_variables=env_variables
)

def status_check(function):
    logger.info("Verifying function deployment")
    start_time = time.time()
    # Repeat until status is ready
    while function.status != "Ready":
        function.update()
        time_elapsed = int(time.time() - start_time)

        print(function.status +
              f". Waiting for {time_elapsed} seconds", end="\r")
        
        if function.status == "Failed":
            print("Failed to deploy function")
            raise Exception("Failed to deploy function")
        time.sleep(5)
    else:
        print(
            f"Function is successfully deployed. Wait time: {time_elapsed} seconds.")
        logger.info(f"Function is successfully deployed. Wait time: {time_elapsed} seconds.")


def main():

    logger.info("Checking Function Name")
    argument_list = sys.argv
    if len(argument_list) > 2:
        raise Exception("Only the function name is accepted as parameter.")
    if len(argument_list) == 1:
        raise Exception("The function name must be provided.")
    
    function_name: str = argument_list[1]
    
    logger.info(f"Starting deployment for function {function_name}")

    logger.info("Finding function configuration")
    functions_str = open(os.path.join(current_path, "core", "functions-configurations.json"))
    type_adapter = TypeAdapter(List[FunctionConfiguration])
    functions = type_adapter.validate_json(functions_str.read())

    func_data: FunctionConfiguration = next(filter(lambda func: func.function_name == function_name, functions), None)
    
    if func_data is None:
        raise Exception(f"The function '{function_name}' was not found on 'functions-configurations.json' file.")
    
    logger.info("Checking functions variables")
    function_location = os.path.join(current_path, "..", *func_data.path)
    env_vars = load_variables(func_data.function_name, function_location)
    processed_function_name = function_name.replace("notification", "ntf")

    try:
        logger.info("Deleting existent function")
        client.functions.delete(external_id=processed_function_name)
        logger.info("Existing function deleted")
    except Exception as e:
        logger.info(f"Function {function_name} doens't exist. Nothing was deleted.")
        print(e)


    logger.info(f"Loading function files from path {function_location} and uploading to Cognite as a File")
    file_id = zip_and_upload_folder(cognite_client=client, 
                                    data_set_id=env_variables.cognite.data_set_id, 
                                    folder=function_location, 
                                    name=processed_function_name, 
                                    external_id=processed_function_name)

    logger.info("Creating Function")
    func = client.functions.create(
        name=processed_function_name,
        external_id=processed_function_name,
        file_id=file_id,
        runtime=func_data.runtime,
        env_vars=env_vars,
        description=func_data.description
    )

    for s in func_data.parameters:
        logger.info(f"Creating schedule {s.schedule_name}")
        # logger.info(env_vars["AUTH_CLIENT_ID"])
        # logger.info(env_vars["AUTH_SECRET"])

        client.functions.schedules.create(
            name=s.schedule_name,
            cron_expression=s.cronn,
            client_credentials={
                "client_id": env_vars["AUTH_CLIENT_ID"],
                "client_secret": env_vars["AUTH_SECRET"],
            },
            function_id=func.id,
            data=s.data
        )

    logger.info("Function created")
    status_check(func)

main()