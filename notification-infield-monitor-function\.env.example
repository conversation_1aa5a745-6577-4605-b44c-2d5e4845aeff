NEXT_PUBLIC_COGNITE_APP_ID=ntf-infield-reader

FUNCTION_NAME=ntf-infield-reader
COGNITE_DATA_SET_ID=2536370642543459
COGNITE_BASE_URI=https://az-eastus-1.cognitedata.com
COGNITE_PROJECT=celanese-dev
COGNITE_CLIENT_NAME=notifications-infield-reader-client
COGNITE_GRAPHQL_URI=https://az-eastus-1.cognitedata.com/api/v1/projects/celanese-dev/userapis/spaces/NTF-COR-ALL-DMD/datamodels/NotificationDOM/versions/1_7_1/graphql
COGNITE_GRAPHQL_APMAPPDATA_URI=https://az-eastus-1.cognitedata.com/api/v1/projects/celanese-dev/userapis/spaces/cdf_apm/datamodels/ApmAppData/versions/v7/graphql
COGNITE_GRAPHQL_MODEL_SPACE=NTF-COR-ALL-DMD

UM_INSTANCE_SPACE=UMG-COR-ALL-DAT
NTF_INSTANCE_SPACE=NTF-COR-ALL-DAT
NTF_PROT_INSTANCE_SPACE=NTF-COR-ALL-PROT

AUTH_SCOPES=https://az-eastus-1.cognitedata.com/.default
AUTH_CLIENT_ID=7133fbd0-29a5-4567-b1cf-bb5e0e0b40b1
AUTH_TENANT_ID=7a3c88ff-a5f6-449d-ac6d-e8e3aa508e37
AUTH_SECRET=<NTF_SECRET> // ask
AUTH_TOKEN_URI=https://login.microsoftonline.com/7a3c88ff-a5f6-449d-ac6d-e8e3aa508e37/oauth2/v2.0/token

NEXT_MINUTES_TO_CONSIDER_PENDING=5
PREVIOUS_MINUTES_TO_CONSIDER_PENDING=120