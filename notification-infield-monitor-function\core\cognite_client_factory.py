from cognite.client import ClientConfig, CogniteClient
from cognite.client.credentials import OAuthClientCredentials, Token
from settings.settings_class import Settings

class CogniteClientFactory:
    def _create_credentials(env_variables: Settings) -> OAuthClientCredentials:
        return OAuthClientCredentials(
            token_url=env_variables.auth_token_uri,
            client_id=env_variables.auth_client_id,
            client_secret=env_variables.auth_secret,
            scopes=env_variables.auth_scopes,
        )

    def _create_client_config(env_variables: Settings) -> ClientConfig:
        return ClientConfig(
            client_name=env_variables.cognite_client_name,
            project=env_variables.cognite_project,
            credentials=CogniteClientFactory._create_credentials(env_variables),
            base_url=env_variables.cognite_base_uri,
        )

    def create(env_variables: Settings) -> CogniteClient:
        return CogniteClient(
            config=CogniteClientFactory._create_client_config(env_variables)
        )
