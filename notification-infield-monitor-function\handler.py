import json
from typing import Any, List
from settings.settings_class import Settings
from dotenv import load_dotenv
from core.cognite_client_factory import CogniteClientFactory
from core.gql_client_factory import GqlClientFactory
from core.graphql_client import GraphQLClient
import services
from models.notification_raw_event_model import NotificationRawEventModel

class NoSecretsException(Exception):
    pass

class NoSettingsException(Exception):
    pass

def handle():
    print("*** Starting Function execution... ***")
    # load environment and create client
    try:
        # Load environment variables from .env file
        load_dotenv()
    except Exception as e:
        print("Error loading the environment. " + e)

    try:
        settings = Settings()
        cogniteClient = CogniteClientFactory.create(settings)
        gqlClient_ntf = GqlClientFactory.create_ntf(cogniteClient, settings)
        gqlClient_apm = GqlClientFactory.create_apm(cogniteClient, settings)
        graphql_client_ntf = GraphQLClient(gqlClient_ntf)
        graphql_client_apm = GraphQLClient(gqlClient_apm)

        observationService = services.ObservationService(
            cogniteClient, gqlClient_apm, settings, graphql_client_apm
        )

        notificationRawEventService = services.NotificationRawEventService(
            cogniteClient, gqlClient_ntf, settings, graphql_client_ntf
        )

        observationsNotified:bool = False

        # Get last observations filtered by Minutes Interval (from CreatedDate)
        print('*** Retrieving latest created Observations... ***')
        observations = observationService.list_by_period()
        observationsExternalIds = [observation.externalId
                                    for observation in observations]

        if observations and len(observations) > 0:
            
            # Get all already processed Observations by their Ids
            print("*** Identifying pending Observations to notify... ***")
            processedRawEvents = notificationRawEventService.list_processed_by_sourceIds(observationsExternalIds)
            processedRawEventsIds = [processedEvent.sourceExternalId for processedEvent in processedRawEvents]

            # Check for already notified Observations
            pendingObservations = [observation
                                    for observation in observations 
                                    if observation.externalId not in processedRawEventsIds]

            if pendingObservations:
                rawEvents = []

                for observation in pendingObservations:      
                    properties = [
                        {"name":"sourceId", "value":observation.sourceId, "type":"text"}, 
                        {"name":"source", "value":observation.source, "type":"text"}, 
                        {"name":"title", "value":observation.title, "type":"text"}, 
                        {"name":"description", "value":observation.description, "type":"text"}, 
                        {"name":"labels", "value":observation.labels, "type":"text"}, 
                        {"name":"visibility", "value":observation.visibility, "type":"text"}, 
                        {"name":"createdBy", "value":observation.createdBy.email, "type":"text"}, 
                        {"name":"updatedBy", "value":observation.updatedBy.email, "type":"text"}, 
                        {"name":"isArchived", "value":observation.isArchived, "type":"text"}, 
                        {"name":"status", "value":observation.status, "type":"text"}, 
                        {"name":"assetExternalId", "value":observation.asset, "type":"text"}, 
                        {"name":"rootLocationExternalId", "value":observation.rootLocation, "type":"text"}, 
                        {"name":"troubleshooting", "value":observation.troubleshooting, "type":"text"}, 
                        {"name":"priority", "value":observation.priority, "type":"text"}, 
                        {"name":"type", "value":observation.type, "type":"text"}
                    ]

                    rawEvents.append(NotificationRawEventModel(
                        notificationType="Observation Creation",
                        description="Type to represent notifications sent from Observations created on Infield.",
                        properties=properties,
                        sourceExternalId=observation.externalId
                    ))

                dataRawEventArray = notificationRawEventService.save_list("APP-INF", rawEvents)
                if dataRawEventArray and len(dataRawEventArray) > 0:
                    print(f"{len(dataRawEventArray)} event(s) created")
                    observationsNotified = True

        if not observationsNotified:
            print("*** No new Observations to notify. ***")
            
        print("*** Finishing Function execution... ***")

    except Exception as e:
        print("Error: " + str(e))
        raise Exception(str(e))

# if __name__ == "__main__":
#     handle()