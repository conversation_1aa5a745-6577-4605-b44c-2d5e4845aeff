from typing import Any, Dict, List, Optional
from pydantic import BaseModel

class NotificationRawEventModel(BaseModel):
    notificationType: str
    description: str
    severity: Optional[str] = None
    roles: Optional[List[str]] = []
    applicationGroups: Optional[List[str]] = []
    users: Optional[List[str]] = []
    properties: List[Dict[str, Any]]
    
    # NotificationRawEventLog
    sourceExternalId: Optional[str] = None