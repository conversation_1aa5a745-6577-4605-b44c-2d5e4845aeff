from typing import Any, List, Optional
from pydantic import BaseModel
from models.observation_user_model import ObservationUserModel


class ObservationModel(BaseModel):
    externalId: str
    sourceId: Optional[str] = None
    source: Optional[str] = None
    title: Optional[str] = None
    description: Optional[str] = None
    labels: Optional[str] = None
    visibility: Optional[str] = None
    createdBy: Optional[ObservationUserModel] = None
    updatedBy: Optional[ObservationUserModel] = None
    isArchived: Optional[bool] = False
    status: Optional[str] = None
    asset: Optional[str] = None
    rootLocation: Optional[str] = None
    troubleshooting: Optional[str] = None
    priority: Optional[str] = None
    type: Optional[str] = None

    def mapFromResult(item: Any):
        return ObservationModel(
            externalId=item.get("externalId", "") if item.get("externalId") else "",
            sourceId=item.get("sourceId", "") if item.get("sourceId") else "",
            source=item.get("source", "") if item.get("source") else "",
            title=item.get("title", "") if item.get("title") else "",
            description=item.get("description", "") if item.get("description") else "",
            labels=",".join(str(element) for element in item.get("labels")) if item.get("labels") else "",
            visibility=item.get("visibility", "") if item.get("visibility") else "",
            createdBy=ObservationUserModel.mapFromResult(item.get("createdBy")) if item.get("createdBy") else None,
            updatedBy=ObservationUserModel.mapFromResult(item.get("updatedBy")) if item.get("updatedBy") else None,
            isArchived=item.get("isArchived", None) if item.get("isArchived") else None,
            status=item.get("status", "") if item.get("status") else "",
            asset=item.get("asset", "").get("externalId") if item.get("asset", None) else "",
            rootLocation=item.get("rootLocation", "").get("externalId") if item.get("rootLocation", None) else "",
            troubleshooting=item.get("troubleshooting", "") if item.get("troubleshooting") else "",
            priority=item.get("priority", "") if item.get("priority") else "",
            type=item.get("type", "") if item.get("type") else "",
        )