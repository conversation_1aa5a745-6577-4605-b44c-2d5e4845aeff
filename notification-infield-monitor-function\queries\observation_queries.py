OBSERVATION_FIND_PENDING_LIST = """
    query listObservation($filter: _ListObservationFilter, $first: Int = 100){
        listObservation(filter: $filter, first: $first){
            items{
                externalId
                source
                title
                description
                labels
                visibility
                createdBy{
                    email
                    name
                    externalId
                }
                updatedBy{
                    email
                    name
                    externalId
                }
                isArchived
                status
                asset{
                    externalId
                }
                rootLocation{
                    externalId
                }
                troubleshooting
                priority
                type
                createdTime
                sourceId
            }
        }
    }
"""