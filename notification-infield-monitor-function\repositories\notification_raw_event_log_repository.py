from typing import List
from gql import gql, Client
from settings.settings_class import Settings
from cognite.client import CogniteClient
import models as models
import queries.notification_raw_event_log_queries as queries

class NotificationRawEventLogRepository:
    def __init__(
        self, cogniteClient: CogniteClient, gqlClient: Client, settings: Settings
    ):
        self.cogniteClient = cogniteClient
        self.gqlClient = gqlClient
        self.settings = settings


    def list_processed_by_sourceIds(self, sourceExternalIds:List[str]):
        filter = {}
        filter["filter"] = {
            "and": [
                {"space": {"eq": self.settings.ntf_instance_space}},
                {"sourceExternalId": {"in": sourceExternalIds }},
                {"rawEvent": {"externalId": {"isNull": "false"} }}
            ]
        }

        result = self.gqlClient.execute(
            gql(queries.NOTIFICATION_RAW_EVENT_LOG_LIST), filter
        )

        if len(result["listNotificationRawEventLog"]["items"]) > 0:
            return result["listNotificationRawEventLog"]["items"]

        return []