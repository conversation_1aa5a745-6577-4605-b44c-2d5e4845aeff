from datetime import datetime, timedelta
from typing import Any, List
from gql import Client, gql
import pytz
import queries.observation_queries as queries
from models.observation_model import ObservationModel
from settings.settings_class import Settings
from cognite.client import CogniteClient
# from cognite.client.data_classes.data_modeling import NodeApply, NodeOrEdgeData, ViewId
# import utils as Utils

ENTITY = "Observation"

class ObservationRepository:

    def __init__(
        self,
        gqlClient: Client,
        settings: Settings
    ):
        self.gqlClient = gqlClient
        self.settings = settings
        
    
    def list_new_by_period(self, next_minutes: int = 5, previous_minutes: int = 5, 
                           status: List[str]=["Completed", "Sent", "Failed"]) -> Any:

        now = datetime.now(pytz.utc)
        minutes_ago = now - timedelta(minutes=previous_minutes)
        minutes_later = now + timedelta(minutes=next_minutes)

        fmt = "%Y-%m-%dT%H:%M:%S"
        formatted_minutes_ago = minutes_ago.strftime(fmt)
        formatted_minutes_later = minutes_later.strftime(fmt)

        filter = {}
        filter["filter"] = {
            "and": [
                {"lastUpdatedTime": {"lt": formatted_minutes_later}},
                {"lastUpdatedTime": {"gte": formatted_minutes_ago}},
                {"status": {"in": status}}
            ]
        }

        filter["first"] = 200

        result = self.gqlClient.execute(
            gql(queries.OBSERVATION_FIND_PENDING_LIST),
            filter)
        
        if (len(result["listObservation"]["items"]) > 0):
            return result["listObservation"]["items"]
        
        return []