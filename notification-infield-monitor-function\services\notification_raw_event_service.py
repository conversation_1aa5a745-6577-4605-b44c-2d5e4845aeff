import json
from typing import Any, List
from gql import Client
from settings.settings_class import Settings
from cognite.client import CogniteClient
from models import NotificationRawEventModel, NotificationRawEventLogModel
import repositories
from core.graphql_client import GraphQLClient


class NotificationRawEventService:
    def __init__(
        self,
        cogniteClient: CogniteClient,
        gqlClient: Client,
        settings: Settings,
        graphql_client: GraphQLClient
    ):
        self.cogniteClient = cogniteClient
        self.gqlClient = gqlClient
        self.settings = settings
        self._graphql_client = graphql_client

        self.notification_application_repository = (
            repositories.NotificationApplicationRepository(
                gqlClient, settings, graphql_client
            )
        )
        self.notification_raw_event_repository = repositories.NotificationRawEventRepository(
            cogniteClient, gqlClient, settings
        )

        self.notification_raw_event_log_repository = repositories.NotificationRawEventLogRepository(
            cogniteClient, gqlClient, settings
        )

    def save_list(
        self, appExternalId: str, notification_events: List[NotificationRawEventModel]
    ):
        results = []
        application_id = self.find_application(appExternalId)
        if application_id is None:
            raise Exception(f"Application {appExternalId} not found")

        for notification_event in notification_events:
            try:
                result = self.save(application_id, notification_event)
                results.append(result)
            except Exception as e:
                notification_event["status"] = "failed"
                notification_event["detail"] = str(e)
                notification_event["externalId"] = None
                results.append(notification_event)
        return results

    def save(
        self,
        application_id: str,
        notification_event: NotificationRawEventModel
    ):
        sourceExternalId = notification_event.sourceExternalId
        del notification_event.sourceExternalId
        raw_event = {
            "sourceApplication": {
                "externalId": application_id,
                "space": self.settings.um_instance_space
            },
            "sourceJson": json.dumps(vars(notification_event))
        }

        return self.notification_raw_event_repository.save_raw_event(
            raw_event, sourceExternalId)

    def find_application(self, externalId: str):
        filterApplication = {}
        filterApplication["filter"] = {
            "and": [
                {"externalId": {"eq": externalId}},
                {"space": {"eq": self.settings.um_instance_space}},
            ]
        }
        application_externalId = self.notification_application_repository.find_external_ids(
            filterApplication
        )
        if len(application_externalId) >= 1:
            return application_externalId[0]["externalId"]
        
        return None

    def list_processed_by_sourceIds(self, sourceExternalIds:List[str]) -> List[NotificationRawEventLogModel]:
        items = self.notification_raw_event_log_repository.list_processed_by_sourceIds(sourceExternalIds)

        if items and len(items) > 0:
            events = [
                NotificationRawEventLogModel.mapFromResult(item)
                for item in items
                if item
            ]
            return events
        
        return []

    