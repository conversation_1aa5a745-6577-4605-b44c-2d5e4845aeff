import json
from typing import Any, List
from gql import Client
from settings.settings_class import Settings
from cognite.client import CogniteClient
import repositories
from core.graphql_client import GraphQLClient
from models.observation_model import ObservationModel

class ObservationService:
    def __init__(
        self,
        cogniteClient: CogniteClient,
        gqlClient: Client,
        settings: Settings,
        graphql_client: GraphQLClient
    ):
        self.cogniteClient = cogniteClient
        self.gqlClient = gqlClient
        self.settings = settings
        self._graphql_client = graphql_client

        self.observation_repository = (
            repositories.ObservationRepository(gqlClient, settings)
        )

    def list_by_period(self) -> List[ObservationModel]:
        items = self.observation_repository.list_new_by_period(
            self.settings.next_minutes_to_consider_pending,
            self.settings.previous_minutes_to_consider_pending
        )
        if items and len(items) > 0:
            observations = [
                ObservationModel.mapFromResult(item)
                for item in items
                if item
            ]
            return observations
        
        return []
        