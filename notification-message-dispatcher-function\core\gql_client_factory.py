from cognite.client import Cognite<PERSON>lient
from gql import Client
from gql.transport.aiohttp import AIOHTTPTransport
from settings.settings_class import Settings

class GqlClientFactory:
    def create(cognite_client: CogniteClient, env_variables: Settings) -> Client:
        key, value = cognite_client.config.credentials.authorization_header()
        headers = {key: value}

        transport = AIOHTTPTransport(
            url=env_variables.cognite_graphql_uri,
            headers=headers,
        )
        return Client(transport=transport, fetch_schema_from_transport=True)