from settings.settings_class import Settings
from dotenv import load_dotenv
from core.cognite_client_factory import CogniteClientFactory
from core.gql_client_factory import GqlClientFactory
import services


class NoSecretsException(Exception):
    pass


class NoSettingsException(Exception):
    pass


def handle():
    print("Init Processing")

    try:
        load_dotenv()
        settings = Settings()
        cogniteClient = CogniteClientFactory.create(settings)
        gqlClient = GqlClientFactory.create(cogniteClient, settings)

        notification_event_service = services.NotificationEventService(
            cogniteClient, gqlClient, settings
        )

        notification_event_service.start()

    except Exception as e:
        print("Error: " + str(e))
        raise Exception(str(e))


# if __name__ == "__main__":
#     handle()
