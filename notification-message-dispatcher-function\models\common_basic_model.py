from typing import Any
from pydantic import BaseModel


class SingleBasicModel(BaseModel):
    externalId: str


class CommonBasicModel(BaseModel):
    externalId: str
    name: str
    description: str
    space: str

    def mapFromResult(item: Any):
        return CommonBasicModel(
            name=item.get("name", ""),
            description=item.get("description", ""),
            externalId=item.get("externalId", ""),
            space=item.get("space", ""),
        )


class RelationModel(BaseModel):
    space: str
    externalId: str

    def mapFromResult(item: Any):
        return RelationModel(
            externalId=item.get("externalId", ""), space=item.get("space", "")
        )
