from typing import List, Optional
from pydantic import BaseModel


class Severity(BaseModel):
    space: str
    externalId: str
    name: str
    description: str

    @staticmethod
    def from_json(data: dict):
        return Severity(
            space=data.get("space", ""),
            externalId=data.get("externalId", ""),
            name=data.get("name", ""),
            description=data.get("description", ""),
        )


class Channel(BaseModel):
    name: str
    description: str
    externalId: str

    @staticmethod
    def from_json(data: dict):
        return Channel(
            name=data.get("name", ""),
            description=data.get("description", ""),
            externalId=data.get("externalId", ""),
        )


class Subscriber(BaseModel):
    email: Optional[str] = None
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    externalId: Optional[str] = None
    space: Optional[str] = None
    smsNumber: Optional[str] = None

    @staticmethod
    def from_json(data: dict):
        return Subscriber(
            email=data["email"],
            first_name=data["firstName"],
            last_name=data["lastName"],
            externalId=data["externalId"],
            space=data["space"],
        )


class Application(BaseModel):
    name: Optional[str] = None
    externalId: Optional[str] = None
    alias: Optional[str] = None

    @staticmethod
    def from_json(data: dict):
        return Application(
            name=data["name"],
            externalId=data["externalId"],
            alias=data["alias"]
        )


class NotificationType(BaseModel):
    name: Optional[str] = None
    externalId: Optional[str] = None
    application: Optional[Application] = None

    @staticmethod
    def from_json(data: dict):
        application = Application.from_json(data["application"])
        return NotificationType(
            name=data["name"],
            externalId=data["externalId"],
            application=application,
        )


class Template(BaseModel):
    name: Optional[str] = None
    externalId: Optional[str] = None
    notificationType: Optional[NotificationType] = None
    subject: Optional[str] = None

    @staticmethod
    def from_json(data: dict):
        if data["notificationType"] is None:
            notificationType = None
        else:
            notificationType = NotificationType.from_json(data["notificationType"])
        return Template(
            name=data["name"],
            externalId=data["externalId"],
            notificationType=notificationType,
            subject=data.get("subject"),
        )


class ReportingSite(BaseModel):
    externalId: Optional[str] = None
    name: Optional[str] = None
    description: Optional[str] = None
    siteCode: Optional[str] = None

    @staticmethod
    def from_json(data: dict):
        return ReportingSite(
            externalId=data.get("externalId", None),
            name=data.get("name", None),
            description=data.get("description", None),
            siteCode=data.get("siteCode", None),
        )


class NotificationDeliverableItem(BaseModel):
    deliveredDate: Optional[str]
    externalId: str
    scheduleDate: str
    channel: Channel
    severity: Severity
    subscribers: List[Subscriber]
    externalSubscribers: Optional[List[str]] = None
    template: Template
    reportingSite: Optional[ReportingSite] = None
    text: str
    isProcessing: Optional[bool] = False

    @classmethod
    def from_json(cls, data: dict):
        channel = Channel.from_json(data["channel"])
        severity = Severity.from_json(data["severity"])
        subscribers = [
            Subscriber.from_json(sub) for sub in data["subscribers"]["items"]
        ]
        template = Template.from_json(data["template"])

        if not data["reportingSite"]:
            reportingSite = None
        else:
            reportingSite = ReportingSite.from_json(data["reportingSite"])

        return NotificationDeliverableItem(
            deliveredDate=data.get("deliveredDate", ""),
            externalId=data["externalId"],
            scheduleDate=data["scheduleDate"],
            channel=channel,
            severity=severity,
            subscribers=subscribers,
            externalSubscribers=data["externalSubscribers"],
            template=template,
            reportingSite=reportingSite,
            text=data["text"],
            isProcessing=data["isProcessing"]
        )
