NOTIFICATION_DELIVERABLE_QUERY = """
    query NotificationDeliverablePending($filter: _ListNotificationDeliverableFilter) {
        listNotificationDeliverable(filter: $filter, first: 1000) {
            items {
                deliveredDate
                externalId
                space
                scheduleDate
                channel {
                    name
                    externalId
                }
                subscribers {
                    items {
                    email
                    firstName
                    lastName
                    externalId
                    space
                    }
                }
                externalSubscribers
                text
                template {
                    name
                    externalId
                    notificationType {
                        name
                        externalId
                        application {
                            externalId
                            name
                            alias
                        }
                    }
                    subject
                }
                severity {
                    externalId
                    space
                    name
                    description
                }
                reportingSite {
                    externalId
                    name
                    description
                    siteCode
                }
                isProcessing
            }
        }
    }
"""


FIND_SUBSCRIBER_PHONE = """
    query GetUserPhoneNumber($filter: _ListUserAzureAttributeFilter) {
        listUserAzureAttribute(filter: $filter) {
            items {
                phoneNumber
                user {
                    email
                    firstName
                    lastName
                }
            }
        }
    }
"""
