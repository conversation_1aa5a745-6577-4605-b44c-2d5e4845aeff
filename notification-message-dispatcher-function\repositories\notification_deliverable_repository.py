import json
from typing import Any, List
from datetime import datetime, timedelta
import pytz
from cognite.client import CogniteClient
from settings.settings_class import Settings
from gql import gql, Client
import queries
import models
from cognite.client.data_classes.data_modeling import NodeApply, NodeOrEdgeData, ViewId
import utils as Utils
from cognite.client.data_classes.data_modeling.query import (
    Query,
    Select,
    NodeResultSetExpression,
    EdgeResultSetExpression,
    SourceSelector,
)
from cognite.client.data_classes.filters import Equals, HasData
from cognite.client.data_classes.data_modeling.ids import ViewId

ENTITY = "NotificationDeliverable"
USER_ENTITY = "NotificationUser"


class NotificationDeliverableRepository:
    def __init__(
        self, gqlClient: Client, cogniteClient: CogniteClient, settings: Settings
    ):
        self.gqlClient = gqlClient
        self.cogniteClient = cogniteClient
        self.settings = settings

    def list_deliverable_pending(
        self, next_minutes: int = 5, previous_minutes: int = 5
    ) -> List[Any]:
        now = datetime.now(pytz.utc)
        minutes_ago = now - timedelta(minutes=previous_minutes)
        minutes_later = now + timedelta(minutes=next_minutes)

        fmt = "%Y-%m-%dT%H:%M:%S"
        formatted_now = now.strftime(fmt)
        formatted_minutes_ago = minutes_ago.strftime(fmt)
        formatted_minutes_later = minutes_later.strftime(fmt)

        print(formatted_now)
        print(formatted_minutes_ago)
        print(formatted_minutes_later)

        filter = {}
        filter["filter"] = {
            "and": [
                {"space": {"eq": self.settings.ntf_prot_instance_space}},
                {"deliveredDate": {"isNull": "true"}},
                {"scheduleDate": {"lt": formatted_minutes_later}},
                {"scheduleDate": {"gte": formatted_minutes_ago}},
                {"isProcessing": {"isNull": "true"}}
            ]
        }

        result = self.gqlClient.execute(
            gql(queries.NotificationDeliverable.list), filter
        )

        if len(result["listNotificationDeliverable"]["items"]) > 0:
            data = result["listNotificationDeliverable"]["items"]
            notification_deliverable_items = [
                models.NotificationDeliverableItem.from_json(item) for item in data
            ]

            self.stamp_as_processing(notification_deliverable_items)

            for item in notification_deliverable_items:
                users_list = self.find_unlimited_template_subscribed_users(
                    item.externalId
                )
                if users_list is not None and len(users_list) > 0:
                    item.subscribers = users_list
                print(f"Send Notifications for {len(item.subscribers)} subscribers")

            return notification_deliverable_items

        return []

    def get_user_phone(self, user_external_id: str, space: str) -> Any:
        filter = {}
        filter["filter"] = {
            "and": [
                {"user": {"externalId": {"eq": user_external_id}}},
                {"user": {"space": {"eq": space}}},
            ]
        }

        result = self.gqlClient.execute(
            gql(queries.NotificationDeliverable.get_user_phone), filter
        )

        if len(result["listUserAzureAttribute"]["items"]) > 0:
            data = result["listUserAzureAttribute"]["items"]
            return data[0]["phoneNumber"]

        return None

    def stamp_as_delivered(self, notification: models.NotificationDeliverableItem):
        print("Stamping as delivered repository started")

        view = Utils.cognite.getView(
            self.cogniteClient, self.settings.cognite_graphql_model_space, ENTITY
        )
        if view is None:
            print("Could not retrieve the view info for the Entity {ENTITY}")
            return

        updateData = {"deliveredDate": notification.deliveredDate}
        print("Stamping as delivered repository - Preparing nodes")

        nodes = NodeApply(
            self.settings.ntf_prot_instance_space,
            notification.externalId,
            sources=[
                NodeOrEdgeData(
                    ViewId(
                        self.settings.cognite_graphql_model_space,
                        ENTITY,
                        view.version,
                    ),
                    updateData,
                )
            ],
        )
        print("Stamping as delivered repository - Apply nodes")
        self.cogniteClient.data_modeling.instances.apply(nodes)
        print("Stamping as delivered repository - Apply nodes success")

    def find_unlimited_template_subscribed_users(self, deliverable_id: str):
        deliverable_view = Utils.cognite.getView(
            self.cogniteClient,
            self.settings.cognite_graphql_model_space,
            ENTITY,
        ).version

        subscribers_view = Utils.cognite.getView(
            self.cogniteClient, self.settings.cognite_graphql_model_space, USER_ENTITY
        ).version

        users_in_subscribers_deliverables_cursor = None
        subscribers_in_deliverables_cursor = None
        deliverables_cursor = None
        has_cursor = True
        response_query = None

        subscribed_user_list = []

        query = Query(
            with_={
                "deliverables": NodeResultSetExpression(
                    filter=Equals(["node", "externalId"], deliverable_id),
                    limit=1,
                    chain_to="destination",
                    direction="outwards",
                ),
                "subscribers_in_deliverables": EdgeResultSetExpression(
                    from_="deliverables",
                    limit=10000,
                    direction="outwards",
                    chain_to="destination",
                    filter=Equals(
                        ["edge", "type"],
                        {
                            "space": self.settings.cognite_graphql_model_space,
                            "externalId": "NotificationDeliverable.subscribers",
                        },
                    ),
                    node_filter=HasData(
                        views=[
                            ViewId(
                                self.settings.cognite_graphql_model_space,
                                USER_ENTITY,
                                subscribers_view,
                            )
                        ]
                    ),
                ),
                "users_in_subscribers_deliverables": NodeResultSetExpression(
                    from_="subscribers_in_deliverables", limit=10000
                ),
            },
            select={
                "deliverables": Select(
                    [
                        SourceSelector(
                            ViewId(
                                self.settings.cognite_graphql_model_space,
                                ENTITY,
                                deliverable_view,
                            ),
                            ["*"],
                        )
                    ],
                    limit=1,
                ),
                "subscribers_in_deliverables": Select(limit=10000),
                "users_in_subscribers_deliverables": Select(
                    [
                        SourceSelector(
                            ViewId(
                                self.settings.cognite_graphql_model_space,
                                USER_ENTITY,
                                subscribers_view,
                            ),
                            ["*"],
                        )
                    ],
                    limit=10000,
                ),
            },
            cursors={
                "users_in_subscribers_deliverables": users_in_subscribers_deliverables_cursor,
                "subscribers_in_deliverables": subscribers_in_deliverables_cursor,
                "deliverables": deliverables_cursor,
            },
        )

        while has_cursor:
            response_query = self.cogniteClient.data_modeling.instances.query(query)

            users_list = json.loads(
                json.dumps(response_query["users_in_subscribers_deliverables"].dump())
            )

            for item in users_list:

                user_props = item["properties"][
                    self.settings.cognite_graphql_model_space
                ][USER_ENTITY + "/" + subscribers_view]
                user_item = {
                    "externalId": item["externalId"],
                    "space": item["space"],
                    "email": user_props["email"],
                    "firstName": user_props.get("firstName", None),
                    "lastName": user_props.get("lastName", None),
                }
                subscribed_user_list.append(models.Subscriber.from_json(user_item))

            if (
                response_query
                and response_query.cursors["subscribers_in_deliverables"]
                and response_query.cursors["users_in_subscribers_deliverables"]
            ):
                has_cursor = True
                query.cursors["subscribers_in_deliverables"] = response_query.cursors[
                    "subscribers_in_deliverables"
                ]
                query.cursors["users_in_subscribers_deliverables"] = (
                    response_query.cursors["users_in_subscribers_deliverables"]
                )

            else:
                has_cursor = False

        return subscribed_user_list
    
    def stamp_as_processing(self, notifications: models.NotificationDeliverableItem):
        print("Stamp as is processing")
        view = Utils.cognite.getView(
            self.cogniteClient, self.settings.cognite_graphql_model_space, ENTITY
        )
        if view is None:
            print("Could not retrieve the view info for the Entity {ENTITY}")
            return

        logNodes = []
        eventLogData = {}
        eventLogData["isProcessing"] = True

        for item in notifications:
            logNodes.append(
                NodeApply(
                    self.settings.ntf_prot_instance_space,
                    item.externalId,
                    sources=[
                        NodeOrEdgeData(
                            ViewId(
                                self.settings.cognite_graphql_model_space,
                                ENTITY,
                                view.version
                            ),
                            eventLogData
                        )
                    ]
                )
            )

        self.cogniteClient.data_modeling.instances.apply(logNodes)
        print("Successfully stamped as is processing")

    def stamp_as_processed(self, notification: models.NotificationDeliverableItem):

        view = Utils.cognite.getView(
            self.cogniteClient, self.settings.cognite_graphql_model_space, ENTITY
        )
        if view is None:
            print("Could not retrieve the view info for the Entity {ENTITY}")
            return

        updateData = {"isProcessing": notification.isProcessing}

        nodes = NodeApply(
            self.settings.ntf_prot_instance_space,
            notification.externalId,
            sources=[
                NodeOrEdgeData(
                    ViewId(
                        self.settings.cognite_graphql_model_space,
                        ENTITY,
                        view.version,
                    ),
                    updateData,
                )
            ],
        )

        self.cogniteClient.data_modeling.instances.apply(nodes)