from typing import List
from gql import Client
from settings.settings_class import Settings
from cognite.client import CogniteClient
import models
import repositories
from datetime import datetime
import pytz
import utils


class NotificationEventService:
    def __init__(
        self,
        cogniteClient: CogniteClient,
        gqlClient: Client,
        settings: Settings,
    ):
        self.cogniteClient = cogniteClient
        self.gqlClient = gqlClient
        self.settings = settings
        self.notification_deliverable_repository = (
            repositories.NotificationDeliverableRepository(
                gqlClient, cogniteClient, settings
            )
        )
        self.subscriber_cache: List[models.Subscriber] = []

    def start(self):
        print("Starting Notification Event Service")

        notifications_pending = self.__list_notification_delivery_pending()
        self.__subscriber_cache_init(notifications_pending)
        self.__send(notifications_pending)

    def __list_notification_delivery_pending(self):
        print("Listing Notification Delivery pending")
        return self.notification_deliverable_repository.list_deliverable_pending(
            self.settings.next_minutes_to_consider_pending,
            self.settings.previous_minutes_to_consider_pending,
        )

    def __send(self, notifications: List[models.NotificationDeliverableItem]):
        print("Sending Notifications")

        if len(notifications) == 0:
            print("No pending notifications")
            return

        listTemplateId: List[str] = []
        listTemplateSitesId: List[str] = []
        listSubscriberId: List[str] = []
        listExternalSubscribers: List[str] = []

        for notification in notifications:
            if notification.template.externalId not in listTemplateId:
                listTemplateId.append(notification.template.externalId)

            if notification.reportingSite:
                siteId = notification.reportingSite.externalId
            else:
                siteId = None

            if siteId not in listTemplateSitesId:
                listTemplateSitesId.append(siteId)

            for item in notification.subscribers:
                if item.externalId not in listSubscriberId:
                    listSubscriberId.append(item.externalId)

            if (
                notification.externalSubscribers is not None
                and len(notification.externalSubscribers) > 0
            ):
                for item in notification.externalSubscribers:
                    if item not in listExternalSubscribers:
                        listExternalSubscribers.append(item)

        for id in listTemplateId:
            listNotificationByTemplate = [
                ntf for ntf in notifications if ntf.template.externalId == id
            ]
            for siteId in listTemplateSitesId:
                listNotificationBySite = [
                    ntf
                    for ntf in listNotificationByTemplate
                    if (ntf.reportingSite.externalId if ntf.reportingSite else None)
                    == siteId
                ]
                listToEmail = [
                    ntf
                    for ntf in listNotificationBySite
                    if ntf.channel.externalId == self.settings.channel_email_external_id
                ]
                listToTeams = [
                    ntf
                    for ntf in listNotificationBySite
                    if ntf.channel.externalId == self.settings.channel_teams_external_id
                ]
                listToSms = [
                    ntf
                    for ntf in listNotificationBySite
                    if ntf.channel.externalId == self.settings.channel_sms_external_id
                ]

                if listSubscriberId is not None and len(listSubscriberId) > 0:
                    self.__send_per_subscriber(
                        False, listSubscriberId, listToEmail, listToTeams, listToSms
                    )

                if (
                    listExternalSubscribers is not None
                    and len(listExternalSubscribers) > 0
                ):
                    self.__send_per_subscriber(
                        True,
                        listExternalSubscribers,
                        listToEmail,
                        listToTeams,
                        listToSms,
                    )

    def __send_per_subscriber(
        self,
        external: bool,
        subscribers: List[str],
        listToEmail: List[models.NotificationDeliverableItem] = None,
        listToTeams: List[models.NotificationDeliverableItem] = None,
        listToSms: List[models.NotificationDeliverableItem] = None,
    ):
        for subscriber in subscribers:
            listToEmailByUser = []
            listToTeamsByUser = []
            listToSMSByUser = []

            for ntf in listToEmail:
                listNtfSubscriberIds = (
                    [item.externalId for item in ntf.subscribers]
                    if not external
                    else [
                        item
                        for item in ntf.externalSubscribers
                        if ntf.externalSubscribers and len(ntf.externalSubscribers) > 0
                    ]
                )
                if subscriber in listNtfSubscriberIds:
                    listToEmailByUser.append(ntf)
            if not external:
                for ntf in listToTeams:
                    listNtfSubscriberIds = [item.externalId for item in ntf.subscribers]
                    if subscriber in listNtfSubscriberIds:
                        listToTeamsByUser.append(ntf)

                for ntf in listToSms:
                    listNtfSubscriberIds = [item.externalId for item in ntf.subscribers]
                    if subscriber in listNtfSubscriberIds:
                        listToSMSByUser.append(ntf)

            is_success = False
            if len(listToEmailByUser) > 0:
                toEmail = self.__get_last_notifications(listToEmailByUser)
                is_success = self.__send_email(toEmail, subscriber, external)
                if is_success:
                    self.__stamp_as_delivered(listToEmailByUser)
                    self.__stamp_as_processed(listToEmailByUser)

            if len(listToTeamsByUser) > 0:
                toTeams = self.__get_last_notifications(listToTeamsByUser)
                is_success = self.__send_teams(toTeams, subscriber)
                if is_success:
                    self.__stamp_as_delivered(listToTeamsByUser)
                    self.__stamp_as_processed(listToTeamsByUser)

            if len(listToSMSByUser) > 0:
                toSms = self.__get_last_notifications(listToSMSByUser)
                is_success = self.__send_sms(toSms, subscriber)
                if is_success:
                    self.__stamp_as_delivered(listToSMSByUser)
                    self.__stamp_as_processed(listToSMSByUser)

    def __send_email(
        self,
        notifications: List[models.NotificationDeliverableItem],
        subscriberId: str,
        external: bool = False,
    ):
        if notifications[0].template.subject is not None and notifications[0].template.subject != "":
            subject = notifications[0].template.subject
        else:
            subject = self.__set_message_subject(notifications)

        body = self.__set_message_text(notifications)
    
        subscriberEmail = (
            [
                item.email
                for item in notifications[0].subscribers
                if item.externalId == subscriberId and hasattr(item, "email")
            ][0]
            if not external
            else [
                item
                for item in notifications[0].externalSubscribers
                if item == subscriberId
            ][0]
        )
    
        if subscriberEmail is not None:
            utils.ms_logic_app.sendMail(
                self.settings.logic_app_url,
                subscriberEmail,
                subject,
                body,
                notifications[0].severity.description,
            )

        return True

    def __send_sms(
        self, notifications: List[models.NotificationDeliverableItem], subscriberId: str
    ):
        print("Sending SMS - Started")

        subject = "".join(self.__set_message_subject(notifications).split(" "))
        body = self.__set_message_text(notifications)
        subscriber = [
            item
            for item in notifications[0].subscribers
            if item.externalId == subscriberId
        ][0]

        if subscriber is not None:
            subscriber_in_cache = self.__find_subscriber_in_cache(
                "externalId", subscriber.externalId
            )

            if subscriber_in_cache.smsNumber is None:
                new_phone_number = (
                    self.notification_deliverable_repository.get_user_phone(
                        subscriber.externalId, subscriber.space
                    )
                )
                self.__update_subscriber_cache(
                    subscriber_in_cache.externalId, "smsNumber", new_phone_number
                )

        if subscriber_in_cache.smsNumber is not None:
            utils.azure_sms.send(
                self.settings.sms_connection_url,
                self.settings.sms_from_number,
                subscriber_in_cache.smsNumber,
                self.__validate_sms_text(subject, body),
            )

        print("Sending SMS - Success")
        return True

    def __send_teams(
        self, notifications: List[models.NotificationDeliverableItem], subscriberId: str
    ):
        print("Sending Teams")
        subject = self.__set_message_subject(notifications)
        body = self.__set_message_text(notifications)
        subscriberEmail = [
            item.email
            for item in notifications[0].subscribers
            if item.externalId == subscriberId
        ][0]

        if subscriberEmail is not None:
            utils.ms_logic_app.sendTeams(
                self.settings.logic_app_url,
                subscriberEmail,
                subject,
                subject + body,
                notifications[0].severity.description,
            )

        return True

    def __stamp_as_delivered(
        self, notifications: List[models.NotificationDeliverableItem]
    ):
        print("Stamp as delivered - Started")
        now = datetime.now(pytz.utc)

        for notification in notifications:
            notification.deliveredDate = now.strftime("%Y-%m-%dT%H:%M:%S")
            print("Stamp as delivered - Set deliveredDate")

            self.notification_deliverable_repository.stamp_as_delivered(notification)

        print("Stamp as successfully delivered")

    def __subscriber_cache_init(
        self, notifications: List[models.NotificationDeliverableItem]
    ):
        print("Initialize subscriber cache")
        for notification in notifications:
            for subscriber in notification.subscribers:
                subscriber_in_cache = self.__find_subscriber_in_cache(
                    "externalId", subscriber.externalId
                )
                if subscriber_in_cache is None:
                    self.subscriber_cache.append(subscriber)

    def __find_subscriber_in_cache(self, field, value) -> List[models.Subscriber]:
        for subscriber in self.subscriber_cache:
            if getattr(subscriber, field, None) == value:
                return subscriber
        return None

    def __update_subscriber_cache(
        self, subscriber_external_id: str, field: str, value: str
    ):
        for subscriber in self.subscriber_cache:
            if subscriber.externalId == subscriber_external_id:
                setattr(subscriber, field, value)
                break

    def __set_message_subject(
        self, notifications: List[models.NotificationDeliverableItem]
    ) -> str:
        notification_type = notifications[0].template.notificationType
        if notification_type is None:
         subject = "Notification"
        else:
         if (
             notifications[0].channel.externalId
             == self.settings.channel_sms_external_id
         ):
             applicationName = self.__set_application_icon(
                 notification_type.application
             )
             subject = f"{applicationName}-{notification_type.name}"
             if notifications[0].reportingSite:
                 subject = subject + f"-{notifications[0].reportingSite.siteCode}"
         else:
             subject = f"Notification - {notifications[0].severity.description} Severity - {notification_type.application.name} - {notification_type.name}"
             if notifications[0].reportingSite:
                 subject = (
                     subject + f" - {notifications[0].reportingSite.description}"
                 )
             if (
                 notifications[0].channel.externalId
                 == self.settings.channel_teams_external_id
             ):
                 subject = f"<span>{subject}</span></br>"

        return subject

    def __set_message_text(
        self, notifications: List[models.NotificationDeliverableItem]
    ) -> str:
        body = ""

        for ntf in notifications:
            if body != "":
                body = body + ntf.text
            else:
                body = ntf.text

        return body

    def __set_application_icon(self, application: models.Application):
        namesArray = application.alias.split(" ")
        firstLetter = ""
        secondLetter = ""

        if len(namesArray) > 0:
            firstLetter = str(namesArray[0])[0]
            if len(namesArray) > 1 and len(namesArray[1]) > 0:
                secondLetter = str(namesArray[1])[0]
            else:
                secondLetter = str(namesArray[0])[1]

        return str(firstLetter + secondLetter).upper()

    def __get_last_notifications(
        self, notifications: List[models.NotificationDeliverableItem]
    ):
        limitNotifications: List[models.NotificationDeliverableItem] = []
        if notifications[0].channel.externalId == self.settings.channel_sms_external_id:
            limitNotifications = [notifications[-1]]
        else:
            if len(notifications) > 10:
                limitNotifications = notifications[-10:-1]
                limitNotifications.append(notifications[-1])

        return limitNotifications if len(limitNotifications) > 0 else notifications

    def __validate_sms_text(self, subject: str, text: str):
        message = f"{subject}-{text}"
        if "html" in message or "<table>" in message or len(message) > 160:
            date = message.split("-")[-1]
            newText = f"{subject}-Notification is not available via SMS. Please, check another channel or on-screen notifications. {date}"
            return newText

        return message

    def __stamp_as_processed(
        self, notifications: List[models.NotificationDeliverableItem]
    ):
        print("Stamp as processed - Started")

        for notification in notifications:
            notification.isProcessing = False

            self.notification_deliverable_repository.stamp_as_processed(notification)

        print("Stamp as successfully processed")

    