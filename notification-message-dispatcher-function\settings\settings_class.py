from pydantic_settings import BaseSettings

class Settings(BaseSettings):
    cognite_data_set_id: str
    cognite_base_uri: str
    cognite_project: str
    cognite_client_name: str
    cognite_graphql_uri: str
    cognite_graphql_model_space: str
    ntf_prot_instance_space:str
    auth_scopes: str
    auth_client_id: str
    auth_tenant_id: str
    auth_secret: str
    auth_token_uri: str
    channel_email_external_id: str
    channel_sms_external_id: str
    channel_teams_external_id: str
    next_minutes_to_consider_pending: int
    previous_minutes_to_consider_pending: int
    logic_app_url: str
    sms_connection_url: str
    sms_from_number: str
