import os
from azure.communication.sms import SmsClient


class AzureSMS:
    @classmethod
    def send(cls, sms_connection_string: str, phoneFrom: str, phoneTo: str, msg: str):
        """
        [PT-BR] Metodo para enviar SMS usando o Azure Communication Services
        [EN-US] Method to send SMS using Azure Communication Services
        """
        # Remove invalid characters from phone number
        phoneTo = phoneTo.replace("-", "")
        phoneTo = phoneTo.replace(" ", "")
        phoneTo = phoneTo.replace("'", "")

        # Valid phone number
        if phoneTo[0] != "+":
            phoneTo = "+" + phoneTo



        if len(phoneTo) <= 10:
            print("Invalid phone number")
        else:
            # Create the SmsClient object which will be used to send SMS messages
            sms_client = SmsClient.from_connection_string(sms_connection_string)

            # Send the SMS message
            sms_responses = sms_client.send(
                from_=phoneFrom,
                to=phoneTo,
                message=msg,
            )

            for sms_response in sms_responses:
                print(sms_response)
