from typing import List, Dict, Any, Optional, TypeVar
from cognite.client import Cognite<PERSON>lient
from cognite.client.data_classes.data_modeling import NodeApply, EdgeApply
from graphql import DocumentNode, Source, parse
from gql import Client, gql

T = TypeVar("T")

class Cognite:
    
    @classmethod
    def getView(
        cls,
        cognite_client: CogniteClient,
        fdm_model_space: str,
        entityName: str
    ):
        """
            [EN] Method for retrieving the view of an entity.\n
            [PT-BR] Metodo para recuperar a view de uma entidade.
        """
        views = cognite_client.data_modeling.views.retrieve(
            (fdm_model_space, entityName)
        )
        view = views.get(external_id=entityName)
        if view is None:
            print("Could not retrieve the view info for the Entity {ENTITY}")
            return
        return view

    @classmethod
    def createRelationship(
        cls,
        list_properties: List[Dict[str, Any]],
        sourceNodes: NodeApply,
        version: Optional[str],
        cognite_client: CogniteClient,
        fdm_model_space: str,
        fdm_instances_space: str,
        relationshipTypeName: str,
    ):
        """
            [EN] Method for creating relationships between entities.\n
            [PT-BR] Metodo para criar relacionamentos entre entidades.
            
            Args:
                list_properties: List of properties to be created.
                sourceNodes: NodeApply object that represents the source of the relationship.
                version: Version of the relationship.
                cognite_client: CogniteClient object.
                fdm_model_space: Model space.
                fdm_instances_space: Instances space.
                relationshipTypeName: Name of the relationship.
        """
        model_space = fdm_model_space
        instances_space = fdm_instances_space
        
        edges = [
            EdgeApply(
                instances_space,
                f"{sourceNodes.external_id}-{payload.externalId}",
                type=(model_space, relationshipTypeName),
                start_node=(
                    instances_space,
                    sourceNodes.external_id,
                ),
                end_node=(
                    instances_space,
                    payload.externalId,
                ),
                sources=sourceNodes.sources
                if version and "source" in payload
                else None,
            )
            for payload in list_properties
        ]
        
        chunks = cls.__createDefaultPagination(edges)  
        
        for chunk in chunks:
            try:
                cognite_client.data_modeling.instances.apply(
                    edges=chunk,  # type: ignore
                    skip_on_version_conflict=False,
                    replace=True,
                    auto_create_direct_relations=True,
                    auto_create_end_nodes=False,
                    auto_create_start_nodes=False,
                )
            except Exception as e:
                print(e)

    # @classmethod
    # def __createDefaultPagination(cls, resources: list[T]) -> list[list[T]]:
    #     return [
    #         resources[1000 * i: 1000 * (i + 1)]
    #         for i in range(int(len(resources) / 1000) + 1)  # type: ignore
    #     ]