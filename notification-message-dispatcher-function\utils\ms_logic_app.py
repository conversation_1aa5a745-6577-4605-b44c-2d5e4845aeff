from typing import List, Dict, Any, Optional
import requests


class MsLogicApp:
    @classmethod
    def sendMail(
        cls, logic_app_url: str, email: str, subject: str, body: str, priority: str
    ):
        """
        [PT-BR] Metodo para enviar e-mail via Microsoft Logic App.
        [EN] Method for sending e-mail via Microsoft Logic App.
        """

        payload = {
            "to": [email],
            "subject": subject,
            "body": body,
            "priority": priority,
            "channel": "email",
        }
        print("Sending email to: " + email)

        response = cls.__send(logic_app_url, payload)

        if response.status_code != 202:
            print(
                "Error sending e-mail via Microsoft Logic App: "
                + str(response.status_code)
                + " - "
                + response.text
            )

    @classmethod
    def sendTeams(cls, logic_app_url: str, email: str, subject: str, body: str, priority: str):
        """
        [PT-BR] Metodo para enviar mensagem para o Teams via Microsoft Logic App.
        [EN] Method for sending message to Teams via Microsoft Logic App.
        """

        payload = {
            "to": [email],
            "subject": subject,
            "body": body,
            "priority": priority,
            "channel": "teams",
        }
        print("Sending TEAMS to: " + email)

        response = cls.__send(logic_app_url, payload)
        if response.status_code != 202:
            print(
                "Error sending TEAMS via Microsoft Logic App: "
                + str(response.status_code)
                + " - "
                + response.text
            )

    @classmethod
    def __send(cls, logic_app_url: str, payload: any):
        """
        [EN] Send Data to Microsoft Logic App.
        [PT-BR] Envia dados para o Microsoft Logic App.
        """

        response = requests.post(logic_app_url, json=payload)
        return response
