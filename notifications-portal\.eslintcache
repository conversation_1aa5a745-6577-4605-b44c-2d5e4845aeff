[{"/Users/<USER>/Desktop/RADIX/CLN/Repositorios/Notifications/notifications-portal/next-env.d.ts": "1", "/Users/<USER>/Desktop/RADIX/CLN/Repositorios/Notifications/notifications-portal/next.config.js": "2", "/Users/<USER>/Desktop/RADIX/CLN/Repositorios/Notifications/notifications-portal/src/common/clients/dataFetchClient.ts": "3", "/Users/<USER>/Desktop/RADIX/CLN/Repositorios/Notifications/notifications-portal/src/common/configurations/auth.ts": "4", "/Users/<USER>/Desktop/RADIX/CLN/Repositorios/Notifications/notifications-portal/src/common/configurations/endpoints.ts": "5", "/Users/<USER>/Desktop/RADIX/CLN/Repositorios/Notifications/notifications-portal/src/common/configurations/enviroment.ts": "6", "/Users/<USER>/Desktop/RADIX/CLN/Repositorios/Notifications/notifications-portal/src/common/configurations/utils.ts": "7", "/Users/<USER>/Desktop/RADIX/CLN/Repositorios/Notifications/notifications-portal/src/common/contexts/TemplatesContext.tsx": "8", "/Users/<USER>/Desktop/RADIX/CLN/Repositorios/Notifications/notifications-portal/src/common/contexts/UserContext.tsx": "9", "/Users/<USER>/Desktop/RADIX/CLN/Repositorios/Notifications/notifications-portal/src/common/factories/msal-factory.ts": "10", "/Users/<USER>/Desktop/RADIX/CLN/Repositorios/Notifications/notifications-portal/src/common/hooks/useApplicationRequest.tsx": "11", "/Users/<USER>/Desktop/RADIX/CLN/Repositorios/Notifications/notifications-portal/src/common/hooks/useAuthToken.tsx": "12", "/Users/<USER>/Desktop/RADIX/CLN/Repositorios/Notifications/notifications-portal/src/common/hooks/useChannelsRequest.tsx": "13", "/Users/<USER>/Desktop/RADIX/CLN/Repositorios/Notifications/notifications-portal/src/common/hooks/useGetCountTemplateNameRequest.tsx": "14", "/Users/<USER>/Desktop/RADIX/CLN/Repositorios/Notifications/notifications-portal/src/common/hooks/useRawNotificationRequest.tsx": "15", "/Users/<USER>/Desktop/RADIX/CLN/Repositorios/Notifications/notifications-portal/src/common/hooks/useSeverityRequest.tsx": "16", "/Users/<USER>/Desktop/RADIX/CLN/Repositorios/Notifications/notifications-portal/src/common/hooks/useTemplatesPaginated.tsx": "17", "/Users/<USER>/Desktop/RADIX/CLN/Repositorios/Notifications/notifications-portal/src/common/layouts/BaseLayout.tsx": "18", "/Users/<USER>/Desktop/RADIX/CLN/Repositorios/Notifications/notifications-portal/src/common/models/application.ts": "19", "/Users/<USER>/Desktop/RADIX/CLN/Repositorios/Notifications/notifications-portal/src/common/models/applicationGroup.ts": "20", "/Users/<USER>/Desktop/RADIX/CLN/Repositorios/Notifications/notifications-portal/src/common/models/channel.ts": "21", "/Users/<USER>/Desktop/RADIX/CLN/Repositorios/Notifications/notifications-portal/src/common/models/notificationType.ts": "22", "/Users/<USER>/Desktop/RADIX/CLN/Repositorios/Notifications/notifications-portal/src/common/models/notificationUser.ts": "23", "/Users/<USER>/Desktop/RADIX/CLN/Repositorios/Notifications/notifications-portal/src/common/models/notificationUserRole.ts": "24", "/Users/<USER>/Desktop/RADIX/CLN/Repositorios/Notifications/notifications-portal/src/common/models/paginatedTemplates.ts": "25", "/Users/<USER>/Desktop/RADIX/CLN/Repositorios/Notifications/notifications-portal/src/common/models/rawNotification.ts": "26", "/Users/<USER>/Desktop/RADIX/CLN/Repositorios/Notifications/notifications-portal/src/common/models/severitiy.ts": "27", "/Users/<USER>/Desktop/RADIX/CLN/Repositorios/Notifications/notifications-portal/src/common/models/template.ts": "28", "/Users/<USER>/Desktop/RADIX/CLN/Repositorios/Notifications/notifications-portal/src/common/utils/showError.ts": "29", "/Users/<USER>/Desktop/RADIX/CLN/Repositorios/Notifications/notifications-portal/src/features/Tab/index.tsx": "30", "/Users/<USER>/Desktop/RADIX/CLN/Repositorios/Notifications/notifications-portal/src/features/Tab/model/index.ts": "31", "/Users/<USER>/Desktop/RADIX/CLN/Repositorios/Notifications/notifications-portal/src/features/Tab/style.ts": "32", "/Users/<USER>/Desktop/RADIX/CLN/Repositorios/Notifications/notifications-portal/src/pages/_app.tsx": "33", "/Users/<USER>/Desktop/RADIX/CLN/Repositorios/Notifications/notifications-portal/src/pages/customize-templates/components/channels-checkbox-group.tsx": "34", "/Users/<USER>/Desktop/RADIX/CLN/Repositorios/Notifications/notifications-portal/src/pages/customize-templates/components/fields-chips.tsx": "35", "/Users/<USER>/Desktop/RADIX/CLN/Repositorios/Notifications/notifications-portal/src/pages/customize-templates/components/severity-select.tsx": "36", "/Users/<USER>/Desktop/RADIX/CLN/Repositorios/Notifications/notifications-portal/src/pages/customize-templates/index.tsx": "37", "/Users/<USER>/Desktop/RADIX/CLN/Repositorios/Notifications/notifications-portal/src/pages/customize-templates/styles.ts": "38", "/Users/<USER>/Desktop/RADIX/CLN/Repositorios/Notifications/notifications-portal/src/pages/customize-templates/useCustomizeTemplatesLogic.tsx": "39", "/Users/<USER>/Desktop/RADIX/CLN/Repositorios/Notifications/notifications-portal/src/pages/index.tsx": "40", "/Users/<USER>/Desktop/RADIX/CLN/Repositorios/Notifications/notifications-portal/src/pages/templates/components/lateral-menu-styles.ts": "41", "/Users/<USER>/Desktop/RADIX/CLN/Repositorios/Notifications/notifications-portal/src/pages/templates/components/lateral-menu.tsx": "42", "/Users/<USER>/Desktop/RADIX/CLN/Repositorios/Notifications/notifications-portal/src/pages/templates/components/notification-type-list-styles.ts": "43", "/Users/<USER>/Desktop/RADIX/CLN/Repositorios/Notifications/notifications-portal/src/pages/templates/components/notification-type-list.tsx": "44", "/Users/<USER>/Desktop/RADIX/CLN/Repositorios/Notifications/notifications-portal/src/pages/templates/components/select-application-styles.ts": "45", "/Users/<USER>/Desktop/RADIX/CLN/Repositorios/Notifications/notifications-portal/src/pages/templates/components/select-application.tsx": "46", "/Users/<USER>/Desktop/RADIX/CLN/Repositorios/Notifications/notifications-portal/src/pages/templates/components/table-pagination.tsx": "47", "/Users/<USER>/Desktop/RADIX/CLN/Repositorios/Notifications/notifications-portal/src/pages/templates/components/templates-table-container-styles.ts": "48", "/Users/<USER>/Desktop/RADIX/CLN/Repositorios/Notifications/notifications-portal/src/pages/templates/components/templates-table-container.tsx": "49", "/Users/<USER>/Desktop/RADIX/CLN/Repositorios/Notifications/notifications-portal/src/pages/templates/components/templates-table-styles.ts": "50", "/Users/<USER>/Desktop/RADIX/CLN/Repositorios/Notifications/notifications-portal/src/pages/templates/components/templates-table.tsx": "51", "/Users/<USER>/Desktop/RADIX/CLN/Repositorios/Notifications/notifications-portal/src/pages/templates/index.tsx": "52", "/Users/<USER>/Desktop/RADIX/CLN/Repositorios/Notifications/notifications-portal/src/services/applicationService.ts": "53", "/Users/<USER>/Desktop/RADIX/CLN/Repositorios/Notifications/notifications-portal/src/services/templateService.ts": "54"}, {"hash": "55", "results": "56", "hashOfConfig": "57"}, {"hash": "58", "results": "59", "hashOfConfig": "57"}, {"hash": "60", "results": "61", "hashOfConfig": "57"}, {"hash": "62", "results": "63", "hashOfConfig": "57"}, {"hash": "64", "results": "65", "hashOfConfig": "57"}, {"hash": "66", "results": "67", "hashOfConfig": "57"}, {"hash": "68", "results": "69", "hashOfConfig": "57"}, {"hash": "70", "results": "71", "hashOfConfig": "57"}, {"hash": "72", "results": "73", "hashOfConfig": "57"}, {"hash": "74", "results": "75", "hashOfConfig": "57"}, {"hash": "76", "results": "77", "hashOfConfig": "57"}, {"hash": "78", "results": "79", "hashOfConfig": "57"}, {"hash": "80", "results": "81", "hashOfConfig": "57"}, {"hash": "82", "results": "83", "hashOfConfig": "57"}, {"hash": "84", "results": "85", "hashOfConfig": "57"}, {"hash": "86", "results": "87", "hashOfConfig": "57"}, {"hash": "88", "results": "89", "hashOfConfig": "57"}, {"hash": "90", "results": "91", "hashOfConfig": "57"}, {"hash": "92", "results": "93", "hashOfConfig": "57"}, {"hash": "94", "results": "95", "hashOfConfig": "57"}, {"hash": "96", "results": "97", "hashOfConfig": "57"}, {"hash": "98", "results": "99", "hashOfConfig": "57"}, {"hash": "100", "results": "101", "hashOfConfig": "57"}, {"hash": "102", "results": "103", "hashOfConfig": "57"}, {"hash": "104", "results": "105", "hashOfConfig": "57"}, {"hash": "106", "results": "107", "hashOfConfig": "57"}, {"hash": "108", "results": "109", "hashOfConfig": "57"}, {"hash": "110", "results": "111", "hashOfConfig": "57"}, {"hash": "112", "results": "113", "hashOfConfig": "57"}, {"hash": "114", "results": "115", "hashOfConfig": "57"}, {"hash": "116", "results": "117", "hashOfConfig": "57"}, {"hash": "118", "results": "119", "hashOfConfig": "57"}, {"hash": "120", "results": "121", "hashOfConfig": "57"}, {"hash": "122", "results": "123", "hashOfConfig": "57"}, {"hash": "124", "results": "125", "hashOfConfig": "57"}, {"hash": "126", "results": "127", "hashOfConfig": "57"}, {"hash": "128", "results": "129", "hashOfConfig": "57"}, {"hash": "130", "results": "131", "hashOfConfig": "57"}, {"hash": "132", "results": "133", "hashOfConfig": "57"}, {"hash": "134", "results": "135", "hashOfConfig": "57"}, {"hash": "136", "results": "137", "hashOfConfig": "57"}, {"hash": "138", "results": "139", "hashOfConfig": "57"}, {"hash": "140", "results": "141", "hashOfConfig": "57"}, {"hash": "142", "results": "143", "hashOfConfig": "57"}, {"hash": "144", "results": "145", "hashOfConfig": "57"}, {"hash": "146", "results": "147", "hashOfConfig": "57"}, {"hash": "148", "results": "149", "hashOfConfig": "57"}, {"hash": "150", "results": "151", "hashOfConfig": "57"}, {"hash": "152", "results": "153", "hashOfConfig": "57"}, {"hash": "154", "results": "155", "hashOfConfig": "57"}, {"hash": "156", "results": "157", "hashOfConfig": "57"}, {"hash": "158", "results": "159", "hashOfConfig": "57"}, {"hash": "160", "results": "161", "hashOfConfig": "57"}, {"hash": "162", "results": "163", "hashOfConfig": "57"}, "3cf36e94213e85dc306ec5f3f3760ecc", {"filePath": "164", "messages": "165", "suppressedMessages": "166", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "16t8jfr", "8286d8c15725dd6af89a12110544b472", {"filePath": "167", "messages": "168", "suppressedMessages": "169", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "541ae9d44f9a0ce477595ca42a76d6ee", {"filePath": "170", "messages": "171", "suppressedMessages": "172", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "e9e2063653a752c05a60fcdc41edbf57", {"filePath": "173", "messages": "174", "suppressedMessages": "175", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "b3a11d457579ab2c772266d27aa5c2bc", {"filePath": "176", "messages": "177", "suppressedMessages": "178", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "616e028c25a77c396d59d85cd97b75e4", {"filePath": "179", "messages": "180", "suppressedMessages": "181", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "178716688aa14e09868d4ab176a4fb69", {"filePath": "182", "messages": "183", "suppressedMessages": "184", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "6fd80556d283d0ae0e9babe9b3c19be8", {"filePath": "185", "messages": "186", "suppressedMessages": "187", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "ad13deac5fb5481a180fd6cc5e4dbb8d", {"filePath": "188", "messages": "189", "suppressedMessages": "190", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "067357ee79928c89b098fc5ffb4e835e", {"filePath": "191", "messages": "192", "suppressedMessages": "193", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "40882ef21bb8f6d00d6a0a78c40a8871", {"filePath": "194", "messages": "195", "suppressedMessages": "196", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "56d23ae2e1e6aff5370901dc4761c481", {"filePath": "197", "messages": "198", "suppressedMessages": "199", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "dc9265d69f1c8a1be8f66c0140d6f928", {"filePath": "200", "messages": "201", "suppressedMessages": "202", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "6b60ddd32b8876282a2a997f7441ad67", {"filePath": "203", "messages": "204", "suppressedMessages": "205", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "a4e7d75f3f7a61def00f8565ffbc634e", {"filePath": "206", "messages": "207", "suppressedMessages": "208", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "8d26d2e407230e15554fc3faa216a811", {"filePath": "209", "messages": "210", "suppressedMessages": "211", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "ab7a6d6b31f9a05654215a194fa2b29f", {"filePath": "212", "messages": "213", "suppressedMessages": "214", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "6b251aa083050a31fe2e18430d84d448", {"filePath": "215", "messages": "216", "suppressedMessages": "217", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "6f01231781b806f186c0072bf7a0bd6c", {"filePath": "218", "messages": "219", "suppressedMessages": "220", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "32142c5cf9f1677b0ac939971a81d634", {"filePath": "221", "messages": "222", "suppressedMessages": "223", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "4987fe97f7d0e7ee329918af58c43003", {"filePath": "224", "messages": "225", "suppressedMessages": "226", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "392ae1b0e798e678f5edd2248a077e7b", {"filePath": "227", "messages": "228", "suppressedMessages": "229", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "21b538e63a6a0bd02cd2c0742ce02120", {"filePath": "230", "messages": "231", "suppressedMessages": "232", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "afedc3b7ebc3ccfe96661d9b2f335835", {"filePath": "233", "messages": "234", "suppressedMessages": "235", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "acb51a1347d2aa616bc93d2278520ed6", {"filePath": "236", "messages": "237", "suppressedMessages": "238", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "f604f1971e8562e19f45e1f3aaddc964", {"filePath": "239", "messages": "240", "suppressedMessages": "241", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1d9b27708eeb7cea15384b386f933cff", {"filePath": "242", "messages": "243", "suppressedMessages": "244", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "ae4a35768fcaaf923b311042b4935ba9", {"filePath": "245", "messages": "246", "suppressedMessages": "247", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "e1602ec68af0f9b7a1cfa3284dc584d8", {"filePath": "248", "messages": "249", "suppressedMessages": "250", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "f4e540516b972c5c40f65b51a2ff8b44", {"filePath": "251", "messages": "252", "suppressedMessages": "253", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "0ddd30b6ee09102f7900da3b841734a8", {"filePath": "254", "messages": "255", "suppressedMessages": "256", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "446e1f57a7d47e2e1d9aecdc0bdacfb1", {"filePath": "257", "messages": "258", "suppressedMessages": "259", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "cfe85686ee8c28b11af1637519fd61d6", {"filePath": "260", "messages": "261", "suppressedMessages": "262", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "beade01575306d20f0ad99c372ac745c", {"filePath": "263", "messages": "264", "suppressedMessages": "265", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "eeaa6d10e7d631c77c33c75182c9f1b4", {"filePath": "266", "messages": "267", "suppressedMessages": "268", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "77b99e675c364c4f71c61db2a3945422", {"filePath": "269", "messages": "270", "suppressedMessages": "271", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "68b644cc1791fb65f043c491a3625518", {"filePath": "272", "messages": "273", "suppressedMessages": "274", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "70057eabca0c05486621fc96ed5bfcb5", {"filePath": "275", "messages": "276", "suppressedMessages": "277", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "e67da7f8467c1984ebd1414ab13bd92b", {"filePath": "278", "messages": "279", "suppressedMessages": "280", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "ad535060947fbad91b351aa9f80eb743", {"filePath": "281", "messages": "282", "suppressedMessages": "283", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "2c21db1ea5d3cc2734bd2705498b43a7", {"filePath": "284", "messages": "285", "suppressedMessages": "286", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "796dac0890b31f1871508f3940f604f5", {"filePath": "287", "messages": "288", "suppressedMessages": "289", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "a8722e03f58f2bc6f2f83b25b039ca49", {"filePath": "290", "messages": "291", "suppressedMessages": "292", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "47282e4925e9b7bc93e9b68a555f3f1c", {"filePath": "293", "messages": "294", "suppressedMessages": "295", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "c76783fed6283b2514e44eefb1803d7f", {"filePath": "296", "messages": "297", "suppressedMessages": "298", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "951e4f581d4bd0461e0efe97fe45fd70", {"filePath": "299", "messages": "300", "suppressedMessages": "301", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "f5051dbe65cab147d89ef4316bc14b1b", {"filePath": "302", "messages": "303", "suppressedMessages": "304", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "64c6fd03a0e319e1c854170b95dc8e61", {"filePath": "305", "messages": "306", "suppressedMessages": "307", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "c8ed6eebe2d2b2b0258d5632e428833a", {"filePath": "308", "messages": "309", "suppressedMessages": "310", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "288340c543cc95e64d4acc5fa9989b42", {"filePath": "311", "messages": "312", "suppressedMessages": "313", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "f22ae8a2237e8012775f01a7d5429154", {"filePath": "314", "messages": "315", "suppressedMessages": "316", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "7ede8e77bb0440decf5a8f00c33ac820", {"filePath": "317", "messages": "318", "suppressedMessages": "319", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "289725a76ed6a43cb4a6eec5a5aee86b", {"filePath": "320", "messages": "321", "suppressedMessages": "322", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "489183ec4aadebfe3705dd043b717fd7", {"filePath": "323", "messages": "324", "suppressedMessages": "325", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/Desktop/RADIX/CLN/Repositorios/Notifications/notifications-portal/next-env.d.ts", [], [], "/Users/<USER>/Desktop/RADIX/CLN/Repositorios/Notifications/notifications-portal/next.config.js", [], [], "/Users/<USER>/Desktop/RADIX/CLN/Repositorios/Notifications/notifications-portal/src/common/clients/dataFetchClient.ts", [], [], "/Users/<USER>/Desktop/RADIX/CLN/Repositorios/Notifications/notifications-portal/src/common/configurations/auth.ts", [], [], "/Users/<USER>/Desktop/RADIX/CLN/Repositorios/Notifications/notifications-portal/src/common/configurations/endpoints.ts", [], [], "/Users/<USER>/Desktop/RADIX/CLN/Repositorios/Notifications/notifications-portal/src/common/configurations/enviroment.ts", [], [], "/Users/<USER>/Desktop/RADIX/CLN/Repositorios/Notifications/notifications-portal/src/common/configurations/utils.ts", [], [], "/Users/<USER>/Desktop/RADIX/CLN/Repositorios/Notifications/notifications-portal/src/common/contexts/TemplatesContext.tsx", [], [], "/Users/<USER>/Desktop/RADIX/CLN/Repositorios/Notifications/notifications-portal/src/common/contexts/UserContext.tsx", [], [], "/Users/<USER>/Desktop/RADIX/CLN/Repositorios/Notifications/notifications-portal/src/common/factories/msal-factory.ts", [], [], "/Users/<USER>/Desktop/RADIX/CLN/Repositorios/Notifications/notifications-portal/src/common/hooks/useApplicationRequest.tsx", [], [], "/Users/<USER>/Desktop/RADIX/CLN/Repositorios/Notifications/notifications-portal/src/common/hooks/useAuthToken.tsx", [], ["326"], "/Users/<USER>/Desktop/RADIX/CLN/Repositorios/Notifications/notifications-portal/src/common/hooks/useChannelsRequest.tsx", [], [], "/Users/<USER>/Desktop/RADIX/CLN/Repositorios/Notifications/notifications-portal/src/common/hooks/useGetCountTemplateNameRequest.tsx", [], [], "/Users/<USER>/Desktop/RADIX/CLN/Repositorios/Notifications/notifications-portal/src/common/hooks/useRawNotificationRequest.tsx", [], [], "/Users/<USER>/Desktop/RADIX/CLN/Repositorios/Notifications/notifications-portal/src/common/hooks/useSeverityRequest.tsx", [], [], "/Users/<USER>/Desktop/RADIX/CLN/Repositorios/Notifications/notifications-portal/src/common/hooks/useTemplatesPaginated.tsx", [], [], "/Users/<USER>/Desktop/RADIX/CLN/Repositorios/Notifications/notifications-portal/src/common/layouts/BaseLayout.tsx", [], [], "/Users/<USER>/Desktop/RADIX/CLN/Repositorios/Notifications/notifications-portal/src/common/models/application.ts", [], [], "/Users/<USER>/Desktop/RADIX/CLN/Repositorios/Notifications/notifications-portal/src/common/models/applicationGroup.ts", [], [], "/Users/<USER>/Desktop/RADIX/CLN/Repositorios/Notifications/notifications-portal/src/common/models/channel.ts", [], [], "/Users/<USER>/Desktop/RADIX/CLN/Repositorios/Notifications/notifications-portal/src/common/models/notificationType.ts", [], [], "/Users/<USER>/Desktop/RADIX/CLN/Repositorios/Notifications/notifications-portal/src/common/models/notificationUser.ts", [], [], "/Users/<USER>/Desktop/RADIX/CLN/Repositorios/Notifications/notifications-portal/src/common/models/notificationUserRole.ts", [], [], "/Users/<USER>/Desktop/RADIX/CLN/Repositorios/Notifications/notifications-portal/src/common/models/paginatedTemplates.ts", [], [], "/Users/<USER>/Desktop/RADIX/CLN/Repositorios/Notifications/notifications-portal/src/common/models/rawNotification.ts", [], [], "/Users/<USER>/Desktop/RADIX/CLN/Repositorios/Notifications/notifications-portal/src/common/models/severitiy.ts", [], [], "/Users/<USER>/Desktop/RADIX/CLN/Repositorios/Notifications/notifications-portal/src/common/models/template.ts", [], [], "/Users/<USER>/Desktop/RADIX/CLN/Repositorios/Notifications/notifications-portal/src/common/utils/showError.ts", [], [], "/Users/<USER>/Desktop/RADIX/CLN/Repositorios/Notifications/notifications-portal/src/features/Tab/index.tsx", [], [], "/Users/<USER>/Desktop/RADIX/CLN/Repositorios/Notifications/notifications-portal/src/features/Tab/model/index.ts", [], [], "/Users/<USER>/Desktop/RADIX/CLN/Repositorios/Notifications/notifications-portal/src/features/Tab/style.ts", [], [], "/Users/<USER>/Desktop/RADIX/CLN/Repositorios/Notifications/notifications-portal/src/pages/_app.tsx", [], [], "/Users/<USER>/Desktop/RADIX/CLN/Repositorios/Notifications/notifications-portal/src/pages/customize-templates/components/channels-checkbox-group.tsx", [], [], "/Users/<USER>/Desktop/RADIX/CLN/Repositorios/Notifications/notifications-portal/src/pages/customize-templates/components/fields-chips.tsx", [], [], "/Users/<USER>/Desktop/RADIX/CLN/Repositorios/Notifications/notifications-portal/src/pages/customize-templates/components/severity-select.tsx", [], [], "/Users/<USER>/Desktop/RADIX/CLN/Repositorios/Notifications/notifications-portal/src/pages/customize-templates/index.tsx", [], [], "/Users/<USER>/Desktop/RADIX/CLN/Repositorios/Notifications/notifications-portal/src/pages/customize-templates/styles.ts", [], [], "/Users/<USER>/Desktop/RADIX/CLN/Repositorios/Notifications/notifications-portal/src/pages/customize-templates/useCustomizeTemplatesLogic.tsx", [], [], "/Users/<USER>/Desktop/RADIX/CLN/Repositorios/Notifications/notifications-portal/src/pages/index.tsx", [], [], "/Users/<USER>/Desktop/RADIX/CLN/Repositorios/Notifications/notifications-portal/src/pages/templates/components/lateral-menu-styles.ts", [], [], "/Users/<USER>/Desktop/RADIX/CLN/Repositorios/Notifications/notifications-portal/src/pages/templates/components/lateral-menu.tsx", [], [], "/Users/<USER>/Desktop/RADIX/CLN/Repositorios/Notifications/notifications-portal/src/pages/templates/components/notification-type-list-styles.ts", [], [], "/Users/<USER>/Desktop/RADIX/CLN/Repositorios/Notifications/notifications-portal/src/pages/templates/components/notification-type-list.tsx", [], [], "/Users/<USER>/Desktop/RADIX/CLN/Repositorios/Notifications/notifications-portal/src/pages/templates/components/select-application-styles.ts", [], [], "/Users/<USER>/Desktop/RADIX/CLN/Repositorios/Notifications/notifications-portal/src/pages/templates/components/select-application.tsx", [], [], "/Users/<USER>/Desktop/RADIX/CLN/Repositorios/Notifications/notifications-portal/src/pages/templates/components/table-pagination.tsx", [], [], "/Users/<USER>/Desktop/RADIX/CLN/Repositorios/Notifications/notifications-portal/src/pages/templates/components/templates-table-container-styles.ts", [], [], "/Users/<USER>/Desktop/RADIX/CLN/Repositorios/Notifications/notifications-portal/src/pages/templates/components/templates-table-container.tsx", [], [], "/Users/<USER>/Desktop/RADIX/CLN/Repositorios/Notifications/notifications-portal/src/pages/templates/components/templates-table-styles.ts", [], [], "/Users/<USER>/Desktop/RADIX/CLN/Repositorios/Notifications/notifications-portal/src/pages/templates/components/templates-table.tsx", [], [], "/Users/<USER>/Desktop/RADIX/CLN/Repositorios/Notifications/notifications-portal/src/pages/templates/index.tsx", [], [], "/Users/<USER>/Desktop/RADIX/CLN/Repositorios/Notifications/notifications-portal/src/services/applicationService.ts", [], [], "/Users/<USER>/Desktop/RADIX/CLN/Repositorios/Notifications/notifications-portal/src/services/templateService.ts", [], [], {"ruleId": "327", "severity": 1, "message": "328", "line": 24, "column": 8, "nodeType": "329", "endLine": 24, "endColumn": 14, "suggestions": "330", "suppressions": "331"}, "react-hooks/exhaustive-deps", "React Hook useCallback has a missing dependency: 'scopes'. Either include it or remove the dependency array.", "ArrayExpression", ["332"], ["333"], {"desc": "334", "fix": "335"}, {"kind": "336", "justification": "337"}, "Update the dependencies array to be: [msal.instance, scopes]", {"range": "338", "text": "339"}, "directive", "", [878, 884], "[msal.instance, scopes]"]