{"plugins": ["unused-imports"], "extends": ["next/core-web-vitals", "plugin:react-hooks/recommended", "prettier"], "rules": {"semi": ["error", "never"], "no-trailing-spaces": ["warn", {"skipBlankLines": true}], "react-hooks/rules-of-hooks": "error", "react-hooks/exhaustive-deps": 0, "unused-imports/no-unused-imports": "error", "prefer-const": ["error", {"destructuring": "any", "ignoreReadBeforeAssign": false}], "no-var": "error", "quotes": ["error", "single", {"avoidEscape": true, "allowTemplateLiterals": true}]}}