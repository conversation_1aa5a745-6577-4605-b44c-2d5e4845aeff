# Use an official Node.js LTS version as the base image
FROM node:lts-alpine

ARG ARG_ENVIRONMENT
ENV ENVIRONMENT=$ARG_ENVIRONMENT

# Set the working directory to /app
WORKDIR /app

# Copy the rest of the application code to the container
COPY . .

RUN echo "Build docker Image for environment ${ENVIRONMENT}"

# Install dependencies
RUN npm install

# Build the Next.js application
RUN npm run build:${ENVIRONMENT}

# Set the environment variable for production
ENV NODE_ENV=${ENVIRONMENT}

# Start the Next.js application
CMD ["npm", "start"]