{"app": {"title": "Notification", "locale_switcher": {"de": "German", "en": "English", "pt": "Portuguese"}, "common": {"search": "Search", "filters": "Filters", "applyFilters": "Apply Filters", "cancel": "Cancel", "confirm": "Confirm", "continue": "Continue", "proceed": "Proceed Anyway", "stayOnPage": "Stay On Page", "save": "Save", "reset": "Reset", "apply": "Apply", "notFound": "Not found", "minutesAgo": "Minutes ago", "send": "SEND", "logOut": "Log Out", "dontHaveAccess": "You don't have access to this content.", "youCanRequest": "You can request access by clicking", "clickHere": "here.", "applyFilter": "Apply Filter", "clear": "Clear", "clearAll": "Clear all", "finish": "Finish", "continueEditing": "Continue Editing", "create": "Create", "site": "Site", "roleSite": "Role Site", "roleApplication": "Role Application", "unit": "Unit", "team": "Team", "reportingLocation": "Reporting Location", "allowlist": "Allowlist", "blocklist": "Blocklist", "and": "and", "months": {"january": "January", "february": "February", "march": "March", "april": "April", "may": "May", "june": "June", "july": "July", "august": "August", "september": "September", "october": "October", "november": "November", "december": "December"}, "days": {"sunday": "Sunday", "monday": "Monday", "tuesday": "Tuesday", "wednesday": "Wednesday", "thursday": "Thursday", "friday": "Friday", "saturday": "Saturday"}}, "menu": {"notifications": "Notifications", "templates": "Templates"}, "steps": {"one": "Content", "two": "Recipients", "three": "Sending and Customization"}, "templates": {"title": "Templates", "adminTemplates": "<PERSON><PERSON>", "customizeTemplate": "Customize Template", "myTemplates": "My Templates", "sendTo": "Send To", "addFields": "Add Fields", "channels": "Channels", "frequencyOption": "Frequency", "severity": "Severity", "editedBy": "Edited By", "editedAt": "Edited At", "advancedSearch": "Advanced Search", "users": "users", "allUsers": "All Users", "selectedUsers": "Selected Users", "externalUsers": "External Users", "externalUsersOptional": "External Users (Optional)", "owner": "Owner", "assignee": "Assignee", "totalUsersSelected": "Total users selected", "external": "External", "copy": " (Copy 1)", "required": "Required", "confirmation": "Confirmation", "messageVariables": "Message Variables", "generalInfo": "General Info", "sideMenu": {"notificationTypes": "Notification Types", "app": "App"}, "alerts": {"noTemplateForThisNotificationType": "No template detected for this notification type", "noApplicationsOrNotificationsTypesAvailable": "No applications or notification types available", "noTemplatesAvaliable": "No templates available", "deleteTemplate": "Delete template", "AreYouSureYouWantToDeleteThisTemplate": "Are you sure? If you delete the template it will affect all users who receive notifications related to that template.", "userCustomization": "User Customization", "textSelectAttrs": "Select which attributes the user can customize. If none is selected, the user will use only the default template.", "conditionals": "Conditionals", "changesWillBeLostContinue": "Changes will be lost. Do you wish to continue?", "unsavedChanges": "Unsaved changes will be lost. Do you want to stay on this page or proceed without saving?", "missingFields": "Missing fields", "selectUserOrGroups": "Select User or Groups of Users", "youCanClickonEachGroup": "You can click on each group to expand the emails and send the notification to people in the group separately", "invalidEmail": "Please, enter a valid email", "duplicateWarning": "You are about to copy this template and create a new one. You will be redirected to the template creation page using the current template as base. Do you want to proceed?", "newDuplicateWarning": "You're about to copy this template and create a new one. Enter the name of the new template below and you'll be redirected to the creation page.", "missingNotificationType": "Notification Type Missing", "missingTemplateName": "Template Name", "duplicateTemplateName": "Please, change the Template Name", "missingSendTo": "Send To", "missingText": "Text", "keepRecipientInDuplicate": "Keep recipient list in duplicated template", "invalidConditions": "Please, fill all the conditions or remove the unfilled", "duplicateSuccess": "Template duplicated successfully", "onDemandFrequency": "All notifications will be displayed on the Notifications screen with the on-demand frequency.", "htmlMVariable": "Note: tables or HTML codes will only be considered in Email and TEAMS channels", "subjectBlank": "If blank, the default subject will be used.", "smsMessageText": "If the chosen sending frequency is too long and there are many notifications to be sent at once, only the most recent notification will be sent by SMS. Furthermore, SMS message only accepts 160 characters.", "teamsMessageText": "Users with a non-Celanese email address will not receive the message via Teams", "sendTo": "The user emails defined in the source application for a specific Notification Type must exist in the template's email list so that the notification can be sent. The system allows you to add Groups that have at least one associated user. If the Group does not appear in the list, please check this in the User Management app.", "notificationsEntities": "Notifications will be sent to selected users and specific entities:", "addedAllUsers": "Have you added all that users you want? You can finish now or go back to continue editing.", "userCustomizationTooltip": "If enabled, users can customize their notification frequency and channels. Changes affect only their own preferences", "channelsTooltip": "Select where users will receive notifications: Teams, Email, or SMS. Please note that SMS is available only in the Americas, has a 160 character limit and if the chosen send frequency is too long and there are many notifications to be sent at once, only the most recent notification will be sent by SMS.", "conditionalsTooltip": "Select specific conditions for sending the notification. These conditions must match the data available in the payload.", "group": {"createStart": "You are about to create a group with all the users selected from the", "createEnd": "Please enter a name and a description for this new group in the fields below.", "newGroup": "You're about to create a new group. You can start from scratch or use an existing group as a base.", "editConfirmationTitle": "Are you sure you want to proceed with editing this group?", "editConfirmationDescription": "This change will apply to all templates where the group is active:", "deleteConfirmationTitle": "Are you sure you want to proceed with deleting this group?", "deleteConfirmationDescription": "Deleting this group will remove it from the following templates where it is active:"}, "noUsersFound": "No users were found for the selected filters", "noOptions": "No options"}, "frequency": {"minutes": "Minutes", "hours": "Hours", "selectTime": "Select Time", "endsIn": "Ends in", "startIn": "Start in", "repeatEvery": "Repeat every", "pleaseSelectTime": "Please select a Start and a End time", "pleaseSelectMonths": "Please Select at Least One Month", "pleaseSelectDays": "Please Select at Least One Day and the Desired Time", "repeatEveryMonth": "Repeat every month", "everyDay": "Every day", "repeatEveryWeek": "Repeat every week", "occursFrom": "occurs from", "occurs": "occurs", "to": "to", "inIntervalsOf": "in intervals of", "at": "at", "atDays": "at days", "frequencyTypes": {"onDemand": "On demand", "byMinute": "By minute", "byHour": "By hour", "weekly": "Weekly", "monthly": "Monthly", "title": "Frequency Type"}}, "buttons": {"createTemplate": "Create Template", "editTemplate": "Edit Template", "enableCreateTemplateInfo": "You need to select a Notification Type to be able to create a template", "enterTemplateName": "Enter Template name", "back": "Back", "save": "Save", "text": "Text", "example": "Example", "frequency": "Frequency", "selectSendTo": "Select User or Groups of Users", "userSearch": "User Search", "newCondition": "New Condition", "externalUsersInsert": "Insert external users", "duplicateTemplate": "Duplicate Template", "next": "Next"}, "table": {"application": "Application", "channels": "Channels", "templateName": "Template Name", "actions": "Actions", "sendTo": "Send To", "editedBy": "Edited By", "editedAt": "Edited At"}, "conditionals": {"andOrOr": "And / Or", "selectVariable": "Select a Variable", "operator": "Operator", "value": "Value", "Variable": "Variable", "addMore": "+ CONDITIONAL"}, "emailSubject": "Email Subject", "tooltips": {"messageVariables": "These placeholders will be replaced with real values when the message is sent, based on the information received in the payload.", "allowlist": "Manually add users or search with filters by clicking on advanced search.", "blocklist": "Insert the recipients emails here if you do not want them to receive the notification.", "group": {"selectRecipients": "Select one or more groups to add to the template. The selected groups will be displayed in the field and can be removed at any time. Click on a group to view its details.", "create": "To create a group, you must first select the recipients.", "edit": "If you want, you can change the name and the description for this group in the fields below.", "deleteConfirmationTemplate": "This group is the only recipient of this template.", "deleteConfirmationButton": "You cannot delete this set of recipients because it is the only one in the template. A template must have at least one set of recipients to remain valid.", "canChangeStep": "To proceed, save your changes or cancel"}}, "group": {"recipients": "Recipients Groups", "description": "Description", "recipientsGroupName": "Recipients Group Name", "enterRecipientsGroupName": "Enter the recipients group name", "createGroup": "Create Group", "createNewGroup": "Create New Group", "editGroup": "Edit Group", "saveChanges": "Save Changes", "deleteGroup": "Delete Group", "updateGroup": "Update Group"}}, "notifications": {"application": "Application", "notificationType": "Notification Type", "period": "Period", "todaysNotifications": "Today's Notifications", "smartFeed": {"name": "Smart Feed", "noNotificationsToday": "No notifications today. Check the Table View if you are looking for older notifications", "sort": {"sortBy": "Sort By", "mostRecent": "most recent", "severityAsc": "severity: low to high", "severityDesc": "severity: high to low"}, "timeSeries": {"details": {"name": "Name", "description": "Description", "unit": "Unit", "id": "Id", "externalId": "External Id", "isString": "Is String", "isStep": "Is Step", "dataSet": "Data set", "linkedAssets": "Linked asset(s)", "createdAt": "Created at", "updatedAt": "Updated at", "lastReading": "Last reading", "seeMoreDetails": "SEE MORE DETAILS", "isTrue": "True", "isFalse": "False"}}}, "table": {"name": "Table View", "noNotificationsAvailable": "No notifications are available. Please, check if the template settings are correct", "noNotificationsToday": "No notifications today. Check the Table View if you are looking for older notifications", "application": "Application", "site": "Site", "severity": "Severity", "date": "Date", "notificationType": "Notification Type", "notificationMessage": "Notification Message", "notificationChats": "Notification Chats"}, "chats": {"name": "Notification Chats", "writeYourComment": "Write your comment...", "comments": "comments", "htmlContentNotAvailable": "Text with HTML content is only available on smart feed details, teams and email"}, "filter": {"severity": {"low": "Low", "medium": "Medium", "high": "High"}}}, "contextualization": {"tooltip": "Contextualization"}}}