/** @type {import('next').NextConfig} */
const nextConfig = {
    staticPageGenerationTimeout: 180,
    reactStrictMode: true,
    output: 'standalone',
    pageExtensions: ['page.tsx'],
    i18n: {
        locales: ['en', 'de'],
        defaultLocale: 'en',
    },
    async redirects() {
        return [{
            source: '/',
            destination: '/notifications',
            permanent: true
        }]
    },
    transpilePackages: ['@celanese/contextualization-lib'],
}

module.exports = nextConfig
