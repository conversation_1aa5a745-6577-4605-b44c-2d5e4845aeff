{"name": "notifications-portal", "version": "0.0.1", "private": true, "scripts": {"dev": "next dev", "build": "next build", "build:dev": "env-cmd -f .env.development next build", "build:qa": "env-cmd -f .env.qa next build", "build:p": "env-cmd -f .env.p next build", "start": "node_modules/next/dist/bin/next start", "lint": "next lint", "prettier": "npx prettier --write \"**/*.(ts|tsx)\""}, "dependencies": {"@azure/msal-browser": "^3.1.0", "@azure/msal-react": "^2.0.3", "@celanese/celanese-sdk": "^1.5.2", "@celanese/ui-lib": "^1.7.0", "@celanese/contextualization-lib": "^1.17.1", "@cognite/sdk": "^8.2.0", "@emotion/react": "^11.13.3", "@emotion/styled": "^11.13.0", "@fontsource/inter": "^4.5.15", "@fontsource/roboto": "^5.0.14", "@hookform/resolvers": "^3.9.1", "@material-symbols/font-400": "^0.23.0", "@mui/icons-material": "^5.16.7", "@mui/material": "^5.16.7", "@mui/x-data-grid-pro": "^7.13.0", "@mui/x-date-pickers-pro": "^7.13.0", "@mui/x-license-pro": "^6.10.2", "@mui/x-tree-view": "^7.13.0", "@tanstack/react-query": "^4.35.7", "@types/node": "18.16.3", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "apexcharts": "^3.47.0", "axios": "^1.5.1", "dayjs": "^1.11.13", "eslint": "9.19.0", "eslint-config-next": "15.2.4", "glob-to-regexp": "^0.4.1", "graphql": "^16.6.0", "lodash": "^4.17.21", "next": "^13.5.4", "react": "^18.2.0", "react-apexcharts": "^1.4.1", "react-dom": "^18.2.0", "react-hook-form": "^7.44.3", "react-intl": "^6.4.2", "react-redux": "^9.1.2", "react-router-dom": "^6.25.1", "typescript": "5.0.4", "zod": "^3.24.0"}, "devDependencies": {"env-cmd": "^10.1.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^5.2.0", "prettier": "2.8.8"}}