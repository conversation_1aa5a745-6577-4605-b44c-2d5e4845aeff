import permissionsDataJson from './auth-guard-rules.json'

export interface AuthGuardFeature {
    feature_code: string
    feature_access_level_code: string
}

export interface AuthGuardFeaturePerApplication {
    application_code: string
    feature_name: string
    feature_access_level_code: string
}

export interface AuthGuardPermission {
    notAuthorizedMessage: string
    roleCodes: string[]
    features: AuthGuardFeature[]
}

export interface AuthGuardRoutePermission extends AuthGuardPermission {
    path: string
}

export interface AuthGuardComponentPermission extends AuthGuardPermission {
    name: string
}

export interface AuthGuardComponentPermissionPerApplication {
    name: string
    notAuthorizedMessage: string
    featuresPerApplication: AuthGuardFeaturePerApplication[]
}

export interface AuthGuardRules {
    routes: AuthGuardRoutePermission[]
    components: AuthGuardComponentPermission[]
    componentsPerApplication: AuthGuardComponentPermissionPerApplication[]
}

export const authGuardRules: AuthGuardRules = permissionsDataJson as AuthGuardRules
