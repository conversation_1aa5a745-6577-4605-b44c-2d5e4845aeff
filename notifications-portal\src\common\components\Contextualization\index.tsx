'use client'

import { PropsWithChildren } from 'react'
import { ContextualizationProvider } from '@celanese/contextualization-lib'
import AppSpeedDial from './SpeedDial'
import { useColorMode } from '@celanese/ui-lib'
import { useAuthToken } from '@/common/hooks'

export const Contextualization = ({ children }: PropsWithChildren) => {
    const { getAuthToken } = useAuthToken()
    const DISABLE_CONTEXTUALIZATION_HIGHLIGHT_BY_DEFAULT = true
    const colorMode = useColorMode()
    const token = async () => {
        const result = await getAuthToken()
        return Array.isArray(result) ? result[0] : result
    }

    return (
        <ContextualizationProvider
            getAuthToken={token}
            colorMode={colorMode}
            turnOffHighlightByDefault={DISABLE_CONTEXTUALIZATION_HIGHLIGHT_BY_DEFAULT}
        >
            <AppSpeedDial />
            <div className="contextualizableArea">{children}</div>
        </ContextualizationProvider>
    )
}

export default Contextualization
