import React from 'react'
import * as S from './styles'
import { translate } from '@celanese/celanese-sdk'
import { enviroment } from '@/common/configurations/enviroment'
import { HeaderNavbarContextParams } from '@/common/contexts/HeaderContex'
import permissionsDataJson from '../../../auth/auth-guard-rules.json'

export default function ErrorScreen() {
    const redirectUser = () => {
        const baseUrl = enviroment.requestAccessUrl || ''
        const { selectedPlant} = HeaderNavbarContextParams()

                if (baseUrl) {

                    const url = new URL(baseUrl)
                    const siteCode = selectedPlant.externalId.includes('-') ? selectedPlant.externalId.split('-').pop()! : selectedPlant.externalId
                    const featureCode = permissionsDataJson.components[0].features[0].feature_code

                    url.searchParams.append('application', 'APP-NTF')
                    url.searchParams.append('site', siteCode)
                    url.searchParams.append('featureCode', featureCode)
                    
                    window.open(url.toString(), '_blank')
                }
    }

    return (
        <S.ContainerErrorScreen>
            <S.ErrorIcon />
            <S.HeadingPrimary>{translate('app.common.dontHaveAccess')}</S.HeadingPrimary>
            <br />
            <S.Secondary>
                {translate('app.common.youCanRequest')}{' '}
                <S.Link onClick={redirectUser}>{translate('app.common.clickHere')}</S.Link>
            </S.Secondary>
        </S.ContainerErrorScreen>
    )
}
