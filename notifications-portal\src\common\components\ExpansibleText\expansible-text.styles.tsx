import { CSSObject } from '@emotion/react'

export const textStyle = (maxWidth: number, fontSize: string, showHover: boolean, isAdminLevel: boolean): CSSObject => {
    return {
        fontSize: fontSize,
        textOverflow: 'ellipsis',
        whiteSpace: 'nowrap',
        overflow: showHover ? 'hidden' : 'unset',
        maxWidth: showHover ? `${maxWidth}px` : 'unset',
        width: 'fit-content',
        '& svg': {
            transform: 'translate(-3px, 5px)',
            color: 'warning.light',
        },
        ':hover': showHover
            ? {
                  overflow: 'visible',
                  whiteSpace: 'normal',
                  position: isAdminLevel ? 'absolute' : 'unset',
                  border: '1px solid',
                  borderColor: 'divider',
                  top: 0,
                  zIndex: 5,
              }
            : {},
    }
}
