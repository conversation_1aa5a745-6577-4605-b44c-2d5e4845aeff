import { Typography } from '@mui/material'
import { useEffect, useRef, useState } from 'react'
import { textStyle } from './expansible-text.styles'

export default function ExpansibleText({
    text,
    maxWidth,
    fontSize = '14px',
    startIcon,
    isAdminLevel,
}: Readonly<{
    text: any
    maxWidth: number
    fontSize?: string
    startIcon?: React.JSX.Element
    isAdminLevel?: boolean
}>) {
    const textRef = useRef<HTMLSpanElement>(null)
    const [showHover, setShowHover] = useState(false)

    useEffect(() => {
        const clientWidth = textRef.current?.getBoundingClientRect().width
        if (clientWidth && clientWidth > maxWidth) {
            setShowHover(true)
        }
    }, [textRef.current?.clientWidth, maxWidth])

    return (
        <Typography ref={textRef} sx={textStyle(maxWidth, fontSize, showHover, isAdminLevel ?? true)}>
            {startIcon || null}
            {text}
        </Typography>
    )
}
