import { ClnCheckbox } from '@celanese/ui-lib'
import { CheckboxItem } from '@celanese/ui-lib'
import { Box, Divider, SxProps, Typography } from '@mui/material'

interface FilterCheckboxSectionProperties {
    label: string
    items: CheckboxItem[]
    value: CheckboxItem[]
    onChange: (items: CheckboxItem[]) => void
    sx?: SxProps
}

export default function FilterCheckboxSection({ label, items, value, onChange, sx }: FilterCheckboxSectionProperties) {
    return (
        <Box sx={sx}>
            <Typography>{label}</Typography>
            <Divider />
            <ClnCheckbox items={items} value={value} onChange={onChange} />
        </Box>
    )
}
