import Button from '@mui/material/Button'
import Dialog from '@mui/material/Dialog'
import DialogActions from '@mui/material/DialogActions'
import DialogContent from '@mui/material/DialogContent'
import DialogContentText from '@mui/material/DialogContentText'
import { translate } from '@celanese/celanese-sdk'
import * as styles from './styles'
import { Dispatch, SetStateAction } from 'react'

type MessageContainerProps = {
    showModalLostData: boolean,
    setShowModalLostData: Dispatch<SetStateAction<boolean>>,
    navigateBack: () => void
}

const LostDataDialog: React.FC<MessageContainerProps> = ({
    showModalLostData,
    setShowModalLostData,
    navigateBack
}) => {
    return (
        <Dialog
            open={showModalLostData}
            onClose={() => setShowModalLostData(false)}
            aria-labelledby="alert-dialog-title"
            aria-describedby="alert-dialog-description"
            sx={{
                '& .MuiDialog-paper': {
                    width: '429px',
                }
            }}
        >
            <DialogContent>
                <DialogContentText id="alert-dialog-description" sx={styles.unsavedChanges}>
                    {translate('app.templates.alerts.unsavedChanges')}
                </DialogContentText>
            </DialogContent>
            <DialogActions>
                <Button onClick={() => setShowModalLostData(false)} color="primary" sx={{ textTransform: 'uppercase' }}>
                    {translate('app.common.stayOnPage')}
                </Button>
                <Button onClick={navigateBack} variant='contained' autoFocus sx={{ textTransform: 'uppercase' }}>
                    {translate('app.common.proceed')}
                </Button>
            </DialogActions>
        </Dialog>
    )
}

export default LostDataDialog