import { CSSObject } from '@emotion/react'

export const textStyle = (maxWidth: number, fontSize: string, showHover: boolean): CSSObject => {
    return {
        fontSize: fontSize,
        textOverflow: 'ellipsis',
        whiteSpace: 'nowrap',
        overflow: showHover ? 'hidden' : 'unset',
        maxWidth: showHover ? `${maxWidth}px` : 'unset',
        width: 'fit-content',
        ':hover': showHover
            ? {
                  overflow: 'visible',
                  whiteSpace: 'normal',
                  position: 'absolute',
                  border: '1px solid',
                  borderColor: 'divider',
                  top: '2px',
              }
            : {},
    }
}
