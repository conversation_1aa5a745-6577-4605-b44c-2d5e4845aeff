import { Box, SxProps, Typography } from '@mui/material'
import React from 'react'

export type Message = {
    text: string
    icon: React.ReactNode
    iconColor?: string
    backgroundColor?: string
}

type MessageContainerProps = {
    messages: Message[]
    sx?: SxProps
}

const MessageContainer: React.FC<MessageContainerProps> = ({ messages, sx }) => {
    return (
        <Box
            sx={{
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'left',
            }}
        >
            {messages.map((message, index) => (
                <Box
                    key={index}
                    sx={{
                        display: 'flex',
                        alignItems: 'left',
                        padding: 2,
                        borderRadius: 1,
                        backgroundColor: 'grey.200',
                        color: 'text.primary',
                        ...sx,
                    }}
                >
                    <Box sx={{ marginRight: 1 }}>{message.icon}</Box>
                    <Typography>{message.text}</Typography>
                </Box>
            ))}
        </Box>
    )
}

export default MessageContainer
