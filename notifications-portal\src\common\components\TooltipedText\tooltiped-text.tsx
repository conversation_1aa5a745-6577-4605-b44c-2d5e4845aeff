import { ClnTooltip } from '@celanese/ui-lib'
import { Typography } from '@mui/material'
import { useEffect, useRef, useState } from 'react'
import * as styles from './tooltiped-text.styles'

interface TooltipedTextProps {
    text: string
    maxWidth: number
    fontSize: string
    tooltipPlacement:
        | 'top'
        | 'right'
        | 'bottom'
        | 'left'
        | 'bottom-end'
        | 'bottom-start'
        | 'left-end'
        | 'left-start'
        | 'right-end'
        | 'right-start'
        | 'top-end'
        | 'top-start'
        | undefined
}

export default function TooltipedText({ text, maxWidth, fontSize, tooltipPlacement }: TooltipedTextProps) {
    const textRef = useRef<HTMLSpanElement>(null)
    const [showHover, setShowHover] = useState(false)

    useEffect(() => {
        const clientWidth = textRef.current?.getBoundingClientRect().width
        if (clientWidth && clientWidth > maxWidth) {
            setShowHover(true)
        }
    }, [textRef.current?.clientWidth, maxWidth])

    return showHover ? (
        <ClnTooltip placement={tooltipPlacement} arrow={true} title={text}>
            <Typography ref={textRef} sx={styles.tooltipedText(maxWidth, fontSize, showHover)}>
                {text}
            </Typography>
        </ClnTooltip>
    ) : (
        <Typography ref={textRef} sx={styles.tooltipedText(maxWidth, fontSize, showHover)}>
            {text}
        </Typography>
    )
}
