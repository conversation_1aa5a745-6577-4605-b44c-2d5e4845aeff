import axios, { AxiosError, AxiosResponse } from 'axios'
import { useAuthToken } from '@/common/hooks'
import { enviroment } from './enviroment'

export type RequestError = AxiosError

const apiService = axios.create({
    baseURL: enviroment.apiBaseURL,
})

apiService.interceptors.request.use(async (config) => {
    const { getAuthToken } = useAuthToken()
    const token = await getAuthToken()
    config.headers.Authorization = `Bearer ${token[1]}`
    return config
})

export async function get<T>(endpoint: string): Promise<T> {
    try {
        const response: AxiosResponse<T> = await apiService.get(endpoint)
        return response.data
    } catch (error) {
        throw error
    }
}

export async function getWithParams<T>(endpoint: string, params?: Record<string, any>): Promise<T> {
    try {
        const response: AxiosResponse<T> = await apiService.get(endpoint, { params })
        return response.data
    } catch (error) {
        throw error
    }
}

export const post = async <T>(endpoint: string, data: any): Promise<T> => {
    try {
        const response: AxiosResponse<T> = await apiService.post(endpoint, data)
        return response.data
    } catch (error) {
        throw error
    }
}

export default apiService