import { enviroment } from '@/common/configurations/enviroment'

export const msalConfig = {
    auth: {
        clientId: enviroment.msalClientId ?? '',
        authority: enviroment.msalAuthority ?? '',
        redirectUri: '/',
    },
    system: {
        tokenRenewalOffsetSeconds: 30 * 60
    }
}

export const msalScopes = enviroment.msalScopes
export const msalGraphScopes = enviroment.msalGraphScopes
