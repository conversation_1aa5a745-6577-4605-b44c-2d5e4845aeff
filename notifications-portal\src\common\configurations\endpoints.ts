import { enviroment } from './enviroment'

const baseURL = enviroment.apiBaseURL
const limit = enviroment.userSearchLimit

const applicationURI = `${baseURL}/application`
const templatePaginatedURI = `${baseURL}/notification-template/by_filter_paginated`
const severitiesURI = `${baseURL}/notification-severities`
const channelsURI = `${baseURL}/notification-channels`
const lastedNotificationEventURI = `${baseURL}/notification-event/{notification_type_external_id}/last/{count}`
const deleteTemplateURI = (externalId: string, isAdminEdition: boolean) =>
    `${baseURL}/notification-template/${externalId}/${isAdminEdition}`
const getTemplateByIdURI = (externalId: string, isAdminEdition: boolean) =>
    `${baseURL}/notification-template/${externalId}/${isAdminEdition}`
const getTemplateByNotificationApplicationGroupIdURI = (externalId: string) =>
    `${baseURL}/notification-template/by_notification_application_group_id/${externalId}`
const userSearchURI = (term: string, application: string) => `${baseURL}/user/search/${term}/${application}/${limit}`
const saveTemplateURI = `${baseURL}/notification-template`
const getNotificationsOnScreenURI = `${baseURL}/notification-on-screen`
const getUnreadNotificationsNumberURI = `${baseURL}/notification-on-screen/notification_not_visualized_amount`
const updateNotificationLastAccessURI = `${baseURL}/notification-on-screen/notification_last_access`
const chatMessageURI = (externalId: string) => `${baseURL}/notification-on-screen/chat/${externalId}`
const getTimeSeriesURI = (externalId: string) => `${baseURL}/timeseries/${externalId}`
const notificationEventURI = `${baseURL}/notification-event`
const notificationTypeURI = `${baseURL}/notification-type`
const getSendToByNotificationTypeURI = `${baseURL}/notification-template/send_to`
const reportingSitesURI = () => `${baseURL}/reporting-sites`
const reportingUnitURI = (reportingSite: string) => `${baseURL}/reporting-unit/${reportingSite}`
const reportingLocationURI = () => `${baseURL}/reporting-location`

const rolesSearchURI = (application: string, term: string) => `${baseURL}/roles/search/${application}/${term}`
const rolesSearchBySiteURI = (reportingSite: string) => `${baseURL}/roles/bysite/${reportingSite}`
const rolesSearchByApplicationURI = (application: string) => `${baseURL}/roles/byapp/${application}`

const teamURI = () => `${baseURL}/team`
const applicationGroupsURI = (application: string) => `${baseURL}/notification-application-group/${application}`
const userSearch = (pageSize: number = 1000) => `${baseURL}/user/search?pageSize=${pageSize}`

//NotificationApplicationGroup
const saveNotificationApplicationGroupURI = `${baseURL}/notification-application-group`
const getNotificationApplicationGroupURI = (application: string) => `${baseURL}/notification-application-group/${application}`
const deleteNotificationApplicationGroupURI = (externalId: string) => `${baseURL}/notification-application-group/${externalId}`

//User Management
const umBaseURL = enviroment.userManagementUrl
const userPermissionsURI = `${umBaseURL}/integration/user`

export {
    applicationURI,
    channelsURI,
    chatMessageURI,
    deleteTemplateURI,
    getNotificationsOnScreenURI,
    getTemplateByIdURI,
    getTimeSeriesURI,
    getUnreadNotificationsNumberURI,
    lastedNotificationEventURI,
    saveTemplateURI,
    severitiesURI,
    templatePaginatedURI,
    updateNotificationLastAccessURI,
    userPermissionsURI,
    userSearchURI,
    notificationEventURI,
    notificationTypeURI,
    getSendToByNotificationTypeURI,
    rolesSearchURI,
    reportingUnitURI,
    reportingLocationURI,
    reportingSitesURI,
    teamURI,
    applicationGroupsURI,
    userSearch,
    rolesSearchBySiteURI,
    rolesSearchByApplicationURI,
    saveNotificationApplicationGroupURI,
    getNotificationApplicationGroupURI,
    deleteNotificationApplicationGroupURI,
    getTemplateByNotificationApplicationGroupIdURI,
}
