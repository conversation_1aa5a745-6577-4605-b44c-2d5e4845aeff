import { TemplatesContextParams } from '@/common/contexts/TemplatesContext'
import { useRouter } from 'next/router'
import { Dispatch, ReactNode, SetStateAction, createContext, useContext, useState, useMemo } from 'react'
import { deleteTemplateURI } from '../configurations/endpoints'
import { useApiService } from '../hooks/useApiService'

interface ActionsContextType {
    isOpen: boolean
    handleClose: () => void
    confirmDeletion: (isAdminLevel: boolean) => void
    editAction: (id: string, isAdminLevel: boolean, isEditMyTemplateAdmin: boolean) => void
    viewAction: (id: string) => void
    deleteAction: (id: string) => void
    actionApplication: string | undefined
    setActionApplication: Dispatch<SetStateAction<string>>
    actionAdminLevel: boolean
    setActionAdminLevel: Dispatch<SetStateAction<boolean>>
    isLoadingDelete: boolean
}

const ActionsContext = createContext<ActionsContextType>({} as ActionsContextType)

function ActionsContextProvider({ children }: ChildrenProps) {
    const [isOpen, setIsOpen] = useState(false)
    const [selectedTemplate, setSelectedTemplate] = useState('')
    const [actionApplication, setActionApplication] = useState<string>('')
    const [actionAdminLevel, setActionAdminLevel] = useState<boolean>(false)

    const { getTemplatesData } = TemplatesContextParams()

    const router = useRouter()
    const axios = useApiService()

    const editAction = (id: string, isAdminLevel: boolean, isEditMyTemplateAdmin: boolean) => {
        let formType = ''
        if (isAdminLevel) {
            formType = btoa('edit')
        }else if(isEditMyTemplateAdmin){
            formType = btoa('editMyTemplateAdmin')
        }else{
            formType = btoa('editMyTemplate')
        }
        
        router.push({
            pathname: '/customize-templates',
            query: { id: id, a: btoa(String(isAdminLevel)), f: formType },
        })
    }

    const viewAction = (id: string) => {
        router.push({
            pathname: '/customize-templates',
            query: { id: id, f: btoa('view') },
        })
    }

    const deleteAction = (id: string) => {
        setSelectedTemplate(id)
        setIsOpen(true)
    }

    const handleClose = () => {
        setSelectedTemplate('')
        setIsOpen(false)
    }

    const [isLoadingDelete, setIsLoadingDelete] = useState(false)

    const confirmDeletion = (isAdminLevel: boolean) => {
        if (selectedTemplate.length > 0) {
            setIsLoadingDelete(true)
            axios.delete(deleteTemplateURI(selectedTemplate, isAdminLevel)).then(() => {
                getTemplatesData()
                setIsLoadingDelete(false)
            })
        }
        setSelectedTemplate('')
        setIsOpen(false)
    }

    return (
        <ActionsContext.Provider
            value={useMemo(
                () => ({
                    isOpen,
                    handleClose,
                    confirmDeletion,
                    editAction,
                    deleteAction,
                    actionApplication,
                    setActionApplication,
                    actionAdminLevel,
                    setActionAdminLevel,
                    isLoadingDelete,
                    viewAction,
                }),
                [
                    isOpen,
                    handleClose,
                    confirmDeletion,
                    editAction,
                    deleteAction,
                    actionApplication,
                    setActionApplication,
                    actionAdminLevel,
                    setActionAdminLevel,
                    isLoadingDelete,
                    viewAction,
                ]
            )}
        >
            {children}
        </ActionsContext.Provider>
    )
}

type ChildrenProps = {
    children: ReactNode
}

function ActionsContextParams() {
    const context = useContext(ActionsContext)
    return context
}

export { ActionsContextParams, ActionsContextProvider }
