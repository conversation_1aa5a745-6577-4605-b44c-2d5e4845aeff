import { ReactNode, createContext, useContext } from 'react'
import { useGetUserApplicationPermissionsRequest } from '../hooks/useGetUserApplicationPermissionsRequest'
import { Application } from '../models/application'

interface ApplicationContextType {
    applications: Application[] | undefined
    loading: boolean
}

const ApplicationContext = createContext<ApplicationContextType>({} as ApplicationContextType)

function ApplicationContextProvider({ children }: ChildrenProps) {
    const { loading, applications } = useGetUserApplicationPermissionsRequest()

    return (
        <ApplicationContext.Provider
            value={{
                applications,
                loading,
            }}
        >
            {children}
        </ApplicationContext.Provider>
    )
}

function ApplicationContextParams() {
    const context = useContext(ApplicationContext)
    return context
}

type ChildrenProps = {
    children: ReactNode
}

export { ApplicationContextParams, ApplicationContextProvider }
