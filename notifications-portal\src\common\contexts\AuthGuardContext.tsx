import React, { createContext, useContext } from 'react'
import { AuthGuardResult, useAuthGuard } from '../hooks/useAuthGuard'
import { useApiService } from '../hooks/useApiService'

interface AuthGuardContextType {
    checkPermissions: (componentName: string, application: string) => AuthGuardResult | undefined
}

const AuthGuardContext = createContext<AuthGuardContextType | undefined>(undefined)

export const AuthGuardProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
    const axios = useApiService()

    const { checkPermissionsFromComponentsPerApplication } = useAuthGuard()
    const checkPermissions = (componentName: string, application: string = '') => {
        return checkPermissionsFromComponentsPerApplication(componentName, application)
    }

    return <AuthGuardContext.Provider value={{ checkPermissions }}>{children}</AuthGuardContext.Provider>
}

export const usePermissions = () => {
    const context = useContext(AuthGuardContext)
    if (!context) {
        throw new Error('usePermissions should be used inside AuthGuardProvider')
    }
    return context
}
