import { PlantItem } from '@celanese/ui-lib'
import { QueryObserverResult, RefetchOptions, RefetchQueryFilters } from '@tanstack/react-query'
import { Dispatch, ReactNode, SetStateAction, createContext, useContext, useState } from 'react'
import { useUnreadNotificationsRequest } from '../hooks/useUnreadNotificationsResquest'
import { GLOBAL } from '../layouts/BaseLayout'

interface HeaderNavbarContextType {
    setSelectedPlant: Dispatch<SetStateAction<PlantItem>>
    selectedPlant: PlantItem
    unreadNotifications: string | undefined
    userName: string
    setUserName: Dispatch<SetStateAction<string>>
    refetch: <TPageData>(
        options?: (RefetchOptions & RefetchQueryFilters<TPageData>) | undefined
    ) => Promise<QueryObserverResult<string, unknown>>
}

const HeaderNavbarContext = createContext<HeaderNavbarContextType>({} as HeaderNavbarContextType)

function HeaderNavbarContextProvider({ children }: ChildrenProps) {
    const [selectedPlant, setSelectedPlant] = useState<PlantItem>(GLOBAL)
    const [userName, setUserName] = useState('')

    const { data: unreadNotifications, refetch } = useUnreadNotificationsRequest()

    return (
        <HeaderNavbarContext.Provider
            value={{
                setSelectedPlant,
                selectedPlant,
                unreadNotifications,
                userName,
                setUserName,
                refetch,
            }}
        >
            {children}
        </HeaderNavbarContext.Provider>
    )
}

function HeaderNavbarContextParams() {
    const context = useContext(HeaderNavbarContext)
    return context
}

type ChildrenProps = {
    children: ReactNode
}

export { HeaderNavbarContextParams, HeaderNavbarContextProvider }
