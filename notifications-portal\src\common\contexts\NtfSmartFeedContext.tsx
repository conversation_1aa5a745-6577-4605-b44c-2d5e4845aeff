import { Order } from '@celanese/ui-lib'
import { DateRange } from '@models/dateRangeTypes'
import dayjs, { Dayjs } from 'dayjs'
import { Dispatch, ReactNode, SetStateAction, createContext, useContext, useState } from 'react'
import { PaginatedNotifications } from '../models/paginatedNotifications'
import { HeaderNavbarContextParams } from './HeaderContex'
import { useSmartFeedPaginated } from '../hooks/useSmartFeedPaginated'

interface NtfSmartFeedContextType {
    page: number
    setPage: Dispatch<SetStateAction<number>>
    rowsPerPage: number
    setRowsPerPage: Dispatch<SetStateAction<number>>
    order: Order
    setOrder: Dispatch<SetStateAction<Order>>
    orderBy: string
    setOrderBy: Dispatch<SetStateAction<string>>
    setSearch: Dispatch<SetStateAction<string>>
    setFilterByApplication: Dispatch<SetStateAction<string | undefined>>
    setFilterByNotificationType: Dispatch<SetStateAction<string | undefined>>
    setFilterBySeverities: Dispatch<SetStateAction<string[]>>
    paginatedSmartFeed: PaginatedNotifications | undefined
    isLoading: boolean
    refetchSmartFeed: () => void
}

const NtfSmartFeedContext = createContext<NtfSmartFeedContextType>({} as NtfSmartFeedContextType)

function NtfSmartFeedContextProvider({ children }: ChildrenProps) {
    const [page, setPage] = useState(0)
    const [rowsPerPage, setRowsPerPage] = useState(10)
    const [order, setOrder] = useState<Order>('asc')
    const [orderBy, setOrderBy] = useState('')

    const [search, setSearch] = useState('')

    const [filterByPeriod, setFilterByPeriod] = useState<DateRange<Dayjs>>([dayjs().startOf('day'), dayjs()])
    const [filterByApplication, setFilterByApplication] = useState<string | undefined>(undefined)
    const [filterByNotificationType, setFilterByNotificationType] = useState<string | undefined>(undefined)
    const [filterBySeverities, setFilterBySeverities] = useState<string[]>([])

    const { selectedPlant } = HeaderNavbarContextParams()

    const { paginatedSmartFeed, isLoading, refetchSmartFeed } = useSmartFeedPaginated(
        rowsPerPage,
        page,
        order,
        orderBy,
        search,
        filterByPeriod,
        filterByApplication,
        filterByNotificationType,
        filterBySeverities,
        selectedPlant
    )

    return (
        <NtfSmartFeedContext.Provider
            value={{
                page,
                setPage,
                rowsPerPage,
                setRowsPerPage,
                order,
                setOrder,
                orderBy,
                setOrderBy,
                setSearch,
                setFilterByApplication,
                setFilterByNotificationType,
                setFilterBySeverities,
                paginatedSmartFeed,
                isLoading,
                refetchSmartFeed,
            }}
        >
            {children}
        </NtfSmartFeedContext.Provider>
    )
}

function NtfSmartFeedContextParams() {
    const context = useContext(NtfSmartFeedContext)
    return context
}

type ChildrenProps = {
    children: ReactNode
}

export { NtfSmartFeedContextParams, NtfSmartFeedContextProvider }
