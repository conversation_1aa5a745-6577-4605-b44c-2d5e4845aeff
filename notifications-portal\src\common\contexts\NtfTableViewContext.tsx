import { Order } from '@celanese/ui-lib'
import dayjs from 'dayjs'
import { Dispatch, ReactNode, SetStateAction, createContext, useContext, useState } from 'react'
import { useNotificationsOnScreenPaginated } from '../hooks/useNotificationsOnScreenPaginated'
import { PaginatedNotifications } from '../models/paginatedNotifications'
import { HeaderNavbarContextParams } from './HeaderContex'
import { DateRange } from '../models/dateRangeTypes'

interface NtfTableViewContextType {
    page: number
    setPage: Dispatch<SetStateAction<number>>
    rowsPerPage: number
    setRowsPerPage: Dispatch<SetStateAction<number>>
    order: Order
    setOrder: Dispatch<SetStateAction<Order>>
    orderBy: string
    setOrderBy: Dispatch<SetStateAction<string>>
    setSearch: Dispatch<SetStateAction<string>>
    setFilterByPeriod: Dispatch<SetStateAction<DateRange<dayjs.Dayjs>>>
    setFilterByApplication: Dispatch<SetStateAction<string | undefined>>
    setFilterByNotificationType: Dispatch<SetStateAction<string | undefined>>
    setFilterBySeverities: Dispatch<SetStateAction<string[]>>
    paginatedNotifications: PaginatedNotifications | undefined
    isLoading: boolean
    refetchTableView: () => void
    newCommentPublished: boolean
    setNewCommentPublished: Dispatch<SetStateAction<boolean>>
    userLastAccess: string | undefined
    setUserLastAccess: Dispatch<SetStateAction<string | undefined>>
}

const NtfTableViewContext = createContext<NtfTableViewContextType>({} as NtfTableViewContextType)

function NtfTableViewContextProvider({ children }: ChildrenProps) {
    const [page, setPage] = useState(0)
    const [rowsPerPage, setRowsPerPage] = useState(10)
    const [order, setOrder] = useState<Order>('asc')
    const [orderBy, setOrderBy] = useState('')

    const [search, setSearch] = useState('')

    const [filterByPeriod, setFilterByPeriod] = useState<DateRange<dayjs.Dayjs>>([dayjs().subtract(1, 'month'), dayjs()])
    const [filterByApplication, setFilterByApplication] = useState<string | undefined>(undefined)
    const [filterByNotificationType, setFilterByNotificationType] = useState<string | undefined>(undefined)
    const [filterBySeverities, setFilterBySeverities] = useState<string[]>([])
    const [userLastAccess, setUserLastAccess] = useState<string>()

    const [newCommentPublished, setNewCommentPublished] = useState<boolean>(false)

    const { selectedPlant } = HeaderNavbarContextParams()

    const { paginatedNotifications, isLoading, refetchTableView } = useNotificationsOnScreenPaginated(
        rowsPerPage,
        page,
        order,
        orderBy,
        search,
        filterByPeriod,
        filterByApplication,
        filterByNotificationType,
        filterBySeverities,
        selectedPlant
    )

    return (
        <NtfTableViewContext.Provider
            value={{
                page,
                setPage,
                rowsPerPage,
                setRowsPerPage,
                order,
                setOrder,
                orderBy,
                setOrderBy,
                setSearch,
                setFilterByPeriod,
                setFilterByApplication,
                setFilterByNotificationType,
                setFilterBySeverities,
                paginatedNotifications,
                isLoading,
                refetchTableView,
                newCommentPublished,
                setNewCommentPublished,
                userLastAccess,
                setUserLastAccess
            }}
        >
            {children}
        </NtfTableViewContext.Provider>
    )
}

function NtfTableViewContextParams() {
    const context = useContext(NtfTableViewContext)
    return context
}

type ChildrenProps = {
    children: ReactNode
}

export { NtfTableViewContextParams, NtfTableViewContextProvider }

