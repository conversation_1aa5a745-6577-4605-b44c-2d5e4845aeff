import { Order } from '@celanese/ui-lib'
import { Dispatch, ReactNode, SetStateAction, createContext, useContext, useState, useMemo } from 'react'
import { useTemplatesPaginated } from '../hooks/useTemplatesPaginated'
import { NotificationType } from '../models/notificationType'
import { Template } from '../models/template'

interface TemplatesContextType {
    currentPageInfiniteScroll: number
    setCurrentPageInfiniteScroll: Dispatch<SetStateAction<number>>
    isLoading: boolean
    setIsLoading: Dispatch<SetStateAction<boolean>>
    notificationType: string | undefined
    setNotificationType: Dispatch<SetStateAction<string | undefined>>
    selectedApplication: string | undefined
    setSelectedApplication: Dispatch<SetStateAction<string>>
    setSubscribersIds: Dispatch<SetStateAction<(string | number)[]>>
    setChannelsIds: Dispatch<SetStateAction<(string | number)[]>>
    order: Order
    setOrder: Dispatch<SetStateAction<Order>>
    orderBy: string
    setOrderBy: Dispatch<SetStateAction<string>>
    setSearch: Dispatch<SetStateAction<string>>
    setIsAdminLevel: Dispatch<SetStateAction<boolean | undefined>>
    setApplicationsIds: Dispatch<SetStateAction<string[] | undefined>>
    applicationsIds: string[] | undefined
    tabIndex: number
    setTabIndex: Dispatch<SetStateAction<number>>
    selectedApplications: string[]
    setSelectedApplications: Dispatch<SetStateAction<string[]>>
    notificationTypeName: string
    setNotificationTypeName: Dispatch<SetStateAction<string>>
    applicationName: string
    setApplicationName: Dispatch<SetStateAction<string>>
    notificationTypeSelected: NotificationType | undefined
    setNotificationTypeSelected: Dispatch<SetStateAction<NotificationType | undefined>>
    currentCursor?: string,
    response: Template[],
    hasNextPage: boolean,
    rowsPerPageOptions: number[]
    rowsPerPageInfiniteScroll: number
    setRowsPerPageInfiniteScroll: Dispatch<SetStateAction<number>>
    getTemplatesData: (infinite?: boolean) => void
    loadingInfinityScroll: boolean
    setCurrentCursor: Dispatch<SetStateAction<string | undefined>>
    setHasNextPage: Dispatch<SetStateAction<boolean>>
    handleScrollToTop: (ref: any) => void
}

const TemplatesContext = createContext<TemplatesContextType>({} as TemplatesContextType)

function TemplatesContextProvider({ children }: ChildrenProps) {
    const rowsPerPageOptions = [5, 10, 25]
    const [currentPageInfiniteScroll, setCurrentPageInfiniteScroll] = useState(0)
    const [rowsPerPageInfiniteScroll, setRowsPerPageInfiniteScroll] = useState(rowsPerPageOptions[2])
    const [notificationType, setNotificationType] = useState<string | undefined>(undefined)
    const [selectedApplication, setSelectedApplication] = useState<string>('')
    const [subscribersIds, setSubscribersIds] = useState<(string | number)[]>([])
    const [channelsIds, setChannelsIds] = useState<(string | number)[]>([])
    const [order, setOrder] = useState<Order>('asc')
    const [orderBy, setOrderBy] = useState<string>('')
    const [search, setSearch] = useState('')
    const [isAdminLevel, setIsAdminLevel] = useState<boolean | undefined>(undefined)
    const [applicationsIds, setApplicationsIds] = useState<string[] | undefined>()
    const [tabIndex, setTabIndex] = useState(0)
    const [selectedApplications, setSelectedApplications] = useState<string[]>([])
    const [notificationTypeName, setNotificationTypeName] = useState<string>('')
    const [notificationTypeSelected, setNotificationTypeSelected] = useState<NotificationType>()
    const [applicationName, setApplicationName] = useState<string>('')
    

    const {
        isLoading,
        setIsLoading,
        currentCursor,
        response,
        hasNextPage,
        getTemplatesData,
        loadingInfinityScroll,
        setCurrentCursor,
        setHasNextPage,
    } = useTemplatesPaginated(
        notificationType,
        subscribersIds,
        channelsIds,
        order,
        orderBy,
        search,
        isAdminLevel,
        applicationsIds
    )
    
    const handleScrollToTop = (el: HTMLElement | null) => {
        if (el) {
            el.scrollTo({
                top: 0,
                behavior: 'smooth',
            })
        }
    }

    return (
        <TemplatesContext.Provider
            value={useMemo(
                () => ({
                    isLoading,
                    setIsLoading,
                    notificationType,
                    setNotificationType,
                    selectedApplication,
                    setSelectedApplication,
                    setSubscribersIds,
                    setChannelsIds,
                    order,
                    setOrder,
                    orderBy,
                    setOrderBy,
                    setSearch,
                    setIsAdminLevel,
                    setApplicationsIds,
                    applicationsIds,
                    tabIndex,
                    setTabIndex,
                    selectedApplications,
                    setSelectedApplications,
                    notificationTypeName,
                    setNotificationTypeName,
                    applicationName,
                    setApplicationName,
                    notificationTypeSelected,
                    setNotificationTypeSelected,
                    currentCursor,
                    response,
                    hasNextPage,
                    currentPageInfiniteScroll,
                    setCurrentPageInfiniteScroll,
                    rowsPerPageOptions,
                    rowsPerPageInfiniteScroll,
                    setRowsPerPageInfiniteScroll,
                    getTemplatesData,
                    loadingInfinityScroll,
                    setCurrentCursor,
                    setHasNextPage,
                    handleScrollToTop,
                }),
                [
                    isLoading,
                    setIsLoading,
                    notificationType,
                    setNotificationType,
                    selectedApplication,
                    setSelectedApplication,
                    setSubscribersIds,
                    setChannelsIds,
                    order,
                    setOrder,
                    orderBy,
                    setOrderBy,
                    setSearch,
                    setIsAdminLevel,
                    setApplicationsIds,
                    applicationsIds,
                    tabIndex,
                    setTabIndex,
                    selectedApplications,
                    setSelectedApplications,
                    notificationTypeName,
                    setNotificationTypeName,
                    applicationName,
                    setApplicationName,
                    notificationTypeSelected,
                    setNotificationTypeSelected,
                    currentCursor,
                    response,
                    hasNextPage,
                    currentPageInfiniteScroll,
                    setCurrentPageInfiniteScroll,
                    rowsPerPageOptions,
                    rowsPerPageInfiniteScroll,
                    setRowsPerPageInfiniteScroll,
                    getTemplatesData,
                    setCurrentCursor,
                    setHasNextPage,
                    handleScrollToTop,
                ]
            )}
        >
            {children}
        </TemplatesContext.Provider>
    )
}

function TemplatesContextParams() {
    const context = useContext(TemplatesContext)
    return context
}

type ChildrenProps = {
    children: ReactNode
}

export { TemplatesContextParams, TemplatesContextProvider }
