import { PublicClientApplication } from '@azure/msal-browser'
import { ReactNode, createContext, useContext } from 'react'

interface UserContextType {
    handleGetUsername: () => string
}

const UserContext = createContext<UserContextType>({} as UserContextType)

type ChildrenProps = {
    children: ReactNode
    msalInstance: PublicClientApplication
}

function UserContextProvider({ children, msalInstance }: ChildrenProps) {
    const handleGetUsername = () => {
        const currentUser = msalInstance.getActiveAccount()?.username.toLowerCase() ?? ''
        return currentUser
    }

    return <UserContext.Provider value={{ handleGetUsername }}>{children}</UserContext.Provider>
}

function UserContextParams() {
    const context = useContext(UserContext)
    return context
}

export { UserContextParams, UserContextProvider }
