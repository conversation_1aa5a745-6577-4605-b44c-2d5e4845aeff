import { <PERSON>actNode, createContext, useState } from 'react'
import { userPermissionsURI } from '../configurations/endpoints'
import { useApiService } from '../hooks/useApiService'

type ChildrenProps = {
    children: ReactNode
}

type ContextReturn = {
    rule: any
}

export const UserRuleContext = createContext<ContextReturn>({} as ContextReturn)

function UserRuleContextProvider({ children }: ChildrenProps) {
    const [rule, setRule] = useState()
    const axios = useApiService()

    if (!rule) {
        const requestBody = {
            site_external_id: 'STS-COR',
            application_code: 'APP-NTF',
            all_applications: true
        }
        axios.post(userPermissionsURI, requestBody).then((response) => {
            Object.keys(response.data).length !== 0 && response.data.constructor === Object
                ? handleSetUserRule(response.data)
                : handleSetUserRule(undefined)
        })
    }

    function handleSetUserRule(userRule: any) {
        userRule = JSON.stringify(userRule)
        setRule(userRule)
    }

    return <UserRuleContext.Provider value={{ rule }}>{children}</UserRuleContext.Provider>
}

export default UserRuleContextProvider
