import { enviroment } from '@/common/configurations/enviroment'
import axios, { AxiosInstance } from 'axios'

export const createAxiosClient = (getAuthToken: () => Promise<string | string[]>): AxiosInstance => {
    const axiosInstance = axios.create({
        baseURL: enviroment.apiBaseURL,
    })

    axiosInstance.interceptors.request.use(async (config) => {
        const token = await getAuthToken()
        config.headers.Authorization = `Bearer ${token[1]}`
        return config
    })

    return axiosInstance
}
