import { AuthGuardFeaturePerApplication, AuthGuardPermission, authGuardRules } from '@/auth/auth-guard-rules'
import { useContext } from 'react'
import { UserRuleContext } from '../contexts/UserRuleContext'
import { UserPermission, UserRolesPermission } from '../models/userPermissions'
import { stringIsNotNullOrEmpty } from '../utils/utils'

export interface AuthGuardResult {
    componentName?: string
    isAuthorized: boolean
    message: string
}

export const useAuthGuard = () => {
    const { rule } = useContext(UserRuleContext)

    function checkPermissionsFromRoutes(path: string): boolean {
        if (!rule) {
            return false
        }
        const routePermission = authGuardRules.routes.find((c) => c.path === path)
        return checkPermissions(routePermission, rule).isAuthorized
    }

    function checkPermissions(componentRules: AuthGuardPermission | undefined, userRules?: any): AuthGuardResult {
        const userPermission = JSON.parse(userRules) as UserRolesPermission
        const authResult: AuthGuardResult = {
            isAuthorized: true,
            message: '',
        }

        if (componentRules) {
            const hasRoles = componentRules.roleCodes.some((r) => userPermission.roles.some((x) => x.roleCode === r))
            const hasFeature = componentRules.features.some((f) =>
                userPermission.roles.some((x) =>
                    x.features.some(
                        (y) => y.featureCode === f.feature_code && y.featureAccessLevel === f.feature_access_level_code
                    )
                )
            )

            return {
                isAuthorized: hasRoles || hasFeature,
                message: componentRules.notAuthorizedMessage,
            }
        }

        return authResult
    }

    function checkPermissionsFromComponents(componentName: string): AuthGuardResult {
        if (rule) {
            return {
                isAuthorized: false,
                message: '',
            }
        }
        const componentPermission = authGuardRules.components.find((c) => c.name === componentName)
        return checkPermissions(componentPermission, rule)
    }

    function checkPermissionsFromComponentsPerApplication(
        componentName: string,
        application: string = ''
    ): AuthGuardResult {
        if (!rule) {
            return {
                isAuthorized: false,
                message: '',
            }
        }
        const componentPermission = authGuardRules.componentsPerApplication.find(
            (c) => c.name === componentName
        )?.featuresPerApplication

        return checkPermissionsPerApplication(componentName, application.replace(/\s/g, ''), componentPermission, rule)
    }

    function checkPermissionsPerApplication(
        componentName: string,
        application: string,
        componentRules?: AuthGuardFeaturePerApplication[],
        userRules?: any
    ): AuthGuardResult {
        const userRulesPermission = JSON.parse(userRules) as UserPermission
        const authResult: AuthGuardResult = {
            componentName: componentName,
            isAuthorized: componentName == 'My Templates' ? true : false,
            message: '',
        }

        if (userRulesPermission.applications) {
            if (componentRules && stringIsNotNullOrEmpty(application)) {
                const userPermissionPerApp = userRulesPermission.applications.find(
                    (p) => p.applicationCode == application
                )?.roles
                const features = componentRules.map((c) => c.feature_name.toUpperCase())
                const hasPermission = userPermissionPerApp
                    ? componentName == 'Edit Admin Template' || componentName == 'Create Admin Template'
                        ? userPermissionPerApp.some((a) =>
                              a.features.some(
                                  (f) =>
                                      features.includes(String(f.featureName.toUpperCase().replace(/\s/g, ''))) &&
                                      f.featureAccessLevelCode == 'EditAccess'
                              )
                          )
                        : userPermissionPerApp.some((a) =>
                              a.features.some((f) =>
                                  features.includes(String(f.featureName.toUpperCase().replace(/\s/g, '')))
                              )
                          )
                    : false
                return {
                    componentName: componentName,
                    isAuthorized: hasPermission,
                    message: '',
                }
            } else if (componentRules) {
                const features = componentRules.map((c) => c.feature_name.toUpperCase())
                const hasPermission =
                    componentName == 'Edit Admin Template' || componentName == 'Create Admin Template'
                        ? userRulesPermission.applications.some((a) =>
                              a.roles.some((r) =>
                                  r.features.some(
                                      (f) =>
                                          features.includes(String(f.featureName.toUpperCase().replace(/\s/g, ''))) &&
                                          f.featureAccessLevelCode == 'EditAccess'
                                  )
                              )
                          )
                        : userRulesPermission.applications.some((a) =>
                              a.roles.some((r) =>
                                  r.features.some((f) =>
                                      features.includes(String(f.featureName.toUpperCase().replace(/\s/g, '')))
                                  )
                              )
                          )
                return {
                    componentName: componentName,
                    isAuthorized: hasPermission,
                    message: '',
                }
            }
        }
        return authResult
    }

    return {
        checkPermissionsFromRoutes,
        checkPermissionsFromComponents,
        checkPermissionsFromComponentsPerApplication,
    }
}
