import { useCallback } from 'react'
import { useMsal } from '@azure/msal-react'
import { msalScopes, msalGraphScopes } from '../configurations/auth'

function useAuthTokenScoped(scopes: string[]): { getAuthToken: () => Promise<string | string[]> } {
    const msal = useMsal()
    const getAuthToken = useCallback(async () => {
        await msal.instance.initialize()
        const envToken = process.env.NEXT_PUBLIC_AUTH_TOKEN
        if (envToken) {
            return envToken
        }

        const account = msal.instance.getActiveAccount()
        if (!account) {
            throw Error('No active account! Verify a user has been signed in and setActiveAccount has been called.')
        }
        const result = await msal.instance.acquireTokenSilent({
            account,
            scopes: scopes,
        })
        return [result.accessToken, result.idToken]
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [msal])

    return { getAuthToken }
}

export function useAuthToken(): { getAuthToken: () => Promise<string | string[]> } {
    return useAuthTokenScoped(msalScopes)
}

export function useGraphAuthToken(): { getAuthToken: () => Promise<string | string[]> } {
    return useAuthTokenScoped(msalGraphScopes)
}
