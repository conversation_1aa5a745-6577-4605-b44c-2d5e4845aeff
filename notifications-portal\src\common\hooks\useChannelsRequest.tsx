import { useQuery } from '@tanstack/react-query'
import { Channel } from '../models/channel'
import { useApiService } from './useApiService'
import { channelsURI } from '../configurations/endpoints'

export function useChannelsRequest(fetchOnLoad: boolean, cacheToken: string[]) {
    const axios = useApiService()

    const getChannels = () => {
        return axios.get(channelsURI).then((response) => {
            return response.data.message as Channel[]
        })
    }

    return useQuery({
        queryKey: cacheToken,
        queryFn: () => getChannels(),
        enabled: fetchOnLoad,
        refetchOnWindowFocus: false,
    })
}
