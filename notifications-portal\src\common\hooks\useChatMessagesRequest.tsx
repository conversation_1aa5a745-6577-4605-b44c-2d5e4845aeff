import { useCallback, useState } from 'react'
import { chatMessageURI, notificationEventURI } from '../configurations/endpoints'
import { ChatMessages } from '../models/chatMessages'
import { useApiService } from './useApiService'

export function useGetChatMessagesRequest(externalId: string) {
    const axios = useApiService()
    const [isLoading, setIsLoading] = useState(false)

    const getChatMessages = useCallback(() => {
        setIsLoading(true)
        return axios.get(chatMessageURI(externalId)).then((response) => {
            setIsLoading(false)
            return response.data.message as ChatMessages
        })
    }, [externalId])

    return { getChatMessages, isLoading }
}

export function usePostChatMessageRequest(externalId: string) {
    const axios = useApiService()
    const [isLoading, setIsLoading] = useState(false)

    const postChatMessage = useCallback(
        (comment: string) => {
            const URI = chatMessageURI(externalId)
            setIsLoading(true)
            return axios.post(URI, { comment: comment }).then((response) => {
                setIsLoading(false)
                return response.data.message as ChatMessages
            })
        },
        [externalId]
    )

    return { postChatMessage, isLoading }
}

export function usePosChattNotificationEvent() {
    const axios = useApiService()
    const [isLoading, setIsLoading] = useState(false)

    const postChatNotificationEvent = (eventData: any) => {
        setIsLoading(true)
        return axios.post(notificationEventURI, [eventData]).then((response) => {
            setIsLoading(false)
        })
    }

    return { postChatNotificationEvent, isLoading }
}

export function useDeleteChatMessageRequest() {
    const axios = useApiService()
    const [isLoading, setIsLoading] = useState(false)

    const deleteChatMessage = (externalId: string) => {
        setIsLoading(true)
        return axios.delete(chatMessageURI(externalId)).then((response) => {
            setIsLoading(false)
        })
    }

    return { isLoading, deleteChatMessage }
}
