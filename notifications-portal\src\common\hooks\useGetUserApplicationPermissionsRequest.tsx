import { applicationURI } from '@/common/configurations/endpoints'
import { useApiService } from '@/common/hooks/useApiService'
import { Application } from '@/common/models/application'
import { useContext, useEffect, useState } from 'react'
import { UserRuleContext } from '../contexts/UserRuleContext'
import { UserPermission } from '../models/userPermissions'

export function useGetUserApplicationPermissionsRequest() {
    const { rule } = useContext(UserRuleContext)
    const [applications, setApplications] = useState<Application[]>([])
    const [loading, setLoading] = useState(false)
    const axios = useApiService()

    useEffect(() => {
        if (rule) {
            setLoading(true)
            const userPermission: UserPermission = JSON.parse(rule)
            if (userPermission) {
                const apps = userPermission.applications.map((a) => a.applicationCode) ?? []
                const requestObject = { codes: apps }
                axios
                    .post(applicationURI, requestObject)
                    .then((response) => {
                        setApplications(response.data.message as Application[])
                    })
                    .finally(() => {
                        setLoading(false)
                    })
            }
        } else {
            setLoading(true)
        }
    }, [rule])

    return { loading, applications }
}
