import { availableLanguages } from '@/common/utils/general'

export const DEFAULT_LOCALE = 'EN'

const LocaleCacheName = 'LocaleData'

export interface UseLocaleResponse {
    locale: string
    switchLocale: (locale: string) => void
}

export const useLocale = (): UseLocaleResponse => {
    let cachedLocale = DEFAULT_LOCALE

    if (typeof window !== 'undefined' && window.localStorage.getItem(LocaleCacheName) === null) {
        window.localStorage.setItem(LocaleCacheName, DEFAULT_LOCALE.toUpperCase())
    }

    if (
        typeof window !== 'undefined' &&
        window.localStorage.getItem(LocaleCacheName) !== null &&
        window.localStorage.getItem(LocaleCacheName) !== '0'
    ) {
        cachedLocale = window.localStorage.getItem(LocaleCacheName) as string
    }

    const switchLocale = (locale: string) => {
        if (typeof window !== 'undefined') {
            if (
                cachedLocale.toLowerCase() === locale.toLowerCase() ||
                availableLanguages == null ||
                !availableLanguages.some((l) => l?.code.toLowerCase() == locale.toLowerCase())
            ) {
                return
            }

            window.localStorage.setItem(LocaleCacheName, locale.toUpperCase())
        }
    }

    return { locale: cachedLocale, switchLocale }
}
