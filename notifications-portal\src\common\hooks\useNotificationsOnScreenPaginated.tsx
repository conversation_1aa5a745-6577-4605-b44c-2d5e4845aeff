import { getNotificationsOnScreenURI } from '@/common/configurations/endpoints'
import { Order, PlantItem } from '@celanese/ui-lib'
import { useEffect, useMemo, useState } from 'react'
import { GLOBAL } from '../layouts/BaseLayout'
import { PaginatedNotifications } from '../models/paginatedNotifications'
import { toCamelCase } from '../utils/utils'
import { useApiService } from './useApiService'
import { Dayjs } from 'dayjs'
import { DateRange } from '@models/dateRangeTypes'

export function useNotificationsOnScreenPaginated(
    rowsPerPage: number,
    page: number,
    order: Order,
    orderBy: string,
    search: string,
    filterByPeriod: DateRange<Dayjs>,
    filterByApplication: string | undefined,
    filterByNotificationType: string | undefined,
    filterBySeverities: string[],
    headerSite: PlantItem
) {
    const axios = useApiService()

    const request = useMemo(() => {
        const sortColumnValue = orderBy != '' ? { [toCamelCase(orderBy)]: order.toUpperCase() } : {}
        const startPeriod = filterByPeriod[0]
            ? filterByPeriod[0].hour(0).minute(0).second(0).format('YYYY-MM-DDTHH:mm:ss')
            : undefined
        const endPeriod = filterByPeriod[1]
            ? filterByPeriod[1].hour(23).minute(59).second(59).format('YYYY-MM-DDTHH:mm:ss')
            : undefined
        const filterBySite = headerSite.externalId != GLOBAL.externalId ? headerSite.externalId : undefined

        const requestObject = {
            filter: {
                sort_column: sortColumnValue,
                search: search,
                site_external_id: filterBySite,
                application_external_id: filterByApplication,
                notification_type_external_id: filterByNotificationType,
                severity_external_ids: filterBySeverities,
                start_period: startPeriod,
                end_period: endPeriod,
            },
            pagination: {
                items_per_page: rowsPerPage,
                page: page + 1,
            },
        }

        return requestObject
    }, [
        rowsPerPage,
        page,
        orderBy,
        order,
        search,
        filterByPeriod,
        filterByApplication,
        filterByNotificationType,
        filterBySeverities,
        headerSite,
    ])

    const [paginatedNotifications, setPaginatedNotifications] = useState<PaginatedNotifications | undefined>(undefined)
    const [isLoading, setIsLoading] = useState(false)

    useEffect(() => {
        setIsLoading(true)
        axios.post(getNotificationsOnScreenURI, request).then((response) => {
            setPaginatedNotifications(response.data.message as PaginatedNotifications)
            setIsLoading(false)
        })
    }, [request])

    const refetchTableView = () => {
        setIsLoading(true)
        axios.post(getNotificationsOnScreenURI, request).then((response) => {
            setPaginatedNotifications(response.data.message as PaginatedNotifications)
            setIsLoading(false)
        })
    }

    return { paginatedNotifications, isLoading, refetchTableView }
}
