import { useQuery } from '@tanstack/react-query'
import { useApiService } from './useApiService'
import { getSendToByNotificationTypeURI } from '../configurations/endpoints'
import { translate } from '@celanese/celanese-sdk'

export function useSendToByNotificationTypeRequest(
    fetchOnLoad: boolean,
    cacheToken: string[],
    notificationTypeExternalId: string
) {
    const axios = useApiService()
    
    const getSendTo = () => {
        return axios.get(getSendToByNotificationTypeURI, { params: { notificationTypeExternalId } }).then((response) => {
            return Object.entries(response.data.message).map(([value, label]) => ({
                label: label === 'allUsers' ? translate('app.templates.allUsers') : label,
                value: value,
            }))
        })
    }

    return useQuery({
        queryKey: cacheToken,
        queryFn: getSendTo,
        enabled: fetchOnLoad,
        refetchOnWindowFocus: false,
    })
}
