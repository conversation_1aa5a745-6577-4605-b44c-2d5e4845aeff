import { useQuery } from '@tanstack/react-query'
import { useApiService } from './useApiService'
import { severitiesURI } from '../configurations/endpoints'
import { Severity } from '../models/severitiy'

export function useSeverityRequest(fetchOnLoad: boolean, cacheToken: string[]) {
    const axios = useApiService()

    const getSeverities = () => {
        return axios.get(severitiesURI).then((response) => {
            return response.data.message as Severity[]
        })
    }

    return useQuery({
        queryKey: cacheToken,
        queryFn: () => getSeverities(),
        enabled: fetchOnLoad,
        refetchOnWindowFocus: false,
    })
}