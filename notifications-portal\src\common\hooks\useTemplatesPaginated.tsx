import { Order } from '@celanese/ui-lib'
import { useContext, useEffect, useState, useMemo } from 'react'
import { toCamelCase } from '../utils/utils'
import { useApiService } from './useApiService'
import { saveTemplateURI } from '../configurations/endpoints'
import { Template } from '../models/template'
import { UserRuleContext } from '../contexts/UserRuleContext'
import { UserPermission } from '../models/userPermissions'

export function useTemplatesPaginated(
    notificationType: string | undefined,
    subscribersIds: (string | number)[],
    channelsIds: (string | number)[],
    order: Order,
    orderBy: string,
    search: string,
    isAdminLevel: boolean | undefined,
    applicationsIds: string[] | undefined
) {
    const { rule } = useContext(UserRuleContext)
    const [response, setResponse] = useState<Template[]>([])
    const [currentCursor, setCurrentCursor] = useState<string | undefined>(undefined)
    const [hasNextPage, setHasNextPage] = useState(false)

    const request = useMemo(() => {
        const userPermission = rule ? (JSON.parse(rule) as UserPermission) : undefined

        if (userPermission && isAdminLevel) {
            const adminApps: string[] = []
            userPermission.applications.filter((a) =>
                a.roles.filter((r) =>
                    r.features.filter((f) => {
                        const found = f.featureName.replace(/\s/g, '') == 'CreateAdminNotificationTemplate'
                        if (found && !adminApps.includes(a.applicationCode)) {
                            adminApps.push(a.applicationCode)
                        }
                    })
                )
            )
            if (applicationsIds && applicationsIds.length > 0) {
                applicationsIds = adminApps.filter((y) => applicationsIds?.includes(y))
            } else {
                applicationsIds = adminApps
            }
        }

        const sortColumnValue = orderBy != '' ? { [toCamelCase(orderBy)]: order.toUpperCase() } : {}

        const requestObject = {
            isAdminEdition: isAdminLevel,
            pageSize: 1000,
            notificationType: notificationType,
            channels: channelsIds.join(','),
            applicationId: applicationsIds?.join(','),
            subscribersIds: subscribersIds.join(','),
            q: search,
            sortColumn: JSON.stringify(sortColumnValue),
        }

        return requestObject
    }, [
        notificationType,
        subscribersIds,
        channelsIds,
        orderBy,
        order,
        search,
        isAdminLevel,
        applicationsIds,
    ])

    const [isLoading, setIsLoading] = useState(false)
    const [loadingInfinityScroll, setLoadingInfinityScroll] = useState(false)
    const axios = useApiService()

    const getTemplatesData = async (infinite: boolean = false) => {
        if (infinite) {
            setLoadingInfinityScroll(true)
        }
        await axios.get(saveTemplateURI, {
            params: {
                ...request,
                cursor: infinite ? currentCursor : undefined,
            }
        }).then((response) => {
            if (infinite) {
                setLoadingInfinityScroll(false)
                setResponse((prevData) => [...prevData, ...response.data.message.templates])
            } else {
                setResponse(response.data.message.templates)
            }
            setCurrentCursor(response.data.message.endCursor)
            setHasNextPage(response.data.message.hasNextPage)
            setIsLoading(false)
        })
    }

    useEffect(() => {
        setIsLoading(true)

        if (isAdminLevel !== undefined && applicationsIds !== undefined) {
            getTemplatesData()
        }
    }, [isAdminLevel, applicationsIds, request])

    return { isLoading, setIsLoading, response, currentCursor, hasNextPage, getTemplatesData, loadingInfinityScroll, setCurrentCursor, setHasNextPage }
}
