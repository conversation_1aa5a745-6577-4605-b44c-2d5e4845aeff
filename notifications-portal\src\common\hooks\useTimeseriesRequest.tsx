import { useCallback, useState } from 'react'
import { getTimeSeriesURI } from '../configurations/endpoints'
import { TimeSeries, TimeSeriesRequest } from '../models/timeSeries'
import { useApiService } from './useApiService'

export function useGetTimeSeries() {
    const axios = useApiService()
    const [isLoading, setIsLoading] = useState(false)

    const getTimeSeriesResponse = useCallback((request: TimeSeriesRequest) => {
        const URI = getTimeSeriesURI(request.externalId)
        setIsLoading(true)
        return axios.post(URI, request).then((response) => {
            setIsLoading(false)
            return response.data.message as TimeSeries
        })
    }, [])

    return { getTimeSeriesResponse, isLoading }
}
