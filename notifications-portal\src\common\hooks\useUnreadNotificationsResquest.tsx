import { useQuery } from '@tanstack/react-query'
import { getUnreadNotificationsNumberURI } from '../configurations/endpoints'
import { useApiService } from './useApiService'

export function useUnreadNotificationsRequest() {
    const axios = useApiService()

    const getUnreadNotifications = () => {
        return axios.get(getUnreadNotificationsNumberURI).then((response) => {
            return response.data.message as string
        })
    }

    return useQuery({
        queryKey: ['unread-notifications'],
        queryFn: () => getUnreadNotifications(),
        refetchOnWindowFocus: false,
    })
}
