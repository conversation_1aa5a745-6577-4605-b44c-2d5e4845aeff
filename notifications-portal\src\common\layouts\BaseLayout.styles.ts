import { CSSObject } from '@emotion/react'

export const baseLayoutContainer = {
    height: '100vh',

    '.logo-icon': {
        display: 'flex',
        alignItems: 'center',
    },

    '.logo-name-bold': {
        fontSize: '1.5rem'
    }
}

export const menuContainer: CSSObject = {
    marginTop: '5px',
    '& .MuiPaper-root ul': { width: '225px', padding: '0px' },
    '& .MuiPaper-root': { borderRadius: '20px' },
}

export const menuContentContainer: CSSObject = {
    padding: '16px',
    display: 'flex',
    flexDirection: 'column',
    gap: '5px',
}

export const avatarAndNameContainer: CSSObject = {
    display: 'flex',
    flexDirection: 'row',
    gap: '10px',
    height: '50px',
    '& .MuiAvatar-root': {
        backgroundColor: 'primary.main',
    },
}

export const pageContainer: CSSObject = {
    padding: '2rem',
    height: '100%',
    width: '100%',
    maxWidth: '100%',
    maxHeight: '100%',
}

export const dynamicTranslationOff: CSSObject = {
    color: 'warning.dark',
}

export const layoutContainer = {
    height: '100%',
    'main': {
        height: '100%'
    },
}