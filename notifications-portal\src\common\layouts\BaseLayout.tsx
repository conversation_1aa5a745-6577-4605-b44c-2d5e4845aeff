import { DEFAULT_LOCALE } from '@/common/hooks'
import { useMsal } from '@azure/msal-react'
import {
    DynamicTranslationArea,
    NoTranslate,
    translate,
    TranslationContext,
    TranslationContextState,
} from '@celanese/celanese-sdk'
import { ClnAvatar, ClnHeaderNavbar, LangItem, MatIcon, type NavbarItemConfig, PlantItem, Wrapper } from '@celanese/ui-lib'
import '@material-symbols/font-400/outlined.css'
import { CircularProgress, Divider, Menu, MenuItem } from '@mui/material'
import Box from '@mui/material/Box'
import CssBaseline from '@mui/material/CssBaseline'
import { useRouter } from 'next/router'
import { Dispatch, ReactNode, SetStateAction, useContext, useEffect, useMemo, useState } from 'react'
import TooltipedText from '../components/TooltipedText/tooltiped-text'
import { HeaderNavbarContextParams } from '../contexts/HeaderContex'
import { NtfSmartFeedContextParams } from '../contexts/NtfSmartFeedContext'
import { UserRuleContext } from '../contexts/UserRuleContext'
import { createPublicClientApplication } from '../factories/msal-factory'
import { UserPermission } from '../models/userPermissions'
import { availableLanguages } from '../utils/general'
import { getIdTokenFromMsal } from '../utils/translation'
import * as styles from './BaseLayout.styles'
import { NoContextualize } from '@celanese/contextualization-lib'
import LostDataDialog from '../components/LostDataDialog/lost-data-dialog'

const languages: LangItem[] = availableLanguages

export const GLOBAL = { value: 'Global', externalId: 'STS-COR' }

interface BaseLayoutProps {
    setLocaleCode: Dispatch<SetStateAction<string>>
    shouldTranslateDynamicState: {
        shouldTranslateDynamic: boolean | undefined
        setShouldTranslateDynamic: Dispatch<SetStateAction<boolean | undefined>>
    }
    dynamicTranslationLoadingState: {
        dynamicTranslationLoading: boolean
        setDynamicTranslationLoading: Dispatch<SetStateAction<boolean>>
    }
    children: ReactNode
}

export default function BaseLayout({
    children,
    setLocaleCode,
    shouldTranslateDynamicState,
    dynamicTranslationLoadingState,
}: BaseLayoutProps) {
    const { refetchSmartFeed } = NtfSmartFeedContextParams()
    const { shouldTranslateDynamic, setShouldTranslateDynamic } = shouldTranslateDynamicState
    const { dynamicTranslationLoading, setDynamicTranslationLoading } = dynamicTranslationLoadingState
    const { availableLanguages, locale, updateLocale } = useContext<TranslationContextState>(TranslationContext)
    const [showLostDataModal, setShowLostDataModal] = useState<boolean>(false)
    const [routerSelected, setRouterSelected] = useState<string>('')

    const router = useRouter()
    const query = new URLSearchParams(window.location.search)
    const decodedFormType = decodeURIComponent(atob(query.get('f') ?? ''))

    const msalInstance = createPublicClientApplication()
    const getAuthToken = (): Promise<string> => getIdTokenFromMsal(msalInstance)

    const shouldSelectSite = router.pathname == '/notifications'

    const items: NavbarItemConfig[] = [
        {
            title: translate('app.menu.notifications'),
            icon: 'notifications',
            onClickHandler: () => {
                refetchSmartFeed()
                if (router.pathname === '/customize-templates' && decodedFormType !== 'view') {
                    setRouterSelected('/notifications')
                    setShowLostDataModal(true)
                } else router.push('/notifications')
            },
        },
        {
            title: translate('app.menu.templates'),
            icon: 'edit_notifications',
            onClickHandler: () => {
                if (router.pathname === '/customize-templates' && decodedFormType !== 'view') {
                    setRouterSelected('/templates')
                    setShowLostDataModal(true)
                } else router.push('/templates')
            },
        },
    ]

    const [plants, setPlants] = useState([GLOBAL] as PlantItem[])
    const [userEmail, setUserEmail] = useState('')
    const [imageSource, setImageSource] = useState('')

    const { selectedPlant, setSelectedPlant, unreadNotifications, userName, setUserName } = HeaderNavbarContextParams()

    const { rule } = useContext(UserRuleContext)

    useEffect(() => {
        if (rule) {
            const userPermission: UserPermission = JSON.parse(rule)
            if (userPermission) {
                const sitesByUser: PlantItem[] = userPermission.sites.map((site) => {
                    return {
                        value: site.siteName,
                        externalId: site.siteId,
                    }
                })

                setPlants([GLOBAL, ...sitesByUser])
                const userDisplayedName = `${userPermission.lastName}, ${userPermission.firstName}`
                setUserName(userDisplayedName)
                setUserEmail(userPermission.email)
                setImageSource(userPermission.avatar)
            }
        }
    }, [rule])

    const handleLangChange = (li: LangItem) => {
        if (li) {
            updateLocale(li.code)
            setLocaleCode(li.code)
        }
    }

    const [anchorElUser, setAnchorElUser] = useState<null | HTMLElement>(null)

    const handleOpenMenu = (event: React.MouseEvent<HTMLElement>) => {
        setAnchorElUser(event.currentTarget)
    }

    const { instance } = useMsal()

    const handleLogout = () => {
        instance.logoutRedirect()
    }

    const UserMenu = () => {
        return (
            <Menu
                sx={styles.menuContainer}
                id="menu-header"
                anchorEl={anchorElUser}
                anchorOrigin={{
                    vertical: 'bottom',
                    horizontal: 'right',
                }}
                transformOrigin={{
                    vertical: 'top',
                    horizontal: 'right',
                }}
                open={Boolean(anchorElUser)}
                onClose={() => setAnchorElUser(null)}
            >
                <Box sx={styles.menuContentContainer}>
                    <Box sx={styles.avatarAndNameContainer}>
                        <ClnAvatar
                            alt={userName}
                            src={imageSource}
                            size="large"
                            sx={{
                                color: 'primary.contrastText',
                                backgroundColor: 'primary.main',
                            }}
                        />
                        <Box>
                            <TooltipedText fontSize="12px" maxWidth={150} text={userName} tooltipPlacement="bottom" />
                            <TooltipedText fontSize="12px" maxWidth={150} text={userEmail} tooltipPlacement="bottom" />
                        </Box>
                    </Box>
                    <Divider />
                    <MenuItem key="logout" onClick={handleLogout} sx={{ padding: '0', fontSize: '12px', marginTop: '11px' }}>
                        <MatIcon icon="logout" fontSize="12px" sx={{ marginRight: '5px' }} />
                        {translate('app.common.logOut')}
                    </MenuItem>
                </Box>
            </Menu>
        )
    }

    const DynamicTranslationIcon = () => {
        return (
            <>
                {dynamicTranslationLoading && <CircularProgress size={20} color="inherit" />}

                {shouldTranslateDynamic ? (
                    <MatIcon icon="translate" sx={styles.dynamicTranslationOff} />
                ) : (
                    <MatIcon icon="translate" />
                )}
            </>
        )
    }

    const selectedLanguage = useMemo(() => {
        const languageSelected =
            availableLanguages.find((l) => l?.code == locale) ?? languages.find((l) => l?.code == DEFAULT_LOCALE)

        const defaultLang: LangItem = {
            externalId: 'EN',
            language: 'English',
            code: 'EN',
        }

        return languageSelected || defaultLang
    }, [locale, languages])

    return (
        <Box sx={styles.baseLayoutContainer}>
            <CssBaseline />
            <NoTranslate>
                <NoContextualize>
                    <ClnHeaderNavbar
                        logo={<MatIcon icon="notifications" fontSize="26px" />}
                        alt={userName}
                        src={imageSource}
                        nameBold="NOTIFICATIONS"
                        language={selectedLanguage}
                        languages={
                            availableLanguages?.map((language) => ({
                                ...language,
                                language: <NoTranslate>{language.language}</NoTranslate>,
                            })) ?? []
                        }
                        onChangeLang={handleLangChange}
                        plant={shouldSelectSite ? selectedPlant : GLOBAL}
                        plants={shouldSelectSite ? plants : [GLOBAL]}
                        onChangePlant={(e) => {
                            setSelectedPlant(e)
                        }}
                        buttonConfigurations={[
                            {
                                Icon: <MatIcon icon="notifications" />,
                                Tooltip: 'Notification',
                                onClick: () => {
                                    router.push('/notifications')
                                },
                            },
                            {
                                Icon: <DynamicTranslationIcon />,
                                Tooltip: shouldTranslateDynamic
                                    ? 'Turn Off Dynamic Translation'
                                    : 'Turn On Dynamic Translation',
                                onClick: () => {
                                    setShouldTranslateDynamic(!shouldTranslateDynamic)
                                },
                            },
                        ]}
                        userMenuOnClick={handleOpenMenu}
                        items={items}
                    />
                </NoContextualize>
            </NoTranslate>
            <UserMenu />
            <DynamicTranslationArea
                getAuthToken={getAuthToken}
                dynamicTranslationLoadingState={{ dynamicTranslationLoading, setDynamicTranslationLoading }}
                shouldTranslateDynamicState={{ shouldTranslateDynamic, setShouldTranslateDynamic }}
                sx={styles.layoutContainer}
                translatedClasses={
                    '.desktop-drawer, .mobile-drawer, div[role="presentation"][id^="menu-"], div[role="presentation"]'
                }
            >
                <Wrapper>
                    <Box sx={styles.pageContainer}>{children}</Box>
                </Wrapper>
            </DynamicTranslationArea>
            <LostDataDialog
                showModalLostData={showLostDataModal}
                setShowModalLostData={setShowLostDataModal}
                navigateBack={() => {
                    setShowLostDataModal(false)
                    router.push(routerSelected)
                }}
            />
        </Box>
    )
}
