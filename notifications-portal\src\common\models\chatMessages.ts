import { NotificationUser } from './notificationUser'

export interface Comment {
    comment: string
    externalId: string
    space: string
    createdTime: string
    user: NotificationUser
}

export interface ChatMessages {
    comments: Comment[]
    createdTime: string
    externalId: string
    space: string
    text: string
    notificationType: string
    application: string
    site?: string
    severityExternalId: string
    severityDescription: string
    rawEvent?: any
}
