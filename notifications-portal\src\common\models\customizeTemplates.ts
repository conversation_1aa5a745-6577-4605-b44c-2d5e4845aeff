import { z } from 'zod'
import { Condition } from './condition'
import { SetStateAction, Dispatch } from 'react'
import { CheckboxItem, SelectItem } from '@celanese/ui-lib'
import {
    type SubmitHandler,
    Control,
    UseFormGetValues,
    UseFormSetValue,
    UseFormHandleSubmit,
    FieldErrors,
    UseFormWatch,
    UseFormResetField,
    UseFieldArrayRemove,
    UseFieldArrayAppend,
    UseFieldArrayReplace,
} from 'react-hook-form'
import { Message } from '@/common/components/MessageContainer/message-container'
import { NotificationApplicationGroupSchema, notificationApplicationGroupSchema } from './notificationApplicationGroup'

export const validationSchemaCustomizeTemplates = z.object({
    externalId: z.string().optional(),
    adminLevel: z.boolean(),
    allUsers: z.array(
        z.object({
            value: z.string(),
            label: z.string(),
        })
    ),
    channels: z
        .array(
            z.object({
                value: z.string(),
                label: z.string(),
            })
        ).optional(),
    conditionals: z
        .array(
            z.object({
                variable: z.string().min(1),
                operator: z.string().min(1),
                value: z.string().min(1),
                conjunction: z.string().min(1),
                isNumeric: z.boolean(),
            })
        )
        .optional(),
    frequency: z.object({
        value: z.string().min(1),
        label: z.string(),
    }),
    customChannelEnabled: z.boolean(),
    customFrequencyEnabled: z.boolean(),
    deleted: z.boolean(),
    frequencyCronExpression: z.nullable(z
        .union([
            z.object({
                schedule_type: z.string(),
                time: z.string().optional(),
                end_time: z.string().optional(),
                interval: z.nullable(z.number().optional()),
                day_of_week: z.nullable(z.array(z.string()).optional()),
                day_of_month: z.nullable(z.string().optional()),
                months: z.nullable(z.array(z.number()).optional()),
            }),
            z.string(),
        ]))
        .optional(),
    name: z.string().min(1),
    notificationType: z.string(),
    severity: z.object({
        value: z.string().min(2),
        label: z.string().min(2),
    }),
    subject: z.string().optional(),
    subscribedApplicationGroups: z.array(notificationApplicationGroupSchema),
    subscribedExternalUsers: z
        .array(
            z.object({
                email: z.string(),
            })
        )
        .optional(),
    subscribedRoles: z
        .array(
            z.object({
                externalId: z.string(),
                name: z.string(),
                space: z.string(),
                users: z
                    .array(
                        z.object({
                            externalId: z.string(),
                            email: z.string(),
                            firstName: z.string().optional().nullable(),
                            lastName: z.string().optional().nullable(),
                            teams: z.array(z.object({})).optional(),
                        })
                    )
                    .optional(),
                site: z.object({}).optional(),
            })
        )
        .optional(),
    subscribedUsers: z
        .array(
            z.object({
                externalId: z.string(),
                email: z.string(),
                firstName: z.string().optional().nullable(),
                lastName: z.string().optional().nullable(),
                teams: z.union([
                    z.object({ items: z.array(z.object({})).optional() }).optional(),
                    z.array(z.object({})).optional()
                ])
            })
        )
        .optional(),
    blockUsersInput: z.string().optional(),
    blockSubscribedApplicationGroups: z
        .array(
            z.object({
                externalId: z.string(),
                space: z.string(),
                name: z.string(),
                description: z.string(),
                users: z
                    .array(
                        z.object({
                            externalId: z.string(),
                            email: z.string(),
                            firstName: z.string().optional().nullable(),
                            lastName: z.string().optional().nullable(),
                            teams: z.array(z.object({})).optional(),
                        })
                    )
                    .optional(),
            })
        )
        .optional(),
    blockSubscribedRoles: z
        .array(
            z.object({
                externalId: z.string(),
                name: z.string(),
                space: z.string(),
                users: z
                    .array(
                        z.object({
                            externalId: z.string(),
                            email: z.string(),
                            firstName: z.string().optional().nullable(),
                            lastName: z.string().optional().nullable(),
                            teams: z.array(z.object({})).optional(),
                        })
                    )
                    .optional(),
                site: z.object({}).optional(),
            })
        )
        .optional(),
    blockSubscribedUsers: z
        .array(
            z.object({
                externalId: z.string(),
                email: z.string(),
                firstName: z.string().optional().nullable(),
                lastName: z.string().optional().nullable(),
                teams: z.union([
                    z.object({ items: z.array(z.object({})).optional() }).optional(),
                    z.array(z.object({})).optional()
                ]),
            })
        )
        .optional(),


    text: z.string().min(1),
    textExample: z.string().optional(),
    externalUsersInput: z.string().optional(),
    usersInput: z.string().optional(),
})

export type ValidationSchemaCustomizeTemplates = z.infer<typeof validationSchemaCustomizeTemplates>

export const validationSchemaAdvancedFilter = z.object({
  reportingSite: z.object({
    value: z.string(),
    label: z.string(),
  }),
  roleSite: z.object({
    value: z.string(),
    label: z.string(),
  }),
  roleApplication: z.object({
    value: z.string(),
    label: z.string(),
  }),
  team: z.object({
    value: z.string(),
    label: z.string(),
  }),
  unit: z.object({
    value: z.string(),
    label: z.string(),
  }),
  reportingLocation: z.object({
    value: z.string(),
    label: z.string(),
  }),
})

export type ValidationSchemaAdvancedSeacrh = z.infer<typeof validationSchemaAdvancedFilter>

export interface ExternalUsersFieldArrayProps {
    subscribedExternalUsers: {
        email: string
    }[]
    removeExternalUsers: UseFieldArrayRemove
    appendExternalUsers: UseFieldArrayAppend<ValidationSchemaCustomizeTemplates, 'subscribedExternalUsers'>
    replaceExternalUsers: UseFieldArrayReplace<ValidationSchemaCustomizeTemplates, 'subscribedExternalUsers'>
}
export interface UsersFieldArrayProps {
    subscribedUsers: {
        externalId: string
        email: string
        firstName?: string | null
        lastName?: string | null
        teams?: { items?: {}[] | undefined; } | {}[] | undefined
    }[]
    removeUsers: UseFieldArrayRemove
    appendUsers: UseFieldArrayAppend<ValidationSchemaCustomizeTemplates, 'subscribedUsers'>
    replaceUsers: UseFieldArrayReplace<ValidationSchemaCustomizeTemplates, 'subscribedUsers'>
}
export interface RolesFieldArrayProps {
    subscribedRoles: {
        externalId: string
        name: string
        space: string
        users?: {
            externalId: string
            email: string
            firstName?: string | null
            lastName?: string | null
            teams?: {}[]
        }[]
        site?: {}
    }[]
    removeRoles: UseFieldArrayRemove
    appendRoles: UseFieldArrayAppend<ValidationSchemaCustomizeTemplates, 'subscribedRoles'>
    replaceRoles: UseFieldArrayReplace<ValidationSchemaCustomizeTemplates, 'subscribedRoles'>
}
export interface ApplicationGroupsFieldArrayProps {
    subscribedApplicationGroups: {
        externalId?: string
        space?: string
        name: string
        description: string
        users?: {
            externalId: string
            email: string
            firstName?: string | null
            lastName?: string | null
        }[]
    }[]
    removeApplicationGroups: UseFieldArrayRemove
    appendApplicationGroups: UseFieldArrayAppend<ValidationSchemaCustomizeTemplates, 'subscribedApplicationGroups'>
    replaceApplicationGroups: UseFieldArrayReplace<ValidationSchemaCustomizeTemplates, 'subscribedApplicationGroups'>
}


export interface BlockUsersFieldArrayProps {
    blockSubscribedUsers: {
        externalId: string
        email: string
        firstName?: string | null
        lastName?: string | null
        teams?: { items?: {}[] | undefined; } | {}[] | undefined
    }[]
    removeBlockUsers: UseFieldArrayRemove
    appendBlockUsers: UseFieldArrayAppend<ValidationSchemaCustomizeTemplates, 'blockSubscribedUsers'>
    replaceBlockUsers: UseFieldArrayReplace<ValidationSchemaCustomizeTemplates, 'blockSubscribedUsers'>
}
export interface BlockRolesFieldArrayProps {
    blockSubscribedRoles: {
        externalId: string
        name: string
        space: string
        users?: {
            externalId: string
            email: string
            firstName?: string | null
            lastName?: string | null
            teams?: {}[]
        }[]
        site?: {}
    }[]
    removeBlockRoles: UseFieldArrayRemove
    appendBlockRoles: UseFieldArrayAppend<ValidationSchemaCustomizeTemplates, 'blockSubscribedRoles'>
    replaceBlockRoles: UseFieldArrayReplace<ValidationSchemaCustomizeTemplates, 'blockSubscribedRoles'>
}
export interface BlockApplicationGroupsFieldArrayProps {
    blockSubscribedApplicationGroups: {
        externalId: string
        space: string
        name: string
        description: string
        users?: {
            externalId: string
            email: string
            firstName?: string | null
            lastName?: string | null
            teams?: {}[]
        }[]
    }[]
    removeBlockApplicationGroups: UseFieldArrayRemove
    appendBlockApplicationGroups: UseFieldArrayAppend<ValidationSchemaCustomizeTemplates, 'blockSubscribedApplicationGroups'>
    replaceBlockApplicationGroups: UseFieldArrayReplace<ValidationSchemaCustomizeTemplates, 'blockSubscribedApplicationGroups'>
}

export interface ConditionalsFieldArrayProps {
    conditionals: Condition[]
    removeConditional: UseFieldArrayRemove
    appendConditional: UseFieldArrayAppend<ValidationSchemaCustomizeTemplates, 'conditionals'>
}

export enum FrequencyType {
    OnDemand = 'On demand',
    ByMinute = 'By minute',
    ByHour = 'By hour',
    Weekly = 'Weekly',
    Monthly = 'Monthly',
}

export const ScheduleToFrequencyType: Record<string, FrequencyType> = {
    minute: FrequencyType.ByMinute,
    hourly: FrequencyType.ByHour,
    weekly: FrequencyType.Weekly,
    monthly: FrequencyType.Monthly,
    onDemand: FrequencyType.OnDemand,
}

export interface AdvancedSearchOptionsProps {
  site: SiteAPIProps[],
  rolesSite: RoleAPIProps[],
  rolesApp: RoleAPIProps[],
  teams: TeamAPIProps[],
  location: LocationAPIProps[],
  unit: UnitAPIProps[],
}
export interface SiteAPIProps {
    externalId: string
    name: string
    space: string
    siteCode: string
}

export interface TeamAPIProps {
    externalId: string
    name: string
    space: string
    description: string
}
export interface UnitAPIProps {
    externalId: string
    name: string
    space: string
    description: string
}

export interface LocationAPIProps {
    externalId: string
    name: string
    space: string
    description: string
}

export interface UseSaveTemplateLogicProps {
    crumbs: {
        title: string
        onClickHandler: () => void
        disabled?: boolean
    }[]
    control: Control<ValidationSchemaCustomizeTemplates>
    onSubmit: SubmitHandler<ValidationSchemaCustomizeTemplates>
    handleSubmit: UseFormHandleSubmit<ValidationSchemaCustomizeTemplates>
    handleBackClick: () => void
    isAdminLevel: boolean
    isAdminEdit: boolean
    warningMessages: Message[]
    handleAddField: (field: string, start: number, end: number) => void
    notificationTypeId: string
    messageVariables: { name: string; value: any; type?: string }[]
    setMessageVariables: Dispatch<SetStateAction<{ name: string; value: any; type?: string }[]>>
    getValues: UseFormGetValues<ValidationSchemaCustomizeTemplates>
    selectedCustomizations: CheckboxItem[]
    setSelectedCustomizations: Dispatch<SetStateAction<CheckboxItem[]>>
    customizations: SelectItem[]
    errors: FieldErrors<ValidationSchemaCustomizeTemplates>
    frequencyType: SelectItem
    setValue: UseFormSetValue<ValidationSchemaCustomizeTemplates>
    showModalLostData: boolean
    setShowModalLostData: Dispatch<SetStateAction<boolean>>
    navigateBack: () => void
    watch: UseFormWatch<ValidationSchemaCustomizeTemplates>
    externalUsersFieldArray: ExternalUsersFieldArrayProps
    usersFieldArray: UsersFieldArrayProps
    rolesFieldArray: RolesFieldArrayProps
    applicationGroupsFieldArray: ApplicationGroupsFieldArrayProps
    blockUsersFieldArray: BlockUsersFieldArrayProps
    blockRolesFieldArray: BlockRolesFieldArrayProps
    blockApplicationGroupsFieldArray: BlockApplicationGroupsFieldArrayProps

    conditionalsFieldArray: ConditionalsFieldArrayProps
    resetField: UseFormResetField<ValidationSchemaCustomizeTemplates>
    backendError: string[]
    setBackendError: Dispatch<SetStateAction<string[]>>
    isLoading: boolean
    previousFrequencyType: string
    setPreviousFrequencyType: Dispatch<SetStateAction<string>>
    defaultValues: ValidationSchemaCustomizeTemplates
    showDuplicateModal: boolean
    setShowDuplicateModal: Dispatch<SetStateAction<boolean>>
    editInfo: {
        showEditInfo: boolean
        editedBy: string
        editedAt: string
    }
    handleDuplicateTemplate: () => void
    isDuplicate: boolean
    notificationTypeName: string
    application: {
        name: string
        id: string
    }
    disabled: boolean
    selectedChannels?: SelectItem[]
    customFrequencyEnabled: boolean
    customChannelEnabled: boolean
    steps: string[]
    showAdvancedSearchModal: any
    handleOpenAdvancedSearchModal: any
    handleCloseAdvancedSearchModal: any
    filteredUsers: any
    applyFilter: any
    selectedUsers: any
    templateName: string
    keepRecipients: CheckboxItem[]
    handleChangeKeepRecipientList: (arg: CheckboxItem[]) => void
    showAllowlistModal: any
    showBlocklistModal: any
    handleOpenAllowlistModal: any
    handleCloseAllowlistModal: any
    handleOpenBlocklistModal: any
    handleCloseBlocklistModal: any
    stepValidation: (activeStep: number) => void
    activeStep: number
    setActiveStep: Dispatch<SetStateAction<number>>
    isNextStepOneClicked: boolean
    selectedGroups: NotificationApplicationGroupSchema[]
    setSelectedGroups: Dispatch<SetStateAction<NotificationApplicationGroupSchema[]>>
    clickedGroup: NotificationApplicationGroupSchema | null
    setClickedGroup: Dispatch<SetStateAction<NotificationApplicationGroupSchema | null>>
    isStepFailed: (step: number) => boolean | undefined
    canChangeStep: { canChange: boolean; message: string }
    setCanChangeStep: Dispatch<SetStateAction<{ canChange: boolean; message: string }>>
    advancedSearchOptions: AdvancedSearchOptionsProps
    setAdvancedSearchOptions: Dispatch<SetStateAction<AdvancedSearchOptionsProps>>
    loadingUsersFilter: boolean
    getNotificationApplicationGroups: (
        groupId?: string,
        setLoading?: (value: SetStateAction<boolean>) => void,
        setNotificationApplicationGroups?: (value: SetStateAction<NotificationApplicationGroupSchema[]>) => void
    ) => void
    setNotificationApplicationGroups: Dispatch<SetStateAction<NotificationApplicationGroupSchema[]>>
    notificationApplicationGroups: NotificationApplicationGroupSchema[]
    formType: string
    notificationEntities: string[] | undefined
    isEdition: boolean
}
export interface RoleAPIProps {
  externalId: string
  name: string
  space: string
  users: Array<{ email: string; externalId: string; firstName: string; lastName: string; teams: string[] }>
  site: {
      externalId: string
      name: string
      space: string
      siteCode: string
  }
}

export type Month =
    | 'January'
    | 'February'
    | 'March'
    | 'April'
    | 'May'
    | 'June'
    | 'July'
    | 'August'
    | 'September'
    | 'October'
    | 'November'
    | 'December'

export type Weekday = 'Sunday' | 'Monday' | 'Tuesday' | 'Wednesday' | 'Thursday' | 'Friday' | 'Saturday'