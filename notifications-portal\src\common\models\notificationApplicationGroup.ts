import { z } from 'zod'

export const notificationApplicationGroupSchema = z.object({
    externalId: z.string().optional(),
    space: z.string().optional(),
    application: z.union([z.string(), z.object({ externalId: z.string(), space: z.string(), })]).optional(),
    name: z.string().trim().min(1),
    description: z.string().trim().min(1),
    users: z
        .array(
            z.object({
                externalId: z.string(),
                email: z.string(),
                firstName: z.string(),
                lastName: z.string(),
            })
        )
        .optional(),
    externalUsers: z.array(
        z.object({
            email: z.string(),
        }).optional()
    ),
    blocklist: z
        .array(
            z.object({
                externalId: z.string(),
                email: z.string(),
                firstName: z.string(),
                lastName: z.string(),
            })
        )
        .optional(),
    usersRoles: z
        .array(
            z.object({
                externalId: z.string(),
                name: z.string(),
                space: z.string(),
                users: z
                    .array(
                        z.object({
                            externalId: z.string(),
                            email: z.string(),
                            firstName: z.string(),
                            lastName: z.string(),
                        })
                    )
                    .optional(),
                site: z.object({}).nullable().optional(),
            })
        )
        .optional(),
    blocklistRoles: z
        .array(
            z.object({
                externalId: z.string(),
                name: z.string(),
                space: z.string(),
                users: z
                    .array(
                        z.object({
                            externalId: z.string(),
                            email: z.string(),
                            firstName: z.string(),
                            lastName: z.string(),
                        })
                    )
                    .optional(),
                site: z.object({}).nullable().optional(),
            })
        )
        .optional(),
    editedBy: z.object({
        externalId: z.string(),
        email: z.string(),
        firstName: z.string(),
        lastName: z.string(),
    }).nullable().optional(),
    editedAt: z.string().nullable().optional(),
    createdBy: z.object({
        externalId: z.string(),
        email: z.string(),
        firstName: z.string(),
        lastName: z.string(),
    }).nullable().optional(),
})

export type NotificationApplicationGroupSchema = z.infer<typeof notificationApplicationGroupSchema>;