import { NotificationUser, NotificationUserSchemaValidation } from './notificationUser'
import { z } from 'zod'

export interface NotificationRole {
    externalId: string
    name: string
    space: string
    users?: NotificationUser[]
}

export const notificationRolesSchemaValidation: z.ZodObject<any> = z.object({
    externalId: z.string(),
    name: z.string(),
    space: z.string(),
    users: z.array(NotificationUserSchemaValidation).optional(),
})
