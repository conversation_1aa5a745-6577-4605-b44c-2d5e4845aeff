import { NotificationRole, notificationRolesSchemaValidation } from './notificationRole'
import { z } from 'zod'

export interface NotificationUser {
    externalId: string
    name?: string
    email: string
    active?: boolean
    admin?: boolean
    roles?: NotificationRole[]
    displayName?: string
    firstName: string
    lastName: string
}


export const NotificationUserSchemaValidation = z.object({
    externalId: z.string(),
    name: z.string().optional(),
    email: z.string(),
    active: z.boolean().optional(),
    admin: z.boolean().optional(),
    roles: z.array(notificationRolesSchemaValidation).optional(),
    displayName: z.string().optional(),
    firstName: z.string(),
    lastName: z.string(),
})