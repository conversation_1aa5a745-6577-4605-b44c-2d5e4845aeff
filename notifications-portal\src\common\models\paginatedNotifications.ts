import { Severity } from './severitiy'

export interface NotificationOnScreen {
    externalId: string
    date: string
    updateDate?: string
    severity: Severity
    site: string
    notificationType: string
    application: string
    applicationIconUrl?: string
    notificationMessage: string
}

export interface PaginatedNotifications {
    notifications: NotificationOnScreen[]
    totalPages: number
    totalItems: number
    sites: Record<string,string>
    applications: Record<string,string>
    notificationTypes: Record<string,string>
}