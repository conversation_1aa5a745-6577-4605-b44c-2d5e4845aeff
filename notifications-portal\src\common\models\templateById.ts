import { ApplicationGroup } from './applicationGroup'
import { Channel } from './channel'
import { CronObject } from './cronObject'
import { NotificationRole } from './notificationRole'
import { NotificationUser } from './notificationUser'
import { Severity } from './severitiy'

export interface TemplateById {
    frequencyCronExpression: CronObject | null
    channels: Channel[]
    externalId: string
    name: string
    severity: Severity
    space: string
    subscribedUserRoles: NotificationRole[]
    subscribedApplicationGroups: ApplicationGroup[]
    subscribedUsers: NotificationUser[]
    subscribedExternalUsers: string[]
    text: string
    customChannelEnabled: boolean
    customFrequencyEnabled: boolean
    conditionalExpression: string
    notificationType: string
    notificationTypeName: string
    adminLevel: boolean
    application: string
    applicationName: string
    editedBy: string
    editedAt: string
    allUsers: boolean
    externalUsers: boolean
    subject: string
    notificationTypeProperties?: []
}
