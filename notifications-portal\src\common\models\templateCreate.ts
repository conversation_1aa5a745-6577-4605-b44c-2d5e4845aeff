import { CronObject } from './cronObject'

export interface TemplateCreate {
    externalId?: string
    name: string
    notificationType: {
        externalId: string
    }
    text: string
    severity: {
        externalId: string
    }
    conditionalExpression?: string
    adminLevel: boolean
    customChannelEnabled: boolean
    customFrequencyEnabled: boolean
    channels: {
        externalId: string
    }[]
    frequencyCronExpression: CronObject | string
    subscribedUsers: string[]
    subscribedUserRoles: string[]
    subscribedApplicationGroups: string[]
    deleted: boolean
}
