export interface DataPointModel {
    timestamp: string
    timestampFormatted: string
    value: number
}

export interface TimeSeries {
    name?: string,
    description?: string,
    units?: string,
    id?: number,
    externalId?: string,
    isString: boolean,
    isStep: boolean,
    datasetId?: string,
    datasetName?: string,
    assetName?: string,
    createdAt?: string,
    updatedAt?: string,
    lastReading?: number,
    datapointsItems?: DataPointModel[],
    urlTimeSeriesDetails: string,
    urlDataSetDetails: string,
    urlLinkedAssets: string,
}

export interface TimeSeriesRequest {
    externalId:string
    startDate: string
    endDate: string
}