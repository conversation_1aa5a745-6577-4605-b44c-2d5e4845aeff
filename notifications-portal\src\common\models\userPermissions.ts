export interface UserPermission {
    avatar: string
    companyName: string
    department: string
    displayName: string
    email: string
    firstName: string
    jobTitle: string
    lanId: string
    lastName: string
    applications: ApplicationsRoles[]
    sites: UserSite[]
    units: UserUnit[]
}

export interface ApplicationsRoles {
    applicationCode: string
    roles: RolesFeature[]
    userSites: UserSite[]
}

export interface RolesFeature {
    roleName: string
    roleCode: string
    siteCodes: string[]
    features: FeaturePermission[]
}

export interface FeaturePermission {
    featureCode: string
    featureName: string
    featureAccessLevel: string
    featureAccessLevelCode: string
}

export interface UserSite {
    siteId: string
    siteName: string
    siteCode: string
}

export interface UserUnit {
    unitName: string
    unitCode: string
}

export interface UserRolesPermission {
    displayName: string
    firstName: string
    lastName: string
    email: string
    lanId: string
    companyName: string
    jobTitle: string
    department: string
    avatarDownloadUrl: string
    roles: RolesFeature[]
}
