@font-face {
    font-family: 'celanese-icon';
    src: url('./../fonts/celanese-icon.eot');
    src: url('./../fonts/celanese-icon.eot') format('embedded-opentype'),
        url('./../fonts/celanese-icon.ttf') format('truetype'), url('./../fonts/celanese-icon.woff') format('woff'),
        url('./../fonts/celanese-icon.svg') format('svg');
    font-weight: normal;
    font-style: normal;
    font-display: block;
}

[class^='cln-ico-'],
[class*=' cln-ico-'] {
    /* use !important to prevent issues with browser extensions that change fonts */
    font-family: 'celanese-icon' !important;
    speak: never;
    font-style: normal;
    font-weight: normal;
    font-variant: normal;
    text-transform: none;
    line-height: 1;

    /* Better Font Rendering =========== */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.cln-ico-account:before {
    content: '\e900';
}
.cln-ico-add:before {
    content: '\e901';
}
.cln-ico-alert:before {
    content: '\e902';
}
.cln-ico-attach_file:before {
    content: '\e903';
}
.cln-ico-calendar:before {
    content: '\e904';
}
.cln-ico-call:before {
    content: '\e905';
}
.cln-ico-camera:before {
    content: '\e906';
}
.cln-ico-cancel:before {
    content: '\e907';
}
.cln-ico-checklist:before {
    content: '\e908';
}
.cln-ico-checklist_right:before {
    content: '\e909';
}
.cln-ico-chevron:before {
    content: '\e90a';
}
.cln-ico-close:before {
    content: '\e90b';
}
.cln-ico-cloud:before {
    content: '\e90c';
}
.cln-ico-column:before {
    content: '\e90d';
}
.cln-ico-config:before {
    content: '\e90e';
}
.cln-ico-dashboard:before {
    content: '\e90f';
}
.cln-ico-data:before {
    content: '\e910';
}
.cln-ico-dead-end:before {
    content: '\e911';
}
.cln-ico-delete:before {
    content: '\e912';
}
.cln-ico-document:before {
    content: '\e913';
}
.cln-ico-download:before {
    content: '\e914';
}
.cln-ico-edit:before {
    content: '\e915';
}
.cln-ico-email:before {
    content: '\e916';
}
.cln-ico-error:before {
    content: '\e917';
}
.cln-ico-export:before {
    content: '\e918';
}
.cln-ico-filter:before {
    content: '\e919';
}
.cln-ico-gauge:before {
    content: '\e91a';
}
.cln-ico-help:before {
    content: '\e91b';
}
.cln-ico-house:before {
    content: '\e91c';
}
.cln-ico-image:before {
    content: '\e91d';
}
.cln-ico-info:before {
    content: '\e91e';
}
.cln-ico-key:before {
    content: '\e91f';
}
.cln-ico-kpi:before {
    content: '\e920';
}
.cln-ico-language:before {
    content: '\e921';
}
.cln-ico-link:before {
    content: '\e922';
}
.cln-ico-location:before {
    content: '\e923';
}
.cln-ico-logbook:before {
    content: '\e924';
}
.cln-ico-logout:before {
    content: '\e925';
}
.cln-ico-menu:before {
    content: '\e926';
}
.cln-ico-message:before {
    content: '\e927';
}
.cln-ico-notification:before {
    content: '\e928';
}
.cln-ico-overview:before {
    content: '\e929';
}
.cln-ico-print:before {
    content: '\e92a';
}
.cln-ico-receipt:before {
    content: '\e92b';
}
.cln-ico-recent:before {
    content: '\e92c';
}
.cln-ico-reports:before {
    content: '\e92d';
}
.cln-ico-save:before {
    content: '\e92e';
}
.cln-ico-search:before {
    content: '\e92f';
}
.cln-ico-signal:before {
    content: '\e930';
}
.cln-ico-store:before {
    content: '\e931';
}
.cln-ico-tools:before {
    content: '\e932';
}
.cln-ico-tracking:before {
    content: '\e933';
}
.cln-ico-upload:before {
    content: '\e934';
}
.cln-ico-logo:before {
    content: '\e935';
}
.cln-ico-logo-name:before {
    content: '\e936';
}
.cln-ico-chevron-right:before {
    content: '\e937';
}
.cln-ico-double-arrow:before {
    content: '\e938';
}
.cln-ico-dark-mode:before {
    content: '\e939';
}
.cln-ico-light-mode:before {
    content: '\e93a';
}
