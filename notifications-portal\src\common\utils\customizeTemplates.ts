import { NotificationApplicationGroupSchema } from '../models/notificationApplicationGroup'
import {
    ScheduleToFrequencyType,
} from '@/common/models/customizeTemplates'


interface KeyValue {
    name: string
    value: any
    type: string
}

export function updateIsNumeric(
    conditional: {
        variable: string
        operator: string
        value: string | number
        conjunction: string
        isNumeric: boolean
    },
    variable?: { name: string; value: any; type?: string }
) {
    if (variable?.name === conditional.variable && variable?.type === 'number') {
        conditional.isNumeric = true
    }
    return conditional
}

export function replaceTextVariables(notificationText: string, messageVariables: { name: string; value: string }[]) {
    return notificationText?.replace(/{{(.*?)}}/g, (match, variableName) => {
        const field = messageVariables && messageVariables.find((f) => f.name === variableName)
        return field ? field.value : match
    })
}

export function removeDuplicatedVariables(properties: any[]) {
    const uniqueNames: string[] = Array.from(
        new Set<string>(properties.map((item: KeyValue) => item.name.toLowerCase()))
    ).sort()

    return uniqueNames.map((name) => {
        return {
            name: name,
            type: properties.find((item: KeyValue) => item.name.toLowerCase() === name)?.type,
            value: properties.find((item: KeyValue) => item.name.toLowerCase() === name)?.value,
        }
    })
}

export function propertyNameToLowerCase(properties: any[]) {
    return properties.reduce((acc: any, obj: { name: string }) => {
        acc.push({
            ...obj,
            name: obj.name.toLowerCase(),
        })
        return acc
    }, [])
}

export const createDefaultValues = (data: any, translate: (label: string) => any, isDuplicate: boolean) => {
    const frequency =
        data.frequencyCronExpression != null
            ? {
                label: ScheduleToFrequencyType[data.frequencyCronExpression.schedule_type],
                value: ScheduleToFrequencyType[data.frequencyCronExpression.schedule_type],
            }
            : {
                value: translate('app.templates.frequency.frequencyTypes.onDemand'),
                label: translate('app.templates.frequency.frequencyTypes.onDemand'),
            }

    const templateName = sessionStorage.getItem('duplicateTemplateName') === '' ? data.name + translate('app.templates.copy') : sessionStorage.getItem('duplicateTemplateName')

    const formatedSubscribedApplicationGroups: NotificationApplicationGroupSchema[] = []

    data.subscribedApplicationGroups.forEach((group: any) => {
        formatedSubscribedApplicationGroups.push({
            ...group,
            application: group?.application?.externalId ?? '',
            externalUsers: group?.externalUsers.map((user: any) => ({ email: user })) ?? [],
        })
    })

    return {
        externalId: isDuplicate ? undefined : data.externalId,
        adminLevel: data.adminLevel || false,
        allUsers: data.allUsers ? [{ value: 'allusers', label: translate('app.templates.allUsers') }] : [],
        channels: data.channels.map((channel: { externalId: string; name: string }) => ({
            value: channel.externalId,
            label: channel.name,
        })),
        conditionals: data.conditionalExpression ? JSON.parse(data.conditionalExpression) : [],
        customChannelEnabled: data.customChannelEnabled || false,
        customFrequencyEnabled: data.customFrequencyEnabled || false,
        deleted: false,
        externalUsers: data.externalUsers
            ? [
                {
                    label: translate('app.templates.externalUsers'),
                    value: 'externalusers',
                },
            ]
            : [],
        frequency,
        frequencyCronExpression: data.frequencyCronExpression || '',
        name: isDuplicate ? templateName : data.name,
        notificationType: data.notificationType,
        severity: {
            value: data.severity.externalId,
            label: data.severity.description,
        },
        subject: data.subject || '',
        subscribedApplicationGroups: formatedSubscribedApplicationGroups,
        subscribedExternalUsers: data.subscribedExternalUsers.map((extUser: string) => ({ email: extUser })) || [],
        subscribedRoles: data.subscribedUserRoles || [],
        subscribedUsers: data.subscribedUsers || [],
        blockSubscribedRoles: data.blocklistRoles || [],
        blockSubscribedUsers: data.blocklist || [],
        text: data.text || '',
        textExample: replaceTextVariables(data.text, data.notificationTypeProperties) || '',
    }
}
