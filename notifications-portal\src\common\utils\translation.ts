import { IPublicClientApplication, PublicClientApplication } from '@azure/msal-browser'
import { msalScopes } from '../configurations/auth'
import { enviroment } from '../configurations/enviroment'
import { availableLanguages } from './general'

export interface LanguageItem {
    externalId: string
    language: string
    code: string
}

const translationEndPointName = enviroment.appsTranslationUrl ?? ''
const dynamicTranslationEndPointName = translationEndPointName.replace('/static', '/dynamic')

function jsonStringifyRecursive(obj: any) {
    const cache = new Set()
    return JSON.stringify(
        obj ?? '',
        (_key, value) => {
            if (typeof value === 'object' && value !== null) {
                if (cache.has(value)) {
                    // Circular reference found, discard key
                    return
                }
                // Store value in our collection
                cache.add(value)
            }
            return value
        },
        4
    )
}

export function safeStringify(value: any) {
    try {
        return jsonStringifyRecursive(value)
    } catch (e) {
        console.error(e)
        return 'err'
    }
}

export async function getIdTokenFromMsal(msal: IPublicClientApplication) {
    const account = msal.getActiveAccount()
    if (!account) {
        return 'NOTOKENFOUND'
    }

    await msal.initialize()

    const result = await msal.acquireTokenSilent({
        account,
        scopes: msalScopes,
    })

    return result.idToken ?? 'NOTOKENFOUND'
}

export async function getTranslation(msal: PublicClientApplication, app: string = 'APP-NTF', lang: string = 'LAN-EN') {
    const token = await getIdTokenFromMsal(msal)
    const translation = await fetch(translationEndPointName, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json', Authorization: `Bearer ${token}` },
        body: safeStringify({
            application: app,
            language: lang,
        }),
    })

    if (translation.status === 200) {
        const result = await translation.json()

        return Object.keys(result).length !== 0 && result.constructor === Object ? result : undefined
    } else {
        return undefined
    }
}

export function getAvailableLanguages() {
    return availableLanguages
}

export async function getDynamicTranslation(getAuthToken: () => Promise<string>, app: string = 'APP-NTF', lang: string = 'LAN-EN', text: string[]) {
    // const token = await getIdTokenFromMsal(msal)
    const token = await getAuthToken
    const translation = await fetch(dynamicTranslationEndPointName, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json', Authorization: `Bearer ${token}` },
        body: safeStringify({
            application: app,
            language: lang,
            texts: text
        }),
    })

    if (translation.status === 200) {
        const result = await translation.json()
        return result && result.length > 0 ? result : ''
    } else {
        return ''
    }
}
