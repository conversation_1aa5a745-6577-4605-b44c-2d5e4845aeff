import Contextualization from '@/common/components/Contextualization'
import { enviroment } from '@/common/configurations/enviroment'
import { ApplicationContextProvider } from '@/common/contexts/ApplicationContext'
import { AuthGuardProvider } from '@/common/contexts/AuthGuardContext'
import { HeaderNavbarContextProvider } from '@/common/contexts/HeaderContex'
import { NtfSmartFeedContextProvider } from '@/common/contexts/NtfSmartFeedContext'
import { NtfTableViewContextProvider } from '@/common/contexts/NtfTableViewContext'
import { TemplatesContextProvider } from '@/common/contexts/TemplatesContext'
import { UserContextProvider } from '@/common/contexts/UserContext'
import UserRuleContextProvider from '@/common/contexts/UserRuleContext'
import { createPublicClientApplication } from '@/common/factories/msal-factory'
import { useLocale } from '@/common/hooks'
import BaseLayout from '@/common/layouts/BaseLayout'
import '@/common/styles/globals.css'
import '@/common/styles/icons.css'
import { getIdTokenFromMsal } from '@/common/utils/translation'
import { AccountInfo, EventType, InteractionType } from '@azure/msal-browser'
import { MsalAuthenticationTemplate, MsalProvider } from '@azure/msal-react'
import { getMessages, TranslationContextProvider } from '@celanese/celanese-sdk'
import { ThemeProvider } from '@celanese/ui-lib'
import '@celanese/ui-lib/src/common/styles/icons.css'
import '@fontsource/roboto/300.css'
import '@fontsource/roboto/400.css'
import '@fontsource/roboto/500.css'
import '@fontsource/roboto/700.css'
import { LicenseInfo } from '@mui/x-license-pro'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { AppProps } from 'next/app'
import { useEffect, useState } from 'react'
import { IntlProvider } from 'react-intl'

LicenseInfo.setLicenseKey(enviroment.muiLicenseKey ?? '')

const msalInstance = createPublicClientApplication()
msalInstance.initialize()

const accounts = msalInstance.getAllAccounts()
if (accounts.length > 0) {
    msalInstance.setActiveAccount(accounts[0])
}

const getAuthToken = (): Promise<string> => getIdTokenFromMsal(msalInstance)

msalInstance.addEventCallback((event) => {
    if (!event) {
        return
    }
    if (event.eventType === EventType.LOGIN_SUCCESS && event.payload) {
        const account: AccountInfo = event.payload as AccountInfo
        msalInstance.setActiveAccount(account)
    }
})

const queryClient = new QueryClient()

export default function App({ Component, pageProps }: AppProps) {
    const { locale, switchLocale } = useLocale()
    const [localeCode, setLocaleCode] = useState<string>(locale)
    const [messages, setMessages] = useState<any>()
    const [shouldTranslateDynamic, setShouldTranslateDynamic] = useState<boolean>()
    const [dynamicTranslationLoading, setDynamicTranslationLoading] = useState(false)

    const cacheNameShouldTranslate = 'shouldTranslateDynamic'

    useEffect(() => {
        getMessages(localeCode, getAuthToken).then((m) => {
            if (m) {
                setMessages(m)
            }
        })
    }, [localeCode])

    useEffect(() => {
        const cacheValue = window.localStorage.getItem(cacheNameShouldTranslate)
        console.log(cacheValue)
        if (cacheValue && cacheValue === 'true') {
            setShouldTranslateDynamic(true)
        } else {
            setShouldTranslateDynamic(false)
        }
    }, [])

    useEffect(() => {
        if (shouldTranslateDynamic !== undefined) {
            window.localStorage.setItem(cacheNameShouldTranslate, JSON.stringify(shouldTranslateDynamic))
        }
    }, [shouldTranslateDynamic])

    return (
        <IntlProvider locale={locale as string} messages={messages}>
            <MsalProvider instance={msalInstance}>
                <MsalAuthenticationTemplate interactionType={InteractionType.Redirect}>
                    <UserContextProvider msalInstance={msalInstance}>
                        <UserRuleContextProvider>
                            <TranslationContextProvider getAuthToken={getAuthToken}>
                                <QueryClientProvider client={queryClient}>
                                    <ThemeProvider>
                                        <AuthGuardProvider>
                                            <Contextualization>
                                                <HeaderNavbarContextProvider>
                                                    <NtfTableViewContextProvider>
                                                        <NtfSmartFeedContextProvider>
                                                            <ApplicationContextProvider>
                                                                <TemplatesContextProvider>
                                                                    <BaseLayout
                                                                        setLocaleCode={setLocaleCode}
                                                                        shouldTranslateDynamicState={{
                                                                            shouldTranslateDynamic,
                                                                            setShouldTranslateDynamic,
                                                                        }}
                                                                        dynamicTranslationLoadingState={{
                                                                            dynamicTranslationLoading,
                                                                            setDynamicTranslationLoading,
                                                                        }}
                                                                    >
                                                                        <Component {...pageProps} />
                                                                    </BaseLayout>
                                                                </TemplatesContextProvider>
                                                            </ApplicationContextProvider>
                                                        </NtfSmartFeedContextProvider>
                                                    </NtfTableViewContextProvider>
                                                </HeaderNavbarContextProvider>
                                            </Contextualization>
                                        </AuthGuardProvider>
                                    </ThemeProvider>
                                </QueryClientProvider>
                            </TranslationContextProvider>
                        </UserRuleContextProvider>
                    </UserContextProvider>
                </MsalAuthenticationTemplate>
            </MsalProvider>
        </IntlProvider>
    )
}
