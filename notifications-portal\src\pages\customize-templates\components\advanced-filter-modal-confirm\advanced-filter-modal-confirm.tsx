// ConfirmationModal.tsx
import React from 'react'
import { Modal, Box, Typography, Button } from '@mui/material'
import * as styles from './styles'
import { translate } from '@celanese/celanese-sdk'

interface ConfirmationModalProps {
    open: boolean;
    handleClose: () => void;
    onStay: () => void;
    onContinue: () => void;
}

const ConfirmationModal: React.FC<ConfirmationModalProps> = ({ open, handleClose, onStay, onContinue }) => {
    return (
        <Modal
            open={open}
            aria-labelledby="confirmation-modal-title"
        >
            <Box sx={styles.modalStyles}>
                <Typography sx={styles.modalText} id="confirmation-modal-title" variant="h6">
                    {translate('app.templates.alerts.addedAllUsers')}
                </Typography>
                <Box sx={styles.modalButtonBox}>
                    <Button variant="outlined" onClick={onStay} sx={{textTransform: 'uppercase'}}>
                        {translate('app.common.continueEditing')}
                    </Button>
                    <Button variant="contained" onClick={onContinue} sx={{textTransform: 'uppercase'}}>
                        {translate('app.common.finish')}
                    </Button>
                </Box>
            </Box>
        </Modal>
    )
}

export default ConfirmationModal
