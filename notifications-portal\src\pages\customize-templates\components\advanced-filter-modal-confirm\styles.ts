import { CSSObject } from '@emotion/react'

export const modalStyles: CSSObject = {
    padding: '16px',
    backgroundColor: 'background.paper',
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: 'translate(-50%, -50%)',
    width: '361px',
    height: 'auto',
    borderRadius: '6px',
    opacity: '1',
    gap: '0px'
}

export const modalText: CSSObject = {
    width: '326px',
    height: 'auto',
    padding: '4px 0px 4px 0px',
    gap: '0px',
    opacity: '0px',
    fontFamily: 'Roboto',
    fontSize: '14px',
    lineHeight: '24px',
    textUnderlinePosition:'from-font',
    textAlign: 'center',
}

export const modalTextBox: CSSObject = {
    display: 'flex',
    justifyContent: 'center',
    width: '331px',
    height:'auto',
    gap: '20px',
    opacity: '0px',
}

export const modalButtonBox: CSSObject = {
    width: '322px',
    height: '36px',
    gap: '10px',
    opacity: '0px',
    marginTop: '10px',
    display: 'flex',
    justifyContent: 'flex-end',
}