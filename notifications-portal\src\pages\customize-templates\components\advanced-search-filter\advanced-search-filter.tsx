import React from 'react'
import { Box, Popover, Button, Autocomplete, TextField, Typography } from '@mui/material'
import * as styles from './styles'
import { NoTranslate, translate } from '@celanese/celanese-sdk'
import { Controller } from 'react-hook-form'
import CircularProgress from '@mui/material/CircularProgress'
import { UseAdvancedFilterLogicProps } from '../../hooks/useAdvancedFilterLogic'

interface FilterModalProps {
    anchorEl: HTMLElement | null
    filterProperties: UseAdvancedFilterLogicProps
}

const FilterModal = ({
    anchorEl,
    filterProperties,
}: FilterModalProps) => {
    const {
        id,
        open,
        handleClose,
        handleClearFilterClick,
        control,
        siteOptions,
        setValue,
        defaultItem,
        setDisabledFields,
        setLoading,
        advancedSearchOptions,
        disabledFields,
        roleOptionsSite,
        loading,
        roleOptionsApp,
        teamOptions,
        unitOptions,
        locationOptions,
        handleSubmit,
        onSubmit,
    } = filterProperties

    return (
        <Popover
            id={id}
            open={open}
            anchorEl={anchorEl}
            onClose={handleClose}
            anchorOrigin={{
                vertical: 'bottom',
                horizontal: 'left',
            }}
            transformOrigin={{
                vertical: 'top',
                horizontal: 'left',
            }}
            sx={styles.popoverStyles}
        >
            <Box sx={styles.popoverBox}>
                <Box sx={styles.clearButtonContainer}>
                    <Button variant="outlined" onClick={handleClearFilterClick} sx={styles.clearButton}>
                        {translate('app.common.clear')}
                    </Button>
                </Box>
                <Box sx={styles.filterContainer}>
                    <Controller
                        control={control}
                        name="reportingSite"
                        render={({ field: { onChange, ...field } }) => (
                            <Autocomplete
                                id="tags-outlined"
                                options={siteOptions}
                                disableClearable
                                size="small"
                                renderInput={(params) => (
                                    <NoTranslate>
                                        <TextField
                                            {...params}
                                            label={translate('app.common.site')}
                                            placeholder={translate('app.common.site')}
                                            sx={{
                                                width: '100%',
                                            }}
                                        />
                                    </NoTranslate>
                                )}
                                onChange={(e, value) => {
                                    if (value) setValue('reportingSite', value)
                                    setValue('roleSite', defaultItem)
                                    setValue('team', defaultItem)
                                    setValue('unit', defaultItem)
                                    setValue('reportingLocation', defaultItem)
                                    setDisabledFields((prev) => ({ ...prev, roleSite: true, team: true, unit: true, reportingLocation: true }))
                                    setLoading((prev) => ({ ...prev, roleSite: true, team: true, unit: true }))
                                }}
                                renderOption={(props, option) => (
                                    <li
                                        {...props}
                                        style={{
                                            display: 'flex',
                                            flexDirection: 'column',
                                            justifyContent: 'flex-start',
                                            alignItems: 'flex-start',
                                        }}
                                    >
                                        <NoTranslate>
                                            <Typography>{option.label}</Typography>
                                        </NoTranslate>
                                    </li>
                                )}
                                {...field}
                            />
                        )}
                    />
                    <Controller
                        control={control}
                        name="roleSite"
                        render={({ field: { onChange, ...field } }) => (
                            <Autocomplete
                                id="tags-outlined"
                                disabled={advancedSearchOptions.rolesSite?.length === 0 || disabledFields.roleSite}
                                options={roleOptionsSite}
                                disableClearable
                                loading={loading.roleSite}
                                size="small"
                                onChange={(e, value) => {
                                    if (value) setValue('roleSite', value)
                                }}
                                renderInput={(params) => (
                                    <NoTranslate>
                                        <TextField
                                            {...params}
                                            label={translate('app.common.roleSite')}
                                            placeholder={translate('app.common.roleSite')}
                                            sx={{
                                                width: '100%',
                                            }}
                                            InputProps={{
                                                ...params.InputProps,
                                                endAdornment: (
                                                    <React.Fragment>
                                                        {loading.roleSite ? <CircularProgress color="inherit" size={20} /> : null}
                                                        {params.InputProps?.endAdornment}
                                                    </React.Fragment>
                                                ),
                                            }}
                                        />
                                    </NoTranslate>
                                )}
                                renderOption={(props, option) => (
                                    <li
                                        {...props}
                                        style={{
                                            display: 'flex',
                                            flexDirection: 'column',
                                            justifyContent: 'flex-start',
                                            alignItems: 'flex-start',
                                        }}
                                    >
                                        <NoTranslate>
                                            <Typography>{option.label}</Typography>
                                        </NoTranslate>
                                    </li>
                                )}
                                {...field}
                            />
                        )}
                    />
                    <Controller
                        control={control}
                        name="roleApplication"
                        render={({ field: { onChange, ...field } }) => (
                            <Autocomplete
                                id="tags-outlined"
                                options={roleOptionsApp}
                                disableClearable
                                size="small"
                                onChange={(e, value) => {
                                    if (value) setValue('roleApplication', value)
                                }}
                                renderInput={(params) => (
                                    <NoTranslate>
                                        <TextField
                                            {...params}
                                            label={translate('app.common.roleApplication')}
                                            placeholder={translate('app.common.roleApplication')}
                                            sx={{
                                                width: '100%',
                                            }}
                                        />
                                    </NoTranslate>
                                )}
                                renderOption={(props, option) => (
                                    <li
                                        {...props}
                                        style={{
                                            display: 'flex',
                                            flexDirection: 'column',
                                            justifyContent: 'flex-start',
                                            alignItems: 'flex-start',
                                        }}
                                    >
                                        <NoTranslate>
                                            <Typography>{option.label}</Typography>
                                        </NoTranslate>
                                    </li>
                                )}
                                {...field}
                            />
                        )}
                    />
                    <Controller
                        control={control}
                        name="team"
                        render={({ field: { onChange, ...field } }) => (
                            <Autocomplete
                                id="tags-outlined"
                                disabled={advancedSearchOptions.teams?.length === 0 || disabledFields.team}
                                options={teamOptions}
                                loading={loading.team}
                                disableClearable
                                size="small"
                                onChange={(e, value) => {
                                    if (value) setValue('team', value)
                                }}
                                renderInput={(params) => (
                                    <NoTranslate>
                                        <TextField
                                            {...params}
                                            label={translate('app.common.team')}
                                            placeholder={translate('app.common.team')}
                                            sx={{
                                                width: '100%',
                                            }}
                                            InputProps={{
                                                ...params.InputProps,
                                                endAdornment: (
                                                    <React.Fragment>
                                                        {loading.team ? <CircularProgress color="inherit" size={20} /> : null}
                                                        {params.InputProps?.endAdornment}
                                                    </React.Fragment>
                                                ),
                                            }}
                                        />
                                    </NoTranslate>
                                )}
                                renderOption={(props, option) => (
                                    <li
                                        {...props}
                                        style={{
                                            display: 'flex',
                                            flexDirection: 'column',
                                            justifyContent: 'flex-start',
                                            alignItems: 'flex-start',
                                        }}
                                    >
                                        <NoTranslate>
                                            <Typography>{option.label}</Typography>
                                        </NoTranslate>
                                    </li>
                                )}
                                {...field}
                            />
                        )}
                    />
                    <Controller
                        control={control}
                        name="unit"
                        render={({ field: { onChange, ...field } }) => (
                            <Autocomplete
                                id="tags-outlined"
                                disabled={advancedSearchOptions.unit?.length === 0 || disabledFields.unit}
                                options={unitOptions}
                                loading={loading.unit}
                                disableClearable
                                size="small"
                                onChange={(e, value) => {
                                    if (value) setValue('unit', value)
                                    setValue('reportingLocation', defaultItem)
                                    setDisabledFields((prev) => ({ ...prev, reportingLocation: true }))
                                    setLoading((prev) => ({ ...prev, reportingLocation: true }))
                                }}
                                renderInput={(params) => (
                                    <NoTranslate>
                                        <TextField
                                            {...params}
                                            label={translate('app.common.unit')}
                                            placeholder={translate('app.common.unit')}
                                            sx={{
                                                width: '100%',
                                            }}
                                            InputProps={{
                                                ...params.InputProps,
                                                endAdornment: (
                                                    <React.Fragment>
                                                        {loading.unit ? <CircularProgress color="inherit" size={20} /> : null}
                                                        {params.InputProps?.endAdornment}
                                                    </React.Fragment>
                                                ),
                                            }}
                                        />
                                    </NoTranslate>
                                )}
                                renderOption={(props, option) => (
                                    <li
                                        {...props}
                                        style={{
                                            display: 'flex',
                                            flexDirection: 'column',
                                            justifyContent: 'flex-start',
                                            alignItems: 'flex-start',
                                        }}
                                    >
                                        <NoTranslate>
                                            <Typography>{option.label}</Typography>
                                        </NoTranslate>
                                    </li>
                                )}
                                {...field}
                            />
                        )}
                    />
                    <Controller
                        control={control}
                        name="reportingLocation"
                        render={({ field: { onChange, ...field } }) => (
                            <Autocomplete
                                id="tags-outlined"
                                disabled={advancedSearchOptions.location?.length === 0 || disabledFields.reportingLocation}
                                options={locationOptions}
                                loading={loading.reportingLocation}
                                disableClearable
                                size="small"
                                onChange={(e, value) => {
                                    if (value) setValue('reportingLocation', value)
                                }}
                                renderInput={(params) => (
                                    <NoTranslate>
                                        <TextField
                                            {...params}
                                            label={locationOptions.length > 0 ? translate('app.common.reportingLocation') : translate('app.templates.alerts.noOptions')}
                                            placeholder={translate('app.common.reportingLocation')}
                                            sx={{
                                                width: '100%',
                                            }}
                                            InputProps={{
                                                ...params.InputProps,
                                                endAdornment: (
                                                    <React.Fragment>
                                                        {loading.reportingLocation ? <CircularProgress color="inherit" size={20} /> : null}
                                                        {params.InputProps?.endAdornment}
                                                    </React.Fragment>
                                                ),
                                            }}
                                        />
                                    </NoTranslate>
                                )}
                                renderOption={(props, option) => (
                                    <li
                                        {...props}
                                        style={{
                                            display: 'flex',
                                            flexDirection: 'column',
                                            justifyContent: 'flex-start',
                                            alignItems: 'flex-start',
                                        }}
                                    >
                                        <NoTranslate>
                                            <Typography>{option.label}</Typography>
                                        </NoTranslate>
                                    </li>
                                )}
                                {...field}
                            />
                        )}
                    />
                </Box>

                <Box sx={styles.applyButtonContainer}>
                    <Button variant="outlined" onClick={handleSubmit(onSubmit)} sx={styles.applyButton}>
                        {translate('app.common.applyFilters')}
                    </Button>
                </Box>
            </Box>
        </Popover>
    )
}

export default FilterModal
