import { CSSObject } from '@emotion/react'

export const modalStyles: CSSObject = {
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: 'translate(-50%, -50%)',
    width: '800px',
    height: '800px',
    bgcolor: 'background.paper',
    borderRadius: '8px',
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'flex-start',
    p: 4,
}

export const titleModal: CSSObject = {
   mb: 3,
   textAlign: 'left'
}

export const buttonFilter: CSSObject = {
    width: '130px',
    height: '30px',
    padding: '4px 10px',
    gap: '8px',
    borderRadius: '4px',
    borderWidth: '1px',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    
 }

 export const text: CSSObject = {
    fontSize: '14px',
    fontFamily: 'Roboto',
    paddingTop: '15px'
 }
 
 export const popoverStyles: CSSObject = {
   // maxWidth: '263px',
   padding: '5px',
   display: 'flex',
   flexDirection: 'column',
   alignItems: 'flex-start',
   justifyContent: 'flex-start',
   borderRadius: '8px',
}

export const popoverBox: CSSObject = {
    width: '263px',
    borderRadius: '4px 0 0 0',
    backgroundColor: 'primary.contrastText',
    boxShadow: '0px 4px 10px rgba(0, 0, 0, 0.1)',
    opacity: 1,
    display: 'flex',
    flexDirection: 'column',
}

export const clearButtonContainer: CSSObject = {
   marginTop: 'auto',
   textAlign: 'right',
   display: 'flex',
   justifyContent: 'flex-end',
   textTransform: 'uppercase',
}

export const clearButton: CSSObject = {
   width: '100%',
   height: '36px',
   display: 'flex',
   textTransform: 'uppercase',
   justifyContent: 'flex-end',
   alignItems: 'center',
   border: '1px solid transparent',
   margin: '5px 0px 5px 0px',
   '&:hover': {
      backgroundColor: 'transparent',
      border: '1px solid transparent',
  },
  '&:focus': {
        outline: 'none',
    },
}

export const applyButtonContainer: CSSObject = {
   width: '250px',
   height: '36px',
   gap: '8px',
   display: 'flex',
   justifyContent: 'flex-end',
   marginBottom: '10px'
}

export const applyButton: CSSObject = {
   height: '36px',
   display: 'flex',
   justifyContent: 'flex-end',
   alignItems: 'center',
   backgroundColor: 'primary.main',
   color: 'primary.contrastText',
   textTransform: 'uppercase',
   '&:hover': {
        backgroundColor: 'primary.main',
        color: 'primary.contrastText',
    },
    '&:focus': {
        backgroundColor: 'primary.main',
        outline: 'none',
    },
}


export const selectField: CSSObject = {
   marginBottom: '16px',
}

export const filterContainer: CSSObject = {
   minWidth: '150px',
   padding: '0px 10px 10px 10px',
   display: 'flex',
   flexDirection: 'column',
   gap: '9px',

   '& .MuiInputLabel-outlined': {
       top: '-4px',
       fontSize: '14px'
   },
   '& .MuiFormLabel-root-MuiInputLabel-root': {
       top: '1px',
   },
   '& .MuiInputLabel-root': {
     top: '1px',
     fontSize: '14px',
     
     '&.Mui-focused, &.MuiFormLabel-filled': {
       top: '1px',
     },
     
   },

   
}

export const select: CSSObject = {
   minWidth: '215px',
   height: '40px',
   '& .MuiOutlinedInput-root': {
    padding: '2px !important',
  },
   '& .MuiSelect-select': {
       padding: '5px'
   },
}

