/* eslint-disable react/jsx-no-undef */
import React, { useState, useEffect, Dispatch, SetStateAction } from 'react'
import { Box, Typography, Button, Modal, CircularProgress } from '@mui/material'
import * as styles from './styles'
import FilterListIcon from '@mui/icons-material/FilterList'
import FilterModal from '../advanced-search-filter/advanced-search-filter'
import { ClnCheckbox } from '@celanese/ui-lib'
import CloseIcon from '@mui/icons-material/Close'
import ConfirmationModal from '../advanced-filter-modal-confirm/advanced-filter-modal-confirm'
import { translate } from '@celanese/celanese-sdk'
import { User } from '../../../../common/models/user'
import { AdvancedSearchOptionsProps } from '@/common/models/customizeTemplates'
import useAdvancedFilterLogic from '../../hooks/useAdvancedFilterLogic'

interface AdvancedSearchModalProps {
    open: boolean
    handleClose: () => void
    users: User[]
    filteredUsers: any
    loading: boolean
    applyFilter: (params?: any) => void
    usersFieldArray?: any
    blockUsersFieldArray?: any
    getFieldsOptions: {
      searchRolesBySite: (params?: any) => void,
      searchTeamsBySite: (params?: any) => void,
      searchUnitsBySite: (params?: any) => void,
      searchReportingLocationsByUnit: (params?: any) => void,
    },
    isBlockUsers: boolean
    advancedSearchOptions: AdvancedSearchOptionsProps
    setAdvancedSearchOptions: Dispatch<SetStateAction<AdvancedSearchOptionsProps>>
}

const AdvancedSearchModal: React.FC<AdvancedSearchModalProps> = ({
    open,
    handleClose,
    users,
    filteredUsers,
    loading,
    applyFilter,
    isBlockUsers,
    usersFieldArray,
    blockUsersFieldArray,
    getFieldsOptions,
    advancedSearchOptions,
    setAdvancedSearchOptions
}) => {
    const [filterAnchorEl, setFilterAnchorEl] = useState<null | HTMLElement>(null)
    const [selectedUsers, setSelectedUsers] = useState<any[]>([])
    const [selectedBlockUsers, setSelectedBlockUsers] = useState<any[]>([])
    const [selectAll, setSelectAll] = useState(false)

    const [openConfirmationModal, setOpenConfirmationModal] = useState(false)

    const handleOpenFilter = (event: React.MouseEvent<HTMLElement>) => {
        setFilterAnchorEl(event.currentTarget)
    }

    const handleUnselectFilter = () => {
        setFilterAnchorEl(null)
    }

    const filterProperties = useAdvancedFilterLogic({
        open: Boolean(filterAnchorEl),
        applyFilter,
        getFieldsOptions,
        advancedSearchOptions,
        handleClose: handleUnselectFilter
    })

    const {
        handleCloseFilter,
        setDisabledFields
    } = filterProperties

    const handleCloseModal = () => {
        setOpenConfirmationModal(true)
    }

    const handleStayOnPage = () => {
        setOpenConfirmationModal(false)
    }

    const handleContinueWithoutSaving = () => {
        applyFilter()
        handleCloseFilter()
        setDisabledFields({
            roleSite: true,
            team: true,
            unit: true,
            reportingLocation: true,
        })
        setOpenConfirmationModal(false)
        handleClose()
    }

    const handleCancel = () => {
        applyFilter()
        handleCloseFilter()
        setDisabledFields({
            roleSite: true,
            team: true,
            unit: true,
            reportingLocation: true,
        })
        setSelectedUsers([])
        handleClose()
    }

    const handleSave = () => {
        const usersToRemove = usersFieldArray.subscribedUsers.filter(
            (user: any) => !selectedUsers.some((selectedUser) => selectedUser.email === user.email)
        )
        usersFieldArray.removeUsers(usersToRemove)

        const usersToAdd = selectedUsers.filter(
            (selectedUser) =>
                !usersFieldArray.subscribedUsers.some((existingUser: any) => existingUser.email === selectedUser.email)
        )
        if (usersToAdd.length > 0) {
            usersFieldArray.appendUsers(usersToAdd)
        }
        handleClose()
    }

    const clickSave = () => {
        if (isBlockUsers) {
            handleSaveBlock()
        } else {
            handleSave()
        }
    }

    const handleSaveBlock = () => {
        const usersToRemove = blockUsersFieldArray.blockSubscribedUsers.filter(
            (user: any) => !selectedBlockUsers.some((selectedUser) => selectedUser.email === user.email)
        )
        blockUsersFieldArray.removeBlockUsers(usersToRemove)

        const usersToAdd = selectedBlockUsers.filter(
            (selectedUser) =>
                !blockUsersFieldArray.blockSubscribedUsers.some(
                    (existingUser: any) => existingUser.email === selectedUser.email
                )
        )

        if (usersToAdd.length > 0) {
            blockUsersFieldArray.appendBlockUsers(usersToAdd)
        }

        handleClose()
    }

    const handleToggleSelectAll = () => {
        const newSelectAll = !selectAll
        setSelectAll(newSelectAll)

        if (isBlockUsers) {
            setSelectedBlockUsers(newSelectAll ? [...users] : [])
        } else {
            setSelectedUsers(newSelectAll ? [...users] : [])
        }
    }

    useEffect(() => {
        if (open) {
            if (isBlockUsers) {
                setSelectedBlockUsers(blockUsersFieldArray.blockSubscribedUsers)
            } else {
                setSelectedUsers(usersFieldArray.subscribedUsers)
            }
        }
    }, [open, isBlockUsers, usersFieldArray.subscribedUsers, blockUsersFieldArray.blockSubscribedUsers])

    useEffect(() => {
        setDisabledFields({
            roleSite: true,
            team: true,
            unit: true,
            reportingLocation: true,
        })
    }, [users])

    return (
        <Modal open={open} aria-labelledby="modal-title">
            <Box sx={styles.modalStyles}>
                <Button color="inherit" onClick={handleCloseModal} sx={styles.closeButton}>
                    <CloseIcon />
                </Button>

                <Typography id="modal-title" variant="h6" sx={styles.titleModal}>
                    {translate('app.templates.advancedSearch')}
                </Typography>

                <Button
                    variant="outlined"
                    startIcon={<FilterListIcon />}
                    onClick={handleOpenFilter}
                    sx={styles.buttonFilter}
                >
                    {translate('app.common.filters')}
                </Button>

                <FilterModal
                    anchorEl={filterAnchorEl}
                    filterProperties={filterProperties}
                />

                <Typography variant="body2" color="textSecondary" sx={styles.text}>
                    {translate('app.templates.totalUsersSelected')}:{' '}
                    {isBlockUsers ? selectedBlockUsers.length : selectedUsers.length}
                </Typography>

                <Box sx={styles.users}>
                    <Box sx={styles.checks}>
                        <ClnCheckbox
                            items={[{ label: '', value: 'all' }]}
                            value={selectAll ? [{ label: '', value: 'all' }] : []}
                            sxProps={{}}
                            onChange={handleToggleSelectAll}
                        ></ClnCheckbox>
                    </Box>
                    <Box sx={styles.contentUsersTitle}>
                        <span>User</span>
                    </Box>
                </Box>

                <Box sx={styles.boxUsers}>
                    {!loading && users.length === 0 && (
                        <Typography sx={{ marginTop: '15px', textAlign: 'center' }}>{translate('app.templates.alerts.noUsersFound')}</Typography>
                    )}
                    {!loading && users.map((user: any, index: any) => (
                        <Box
                            key={user.email}
                            sx={[
                                styles.users,
                                { backgroundColor: index % 2 === 0 ? 'grey.300' : 'background.default' },
                            ]}
                        >
                            {/* Lado do Checkbox */}
                            <Box sx={styles.checks}>
                                <ClnCheckbox
                                    items={[
                                        {
                                            label: '',
                                            value: user.email,
                                        },
                                    ]}
                                    value={(isBlockUsers ? selectedBlockUsers : selectedUsers).map((u) => ({
                                        label: u.email,
                                        value: u.email,
                                    }))}
                                    sxProps={{}}
                                    onChange={(updatedSelection) => {
                                        const isChecked = updatedSelection.some((item) => item.value === user.email)
                                        if (isBlockUsers) {
                                            setSelectedBlockUsers((prev) =>
                                                isChecked ? [...prev, user] : prev.filter((u) => u.email !== user.email)
                                            )
                                        } else {
                                            setSelectedUsers((prev) =>
                                                isChecked ? [...prev, user] : prev.filter((u) => u.email !== user.email)
                                            )
                                        }
                                    }}
                                />
                            </Box>

                            {/* Lado dos Emails (Nome e E-mail) */}
                            <Box sx={styles.contentUsers}>
                                <span style={{ marginBottom: '4px' }}>
                                    {user.lastName}, {user.firstName} {'(' + user.email + ')'}
                                </span>
                            </Box>
                        </Box>
                    ))}
                    {loading && <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', width: '100%', height: '100%' }} ><CircularProgress /></Box>}
                </Box>

                <Box sx={styles.boxButtons}>
                    <Button
                        variant="outlined"
                        onClick={handleCancel}
                        sx={{ marginRight: '8px', textTransform: 'uppercase' }}
                    >
                        {translate('app.common.cancel')}
                    </Button>
                    <Button variant="contained" onClick={clickSave} sx={{ textTransform: 'uppercase' }}>
                        {translate('app.common.save')}
                    </Button>
                </Box>

                <ConfirmationModal
                    open={openConfirmationModal}
                    handleClose={() => setOpenConfirmationModal(false)}
                    onStay={handleStayOnPage}
                    onContinue={handleContinueWithoutSaving}
                />
            </Box>
        </Modal>
    )
}

export default AdvancedSearchModal
