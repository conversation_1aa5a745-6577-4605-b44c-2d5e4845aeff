import { CSSObject } from '@emotion/react'

export const modalStyles: CSSObject = {
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: 'translate(-50%, -50%)',
    width: '950px',
    height: '763px',
    bgcolor: 'background.paper',
    borderRadius: '8px',
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'flex-start',
    p: 4,

    '@media (max-height: 768px)': {
        height: '673px',
    },

    '@media (max-height: 690px)': {
        height: '520px'
    }
}

export const titleModal: CSSObject = {
    mb: 3,
    textAlign: 'left',
}

export const buttonFilter: CSSObject = {
    width: '130px',
    height: '30px',
    padding: '4px 10px',
    gap: '8px',
    borderRadius: '4px',
    borderWidth: '1px',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    textTransform: 'uppercase',
}

export const text: CSSObject = {
    fontSize: '14px',
    fontFamily: 'Roboto',
    paddingTop: '15px',
}

export const popoverStyles: CSSObject = {
    maxWidth: '300px',
    padding: '16px',
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'flex-start',
    justifyContent: 'flex-start',
    borderRadius: '8px',
}

export const popoverBox: CSSObject = {
    padding: '8px',
    display: 'flex',
    flexDirection: 'column',
    gap: '8px',
}

export const checkBox: CSSObject = {
    width: '12px',
    height: '12px',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: '16px',
    marginTop: '14px',
    fontSize: '20px',
    color: 'primary.contrastText',
    borderRadius: '2px',
    border: '1px solid primary.main',
    backgroundColor: 'primary.main',
}

export const contentUsersTitle: CSSObject = {
    width: '844px',
    height: '56px',
    padding: '6px 0px 0px 0px',
    gap: '0px',
    border: '0px 0px 1px 0px',
    opacity: '0px',
    display: 'flex',
    alignItems: 'center',
    backgroundColor: 'background.default',
    margin: '0px',
}

export const contentUsers: CSSObject = {
    width: '840px',
    height: '56px',
    padding: '6px 0px 0px 0px',
    gap: '0px',
    border: '0px 0px 1px 0px',
    opacity: '0px',
    display: 'flex',
    alignItems: 'center',
}

export const boxUsers: CSSObject = {
    overflowY: 'auto',
    width: '892px',
    height: '510px',
}

export const boxLoading: CSSObject = {
    width: '892px',
    height: '510px',
}

export const boxButtons: CSSObject = {
    display: 'flex',
    justifyContent: 'flex-end',
    marginTop: '16px',
    width: '890px',
}

export const users: CSSObject = {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderBottom: '1px solid',
    borderColor: 'grey.400',
    backgroundColor: 'background.default',
}

export const checks: CSSObject = {
    width: '38px',
    height: '56px',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: '10px',
}

export const box: CSSObject = {
    width: '38px',
    height: '38px',
}

export const closeButton: CSSObject = {
    position: 'absolute',
    top: '31px',
    right: '16px',
}
