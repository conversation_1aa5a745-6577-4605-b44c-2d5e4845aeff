// components/ChannelCheckboxGroup.tsx

import { useChannelsRequest } from '@/common/hooks/useChannelsRequest'
import { ClnCheckbox } from '@celanese/ui-lib'
import { CheckboxItem } from '@celanese/ui-lib'
import { FC } from 'react'
import * as styles from '../styles'
import { NoTranslate, translate } from '@celanese/celanese-sdk'

interface ChannelCheckboxGroupProps {
    selectedChannels?: CheckboxItem[]
    onChange: (value: CheckboxItem[]) => void
    error?: boolean
    disabled?: boolean
}

const ChannelCheckboxGroup: FC<ChannelCheckboxGroupProps> = ({ selectedChannels, onChange, error, disabled }) => {
    const { data: CHANNELS } = useChannelsRequest(true, ['channels'])

    const mappedChannels =
        CHANNELS?.map((channel) => ({
            value: channel.externalId,
            label: channel.name,
        })) || []

    return (
        <NoTranslate>
            <ClnCheckbox
                items={mappedChannels}
                value={selectedChannels || []}
                sxProps={styles.checkbox}
                onChange={onChange}
                error={error ? translate('app.templates.required') : ''}
                disabled={disabled}
            />
        </NoTranslate>
    )
}

export default ChannelCheckboxGroup
