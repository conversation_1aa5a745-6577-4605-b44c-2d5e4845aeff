import { useChannelsRequest } from '@/common/hooks/useChannelsRequest'
import { FC, useEffect, useState } from 'react'
import { NoTranslate, translate } from '@celanese/celanese-sdk'
import {
    Backdrop,
    Box,
    Card,
    CardContent,
    Checkbox,
    CircularProgress,
    FormControlLabel,
    FormGroup,
    Typography,
} from '@mui/material'
import * as styles from '../styles'
import { ClnTooltip, MatIcon } from '@celanese/ui-lib'

interface CheckboxItem {
    value: string
    label: string
}

interface ChannelCheckboxGroupProps {
    selectedChannels: CheckboxItem[]
    onChange: (value: CheckboxItem[]) => void
    error?: boolean
    disabled?: boolean
}

const defaultChannels: CheckboxItem[] = [
    { value: 'TEAMS', label: 'TEAMS' },
    { value: 'EMAIL', label: 'EMAIL' },
    { value: 'SMS', label: 'SMS' },
]

const ChannelCheckboxGroupComponent: FC<ChannelCheckboxGroupProps> = ({
    selectedChannels,
    onChange,
    error,
    disabled,
}) => {
    const { data: CHANNELS, isLoading } = useChannelsRequest(true, ['channels'])
    const [mappedChannels, setMappedChannels] = useState<CheckboxItem[]>(defaultChannels)
    const [loading, setLoading] = useState(true)

    useEffect(() => {
        if (CHANNELS?.length) {
            setMappedChannels(
                CHANNELS.map((channel) => ({
                    value: channel.externalId,
                    label: channel.description,
                }))
            )
            setLoading(false)
        }
    }, [CHANNELS])

    const handleCheckboxChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        const { value, checked } = event.target
        const newSelection = checked
            ? [...selectedChannels, mappedChannels.find((ch) => ch.value === value)!]
            : selectedChannels.filter((ch) => ch.value !== value)

        onChange(newSelection)
    }

    return (
        <NoTranslate>
            <Card sx={styles.customizationTemplateCard}>
                <CardContent>
                    <Box display="flex" alignItems="center" gap={0.5}>
                        <Typography variant="subtitle1" fontWeight="bold">
                            {translate('app.templates.channels')}
                        </Typography>
                        <ClnTooltip title={translate('app.templates.alerts.channelsTooltip')} placement="right">
                            <Box display="flex" alignItems="center">
                                <MatIcon color="#757575" icon="info" fontSize="20px" />
                            </Box>
                        </ClnTooltip>
                    </Box>

                    {loading && (
                        <Backdrop sx={{ zIndex: 10 }} open={isLoading}>
                            <CircularProgress />
                        </Backdrop>
                    )}

                    <FormGroup row sx={{ mt: '20px' }}>
                        {mappedChannels.map((channel) => (
                            <FormControlLabel
                                key={channel.value}
                                control={
                                    <Checkbox
                                        checked={selectedChannels.some((ch) => ch.value === channel.value)}
                                        onChange={handleCheckboxChange}
                                        value={channel.value}
                                        disabled={loading || disabled}
                                    />
                                }
                                label={channel.label}
                            />
                        ))}
                    </FormGroup>
                </CardContent>
            </Card>
        </NoTranslate>
    )
}

export default ChannelCheckboxGroupComponent
