import { ClnButton, ClnSelect, ClnTextField, ClnTooltip, MatIcon } from '@celanese/ui-lib'
import { Controller, Control, UseFormGetValues, FieldErrors } from 'react-hook-form'
import {
    Box,
    Typography,
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableRow,
    IconButton,
    FormControl,
    MenuItem,
    Select,
    FormHelperText,
} from '@mui/material'
import { FC } from 'react'
import { NoTranslate, translate } from '@celanese/celanese-sdk'
import { ConditionalsFieldArrayProps, ValidationSchemaCustomizeTemplates } from '@/common/models/customizeTemplates'

type ConditionGroupProps = {
    conditionalsFieldArray: ConditionalsFieldArrayProps
    control: Control<ValidationSchemaCustomizeTemplates>
    disabled: boolean
    variables: { name: string; value: any; type?: string }[]
    errors: FieldErrors<ValidationSchemaCustomizeTemplates>
    getValues: UseFormGetValues<ValidationSchemaCustomizeTemplates>
}

const ConditionComponent: FC<ConditionGroupProps> = ({
    conditionalsFieldArray,
    control,
    disabled,
    variables,
    errors,
    getValues,
}) => {
    const { conditionals, appendConditional, removeConditional } = conditionalsFieldArray

    const getOperatorOptions = (index: number) => {
        const variable = variables.find((el) => el.name === getValues(`conditionals.${index}.variable`))
        return variable?.type === 'number' ? ['=', '>', '<', '≠'] : ['=', '≠']
    }

    return (
        <Box sx={{ mt: 3, p: 2, border: '1px solid #ddd', borderRadius: 2 }}>
            {/* Header Section */}
            <Box display="flex" alignItems="center" gap={0.5}>
                <Typography variant="subtitle1" fontWeight="bold">
                    {translate('app.templates.alerts.conditionals')}
                </Typography>
                <ClnTooltip title={translate('app.templates.alerts.conditionalsTooltip')} placement="right">
                    <Box display="flex" alignItems="center">
                        <MatIcon color="#757575" icon="info" fontSize="20px" />
                    </Box>
                </ClnTooltip>
            </Box>

            {/* Table Layout */}
            <Table sx={{ mt: 1 }}>
                <TableHead>
                    <TableRow>
                        <TableCell sx={{ width: '24%' }}>{translate('app.templates.conditionals.andOrOr')}</TableCell>
                        <TableCell sx={{ width: '24%' }}>{translate('app.templates.conditionals.Variable')}</TableCell>
                        <TableCell sx={{ width: '24%' }}>{translate('app.templates.conditionals.operator')}</TableCell>
                        <TableCell sx={{ width: '24%' }}>{translate('app.templates.conditionals.value')}</TableCell>
                        <TableCell sx={{ width: '6%' }}></TableCell>
                    </TableRow>
                </TableHead>
                <TableBody>
                    {conditionals.map((cond, index: number) => (
                        <TableRow key={index} sx={{ backgroundColor: index % 2 === 0 ? '#f9f9f9' : '#ffffff' }}>
                            {/* And/Or Selector (Hidden for first row) */}
                            <TableCell sx={{ width: '24%', padding: '0px 16px 0px 16px' }}>
                                {index > 0 && (
                                    <Box sx={{ height: '54px' }}>
                                    <Controller
                                        control={control}
                                        name={`conditionals.${index}.conjunction`}
                                        render={({ field }) => (
                                            <ClnSelect
                                                options={['And', 'Or']}
                                                fullWidth
                                                helperText=" "
                                                disabled={disabled}
                                                {...field}
                                            />
                                        )}
                                    />
                                    </Box>
                                )}
                            </TableCell>

                            {/* Variable Dropdown */}
                            <TableCell sx={{ width: '24%', alignContent: 'flex-start' }}>
                            <Box sx={{ height: '54px' }}>
                                <Controller
                                    control={control}
                                    name={`conditionals.${index}.variable`}
                                    render={({ field }) => (
                                        <NoTranslate>
                                            <FormControl fullWidth error={!!errors.conditionals?.[index]?.variable}>
                                                <Select
                                                    {...field}
                                                    error={!!errors.conditionals?.[index]?.variable}
                                                    disabled={disabled}
                                                >
                                                    {variables.map((item) => (
                                                        <MenuItem key={item.name} value={item.name}>
                                                            <NoTranslate>{item.name}</NoTranslate>
                                                        </MenuItem>
                                                    ))}
                                                </Select>
                                                <FormHelperText>
                                                    {errors.conditionals?.[index]?.variable
                                                        ? translate('app.templates.required')
                                                        : ''}
                                                </FormHelperText>
                                            </FormControl>
                                        </NoTranslate>
                                    )}
                                />
                            </Box>
                            </TableCell>

                            {/* Operator Dropdown */}
                            <TableCell sx={{ width: '24%' }}>
                            <Box sx={{ height: '54px' }}>
                                <Controller
                                    control={control}
                                    name={`conditionals.${index}.operator`}
                                    render={({ field }) => (
                                        <ClnSelect
                                            error={!!errors.conditionals?.[index]?.operator}
                                            options={getOperatorOptions(index)}
                                            disabled={disabled}
                                            helperText={
                                                errors.conditionals?.[index]?.operator
                                                    ? translate('app.templates.required')
                                                    : ' '
                                            }
                                            fullWidth
                                            {...field}
                                        />
                                    )}
                                />
                            </Box>
                            </TableCell>

                            {/* Value Input */}
                            <TableCell sx={{ width: '24%' }}>
                                <Controller
                                    control={control}
                                    name={`conditionals.${index}.value`}
                                    render={({ field }) => (
                                        <ClnTextField
                                            label={translate('app.templates.conditionals.value')}
                                            variant="outlined"
                                            helperText={
                                                errors.conditionals?.[index]?.value
                                                    ? translate('app.templates.required')
                                                    : ' '
                                            }
                                            error={!!errors.conditionals?.[index]?.value}
                                            fullWidth
                                            disabled={disabled}
                                            type={getOperatorOptions(index).length > 2 ? 'number' : 'text'}
                                            {...field}
                                            sx={{
                                                height: '57px'
                                            }}
                                        />
                                    )}
                                />
                            </TableCell>

                            {/* Delete Button */}
                            <TableCell sx={{ width: '6%' }}>
                                <IconButton
                                    onClick={() => removeConditional(index)}
                                    disabled={disabled}
                                    sx={{ color: 'red' }}
                                >
                                    <MatIcon fontSize="24px" icon="delete" />
                                </IconButton>
                            </TableCell>
                        </TableRow>
                    ))}
                </TableBody>
            </Table>

            {/* Add Condition Button */}
            <Box display="flex" justifyContent="flex-end" mt={2}>
                <ClnButton
                    label={translate('app.templates.conditionals.addMore')}
                    onClick={() =>
                        appendConditional({
                            variable: '',
                            operator: '',
                            value: '',
                            conjunction: 'And',
                            isNumeric: false,
                        })
                    }
                    size="large"
                    variant="text"
                    disabled={disabled}
                />
            </Box>
        </Box>
    )
}

export default ConditionComponent
