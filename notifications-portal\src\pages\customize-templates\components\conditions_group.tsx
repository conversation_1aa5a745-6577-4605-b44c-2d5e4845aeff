import { Cln<PERSON><PERSON>on, ClnSelect, ClnTextField, MatIcon } from '@celanese/ui-lib'
import { Controller, Control, UseFormGetValues, FieldErrors } from 'react-hook-form'
import { Box, Typography } from '@mui/material'
import { FC } from 'react'
import * as styles from '../styles'
import { translate } from '@celanese/celanese-sdk'
import { ConditionalsFieldArrayProps, ValidationSchemaCustomizeTemplates } from '@/common/models/customizeTemplates'

type ConditionGroupProps = {
    conditionalsFieldArray: ConditionalsFieldArrayProps
    control: Control<ValidationSchemaCustomizeTemplates>
    disabled: boolean
    variables: { name: string; value: any; type?: string; }[]
    errors: FieldErrors<ValidationSchemaCustomizeTemplates>
    getValues: UseFormGetValues<ValidationSchemaCustomizeTemplates>
}

const ConditionGroup: FC<ConditionGroupProps> = ({ conditionalsFieldArray, control, disabled, variables, errors, getValues }) => {
    const { conditionals, appendConditional, removeConditional } = conditionalsFieldArray
    const getOperatorOptions = (index: number) => {
        const variable = variables.find((el) => el.name === getValues(`conditionals.${index}.variable`))
        return variable?.type === 'number' ? ['=', '>', '<', '≠'] : ['=', '≠']
    }

    return (
        <Box sx={styles.inputContainer}>
            <Typography sx={styles.inputLabel}>{translate('app.templates.alerts.conditionals')}</Typography>
            <div>
                {conditionals.map((cond, index: number) => (
                    <Box sx={styles.formRow} key={index} mb={2}>
                        <Box sx={styles.inputContainer}>
                            {index > 0 && (
                                <Controller
                                    control={control}
                                    name={`conditionals.${index}.conjunction`}
                                    render={({ field }) => (
                                        <ClnSelect
                                            label={translate('app.templates.conditionals.andOrOr')}
                                            options={['And', 'Or']}
                                            fullWidth
                                            disabled={disabled}
                                            MenuProps={{
                                                PaperProps: {
                                                    style: { maxHeight: '150px' },
                                                },
                                            }}
                                            variant="outlined"
                                            {...field}
                                        />
                                    )}
                                />
                            )}
                        </Box>
                        <Box sx={styles.inputContainer}>
                            <Controller
                                control={control}
                                name={`conditionals.${index}.variable`}
                                render={({ field }) => (
                                    <ClnSelect
                                        error={errors.conditionals?.[index]?.variable as unknown as boolean}
                                        label={translate('app.templates.conditionals.Variable')}
                                        options={variables.map(mes => {
                                            return mes.name
                                        })}
                                        disabled={disabled}
                                        helperText={errors.conditionals?.[index]?.variable ? translate('app.templates.required') : ''}
                                        fullWidth
                                        MenuProps={{
                                            PaperProps: {
                                                style: { maxHeight: '75px' },
                                            },
                                        }}
                                        variant="outlined"
                                        {...field}
                                    />
                                )}
                            />
                        </Box>
                        <Box sx={styles.inputContainer}>
                            <Controller
                                control={control}
                                name={`conditionals.${index}.operator`}
                                render={({ field }) => (
                                    <ClnSelect
                                        error={errors.conditionals?.[index]?.operator as unknown as boolean}
                                        label={translate('app.templates.conditionals.operator')}
                                        fullWidth
                                        disabled={disabled}
                                        helperText={errors.conditionals?.[index]?.variable ? translate('app.templates.required') : ''}
                                        options={getOperatorOptions(index)}
                                        variant="outlined"
                                        {...field}
                                    />
                                )}
                            />
                        </Box>
                        <Box sx={styles.inputContainer}>
                            <Controller
                                control={control}
                                name={`conditionals.${index}.value`}
                                render={({ field }) => (
                                    <ClnTextField
                                        error={errors.conditionals?.[index]?.value as unknown as boolean}
                                        label={translate('app.templates.conditionals.value')}
                                        fullWidth
                                        disabled={disabled}
                                        helperText={errors.conditionals?.[index]?.variable ? translate('app.templates.required') : ''}
                                        type={getOperatorOptions(index).length > 2 ? 'number' : 'text'}
                                        variant="outlined"
                                        {...field}
                                    />
                                )}
                            />
                        </Box>
                        <Box sx={styles.inputContainer}>
                            <ClnButton
                                onClick={() => removeConditional(index)}
                                size="small"
                                variant="text"
                                startIcon={<MatIcon fontSize={'24px'} icon="delete" />}
                                disabled={disabled}
                            />
                        </Box>
                    </Box>
                ))}
                <Box>
                    <ClnButton
                        label={translate('app.templates.buttons.newCondition')}
                        onClick={() => appendConditional({ variable: '', operator: '', value: '', conjunction: 'And', isNumeric: false })}
                        size="small"
                        variant="text"
                        startIcon={<MatIcon fontSize={'24px'} icon="add_circle" />}
                        disabled={disabled}
                    />
                </Box>
            </div>
        </Box>
    )
}

export default ConditionGroup
