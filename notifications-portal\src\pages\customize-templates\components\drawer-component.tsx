import { CronObject } from '@/common/models/cronObject'
import dayjs from 'dayjs'
import customParseFormat from 'dayjs/plugin/customParseFormat'
import { Dispatch, SetStateAction, useState } from 'react'
import useFrequencyLogic, { FrequencyType } from '../hooks/useFrequencyLogic'
import { Box } from '@mui/material'
import { ClnSelect } from '@celanese/ui-lib'
import FrequencyWeeklyDrawerComponent from './frequency-weekly-drawer'
import FrequencyMonthlyDrawerComponent from './frequency-monthly-drawer'
import FrequencyHoursMinutesDrawerComponent from './frequency-HH-mm-drawer-component'
import { translate } from '@celanese/celanese-sdk'

interface DrawerContentProps {
    frequencyType: FrequencyType
    setCronRequest: Dispatch<SetStateAction<CronObject>>
    setIsDrawerOpen: Dispatch<SetStateAction<boolean>>
    cronRequest: CronObject
    setPreviousFrequencyType: any
    handleCancel: () => void
}

export default function DrawerComponent({
    frequencyType: initialFrequencyType,
    setCronRequest,
    setIsDrawerOpen,
    cronRequest,
    setPreviousFrequencyType,
    handleCancel,
}: DrawerContentProps) {
    dayjs.extend(customParseFormat)

    const { parseMonthsIndexToMonthsNames } = useFrequencyLogic()

    const startTime = cronRequest.time ? dayjs(cronRequest.time, 'hh:mm A') : undefined
    const endTime = cronRequest.end_time ? dayjs(cronRequest.end_time, 'hh:mm A') : undefined

    const weekday = cronRequest.day_of_week ? cronRequest.day_of_week : []

    const months = parseMonthsIndexToMonthsNames(cronRequest.months) ?? []

    const dayOfMonth = cronRequest.day_of_month ? parseInt(cronRequest.day_of_month) : 0

    const [selectedFrequency, setSelectedFrequency] = useState(initialFrequencyType)

    const handleFrequencyChange = (event: any) => {
        setPreviousFrequencyType(selectedFrequency)
        setSelectedFrequency(event.value)

        setCronRequest((prev) => {
            const updated = { ...prev, frequency: event.value }
            return updated
        })
    }

    return (
        <Box sx={{ marginTop: '15px' }}>
            {/* Frequency Type Selector */}
            <ClnSelect
                value={selectedFrequency}
                onChange={handleFrequencyChange}
                label={translate('app.templates.frequency.frequencyTypes.title')}
                options={[
                    { label: translate('app.templates.frequency.frequencyTypes.byMinute'), value: FrequencyType.ByMinute },
                    { label: translate('app.templates.frequency.frequencyTypes.byHour'), value: FrequencyType.ByHour },
                    { label: translate('app.templates.frequency.frequencyTypes.weekly'), value: FrequencyType.Weekly },
                    { label: translate('app.templates.frequency.frequencyTypes.monthly'), value: FrequencyType.Monthly },
                ]}
                fullWidth
            />

            {selectedFrequency === FrequencyType.ByMinute && (
                <FrequencyHoursMinutesDrawerComponent
                    repeatEvery={cronRequest.interval ?? 30}
                    setCronRequest={setCronRequest}
                    frequencyType="minute"
                    isMinutes={true}
                    startTime={startTime}
                    endTime={endTime}
                    setIsDrawerOpen={setIsDrawerOpen}
                    setPreviousFrequencyType={setPreviousFrequencyType}
                    handleCancel={handleCancel}
                />
            )}

            {selectedFrequency === FrequencyType.ByHour && (
                <FrequencyHoursMinutesDrawerComponent
                    repeatEvery={cronRequest.interval ?? 12}
                    setCronRequest={setCronRequest}
                    frequencyType="hourly"
                    isMinutes={false}
                    startTime={startTime}
                    endTime={endTime}
                    setIsDrawerOpen={setIsDrawerOpen}
                    setPreviousFrequencyType={setPreviousFrequencyType}
                    handleCancel={handleCancel}
                />
            )}

            {selectedFrequency === FrequencyType.Weekly && (
                <FrequencyWeeklyDrawerComponent
                    weekdays={weekday}
                    frequencyType="weekly"
                    startTime={startTime}
                    setCronRequest={setCronRequest}
                    setIsDrawerOpen={setIsDrawerOpen}
                    setPreviousFrequencyType={setPreviousFrequencyType}
                    handleCancel={handleCancel}
                />
            )}

            {selectedFrequency === FrequencyType.Monthly && (
                <FrequencyMonthlyDrawerComponent
                    months={months}
                    dayOfMonth={dayOfMonth}
                    frequencyType="monthly"
                    setCronRequest={setCronRequest}
                    setIsDrawerOpen={setIsDrawerOpen}
                    setPreviousFrequencyType={setPreviousFrequencyType}
                    handleCancel={handleCancel}
                />
            )}
        </Box>
    )
}
