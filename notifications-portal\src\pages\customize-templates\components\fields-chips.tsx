import { NoTranslate } from '@celanese/celanese-sdk'
import { Box, Chip } from '@mui/material'
import { FC } from 'react'

interface FieldsChipsProps {
    onFieldClick?: (fieldName: string) => void
    fields: { name: string; value: any; type?: string }[]
    disabled: boolean
}

const FieldsChips: FC<FieldsChipsProps> = ({
    onFieldClick,
    fields,
    disabled,
}) => {
    return (
        <NoTranslate>
            <Box display="flex" flexWrap="wrap" gap={1} sx={{ maxHeight: '125px', overflow: 'auto' }}>
                {fields.map((field, index) => (
                    <Chip
                        key={index}
                        label={field.name}
                        onClick={() => onFieldClick && onFieldClick(field.name)}
                        disabled={disabled}
                    />
                ))}
            </Box>
        </NoTranslate>
    )
}

export default FieldsChips
