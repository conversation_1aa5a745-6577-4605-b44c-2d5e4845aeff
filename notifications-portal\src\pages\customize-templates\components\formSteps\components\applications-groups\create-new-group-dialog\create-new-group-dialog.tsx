import { translate } from '@celanese/celanese-sdk'
import { ClnSelect, SelectItem } from '@celanese/ui-lib'
import {
    Dialog,
    DialogTitle,
    DialogContent,
    DialogContentText,
    Typography,
    Backdrop,
    CircularProgress,
    DialogActions,
    Button,
} from '@mui/material'
import * as styles from './styles'

interface CreateNewGroupDialogProps {
    showCreateNewGroupModal: boolean
    setShowCreateNewGroupModal: (show: boolean) => void
    selectItemsGroups: SelectItem[]
    selectedNewGroup: string | undefined
    onNewGroupOptionClick: (value: string) => void
    isLoading: boolean
    handleCreateNewGroup: () => void
}

const CreateNewGroupDialog = ({
    showCreateNewGroupModal,
    setShowCreateNewGroupModal,
    selectItemsGroups,
    selectedNewGroup,
    onNewGroupOptionClick,
    isLoading,
    handleCreateNewGroup,
}: CreateNewGroupDialogProps) => {
    return (
        <Dialog open={showCreateNewGroupModal} onClose={() => setShowCreateNewGroupModal(false)}>
            <DialogTitle id="alert-dialog-title">{translate('app.templates.group.createNewGroup')}</DialogTitle>
            <DialogContent sx={styles.duplicateModalContent}>
                <DialogContentText id="alert-dialog-description">
                    <Typography>{translate('app.templates.alerts.group.newGroup')}</Typography>
                </DialogContentText>
                <ClnSelect
                    label={translate('app.templates.group.recipientsGroupName')}
                    size="medium"
                    options={selectItemsGroups}
                    value={selectedNewGroup ?? ''}
                    onOptionClick={onNewGroupOptionClick}
                />
                <Backdrop sx={{ zIndex: 10 }} open={isLoading}>
                    <CircularProgress color="inherit" />
                </Backdrop>
            </DialogContent>
            <DialogActions>
                <Button
                    onClick={() => {
                        setShowCreateNewGroupModal(false)
                    }}
                    color="primary"
                    sx={{ textTransform: 'uppercase' }}
                >
                    {translate('app.common.cancel')}
                </Button>
                <Button
                    onClick={handleCreateNewGroup}
                    color="primary"
                    variant="contained"
                    autoFocus
                    sx={{ textTransform: 'uppercase' }}
                >
                    {translate('app.common.create')}
                </Button>
            </DialogActions>
        </Dialog>
    )
}

export default CreateNewGroupDialog
