import React from 'react'
import { <PERSON><PERSON>, <PERSON>, <PERSON>po<PERSON>, <PERSON>ton, Link } from '@mui/material'
import * as styles from './styles'
import { translate } from '@celanese/celanese-sdk'
import { ValidationSchemaCustomizeTemplates } from '@/common/models/customizeTemplates'
import { ClnTooltip, MatIcon } from '@celanese/ui-lib'

interface DeleteTemplateGroupModalProps {
    open: boolean
    onCancel: () => void
    onContinue: () => void
    templates: ValidationSchemaCustomizeTemplates[]
    isAdminLevel: boolean
}

const DeleteGroupConfirmationModal: React.FC<DeleteTemplateGroupModalProps> = ({
    open,
    onCancel,
    onContinue,
    templates,
    isAdminLevel,
}) => {
    const templatesCannotBeDeleted = templates.filter(
        (template) =>
            template.subscribedApplicationGroups.length === 1 &&
            template.subscribedExternalUsers?.length === 0 &&
            template.subscribedRoles?.length === 0 &&
            template.subscribedUsers?.length === 0
    )

    const canBeDeleted = templatesCannotBeDeleted.length === 0

    return (
        <Modal open={open} aria-labelledby="confirmation-modal-title">
            <Box sx={{ ...styles.modalStyles, width: templates.length === 0 ? '429px' : '600px' }}>
                <Typography sx={styles.modalTitle} id="confirmation-modal-title" variant="h6">
                    {translate('app.templates.alerts.group.deleteConfirmationTitle')}
                </Typography>

                {templates.length > 0 && (
                    <>
                        <Typography sx={styles.modalText} id="confirmation-modal-description" variant="h6">
                            {translate('app.templates.alerts.group.deleteConfirmationDescription')}
                        </Typography>
                        <Box sx={styles.boxTemplates}>
                            {templates.map((template, index) => (
                                <Box paddingLeft={2} sx={{ display: 'flex', gap: 1 }} key={template.externalId}>
                                    {templatesCannotBeDeleted.some(
                                        (templateCannotBeDeleted) =>
                                            templateCannotBeDeleted.externalId === template.externalId
                                    ) && (
                                        <ClnTooltip
                                            arrow={true}
                                            title={translate('app.templates.tooltips.group.deleteConfirmationTemplate')}
                                        >
                                            <span style={{ display: 'flex' }}>
                                                <MatIcon icon="info" />
                                            </span>
                                        </ClnTooltip>
                                    )}

                                    <Link
                                        href={`?id=${template.externalId}&a=${btoa(String(isAdminLevel))}&f=${btoa(
                                            'edit'
                                        )}`}
                                        target="_blank"
                                        rel="noreferrer"
                                    >
                                        {template.name}
                                    </Link>
                                </Box>
                            ))}
                        </Box>
                    </>
                )}
                <Box sx={styles.modalButtonBox}>
                    <Button variant="outlined" onClick={onCancel} sx={{ textTransform: 'uppercase' }}>
                        {translate('app.common.cancel')}
                    </Button>
                    <ClnTooltip
                        title={!canBeDeleted ? translate('app.templates.tooltips.group.deleteConfirmationButton') : ''}
                        placement="left"
                        arrow={false}
                    >
                        <span>
                            <Button
                                variant="contained"
                                onClick={onContinue}
                                sx={{ textTransform: 'uppercase' }}
                                disabled={!canBeDeleted}
                            >
                                {translate('app.common.confirm')}
                            </Button>
                        </span>
                    </ClnTooltip>
                </Box>
            </Box>
        </Modal>
    )
}

export default DeleteGroupConfirmationModal
