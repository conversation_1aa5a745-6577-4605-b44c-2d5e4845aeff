import React from 'react'
import { <PERSON><PERSON>, <PERSON>, <PERSON>po<PERSON>, Button, Link } from '@mui/material'
import * as styles from './styles'
import { translate } from '@celanese/celanese-sdk'
import { ValidationSchemaCustomizeTemplates } from '@/common/models/customizeTemplates'

interface EditConfirmationModalProps {
    open: boolean
    onCancel: () => void
    onContinue: () => void
    templates: ValidationSchemaCustomizeTemplates[]
    isAdminLevel: boolean
}

const EditConfirmationModal: React.FC<EditConfirmationModalProps> = ({
    open,
    onCancel,
    onContinue,
    templates,
    isAdminLevel,
}) => {
    return (
        <Modal open={open} aria-labelledby="confirmation-modal-title">
            <Box sx={styles.modalStyles}>
                <Typography sx={styles.modalTitle} id="confirmation-modal-title" variant="h6">
                    {translate('app.templates.alerts.group.editConfirmationTitle')}
                </Typography>

                <Typography sx={styles.modalText} id="confirmation-modal-description" variant="h6">
                    {translate('app.templates.alerts.group.editConfirmationDescription')}
                </Typography>
                <Box sx={styles.boxTemplates}>
                    {templates.map((template, index) => (
                        <Link
                            key={template.externalId}
                            href={`?id=${template.externalId}&a=${btoa(String(isAdminLevel))}&f=${btoa('edit')}`}
                            target="_blank"
                            rel="noreferrer"
                        >
                            {template.name}
                        </Link>
                    ))}
                </Box>
                <Box sx={styles.modalButtonBox}>
                    <Button variant="outlined" onClick={onCancel} sx={{ textTransform: 'uppercase' }}>
                        {translate('app.common.cancel')}
                    </Button>
                    <Button variant="contained" onClick={onContinue} sx={{ textTransform: 'uppercase' }}>
                        {translate('app.common.continue')}
                    </Button>
                </Box>
            </Box>
        </Modal>
    )
}

export default EditConfirmationModal
