import { CSSObject } from '@emotion/react'

export const modalStyles: CSSObject = {
    padding: '16px',
    backgroundColor: 'background.paper',
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: 'translate(-50%, -50%)',
    width: '600px',
    height: 'auto',
    borderRadius: '6px',
    opacity: '1',
    gap: '0px'
}

export const modalTitle: CSSObject = {
    width: '100%',
    height: 'auto',
    padding: '4px 0px 4px 0px',
    marginBottom: '15px',
    gap: '0px',
    opacity: '0px',
    fontFamily: 'Roboto',
    fontSize: '14px',
    lineHeight: '24px',
    textUnderlinePosition: 'from-font',
    textAlign: 'center',
    fontWeight: 'bold',
}

export const modalText: CSSObject = {
    width: '100%',
    height: 'auto',
    padding: '4px 0px 4px 0px',
    gap: '0px',
    opacity: '0px',
    fontFamily: 'Roboto',
    fontSize: '14px',
    lineHeight: '24px',
    textUnderlinePosition:'from-font',
}

export const modalTextBox: CSSObject = {
    display: 'flex',
    justifyContent: 'center',
    width: '331px',
    height:'auto',
    gap: '20px',
    opacity: '0px',
}

export const modalButtonBox: CSSObject = {
    gap: '10px',
    opacity: '0px',
    marginTop: '10px',
    display: 'flex',
    justifyContent: 'flex-end',
}

export const boxTemplates: CSSObject = {
    overflowY: 'auto',
    height: '72px',
    width: '100%',
    margin: '0 auto',
    padding: '0',
    border: '1px solid #E0E0E0',
    borderRadius: '4px',
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'flex-start',
    boxShadow: '0px 2px 4px rgba(0, 0, 0, 0.1)',
    backgroundColor: 'background.paper',
    opacity: '1',
    transition: 'opacity 0.3s ease',
}

export const closeButton: CSSObject = {
    position: 'absolute',
    top: '5px',
    right: '0px',
}