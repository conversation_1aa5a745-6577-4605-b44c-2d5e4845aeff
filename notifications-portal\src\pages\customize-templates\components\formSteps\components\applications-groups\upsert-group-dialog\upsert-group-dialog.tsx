import { translate } from '@celanese/celanese-sdk'
import { ClnTextField } from '@celanese/ui-lib'
import {
    Dialog,
    DialogTitle,
    DialogContent,
    DialogContentText,
    Typography,
    Backdrop,
    CircularProgress,
    DialogActions,
    Button,
} from '@mui/material'
import * as styles from './styles'
import { Controller } from 'react-hook-form'
import { ValidationSchemaCustomizeTemplates } from '@/common/models/customizeTemplates'

interface UpsertGroupDialogProps {
    showCreateGroupModal: boolean
    setShowCreateGroupModal: (value: boolean) => void
    isEditGroup: boolean
    createGroupControl: any
    createGroupErros: any
    isLoading: boolean
    createGroupHandleSubmit: (onSubmit: any) => any
    createGroupOnSubmit: any
    resetCreateGroupField: any
    templates: ValidationSchemaCustomizeTemplates[]
}

const UpsertGroupDialog = ({
    showCreateGroupModal,
    setShowCreateGroupModal,
    isEditGroup,
    createGroupControl,
    createGroup<PERSON>rros,
    isLoading,
    createGroupHandleSubmit,
    createGroupOnSubmit,
    resetCreateGroupField,
}: UpsertGroupDialogProps) => {
    return (
        <Dialog open={showCreateGroupModal} onClose={() => setShowCreateGroupModal(false)}>
            <DialogTitle id="alert-dialog-title">
                {isEditGroup
                    ? translate('app.templates.group.editGroup')
                    : translate('app.templates.group.createGroup')}
            </DialogTitle>
            <DialogContent sx={styles.duplicateModalContent}>
                <DialogContentText id="alert-dialog-description">
                    <Typography>
                        {isEditGroup ? (
                            translate('app.templates.alerts.group.editConfirmationTitle')
                        ) : (
                            <>
                                {translate('app.templates.alerts.group.createStart')}{' '}
                                <b>{translate('app.common.allowlist')}</b>, <b>{translate('app.common.blocklist')}</b>{' '}
                                {translate('app.common.and')} <b>{translate('app.templates.externalUsers')}</b>.{' '}
                                {translate('app.templates.alerts.group.createEnd')}
                            </>
                        )}
                    </Typography>
                </DialogContentText>
                <Controller
                    control={createGroupControl}
                    name="name"
                    render={({ field }) => (
                        <ClnTextField
                            variant="outlined"
                            label={translate('app.templates.group.recipientsGroupName')}
                            fullWidth
                            error={createGroupErros.name as unknown as boolean}
                            helperText=""
                            {...field}
                        />
                    )}
                />
                <Controller
                    control={createGroupControl}
                    name="description"
                    render={({ field }) => (
                        <ClnTextField
                            variant="outlined"
                            multiline={true}
                            label={translate('app.templates.group.description')}
                            fullWidth
                            error={createGroupErros.description as unknown as boolean}
                            helperText=""
                            {...field}
                        />
                    )}
                />
                <Backdrop sx={{ zIndex: 10 }} open={isLoading}>
                    <CircularProgress color="inherit" />
                </Backdrop>
            </DialogContent>
            <DialogActions>
                <Button
                    onClick={() => {
                        setShowCreateGroupModal(false)
                        resetCreateGroupField('name')
                        resetCreateGroupField('description')
                    }}
                    color="primary"
                    sx={{ textTransform: 'uppercase' }}
                >
                    {translate('app.common.cancel')}
                </Button>
                <Button
                    type="submit"
                    onClick={createGroupHandleSubmit(createGroupOnSubmit)}
                    color="primary"
                    variant="contained"
                    autoFocus
                    sx={{ textTransform: 'uppercase' }}
                >
                    {isEditGroup ? translate('app.templates.group.saveChanges') : translate('app.common.create')}
                </Button>
            </DialogActions>
        </Dialog>
    )
}

export default UpsertGroupDialog
