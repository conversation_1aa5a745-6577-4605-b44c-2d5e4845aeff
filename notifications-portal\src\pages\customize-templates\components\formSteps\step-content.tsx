import Button from '@mui/material/Button'
import Dialog from '@mui/material/Dialog'
import DialogActions from '@mui/material/DialogActions'
import DialogContent from '@mui/material/DialogContent'
import DialogContentText from '@mui/material/DialogContentText'
import DialogTitle from '@mui/material/DialogTitle'
import FieldsChips from '../../components/fields-chips'
import { translate } from '@celanese/celanese-sdk'
import { Box, Typography, TextField } from '@mui/material'
import * as styles from '../../styles'
import { UseSaveTemplateLogicProps } from '@/common/models/customizeTemplates'
import dayjs from 'dayjs'
import ContentCopyOutlinedIcon from '@mui/icons-material/ContentCopyOutlined'
import SeveritySelect from '../../components/severity-select'
import { ClnButton, ClnCheckbox, ClnTextField, ClnTooltip, MatIcon } from '@celanese/ui-lib'
import { Controller } from 'react-hook-form'
import { useRef } from 'react'

const TemplateContent = (properties: UseSaveTemplateLogicProps) => {
    const {
        editInfo,
        isDuplicate,
        setShowDuplicateModal,
        showDuplicateModal,
        handleDuplicateTemplate,
        control,
        disabled,
        errors,
        application,
        notificationTypeName,
        handleAddField,
        messageVariables,
        setValue,
        templateName,
        resetField,
        keepRecipients,
        handleChangeKeepRecipientList,
    } = properties

        const textAreaRef = useRef<HTMLTextAreaElement | null>(null)
    
    return (
        <>
            {editInfo.showEditInfo && !isDuplicate && (
                <Box sx={styles.formRow}>
                    <Box sx={styles.inputContainer}>
                        <Box sx={styles.formRowWithoutSpace}>
                            <Typography sx={styles.inputLabel}>{translate('app.templates.editedBy')}:</Typography>
                            <Typography sx={styles.secondaryTextField}>{editInfo.editedBy}</Typography>
                        </Box>
                    </Box>
                    <Box sx={styles.inputContainer}>
                        <Box sx={styles.formRowWithoutSpace}>
                            <Typography sx={styles.inputLabel}>{translate('app.templates.editedAt')}:</Typography>
                            <Typography sx={styles.secondaryTextField}>
                                {dayjs(editInfo.editedAt).format('MM/DD/YYYY hh:mm A')}
                            </Typography>
                        </Box>
                    </Box>
                    {!disabled && (
                        <Box sx={styles.inputContainer}>
                            <Box sx={styles.formRowAlignedRight}>
                                <ClnButton
                                    label={translate('app.templates.buttons.duplicateTemplate')}
                                    variant='text'
                                    startIcon={<ContentCopyOutlinedIcon fontSize="inherit" />}
                                    onClick={() => {
                                        setShowDuplicateModal(true)
                                        setValue('name', templateName + translate('app.templates.copy'))
                                    }}
                                />
                            </Box>
                        </Box>
                    )}
                </Box>
            )}
            <Box sx={styles.fieldGroups}>
                <Typography className='group_title'>{translate('app.templates.generalInfo')}</Typography>
                <div className='group_fields three-columns'>
                    <Box>
                        <TextField
                            disabled
                            variant="outlined"
                            label={translate('app.templates.table.application')}
                            helperText=""
                            value={application.name}
                            fullWidth
                        />
                    </Box>
                    <Box>
                        <TextField
                            disabled
                            variant="outlined"
                            label={translate('app.notifications.notificationType')}
                            helperText=""
                            value={notificationTypeName}
                            fullWidth
                        />
                    </Box>
                    <Box sx={styles.inputContainer}>
                        <Controller
                            control={control}
                            name="name"
                            render={({ field }) => (
                                <TextField
                                    disabled={disabled}
                                    variant="outlined"
                                    label={translate('app.templates.table.templateName')}
                                    helperText={errors.name ? translate('app.templates.alerts.duplicateTemplateName') : ''}
                                    fullWidth
                                    error={errors.name as unknown as boolean}
                                    inputProps={{
                                        maxLength: 50,
                                    }}
                                    {...field}
                                />
                            )}
                        />
                    </Box>

                    <Box sx={styles.inputContainer}>
                        <SeveritySelect
                            control={control}
                            disabled={disabled}
                        />
                    </Box>
                    <Box>
                        <Controller
                            control={control}
                            name="subject"
                            render={({ field }) => (
                                <TextField
                                    disabled={disabled}
                                    variant="outlined"
                                    label={translate('app.templates.emailSubject')}
                                    helperText={translate('app.templates.alerts.subjectBlank')}
                                    fullWidth
                                    inputProps={{
                                        maxLength: 200,
                                    }}
                                    {...field}
                                />
                            )}
                        />
                    </Box>
                </div>
            </Box>
            <Box sx={styles.fieldGroups}>
                <Typography className='group_title'>{translate('app.notifications.table.notificationMessage')}</Typography>
                <div className='group_fields'>
                    <Box sx={styles.inputContainer}>
                        <Controller
                            control={control}
                            name="text"
                            render={({ field }) => (
                                <ClnTextField
                                    disabled={disabled}
                                    error={errors.text as unknown as boolean}
                                    helperText={errors.text ? translate('app.templates.required') : ' '}
                                    variant="outlined"
                                    label={translate('app.templates.buttons.text')}
                                    fullWidth
                                    multiline
                                    rows={4}
                                    spellCheck={false}
                                    inputRef={(el) => {
                                        textAreaRef.current = el
                                        field.ref(el)
                                      }}
                                    {...field}
                                />
                            )}
                        />
                    </Box>
                    <Box sx={styles.inputContainer}>
                        <Controller
                            control={control}
                            name="textExample"
                            disabled
                            render={({ field }) => (
                            <ClnTextField
                                label={translate('app.templates.buttons.example')}
                                helperText=" "
                                variant="outlined"
                                fullWidth
                                sx={{
                                ...styles.secondaryTextField,
                                '& .MuiInputBase-root': {
                                    padding: '0 !important',
                                    height: '126px',
                                    overflow: 'visible',
                                    '& .MuiOutlinedInput-notchedOutline': {
                                    transition: 'none !important'
                                    }
                                }
                                }}
                                InputProps={{
                                inputComponent: ({ onBlur, onFocus, ...props }: any) => (
                                    <Box
                                    component="div"
                                    {...props}
                                    onBlur={onBlur}
                                    onFocus={onFocus}
                                    dangerouslySetInnerHTML={{
                                        __html: `
                                        <div class="table" style="height: 78px; word-wrap: break-word;">
                                            ${field.value?.replace(/<style.*?>.*?<\/style>/g, '')}
                                        </div>
                                        `
                                    }}
                                    sx={{
                                        width: '100%',
                                        height: '78px !important',
                                        overflow: 'auto',
                                        position: 'relative',
                                        top: '0px',
                                        margin: '4px',
                                        '& .table-container': {
                                        minWidth: '650px',
                                        paddingRight: '15px',
                                        '& table': {
                                            width: '100%',
                                            borderCollapse: 'collapse',
                                            backgroundColor: '#fff',
                                            '& td, & th': {
                                            border: '1px solid',
                                            borderColor: 'divider',
                                            padding: '8px',
                                            textAlign: 'left',
                                            whiteSpace: 'nowrap'
                                            },
                                            '& th': {
                                            backgroundColor: 'action.hover',
                                            fontWeight: 'bold'
                                            },
                                            '& tr:nth-of-type(even)': {
                                            backgroundColor: 'background.default'
                                            }
                                        }
                                        }
                                    }}
                                    />
                                )
                                }}
                                {...field}
                            />
                            )}
                        />
                        </Box>
                </div>
                <Box sx={styles.messageVariables}>
                    <div className='title'>
                        <Typography className='text'>{translate('app.templates.messageVariables')}:</Typography>
                        <ClnTooltip title={translate('app.templates.tooltips.messageVariables')} placement='right'>
                            <Typography>
                                <MatIcon color='#757575' icon='info' fontSize='20px' />
                            </Typography>
                        </ClnTooltip>
                    </div>
                    <FieldsChips
                        onFieldClick={(field) => {
                            const start = textAreaRef.current?.selectionStart || 0
                            const end = textAreaRef.current?.selectionEnd || 0
                            handleAddField(field, start, end)
                            setTimeout(() => {
                                if (textAreaRef.current) {
                                  const newCursorPos = start + `{{${field}}}`.length
                                  textAreaRef.current.focus()
                                  textAreaRef.current.setSelectionRange(newCursorPos, newCursorPos)
                                }
                              }, 0)
                          }}
                        
                        fields={messageVariables}
                        disabled={disabled}
                    />
                </Box>
            </Box>
            <Dialog
                open={showDuplicateModal}
                onClose={() => setShowDuplicateModal(false)}
            >
                <DialogTitle id="alert-dialog-title">{translate('app.templates.buttons.duplicateTemplate')}</DialogTitle>
                <DialogContent sx={styles.duplicateModalContent}>
                    <DialogContentText id="alert-dialog-description">
                        {translate('app.templates.alerts.newDuplicateWarning')}
                    </DialogContentText>
                    <Controller
                        control={control}
                        name="name"
                        render={({ field }) => (
                            <ClnTextField
                                disabled={disabled}
                                variant="outlined"
                                label={translate('app.templates.buttons.enterTemplateName')}
                                helperText={errors.name ? translate('app.templates.alerts.duplicateTemplateName') : ''}
                                fullWidth
                                error={errors.name as unknown as boolean}
                                inputProps={{
                                    maxLength: 50,
                                }}
                                {...field}
                            />
                        )}
                    />
                    <ClnCheckbox
                        items={[{
                            value: 'keepRecipientList',
                            label: translate('app.templates.alerts.keepRecipientInDuplicate'),
                        }]}
                        value={keepRecipients}
                        onChange={(e) => handleChangeKeepRecipientList(e)}
                    />
                </DialogContent>
                <DialogActions>
                    <Button onClick={() => {
                        setShowDuplicateModal(false)
                        resetField('name')
                    }} color="primary" sx={styles.buttons}>
                        {translate('app.common.cancel')}
                    </Button>
                    <Button onClick={() => handleDuplicateTemplate()} color="primary" autoFocus variant='contained' sx={styles.buttons}>
                        {translate('app.common.continue')}
                    </Button>
                </DialogActions>
            </Dialog>
        </>
    )
}

export default TemplateContent