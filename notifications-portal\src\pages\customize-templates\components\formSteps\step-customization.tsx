import { <PERSON>box<PERSON><PERSON>, ClnDrawer, SelectItem, ClnTooltip, MatIcon } from '@celanese/ui-lib'
import useFrequencyLogic, { FrequencyType } from '../../hooks/useFrequencyLogic'
import * as styles from '../../styles'
import { translate } from '@celanese/celanese-sdk'
import { SetStateAction, Dispatch, useEffect } from 'react'
import { ConditionalsFieldArrayProps, ValidationSchemaCustomizeTemplates } from '@/common/models/customizeTemplates'
import { Control, Controller, FieldErrors, UseFormGetValues, UseFormSetValue } from 'react-hook-form'
import {
    Box,
    Checkbox,
    FormGroup,
    FormControlLabel,
    Card,
    CardContent,
    Grid,
    Typography,
    Backdrop,
    CircularProgress,
    FormControl,
    InputLabel,
    MenuItem,
    Select,
} from '@mui/material'
import ConditionComponent from '../conditions-component'

import DrawerComponent from '../drawer-component'
import ChannelCheckboxGroupComponent from '../channels-component'

interface TemplateCustomizationProps {
    frequencyType: SelectItem
    setValue?: UseFormSetValue<ValidationSchemaCustomizeTemplates>
    application: {
        name: string
        id: string
    }
    notificationTypeName: string
    previousFrequencyType: string
    setPreviousFrequencyType: Dispatch<SetStateAction<string>>
    defaultValues?: any
    conditionalsFieldArray: ConditionalsFieldArrayProps
    disabled: boolean
    messageVariables: { name: string; value: any; type?: string }[]
    errors: FieldErrors<ValidationSchemaCustomizeTemplates>
    getValues: UseFormGetValues<ValidationSchemaCustomizeTemplates>
    control: Control<ValidationSchemaCustomizeTemplates>
    selectedChannels?: SelectItem[]
    customChannelEnabled: boolean
    customizations: SelectItem[]
    selectedCustomizations: CheckboxItem[]
    setSelectedCustomizations: Dispatch<SetStateAction<CheckboxItem[]>>
    customFrequencyEnabled: boolean
    isLoading: boolean
    isAdminLevel: boolean
    isAdminEdit: boolean
}

const TemplateCustomization = ({
    frequencyType,
    setValue,
    defaultValues,
    application,
    notificationTypeName,
    previousFrequencyType,
    setPreviousFrequencyType,
    conditionalsFieldArray,
    disabled,
    messageVariables,
    errors,
    getValues,
    control,
    customChannelEnabled,
    customizations,
    selectedCustomizations,
    setSelectedCustomizations,
    customFrequencyEnabled,
    isLoading,
    isAdminLevel,
    isAdminEdit,
}: TemplateCustomizationProps) => {
    const frequencyCronExpression = getValues('frequencyCronExpression')
    const frequencyValue = getValues('frequency')

    const {
        isDrawerOpen,
        setIsDrawerOpen,
        setCronRequest,
        cronRequest,
        handleCloseDrawer,
        selectItems,
        handleOptionClick,
        handleFrequencyChange,
    } = useFrequencyLogic({
        frequencyType,
        setValue,
        defaultValues,
        frequencyCronExpression,
        frequencyValue,
    })
    const getFrequencyType = (scheduleType: string) => {
        switch (scheduleType) {
            case 'minute':
                return FrequencyType.ByMinute
            case 'hourly':
                return FrequencyType.ByHour
            case 'weekly':
                return FrequencyType.Weekly
            case 'monthly':
                return FrequencyType.Monthly
            default:
                return FrequencyType.OnDemand
        }
    }

    const DEFAULT_CHANNELS: CheckboxItem[] = [
        { value: 'teams', label: 'TEAMS' },
        { value: 'email', label: 'EMAIL' },
        { value: 'sms', label: 'SMS' },
    ] as CheckboxItem[]

    useEffect(() => {
        const mappedFrequency = getFrequencyType(cronRequest.schedule_type)
        setValue && setValue('frequency', { value: mappedFrequency, label: mappedFrequency })
    }, [cronRequest])

    const columnSize = isAdminLevel && isAdminEdit && !disabled ? 4 : 6

    return (
        <>
            <Box sx={styles.container}>
                <Grid container spacing={2}>
                    {/* Frequency Card */}
                    <Grid item xs={12} md={columnSize}>
                        <Card sx={styles.customizationTemplateCard}>
                            <CardContent>
                                <Box display="flex" alignItems="center" marginBottom="15px">
                                    <Typography variant="subtitle1" fontWeight="bold" marginRight="5px">
                                        {translate('app.templates.buttons.frequency')}
                                    </Typography>
                                    <ClnTooltip
                                        title={translate('app.templates.alerts.onDemandFrequency')}
                                        placement="left"
                                    >
                                        <Box display="flex" alignItems="center">
                                            <MatIcon color="#757575" icon="info" fontSize="20px" />
                                        </Box>
                                    </ClnTooltip>
                                </Box>
                                <Controller
                                    control={control}
                                    name="frequency"
                                    render={({ field: { onChange, ...field } }) => (
                                        <FormControl fullWidth>
                                            <InputLabel id="frequency-select-label">
                                                {translate('app.templates.frequency.frequencyTypes.title')}
                                            </InputLabel>
                                            <Select
                                                {...field}
                                                disabled={disabled && !customFrequencyEnabled}
                                                value={field.value.value}
                                                labelId="frequency-select-label"
                                                label={translate('app.templates.frequency.frequencyTypes.title')}
                                                onChange={(e) => {
                                                    handleFrequencyChange(e.target)
                                                }}
                                            >
                                                {selectItems.map((item) => (
                                                    <MenuItem key={item.value} value={item.value} onClick={() => handleOptionClick(item.value)}>
                                                        {translate(item.label)}
                                                    </MenuItem>
                                                ))}
                                            </Select>
                                        </FormControl>
                                    )}
                                />
                            </CardContent>
                        </Card>
                    </Grid>

                    {/* Channels Card */}
                    <Grid item xs={12} md={columnSize}>
                        <Box sx={styles.inputContainer}>
                            <Controller
                                control={control}
                                name="channels"
                                render={({ field }) => (
                                    <ChannelCheckboxGroupComponent
                                        error={errors.channels as unknown as boolean}
                                        selectedChannels={field.value || []}
                                        disabled={disabled && !customChannelEnabled}
                                        onChange={(updatedChannels) => field.onChange(updatedChannels)}
                                    />
                                )}
                            />
                        </Box>
                    </Grid>

                    {/* User Customization Card */}
                    {isAdminLevel && isAdminEdit && !disabled && (
                        <Grid item xs={12} md={4}>
                            <Card sx={styles.customizationTemplateCard}>
                                <CardContent>
                                    <Box display="flex" alignItems="center" gap={0.5}>
                                        <Typography variant="subtitle1" fontWeight="bold">
                                            {translate('app.templates.alerts.userCustomization')}
                                        </Typography>
                                        <ClnTooltip
                                            title={translate('app.templates.alerts.userCustomizationTooltip')}
                                            placement="right"
                                        >
                                            <Box display="flex" alignItems="center">
                                                <MatIcon color="#757575" icon="info" fontSize="20px" />
                                            </Box>
                                        </ClnTooltip>
                                    </Box>
                                    <FormGroup row sx={{ mt: '20px' }}>
                                        {customizations.map((item) => (
                                            <FormControlLabel
                                                key={item.value}
                                                control={
                                                    <Checkbox
                                                        checked={selectedCustomizations.some(
                                                            (customization) => customization.value === item.value
                                                        )}
                                                        onChange={(event) => {
                                                            const newSelection = event.target.checked
                                                                ? [...selectedCustomizations, item]
                                                                : selectedCustomizations.filter(
                                                                      (customization) =>
                                                                          customization.value !== item.value
                                                                  )
                                                            setSelectedCustomizations(newSelection)
                                                        }}
                                                    />
                                                }
                                                label={translate(item.label)}
                                            />
                                        ))}
                                    </FormGroup>
                                </CardContent>
                            </Card>
                        </Grid>
                    )}
                </Grid>

                {/* Conditionals Section */}
                <Box>
                    <ConditionComponent
                        conditionalsFieldArray={conditionalsFieldArray}
                        control={control}
                        disabled={disabled}
                        variables={messageVariables}
                        errors={errors}
                        getValues={getValues}
                    />
                </Box>
            </Box>
            {/* Frequency Drawer */}
            <ClnDrawer
                open={isDrawerOpen}
                overlineMeta={application.name}
                caption={notificationTypeName}
                sx={styles.drawerDefault}
                header={translate('app.templates.buttons.frequency')}
                anchor="right"
                className="no-translate"
                closeDrawer={() => handleCloseDrawer(previousFrequencyType)}
            >
                <DrawerComponent
                    handleCancel={() => handleCloseDrawer(previousFrequencyType)}
                    frequencyType={frequencyType.value as FrequencyType}
                    setCronRequest={setCronRequest}
                    setIsDrawerOpen={setIsDrawerOpen}
                    cronRequest={cronRequest}
                    setPreviousFrequencyType={setPreviousFrequencyType}
                />
            </ClnDrawer>
            <Backdrop sx={{ zIndex: 10 }} open={isLoading}>
                <CircularProgress color="inherit" />
            </Backdrop>
        </>
    )
}

export default TemplateCustomization
