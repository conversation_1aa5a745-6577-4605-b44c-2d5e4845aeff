import { Autocomplete, Backdr<PERSON>, Box, <PERSON>ton, Chip, CircularProgress, <PERSON><PERSON>ield, Typography } from '@mui/material'
import { Controller } from 'react-hook-form'
import { Cln<PERSON>lert, ClnButton, ClnCheckbox, ClnTextField, ClnTooltip, MatIcon, SelectItem } from '@celanese/ui-lib'
import { NoTranslate, translate } from '@celanese/celanese-sdk'
import * as styles from '../../styles'
import WarningIcon from '@mui/icons-material/Warning'
import ManageSearchIcon from '@mui/icons-material/ManageSearch'
import SelectedChip from '../sendTo/selected-chip'
import MessageContainer from '@/common/components/MessageContainer/message-container'
import { UseSaveTemplateLogicProps } from '@/common/models/customizeTemplates'
import React, { useEffect, useRef } from 'react'
import AdvancedSearchModal from '../advanced-search/advanced-search'
import useSendToLogic from '../../hooks/useSendToLogic'
import useNotificationApplicationGroup from '../../hooks/useNotificationApplicationGroup'
import dayjs from 'dayjs'
import UpsertGroupDialog from './components/applications-groups/upsert-group-dialog/upsert-group-dialog'
import EditConfirmationModal from './components/applications-groups/edit-modal-confirm/edit-modal-confirm'
import DeleteGroupConfirmationModal from './components/applications-groups/delete-modal-confirm/delete-modal-confirm'
import CreateNewGroupDialog from './components/applications-groups/create-new-group-dialog/create-new-group-dialog'

const TemplateRecipients = (properties: UseSaveTemplateLogicProps, applicationId: string) => {
    const {
        watch,
        externalUsersFieldArray,
        resetField,
        usersFieldArray,
        rolesFieldArray,
        applicationGroupsFieldArray,
        application,
        control,
        blockUsersFieldArray,
        blockRolesFieldArray,
        blockApplicationGroupsFieldArray,
        errors,
        disabled,
        filteredUsers,
        applyFilter,
        showAllowlistModal,
        showBlocklistModal,
        handleOpenAllowlistModal,
        handleCloseAllowlistModal,
        handleOpenBlocklistModal,
        handleCloseBlocklistModal,
        setValue,
        isNextStepOneClicked,
        getValues,
        selectedGroups,
        setSelectedGroups,
        isAdminLevel,
        clickedGroup,
        setClickedGroup,
        setCanChangeStep,
        advancedSearchOptions,
        setAdvancedSearchOptions,
        setBackendError,
        backendError,
        loadingUsersFilter,
        getNotificationApplicationGroups,
        setNotificationApplicationGroups,
        notificationApplicationGroups,
        notificationEntities,
    } = properties

    const {
        users,
        loading,
        isAllUsers,
        externalUsersInput,
        rolesOptions,
        usersOptions,
        showSuggestions,
        showExternalSuggestions,
        rolesBlockOptions,
        usersBlockOptions,
        showBlockSuggestions,
        isLoadingSearch,
        isLoadingBlockSearch,
        showNoResultsMessage,
        showNoResultsMessageForBlockUsers,
        externalUserEmailisValid,
        setShowSuggestions,
        setShowExternalSuggestions,
        setShowBlockSuggestions,
        handleAppendExternalUser,
        handleResetStates,
        handleResetExternalUsersStates,
        handleResetBlockStates,
        searchLabel,
        hasError,
        getFieldsOptions,
    } = useSendToLogic({
        watch,
        externalUsersFieldArray,
        resetField,
        application,
        setValue,
        filteredUsers,
        applicationId,
        setAdvancedSearchOptions,
    })

    const {
        showCreateGroupModal,
        setShowCreateGroupModal,
        showCreateNewGroupModal,
        setShowCreateNewGroupModal,
        errorsGroup,
        controlGroup,
        handleSubmitGroup,
        onSubmitGroup,
        resetFieldGroup,
        selectItemsGroups,
        selectedNewGroup,
        handleOptionClickGroups,
        handleOptionClickRemoveGroup,
        handleCreateNewGroup,
        onNewGroupOptionClick,
        isLoading,
        isNewGroup,
        handleCancelOperationsGroup,
        isEditGroup,
        setIsEditGroup,
        handleUpsertGroup,
        handleSelectGroups,
        templateGroups,
        editModelConfirmation,
        setEditModelConfirmation,
        isLoadingGroup,
        handleEditGroup,
        deleteGroupModalConfirmation,
        setDeleteGroupModalConfirmation,
        handleDeleteGroup,
        onDeleteGroup,
        currentSentTo,
    } = useNotificationApplicationGroup({
        usersFieldArray,
        rolesFieldArray,
        externalUsersFieldArray,
        blockUsersFieldArray,
        blockRolesFieldArray,
        applicationGroupsFieldArray,
        setValue,
        getValues,
        selectedGroups,
        setSelectedGroups,
        clickedGroup,
        setClickedGroup,
        backendError,
        setBackendError,
        application,
        getNotificationApplicationGroups,
        setNotificationApplicationGroups,
        notificationApplicationGroups,
    })

    const clickedGroupRef = useRef(clickedGroup)
    const currentSentToRef = useRef(currentSentTo)

    useEffect(() => {
        clickedGroupRef.current = clickedGroup
        currentSentToRef.current = currentSentTo
    }, [clickedGroup, currentSentTo])

    useEffect(() => {
        return () => {
            if (clickedGroupRef.current) {
                setClickedGroup(null)
                usersFieldArray.replaceUsers(currentSentToRef.current.users)
                rolesFieldArray.replaceRoles(currentSentToRef.current.roles)
                externalUsersFieldArray.removeExternalUsers()
                currentSentToRef.current.externalUsers.forEach((user: any) => {
                    externalUsersFieldArray.appendExternalUsers(user.email)
                })
                blockUsersFieldArray.replaceBlockUsers(currentSentToRef.current.blockUsers)
                blockRolesFieldArray.replaceBlockRoles(currentSentToRef.current.blockRoles)
            }
        }
    }, [])

    useEffect(() => {
        if (isEditGroup) {
            setCanChangeStep({ canChange: false, message: 'app.templates.tooltips.group.canChangeStep' })
        } else {
            setCanChangeStep({ canChange: true, message: '' })
        }
    }, [isEditGroup])

    const hasSelectedUsers = !!(
        usersFieldArray.subscribedUsers.length +
        rolesFieldArray.subscribedRoles.length +
        externalUsersFieldArray.subscribedExternalUsers.length +
        blockUsersFieldArray.blockSubscribedUsers.length +
        blockRolesFieldArray.blockSubscribedRoles.length +
        blockApplicationGroupsFieldArray.blockSubscribedApplicationGroups.length
    )

    function isValidJSON(str: string) {
        try {
            JSON.parse(str)
            return true
        } catch (e) {
            return false
        }
    }

    function readOnly() {
        return !!(clickedGroup && !isEditGroup)
    }

    const allUsersOption = [{ value: 'allusers', label: translate('app.templates.allUsers') }]
    const [watchSubscribedUsers, watchSubscribedRoles] = watch(['subscribedUsers', 'subscribedRoles'])

    return (
        <>
            <Box display={'flex'} justifyContent={'space-between'} alignItems={'center'}>
                <Box display={'flex'} gap={1} alignItems="center">
                    {!isNewGroup && (
                        <>
                            <Autocomplete
                                multiple
                                id="tags-outlined"
                                loading={isLoadingGroup}
                                options={selectItemsGroups}
                                getOptionLabel={(option: SelectItem) => option.label}
                                value={
                                    (selectedGroups?.map((group) => ({
                                        label: group.name,
                                        value: group.externalId,
                                    })) as SelectItem[]) ?? []
                                }
                                filterSelectedOptions
                                size="small"
                                disabled={isEditGroup}
                                onChange={(e, value) => {
                                    handleSelectGroups(value)
                                }}
                                renderTags={(tagValue, getTagProps) =>
                                    tagValue.map((option, index) => (
                                        // The key is already being defined through {...getTagProps({ index })}
                                        // eslint-disable-next-line react/jsx-key
                                        <Chip
                                            {...getTagProps({ index })}
                                            label={option.label}
                                            onDelete={(event) => handleOptionClickRemoveGroup(option)}
                                            onClick={() => handleOptionClickGroups(option)}
                                            variant={clickedGroup?.externalId === option.value ? 'filled' : 'outlined'}
                                        />
                                    ))
                                }
                                renderInput={(params) => {
                                    return (
                                        <TextField
                                            {...params}
                                            label={translate('app.templates.group.recipientsGroupName')}
                                            placeholder={
                                                selectedGroups.length > 0
                                                    ? ''
                                                    : translate('app.templates.group.enterRecipientsGroupName')
                                            }
                                            sx={{
                                                minWidth: '450px',
                                            }}
                                            InputProps={{
                                                ...params.InputProps,
                                                endAdornment: (
                                                    <React.Fragment>
                                                        {isLoadingGroup ? (
                                                            <CircularProgress color="inherit" size={20} />
                                                        ) : null}
                                                        {params.InputProps.endAdornment}
                                                    </React.Fragment>
                                                ),
                                            }}
                                        />
                                    )
                                }}
                                renderOption={(props, option) => (
                                    <li
                                        {...props}
                                        style={{
                                            display: 'flex',
                                            flexDirection: 'column',
                                            justifyContent: 'flex-start',
                                            alignItems: 'flex-start',
                                        }}
                                    >
                                        <NoTranslate>
                                            <Typography>{option.label}</Typography>
                                        </NoTranslate>
                                    </li>
                                )}
                            />
                            <ClnTooltip
                                title={translate('app.templates.tooltips.group.selectRecipients')}
                                placement="right"
                            >
                                <Box alignItems="center">
                                    <MatIcon color="#757575" icon="info" fontSize="20px" marginTop="7px" />
                                </Box>
                            </ClnTooltip>
                        </>
                    )}
                </Box>

                <Box sx={{ justifyContent: 'flex-end' }} gap={1}>
                    {(isNewGroup || !clickedGroup) && (
                        <Box display="flex" gap={1}>
                            {isNewGroup && (
                                <Button
                                    variant="text"
                                    color="primary"
                                    onClick={handleCancelOperationsGroup}
                                    sx={{ textTransform: 'uppercase' }}
                                >
                                    {translate('app.common.cancel')}
                                </Button>
                            )}

                            <ClnTooltip
                                title={!hasSelectedUsers ? translate('app.templates.tooltips.group.create') : ''}
                                placement="left"
                                arrow={false}
                            >
                                <span>
                                    <Button
                                        variant="contained"
                                        color="primary"
                                        onClick={handleUpsertGroup}
                                        disabled={!hasSelectedUsers}
                                        sx={{ textTransform: 'uppercase' }}
                                    >
                                        {translate('app.templates.group.createGroup')}
                                    </Button>
                                </span>
                            </ClnTooltip>
                        </Box>
                    )}

                    {clickedGroup && !isEditGroup && (
                        <Box display="flex" gap={1}>
                            <Button
                                variant="outlined"
                                color="primary"
                                sx={{ textTransform: 'uppercase' }}
                                onClick={handleEditGroup}
                            >
                                {translate('app.templates.group.editGroup')}
                            </Button>
                            <Button
                                variant="contained"
                                color="primary"
                                sx={{ textTransform: 'uppercase' }}
                                onClick={() => {
                                    setShowCreateNewGroupModal(true)
                                }}
                            >
                                {translate('app.templates.group.createNewGroup')}
                            </Button>
                        </Box>
                    )}

                    {clickedGroup && isEditGroup && (
                        <Box display="flex" gap={1}>
                            <Button
                                variant="text"
                                color="primary"
                                onClick={handleCancelOperationsGroup}
                                sx={{ textTransform: 'uppercase' }}
                            >
                                {translate('app.common.cancel')}
                            </Button>

                            <Button
                                variant="outlined"
                                color="primary"
                                onClick={handleDeleteGroup}
                                sx={{ textTransform: 'uppercase' }}
                            >
                                {translate('app.templates.group.deleteGroup')}
                            </Button>

                            <Button
                                variant="contained"
                                color="primary"
                                onClick={handleUpsertGroup}
                                sx={{ textTransform: 'uppercase' }}
                            >
                                {translate('app.templates.group.updateGroup')}
                            </Button>
                        </Box>
                    )}
                </Box>
            </Box>
            {clickedGroup && (
                <Box>
                    <Box sx={styles.boxDescription}>
                        <Typography>{`Description: ${clickedGroup?.description}`}</Typography>
                    </Box>
                    <Box sx={styles.inputContainer}>
                        {clickedGroup?.editedBy && (
                            <Box sx={styles.formRowWithoutSpace}>
                                <Typography sx={styles.inputLabel}>{translate('app.templates.editedBy')}:</Typography>
                                <Typography sx={styles.secondaryTextField}>
                                    {`${clickedGroup.editedBy.firstName} ${clickedGroup.editedBy.lastName}`}
                                </Typography>

                                <Typography sx={styles.inputLabel}>{translate('app.templates.editedAt')}:</Typography>
                                <Typography sx={styles.secondaryTextField}>
                                    {dayjs(clickedGroup.editedAt).format('MM/DD/YYYY hh:mm A')}
                                </Typography>
                            </Box>
                        )}
                    </Box>
                </Box>
            )}
            <Box sx={styles.drawerContentContainer}>
                <Typography sx={{ ...styles.subHeader, display: 'flex', alignItems: 'center' }}>
                    {translate('app.common.allowlist')}
                    <tr></tr>
                    <ClnTooltip arrow={true} title={translate('app.templates.tooltips.allowlist')}>
                        <span style={{ display: 'flex' }}>
                            <MatIcon icon="info" fontSize="20px" />
                        </span>
                    </ClnTooltip>
                    {!isAllUsers && (
                        <Button
                            variant="outlined"
                            startIcon={<ManageSearchIcon />}
                            onClick={handleOpenAllowlistModal}
                            sx={styles.advancedButton}
                        >
                            {translate('app.templates.advancedSearch')}
                        </Button>
                    )}
                    <AdvancedSearchModal
                        getFieldsOptions={getFieldsOptions}
                        open={showAllowlistModal}
                        handleClose={handleCloseAllowlistModal}
                        users={users}
                        filteredUsers={filteredUsers}
                        applyFilter={applyFilter}
                        isBlockUsers={false}
                        usersFieldArray={usersFieldArray}
                        blockUsersFieldArray={blockUsersFieldArray}
                        advancedSearchOptions={advancedSearchOptions}
                        setAdvancedSearchOptions={setAdvancedSearchOptions}
                        loading={loadingUsersFilter}
                    />
                </Typography>

                {notificationEntities && notificationEntities.length > 0 && (
                    <Box sx={styles.tagsContainer}>
                        <Typography>{translate('app.templates.alerts.notificationsEntities')} </Typography>
                        {notificationEntities?.map((ent, index) => (
                            <Box key={index} sx={styles.tagAllowList}>{ent}</Box>
                        ))}
                    </Box>
                )}

                <Box>
                    {!clickedGroup && (
                        <Box sx={styles.formRow}>
                            <Box sx={styles.inputContainer}>
                                <Controller
                                    control={control}
                                    name="allUsers"
                                    render={({ field }) => (
                                        <ClnCheckbox items={allUsersOption} sxProps={styles.checkbox} {...field} />
                                    )}
                                />
                            </Box>
                        </Box>
                    )}

                    <Box sx={{ position: 'relative', width: '100%' }}>
                        <Button
                            sx={{
                                position: 'absolute',
                                right: '8px',
                                top: '-28px'
                            }}
                            variant='text'
                            onClick={() => {
                                usersFieldArray.replaceUsers([])
                                rolesFieldArray.replaceRoles([])
                            }}
                            disabled={
                                usersFieldArray.subscribedUsers.length <= 0 &&
                                rolesFieldArray.subscribedRoles.length <= 0
                            }
                        >
                            {translate('app.common.clearAll')}
                        </Button>
                        <Box sx={styles.sendToInputContainer}>
                            <Controller
                                control={control}
                                name="usersInput"
                                render={({ field }) => (
                                    <ClnTextField
                                        disabled={isAllUsers || disabled}
                                        error={
                                            !isAllUsers &&
                                            isNextStepOneClicked &&
                                            Boolean(
                                                !(
                                                    (watchSubscribedUsers && watchSubscribedUsers?.length > 0) ||
                                                    (watchSubscribedRoles && watchSubscribedRoles.length > 0) ||
                                                    watch('subscribedApplicationGroups')?.length > 0
                                                )
                                            )
                                        }
                                        helperText={
                                            !isAllUsers &&
                                            isNextStepOneClicked &&
                                            !(
                                                (watchSubscribedUsers && watchSubscribedUsers.length > 0) ||
                                                (watchSubscribedRoles && watchSubscribedRoles.length > 0) ||
                                                watch('subscribedApplicationGroups')?.length > 0
                                            )
                                                ? translate('app.templates.required')
                                                : ''
                                        }
                                        label={searchLabel()}
                                        sx={{
                                            ...styles.sendToInput,
                                            display: isAllUsers ? 'none' : 'flex',
                                            alignItems: 'center',
                                            flexWrap: 'wrap',
                                            padding: '8px',
                                        }}
                                        variant="outlined"
                                        fullWidth
                                        InputProps={{
                                            readOnly: readOnly(),
                                            startAdornment: (
                                                <Box sx={{ width: '100%' }}>
                                                    <Box sx={styles.selectedUsersContainer}>
                                                        {!isAllUsers && (
                                                            <>
                                                                {rolesFieldArray.subscribedRoles.map(
                                                                    (role: any, index: any) => (
                                                                        <SelectedChip
                                                                            key={role.externalId}
                                                                            label={role.name}
                                                                            readOnly={readOnly()}
                                                                            isGroup={
                                                                                role.users && role.users.length > 0
                                                                            }
                                                                            onExpandClick={() => {
                                                                                const users =
                                                                                    role.users && role.users.length > 0
                                                                                        ? role.users
                                                                                        : []
                                                                                usersFieldArray.appendUsers(users)
                                                                                rolesFieldArray.removeRoles(index)
                                                                            }}
                                                                            onRemoveClick={() =>
                                                                                rolesFieldArray.removeRoles(index)
                                                                            }
                                                                        />
                                                                    )
                                                                )}
                                                                {usersFieldArray.subscribedUsers.map(
                                                                    (
                                                                        user: { externalId: string; email: string },
                                                                        index: any
                                                                    ) => (
                                                                        <SelectedChip
                                                                            key={user.externalId}
                                                                            label={user.email}
                                                                            readOnly={readOnly()}
                                                                            onRemoveClick={() =>
                                                                                usersFieldArray.removeUsers(index)
                                                                            }
                                                                        />
                                                                    )
                                                                )}
                                                                <input
                                                                    type="text"
                                                                    readOnly={readOnly()}
                                                                    placeholder=""
                                                                    style={{
                                                                        border: 'none',
                                                                        outline: 'none',
                                                                        flex: 1,
                                                                        minWidth: '100px',
                                                                    }}
                                                                    {...field}
                                                                    onFocus={() => {
                                                                        if (field.value && field.value?.length > 0) {
                                                                            setShowSuggestions(true)
                                                                        }
                                                                    }}
                                                                    onBlur={() => {
                                                                        setTimeout(() => {
                                                                            setShowSuggestions(false)
                                                                        }, 100)
                                                                    }}
                                                                />
                                                            </>
                                                        )}
                                                    </Box>
                                                </Box>
                                            ),
                                        }}
                                    />
                                )}
                            />

                            {isLoadingSearch && (
                                <CircularProgress
                                    sx={{
                                        ...styles.loadingIndicator,
                                        top: hasError(isNextStepOneClicked) ? '20px' : '',
                                    }}
                                />
                            )}
                        </Box>
                        {showSuggestions && (
                            <Box
                                sx={{
                                    ...styles.suggestionsContainter,
                                    top: hasError(isNextStepOneClicked) ? '74%' : '100%',
                                }}
                            >
                                {rolesOptions.map((role: any, index: any) => (
                                    <ClnButton
                                        key={role.externalId}
                                        label={role.name}
                                        variant="text"
                                        sx={styles.searchSugestion}
                                        onClick={() => {
                                            rolesFieldArray.appendRoles(role)
                                            handleResetStates()
                                        }}
                                    />
                                ))}
                                {usersOptions.map((user: any, index: any) => (
                                    <ClnButton
                                        key={user.externalId}
                                        label={user.email}
                                        variant="text"
                                        sx={styles.searchSugestion}
                                        onClick={() => {
                                            usersFieldArray.appendUsers(user)
                                            handleResetStates()
                                        }}
                                    />
                                ))}
                                {showNoResultsMessage && (
                                    <Typography sx={styles.noResultsMessage}>
                                        {translate('app.common.notFound')}
                                    </Typography>
                                )}
                            </Box>
                        )}
                    </Box>
                </Box>
                <Box sx={{ position: 'relative', marginTop: '15px' }}>
                    <Box sx={styles.inputContainer}>
                        {externalUserEmailisValid && (
                            <Box>
                                <MessageContainer
                                    messages={[
                                        {
                                            icon: <WarningIcon />,
                                            text: translate('app.templates.alerts.invalidEmail'),
                                        },
                                    ]}
                                    sx={styles.invalidEmail}
                                />
                            </Box>
                        )}
                    </Box>
                    <Box sx={styles.sendToInputContainer}>
                        <Button
                            sx={{
                                position: 'absolute',
                                right: '8px',
                                top: '-28px'
                            }}
                            variant='text'
                            onClick={() => {
                                externalUsersFieldArray.replaceExternalUsers([])
                            }}
                            disabled={externalUsersFieldArray.subscribedExternalUsers.length <= 0}
                        >
                            {translate('app.common.clearAll')}
                        </Button>
                        <Controller
                            control={control}
                            name="externalUsersInput"
                            render={({ field }) => (
                                <ClnTextField
                                    helperText=""
                                    label={translate('app.templates.externalUsersOptional')}
                                    sx={{
                                        ...styles.sendToInput,
                                        display: 'flex',
                                        alignItems: 'center',
                                        flexWrap: 'wrap',
                                        padding: '8px',
                                        paddingBottom: '24px',
                                    }}
                                    variant="outlined"
                                    fullWidth
                                    InputProps={{
                                        readOnly: readOnly(),
                                        startAdornment: (
                                            <Box sx={{ width: '100%' }}>
                                                <Box sx={styles.selectedUsersContainer}>
                                                    {externalUsersFieldArray.subscribedExternalUsers.map(
                                                        (user: any, index: any) => (
                                                            <SelectedChip
                                                                key={user.email}
                                                                label={user.email}
                                                                readOnly={readOnly()}
                                                                onRemoveClick={() =>
                                                                    externalUsersFieldArray.removeExternalUsers(index)
                                                                }
                                                            />
                                                        )
                                                    )}

                                                    <input
                                                        type="text"
                                                        readOnly={readOnly()}
                                                        placeholder=""
                                                        style={{
                                                            border: 'none',
                                                            outline: 'none',
                                                            flex: 1,
                                                            minWidth: '100px',
                                                        }}
                                                        value={field.value}
                                                        onChange={(e) => field.onChange(e.target.value)}
                                                        onFocus={() => {
                                                            if (field.value && field.value?.length > 0) {
                                                                setShowExternalSuggestions(true)
                                                            }
                                                        }}
                                                        onBlur={() => {
                                                            setTimeout(() => {
                                                                setShowExternalSuggestions(false)
                                                            }, 100)
                                                        }}
                                                    />
                                                </Box>
                                            </Box>
                                        ),
                                    }}
                                />
                            )}
                        />

                        {showExternalSuggestions && externalUsersInput.length > 0 && (
                            <Box sx={{ ...styles.suggestionsContainter, top: '100%' }}>
                                <ClnButton
                                    label={externalUsersInput}
                                    variant="text"
                                    sx={styles.searchSugestion}
                                    onClick={() => {
                                        handleAppendExternalUser(externalUsersInput)
                                        handleResetExternalUsersStates()
                                    }}
                                />
                            </Box>
                        )}
                    </Box>
                </Box>
                <Box></Box>
                <Box sx={{ ...styles.usersCount, display: isAllUsers ? 'none' : 'flex' }}>
                    <Typography variant="body2" color="textSecondary">
                        {` ${
                            usersFieldArray.subscribedUsers.length +
                            rolesFieldArray.subscribedRoles.reduce(
                                (acc: number, role: { users?: any[] }) => acc + (role.users?.length ?? 0),
                                0
                            ) +
                            externalUsersFieldArray.subscribedExternalUsers.length
                        }`}{' '}
                        {translate('app.templates.users')}
                    </Typography>
                </Box>
            </Box>
            <Box sx={styles.drawerContentContainer}>
                <Typography sx={styles.subHeader} display="inline-flex" alignItems="center">
                    {translate('app.common.blocklist')}

                    <ClnTooltip arrow={true} title={translate('app.templates.tooltips.blocklist')}>
                        <span style={{ display: 'flex' }}>
                            <MatIcon icon="info" fontSize="20px" />
                        </span>
                    </ClnTooltip>
                    <Button
                        variant="outlined"
                        startIcon={<ManageSearchIcon />}
                        onClick={handleOpenBlocklistModal}
                        sx={styles.advancedButton}
                    >
                        {translate('app.templates.advancedSearch')}
                    </Button>
                    <AdvancedSearchModal
                        getFieldsOptions={getFieldsOptions}
                        open={showBlocklistModal}
                        handleClose={handleCloseBlocklistModal}
                        users={users}
                        filteredUsers={filteredUsers}
                        applyFilter={applyFilter}
                        isBlockUsers={true}
                        usersFieldArray={usersFieldArray}
                        blockUsersFieldArray={blockUsersFieldArray}
                        advancedSearchOptions={advancedSearchOptions}
                        setAdvancedSearchOptions={setAdvancedSearchOptions}
                        loading={loadingUsersFilter}
                    />
                </Typography>
                <Box sx={{ position: 'relative', width: '100%', marginTop: '15px' }}>
                    <Button
                        sx={{
                            position: 'absolute',
                            right: '8px',
                            top: '-18px'
                        }}
                        variant='text'
                        onClick={() => {
                            blockRolesFieldArray.replaceBlockRoles([])
                            blockUsersFieldArray.replaceBlockUsers([])
                        }}
                        disabled={
                            blockRolesFieldArray.blockSubscribedRoles.length <= 0 &&
                            blockUsersFieldArray.blockSubscribedUsers.length <= 0
                        }
                    >
                        {translate('app.common.clearAll')}
                    </Button>
                    <Box sx={styles.sendToInputContainer}>
                        <Controller
                            control={control}
                            name="blockUsersInput"
                            render={({ field }) => (
                                <ClnTextField
                                    helperText=""
                                    label={translate('app.templates.selectedUsers')}
                                    sx={{
                                        ...styles.sendToInput,
                                        display: 'flex',
                                        alignItems: 'center',
                                        flexWrap: 'wrap',
                                        padding: '8px',
                                    }}
                                    variant="outlined"
                                    fullWidth
                                    InputProps={{
                                        readOnly: readOnly(),
                                        startAdornment: (
                                            <Box sx={{ width: '100%' }}>
                                                <Box sx={styles.selectedUsersContainer}>
                                                    {blockApplicationGroupsFieldArray.blockSubscribedApplicationGroups.map(
                                                        (application: any, index: any) => (
                                                            <SelectedChip
                                                                key={application.externalId}
                                                                label={application.description}
                                                                readOnly={readOnly()}
                                                                isGroup={
                                                                    application.users && application.users.length > 0
                                                                }
                                                                onExpandClick={() => {
                                                                    const users =
                                                                        application.users &&
                                                                        application.users.length > 0
                                                                            ? application.users
                                                                            : []
                                                                    blockUsersFieldArray.appendBlockUsers(users)
                                                                    blockApplicationGroupsFieldArray.removeBlockApplicationGroups(
                                                                        index
                                                                    )
                                                                }}
                                                                onRemoveClick={() =>
                                                                    blockApplicationGroupsFieldArray.removeBlockApplicationGroups(
                                                                        index
                                                                    )
                                                                }
                                                            />
                                                        )
                                                    )}
                                                    {blockRolesFieldArray.blockSubscribedRoles.map(
                                                        (role: any, index: any) => (
                                                            <SelectedChip
                                                                key={role.externalId}
                                                                label={role.name}
                                                                readOnly={readOnly()}
                                                                isGroup={role.users && role.users.length > 0}
                                                                onExpandClick={() => {
                                                                    const users =
                                                                        role.users && role.users.length > 0
                                                                            ? role.users
                                                                            : []
                                                                    blockUsersFieldArray.appendBlockUsers(users)
                                                                    blockRolesFieldArray.removeBlockRoles(index)
                                                                }}
                                                                onRemoveClick={() =>
                                                                    blockRolesFieldArray.removeBlockRoles(index)
                                                                }
                                                            />
                                                        )
                                                    )}
                                                    {blockUsersFieldArray.blockSubscribedUsers.map(
                                                        (user: { externalId: string; email: string }, index: any) => (
                                                            <SelectedChip
                                                                key={user.externalId}
                                                                label={user.email}
                                                                readOnly={readOnly()}
                                                                onRemoveClick={() =>
                                                                    blockUsersFieldArray.removeBlockUsers(index)
                                                                }
                                                            />
                                                        )
                                                    )}
                                                    <input
                                                        type="text"
                                                        readOnly={readOnly()}
                                                        placeholder=""
                                                        style={{
                                                            border: 'none',
                                                            outline: 'none',
                                                            flex: 1,
                                                            minWidth: '100px',
                                                        }}
                                                        {...field}
                                                        onFocus={() => {
                                                            if (field.value && field.value?.length > 0) {
                                                                setShowBlockSuggestions(true)
                                                            }
                                                        }}
                                                        onBlur={() => {
                                                            setTimeout(() => {
                                                                setShowBlockSuggestions(false)
                                                            }, 100)
                                                        }}
                                                    />
                                                </Box>
                                            </Box>
                                        ),
                                    }}
                                />
                            )}
                        />
                        {isLoadingBlockSearch && <CircularProgress sx={styles.loadingIndicator} />}
                    </Box>
                    {showBlockSuggestions && (
                        <Box sx={{ ...styles.suggestionsContainter, top: '100%' }}>
                            {rolesBlockOptions.map((role: any, index: any) => (
                                <ClnButton
                                    key={role.externalId}
                                    label={role.name}
                                    variant="text"
                                    sx={styles.searchSugestion}
                                    onClick={() => {
                                        blockRolesFieldArray.appendBlockRoles(role)
                                        handleResetBlockStates()
                                    }}
                                />
                            ))}
                            {usersBlockOptions.map((user: any, index: any) => (
                                <ClnButton
                                    key={user.externalId}
                                    label={user.email}
                                    variant="text"
                                    sx={styles.searchSugestion}
                                    onClick={() => {
                                        blockUsersFieldArray.appendBlockUsers(user)
                                        handleResetBlockStates()
                                    }}
                                />
                            ))}
                            {showNoResultsMessageForBlockUsers && (
                                <Typography sx={styles.noResultsMessage}>{translate('app.common.notFound')}</Typography>
                            )}
                        </Box>
                    )}
                </Box>

                <Box sx={styles.usersCount}>
                    <Typography variant="body2" color="textSecondary">
                        {` ${
                            blockUsersFieldArray.blockSubscribedUsers.length +
                            blockRolesFieldArray.blockSubscribedRoles.reduce(
                                (acc: number, role: { users?: any[] }) => acc + (role.users?.length ?? 0),
                                0
                            ) +
                            blockApplicationGroupsFieldArray.blockSubscribedApplicationGroups.length
                        }`}{' '}
                        {translate('app.templates.users')}
                    </Typography>
                </Box>
            </Box>

            <form>
                <UpsertGroupDialog
                    showCreateGroupModal={showCreateGroupModal}
                    setShowCreateGroupModal={setShowCreateGroupModal}
                    createGroupControl={controlGroup}
                    createGroupErros={errorsGroup}
                    isLoading={isLoading}
                    createGroupHandleSubmit={handleSubmitGroup}
                    createGroupOnSubmit={onSubmitGroup}
                    resetCreateGroupField={resetFieldGroup}
                    isEditGroup={isEditGroup}
                    templates={templateGroups}
                ></UpsertGroupDialog>
            </form>
            <CreateNewGroupDialog
                showCreateNewGroupModal={showCreateNewGroupModal}
                setShowCreateNewGroupModal={setShowCreateNewGroupModal}
                selectItemsGroups={
                    (notificationApplicationGroups
                        .sort((a, b) =>
                            a.name.toUpperCase().localeCompare(b.name.toUpperCase(), undefined, { numeric: true })
                        )
                        .map((group) => ({
                            label: group.name,
                            value: group.externalId,
                        })) as SelectItem[]) ?? []
                }
                selectedNewGroup={selectedNewGroup}
                onNewGroupOptionClick={onNewGroupOptionClick}
                isLoading={isLoading}
                handleCreateNewGroup={handleCreateNewGroup}
            ></CreateNewGroupDialog>
            <EditConfirmationModal
                open={editModelConfirmation}
                onCancel={() => setEditModelConfirmation(false)}
                onContinue={() => {
                    setEditModelConfirmation(false)
                    setIsEditGroup(true)
                }}
                templates={templateGroups}
                isAdminLevel={isAdminLevel}
            />
            <DeleteGroupConfirmationModal
                open={deleteGroupModalConfirmation}
                onCancel={() => setDeleteGroupModalConfirmation(false)}
                onContinue={onDeleteGroup}
                templates={templateGroups}
                isAdminLevel={isAdminLevel}
            />
            {backendError.length > 0 &&
                backendError.map((errorString) => {
                    const normalizedString = errorString.replace(/'/g, '"')

                    if (isValidJSON(normalizedString)) {
                        const arr = JSON.parse(normalizedString)

                        return arr.map((error: string) => (
                            <ClnAlert
                                key={error}
                                onClose={() => setBackendError([])}
                                position="secondary"
                                content={error}
                                open={true}
                                severity="error"
                            />
                        ))
                    } else {
                        return (
                            <ClnAlert
                                key={errorString}
                                onClose={() => setBackendError([])}
                                position="secondary"
                                content={errorString}
                                open={true}
                                severity="error"
                            />
                        )
                    }
                })}

            <Backdrop sx={{ zIndex: deleteGroupModalConfirmation ? 9999 : 10 }} open={isLoading}>
                <CircularProgress color="inherit" />
            </Backdrop>
        </>
    )
}

export default TemplateRecipients
