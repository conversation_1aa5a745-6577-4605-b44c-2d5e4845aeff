import { CronObject } from '@/common/models/cronObject'
import { Cln<PERSON><PERSON>t, ClnButton, ClnSelect, ClnTimePicker } from '@celanese/ui-lib'
import { Box } from '@mui/material'
import dayjs from 'dayjs'
import { Dispatch, FC, SetStateAction, useState } from 'react'
import * as styles from '../styles'
import { translate } from '@celanese/celanese-sdk'

interface FrequencyHoursMinutesDrawerProps {
    repeatEvery: number
    frequencyType: string
    setCronRequest: Dispatch<SetStateAction<CronObject>>
    isMinutes: boolean
    setIsDrawerOpen: Dispatch<SetStateAction<boolean>>
    setPreviousFrequencyType: Dispatch<SetStateAction<string>>
    startTime?: dayjs.Dayjs
    endTime?: dayjs.Dayjs
    handleCancel: () => void
}

const FrequencyHoursMinutesDrawerComponent: FC<FrequencyHoursMinutesDrawerProps> = ({
    repeatEvery,
    frequencyType,
    setCronRequest,
    isMinutes,
    setIsDrawerOpen,
    setPreviousFrequencyType,
    startTime,
    endTime,
    handleCancel,
}) => {
    const [selectedRepeat, setSelectedRepeat] = useState<{ value: string; label: string }>(() => {
        const defaultValue = isMinutes ? 30 : 4
        return {
            value: defaultValue.toString(),
            label: `${defaultValue} ${isMinutes ? 'minutes' : 'hours'}`,
        }
    })
    const [selectedStart, setSelectedStart] = useState<dayjs.Dayjs | undefined>(startTime)
    const [selectedEnd, setSelectedEnd] = useState<dayjs.Dayjs | undefined>(endTime)
    const [showError, setShowError] = useState(false)

    const options = Array.from({ length: isMinutes ? 59 : 24 }, (_, i) => ({
        label: `${i + 1} ${isMinutes ? 'minutes' : 'hours'}`,
        value: (i + 1).toString(),
    }))

    const handleChangeStart = (value: dayjs.Dayjs | null) => {
        if (value) setSelectedStart(value)
    }

    const handleChangeEnd = (value: dayjs.Dayjs | null) => {
        if (value) setSelectedEnd(value)
    }

    const handleSave = () => {
        if (selectedStart && selectedEnd) {
            const formatedStartTime = selectedStart.format('hh:mm A')
            const formatedEndTime = selectedEnd.format('hh:mm A')

            const formatedRepeat = parseInt(selectedRepeat?.value || (isMinutes ? '30' : '4'), 10)

            setCronRequest({
                schedule_type: frequencyType,
                time: formatedStartTime,
                end_time: formatedEndTime,
                interval: formatedRepeat,
            })

            setPreviousFrequencyType('By minute')
            setIsDrawerOpen(false)
        } else {
            setShowError(true)
        }
    }

    isMinutes ? translate('app.templates.frequency.minutes') : translate('app.templates.frequency.hours')

    return (
        <Box sx={styles.container}>
            <Box sx={{ ...styles.formRowNoSpace, marginTop: '10px' }}>
                <ClnSelect
                    id="atDay"
                    label="Every"
                    onChange={(value) => {
                        setSelectedRepeat(value as { value: string; label: string })
                    }}
                    options={options}
                    size="small"
                    value={selectedRepeat}
                    variant="outlined"
                    fullWidth
                />
            </Box>

            <Box sx={styles.formRow}>
                <ClnTimePicker
                    label={translate('app.templates.frequency.startIn')}
                    ampm={true}
                    onChange={handleChangeStart}
                    value={selectedStart || null}
                    size="small"
                    sxProps={{
                        width: '400px',
                    }}
                />
            </Box>

            <Box sx={styles.formRow}>
                <ClnTimePicker
                    label={translate('app.templates.frequency.endsIn')}
                    ampm={true}
                    onChange={handleChangeEnd}
                    value={selectedEnd || null}
                    size="small"
                    sxProps={{
                        width: '400px',
                    }}
                />
            </Box>

            <Box sx={styles.drawerFooter}>
                <ClnButton label={translate('app.common.cancel')} onClick={handleCancel} variant="text" />
                <ClnButton label={translate('app.templates.buttons.save')} onClick={handleSave} variant="contained" />
            </Box>
            {showError && (
                <ClnAlert
                    onClose={() => setShowError(false)}
                    position="secondary"
                    content={translate('app.templates.frequency.pleaseSelectTime')}
                    open={true}
                    severity="error"
                />
            )}
        </Box>
    )
}

export default FrequencyHoursMinutesDrawerComponent
