import { CronObject } from '@/common/models/cronObject'
import { <PERSON>ln<PERSON><PERSON>t, ClnButton, ClnSelect } from '@celanese/ui-lib'
import { Box, Typography } from '@mui/material'
import { Dispatch, FC, SetStateAction, useEffect, useState } from 'react'
import * as styles from '../styles'
import { translate } from '@celanese/celanese-sdk'
import MonthButtonsComponent from './month-buttons-component'

interface FrequencyMonthlyDrawerProps {
    months: string[]
    dayOfMonth: number
    frequencyType: string
    setCronRequest: Dispatch<SetStateAction<CronObject>>
    setIsDrawerOpen: Dispatch<SetStateAction<boolean>>
    setPreviousFrequencyType: Dispatch<SetStateAction<string>>
    handleCancel: () => void
}

export type Month =
    | 'January'
    | 'February'
    | 'March'
    | 'April'
    | 'May'
    | 'June'
    | 'July'
    | 'August'
    | 'September'
    | 'October'
    | 'November'
    | 'December'

const MonthsIndexes: Record<Month, number> = {
    January: 1,
    February: 2,
    March: 3,
    April: 4,
    May: 5,
    June: 6,
    July: 7,
    August: 8,
    September: 9,
    October: 10,
    November: 11,
    December: 12,
}

const initialButtonStates: Record<Month, boolean> = {
    January: false,
    February: false,
    March: false,
    April: false,
    May: false,
    June: false,
    July: false,
    August: false,
    September: false,
    October: false,
    November: false,
    December: false,
}

const FrequencyMonthlyDrawerComponent: FC<FrequencyMonthlyDrawerProps> = ({
    months,
    dayOfMonth,
    frequencyType,
    setCronRequest,
    setIsDrawerOpen,
    setPreviousFrequencyType,
    handleCancel
}) => {
    const optionsDayOfMonth = Array.from({ length: 31 }, (_, i) => (i + 1).toString())

    const [buttonStates, setButtonStates] = useState<Record<Month, boolean>>(initialButtonStates)
    const [selectedDay, setSelectedDay] = useState(dayOfMonth ? dayOfMonth.toString() : '10')
    const [showError, setShowError] = useState(false)

    useEffect(() => {
        const newButtonStates = { ...initialButtonStates }

        if (months.length === 0) {
            Object.keys(newButtonStates).forEach((month) => {
                newButtonStates[month as Month] = true
            })
        } else {
            months.forEach((month) => {
                newButtonStates[month as Month] = true
            })
        }

        setButtonStates(newButtonStates)
    }, [months])

    const handleSave = () => {
        const selectedMonths = []
        for (const key in buttonStates) {
            if (buttonStates[key as Month] === true) {
                selectedMonths.push(MonthsIndexes[key as Month])
            }
        }

        if (selectedMonths.length > 0) {
            setCronRequest({
                schedule_type: frequencyType,
                day_of_month: selectedDay,
                months: selectedMonths,
            })
            setPreviousFrequencyType('Monthly')
            setIsDrawerOpen(false)
        } else {
            setShowError(true)
        }
    }

    return (
        <Box sx={styles.container}>
            <Box sx={styles.formRow}>
                <Typography sx={{ fontSize: '16px', fontWeight: 'bold', marginTop: '15px' }}>
                    {translate('app.templates.frequency.repeatEveryMonth')}
                </Typography>
            </Box>
            <Box sx={styles.formRow}>
                <MonthButtonsComponent buttonStates={buttonStates} setButtonStates={setButtonStates} />
            </Box>

            <Box sx={styles.formRow}>
                <ClnSelect
                    id="atDay"
                    label="Every day"
                    onChange={(value) => setSelectedDay(value as string)}
                    options={optionsDayOfMonth}
                    size="small"
                    value={selectedDay}
                    variant="outlined"
                    fullWidth
                />
            </Box>

            <Box sx={styles.drawerFooter}>
                <ClnButton
                    label={translate('app.common.cancel')}
                    onClick={handleCancel}
                    variant="text"
                />
                <ClnButton label={translate('app.templates.buttons.save')} onClick={handleSave} variant="contained" />
            </Box>
            {showError && (
                <ClnAlert
                    onClose={() => setShowError(false)}
                    position="secondary"
                    content={translate('app.templates.frequency.pleaseSelectMonths')}
                    open={true}
                    severity="error"
                />
            )}
        </Box>
    )
}

export default FrequencyMonthlyDrawerComponent
