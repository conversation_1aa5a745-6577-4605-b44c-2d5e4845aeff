import { CronObject } from '@/common/models/cronObject'
import { <PERSON>ln<PERSON><PERSON><PERSON>, ClnButton, ClnTimePicker } from '@celanese/ui-lib'
import { Box, Typography } from '@mui/material'
import dayjs from 'dayjs'
import { Dispatch, FC, SetStateAction, useEffect, useState } from 'react'
import * as styles from '../styles'
import { translate } from '@celanese/celanese-sdk'
import WeekdayButtonsComponent from './weekday-buttons-component'

interface FrequencyWeeklyDrawerProps {
    weekdays: string[]
    frequencyType: string
    setCronRequest: Dispatch<SetStateAction<CronObject>>
    setIsDrawerOpen: Dispatch<SetStateAction<boolean>>
    startTime?: dayjs.Dayjs
    setPreviousFrequencyType: Dispatch<SetStateAction<string>>
    handleCancel: () => void
}

export type Weekday = 'Sunday' | 'Monday' | 'Tuesday' | 'Wednesday' | 'Thursday' | 'Friday' | 'Saturday'

const initialButtonStates: Record<Weekday, boolean> = {
    Sunday: false,
    Monday: false,
    Tuesday: false,
    Wednesday: false,
    Thursday: false,
    Friday: false,
    Saturday: false,
}

const FrequencyWeeklyDrawerComponent: FC<FrequencyWeeklyDrawerProps> = ({
    weekdays,
    frequencyType,
    setCronRequest,
    setIsDrawerOpen,
    setPreviousFrequencyType,
    startTime,
    handleCancel,
}) => {
    const [buttonStates, setButtonStates] = useState<Record<Weekday, boolean>>(initialButtonStates)
    const [selectedTime, setSelectedTime] = useState<dayjs.Dayjs | undefined>(startTime)
    const [showError, setShowError] = useState(false)

    const defaultWeekdays: Weekday[] = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday']

    useEffect(() => {
        const newButtonStates = { ...initialButtonStates }
        const selectedWeekDaysName = weekdays.length > 0 ? (weekdays as Weekday[]) : defaultWeekdays

        selectedWeekDaysName.map((weekday) => {
            newButtonStates[weekday] = true
        })

        setButtonStates(newButtonStates)
    }, [weekdays])

    const handleChangeTime = (value: dayjs.Dayjs | null) => {
        if (value) setSelectedTime(value)
    }

    const handleSave = () => {
        if (selectedTime) {
            const formatedTime = selectedTime.format('hh:mm A')
            const selectedDays = []

            for (const key in buttonStates) {
                if (buttonStates[key as Weekday] === true) {
                    selectedDays.push(key)
                }
            }

            if (selectedDays.length > 0) {
                setCronRequest({
                    schedule_type: frequencyType,
                    time: formatedTime,
                    day_of_week: selectedDays,
                })

                setPreviousFrequencyType('Weekly')
                setIsDrawerOpen(false)
            } else {
                setShowError(true)
            }
        } else {
            setShowError(true)
        }
    }

    return (
        <Box sx={[styles.container, { marginTop: '15px' }]}>
            <Box sx={styles.formRow}>
                <Typography sx={{ fontSize: '16px', fontWeight: 'bold' }}>{translate('app.templates.frequency.repeatEvery')}</Typography>
            </Box>
            <Box sx={{ gap: '20px', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                <WeekdayButtonsComponent buttonStates={buttonStates} setButtonStates={setButtonStates} />
            </Box>
            <Box>
                <ClnTimePicker
                    label={'At'}
                    ampm={true}
                    onChange={handleChangeTime}
                    value={selectedTime || null}
                    sx={{ width: '100%', marginTop: '15px' }}
                />
            </Box>

            <Box sx={styles.drawerFooter}>
                <ClnButton
                    label={translate('app.common.cancel')}
                    onClick={handleCancel}
                    variant="text"
                />
                <ClnButton label={translate('app.templates.buttons.save')} onClick={handleSave} variant="contained" />
            </Box>
            {showError && (
                <ClnAlert
                    onClose={() => setShowError(false)}
                    position="secondary"
                    content={translate('app.templates.frequency.pleaseSelectDays')}
                    open={true}
                    severity="error"
                />
            )}
        </Box>
    )
}

export default FrequencyWeeklyDrawerComponent
