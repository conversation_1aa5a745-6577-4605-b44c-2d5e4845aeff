import { ClnButton } from '@celanese/ui-lib'
import { Box } from '@mui/material'
import React from 'react'
import * as styles from '../styles'
import { Month } from '@/common/models/customizeTemplates'

interface MonthButtonsProps {
    buttonStates: Record<Month, boolean>
    setButtonStates: React.Dispatch<React.SetStateAction<Record<Month, boolean>>>
}

const MonthButtons: React.FC<MonthButtonsProps> = ({ buttonStates, setButtonStates }) => {
    const handleButtonClick = (month: Month) => {
        setButtonStates((prevState) => ({
            ...prevState,
            [month]: !prevState[month],
        }))
    }

    return (
        <Box sx={{ display: 'grid', gridTemplateColumns: '1fr 1fr 1fr' }}>
            {(Object.keys(buttonStates) as Month[]).map((month) => (
                <ClnButton
                    key={month}
                    label={month}
                    sxProps={{ ...styles.buttonRoundedMonth, margin: '5px' }}
                    size="small"
                    onClick={() => handleButtonClick(month)}
                    variant={buttonStates[month] ? 'contained' : 'outlined'}
                />
            ))}
        </Box>
    )
}

export default MonthButtons
