import { ClnBaseIcon } from '@celanese/ui-lib'
import { Box, Button, Typography } from '@mui/material'
import * as styles from './send-to.styles'

interface SelectedChipProps {
    key: string
    label: string
    isGroup?: boolean
    onExpandClick?: () => void
    onRemoveClick?: () => void
    readOnly?: boolean
}

export default function SelectedChip({
    label,
    isGroup = false,
    onExpandClick,
    onRemoveClick,
    readOnly = false,
}: Readonly<SelectedChipProps>) {
    return (
        <Box sx={styles.selectedChipContainer}>
            {isGroup &&
                (readOnly ? (
                    <></>
                ) : (
                    <Button onClick={onExpandClick}>
                        <ClnBaseIcon className="cln-ico-add" sx={styles.expandIcon} />
                    </Button>
                ))}
            <Typography sx={styles.buttonLabel}>{label}</Typography>
            {!readOnly && (
                <Button onClick={onRemoveClick}>
                    <ClnBaseIcon className="cln-ico-close" sx={styles.icon} />
                </Button>
            )}
        </Box>
    )
}
