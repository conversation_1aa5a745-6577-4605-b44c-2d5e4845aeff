import { CSSObject } from 'styled-components'

export const selectedChipContainer: CSSObject = {
    display: 'flex',
    alignItems: 'center',
    backgroundColor: 'action.hover',
    padding: '5px 8px',
    gap: '1px',
    borderRadius: '50px',
    maxWidth: 'calc(25vw - 84px)',
    '& .MuiButtonBase-root': {
        minWidth: 0,
        padding: 0,
    },
    ':hover': {
        backgroundColor: 'action.selected',
        cursor: 'pointer',
    },
}

export const buttonLabel: CSSObject = {
    textOverflow: 'ellipsis',
    whiteSpace: 'nowrap',
    overflow: 'hidden',
}

export const icon: CSSObject = {
    color: 'text.primary',
}

export const expandIcon: CSSObject = {
    ...icon,
    fontSize: '20px',
}
