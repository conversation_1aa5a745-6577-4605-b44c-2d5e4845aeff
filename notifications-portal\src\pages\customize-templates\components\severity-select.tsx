import { useSeverityRequest } from '@/common/hooks/useSeverityRequest'
import { ClnSelect } from '@celanese/ui-lib'
import { SelectItem } from '@celanese/ui-lib'
import { FC } from 'react'
import { translate } from '@celanese/celanese-sdk'
import { ValidationSchemaCustomizeTemplates } from '@/common/models/customizeTemplates'
import { Control, Controller } from 'react-hook-form'


interface SeveritySelectProps {
    disabled: boolean
    control: Control<ValidationSchemaCustomizeTemplates>
}

const SeveritySelect: FC<SeveritySelectProps> = ({ disabled, control }) => {
    const { data: severities } = useSeverityRequest(true, ['severity'])

    const translations: Record<string, string> = {
        'LOW': translate('app.notifications.filter.severity.low'),
        'MEDIUM': translate('app.notifications.filter.severity.medium'),
        'HIGH': translate('app.notifications.filter.severity.high')
    }

    const getSeverityOptions = () => {
        const options: SelectItem[] = severities
            ? severities.map((severity) => {
                return {
                    label: translations[severity.name] || severity.description,
                    value: severity.externalId,
                }
            })
            : []

        return options
    }

    return (
        <Controller
            control={control}
            name="severity"
            render={({ field }) => (
                <ClnSelect
                    disabled={disabled}
                    label={translate('app.templates.severity')}
                    options={getSeverityOptions()}
                    fullWidth
                    helperText=' '
                    variant="outlined"
                    {...field}
                />
            )}
        />
    )
}

export default SeveritySelect
