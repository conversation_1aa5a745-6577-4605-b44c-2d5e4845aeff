import { Dispatch, SetStateAction } from 'react'
import { ClnButton, ClnTooltip } from '@celanese/ui-lib'
import { Box } from '@mui/material'
import * as styles from '../styles'
import { translate } from '@celanese/celanese-sdk'

interface StepperButtonsProps {
    disabled: boolean
    activeStep: number
    setActiveStep: Dispatch<SetStateAction<number>>
    steps: string[]
    handleCancel: () => void
    handleSave: () => void
    stepValidation: (activeStep: number) => void
    canChangeStep: { canChange: boolean; message: string }
}

const StepperButtons = ({
    disabled,
    activeStep,
    setActiveStep,
    steps,
    handleCancel,
    handleSave,
    stepValidation,
    canChangeStep,
}: StepperButtonsProps) => {
    const totalSteps = () => {
        return steps.length
    }

    const isLastStep = () => {
        return activeStep === totalSteps() - 1
    }

    const handleNext = () => {
        stepValidation(activeStep)
    }

    const handleBack = () => {
        setActiveStep((prevActiveStep: number) => prevActiveStep - 1)
    }

    return (
        <Box sx={styles.stepperButtonsContainer}>
            <ClnButton label={translate('app.common.cancel')} variant="text" onClick={handleCancel} size="medium" />
            {activeStep != 0 && (
                <ClnTooltip title={translate(canChangeStep.message)}>
                    <span>
                        <ClnButton
                            label={translate('app.templates.buttons.back')}
                            variant="outlined"
                            onClick={handleBack}
                            size="medium"
                            disabled={!canChangeStep.canChange}
                        />
                    </span>
                </ClnTooltip>
            )}
            {isLastStep() ? (
                <span>
                    <ClnButton disabled={disabled} label={translate('app.templates.buttons.save')} onClick={handleSave} size="medium" />
                </span>
            ) : (
                <ClnTooltip title={translate(canChangeStep.message)}>
                    <span>
                        <ClnButton
                            label={translate('app.templates.buttons.next')}
                            onClick={handleNext}
                            size="medium"
                            disabled={!canChangeStep.canChange}
                        />
                    </span>
                </ClnTooltip>
            )}
        </Box>
    )
}

export default StepperButtons
