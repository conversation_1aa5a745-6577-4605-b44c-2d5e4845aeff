import { ClnButton } from '@celanese/ui-lib'
import React, { Dispatch, SetStateAction } from 'react'
import * as styles from '../styles'
import { Weekday } from '@/common/models/customizeTemplates'

interface WeekdayButtonsProps {
    buttonStates: Record<Weekday, boolean>
    setButtonStates: Dispatch<SetStateAction<Record<Weekday, boolean>>>
}

const WeekdayButtonsComponent: React.FC<WeekdayButtonsProps> = ({ buttonStates, setButtonStates }) => {
    const handleButtonClick = (day: Weekday) => {
        setButtonStates((prevState) => ({
            ...prevState,
            [day]: !prevState[day],
        }))
    }

    return (
        <>
            {(Object.keys(buttonStates) as Weekday[]).map((day) => (
                <ClnButton
                    key={day}
                    label={day.charAt(0)}
                    sxProps={Object.assign({}, styles.buttonRoundedWeekdayComponent, { backgroundColor: !buttonStates[day] ? 'lightgrey' : undefined })}
                    size="large"
                    onClick={() => handleButtonClick(day)}
                />
            ))}
        </>
    )
}

export default WeekdayButtonsComponent
