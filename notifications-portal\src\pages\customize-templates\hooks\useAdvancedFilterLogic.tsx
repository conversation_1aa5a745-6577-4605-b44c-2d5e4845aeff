import { useState, useEffect, type Dispatch, type SetStateAction } from 'react'
import { useForm, SubmitHandler, Control, UseFormSetValue, UseFormHandleSubmit } from 'react-hook-form'
import {
    AdvancedSearchOptionsProps,
    LocationAPIProps,
    RoleAPIProps,
    SiteAPIProps,
    TeamAPIProps,
    UnitAPIProps,
    ValidationSchemaAdvancedSeacrh,
} from '@/common/models/customizeTemplates'
import { SelectItem } from '@celanese/ui-lib'

interface FieldsProps {
    roleSite: boolean,
    team: boolean,
    unit: boolean,
    reportingLocation: boolean,
}

export interface UseAdvancedFilterLogicProps {
    id: string | undefined,
    open: boolean,
    handleClose: () => void,
    handleClearFilterClick: () => void,
    control: Control<ValidationSchemaAdvancedSeacrh>,
    siteOptions: SelectItem[],
    setValue: UseFormSetValue<ValidationSchemaAdvancedSeacrh>,
    defaultItem: SelectItem,
    setDisabledFields: Dispatch<SetStateAction<FieldsProps>>,
    setLoading: Dispatch<SetStateAction<FieldsProps>>,
    advancedSearchOptions: AdvancedSearchOptionsProps,
    disabledFields: FieldsProps,
    roleOptionsSite: SelectItem[],
    loading: FieldsProps,
    roleOptionsApp: SelectItem[],
    teamOptions: SelectItem[],
    unitOptions: SelectItem[],
    locationOptions: SelectItem[],
    handleSubmit: UseFormHandleSubmit<ValidationSchemaAdvancedSeacrh>
    onSubmit: SubmitHandler<ValidationSchemaAdvancedSeacrh>
    handleCloseFilter: () => void
}

interface AdvancedFilterLogicProps {
    open: boolean,
    applyFilter: (params?: any) => void,
    getFieldsOptions: {
          searchRolesBySite: (params?: any) => void,
          searchTeamsBySite: (params?: any) => void,
          searchUnitsBySite: (params?: any) => void,
          searchReportingLocationsByUnit: (params?: any) => void,
        },
    advancedSearchOptions: AdvancedSearchOptionsProps
    handleClose: () => void
}

export default function useAdvancedFilterLogic({
    open,
    applyFilter,
    getFieldsOptions,
    advancedSearchOptions,
    handleClose,
}: AdvancedFilterLogicProps): UseAdvancedFilterLogicProps  {
    const id = open ? 'filter-popover' : undefined

    const defaultItem = {
        value: '',
        label: '',
    }
    const { control, watch, setValue, reset, handleSubmit } = useForm<ValidationSchemaAdvancedSeacrh>({
        defaultValues: {
            reportingSite: defaultItem,
            roleSite: defaultItem,
            roleApplication: defaultItem,
            team: defaultItem,
            unit: defaultItem,
            reportingLocation: defaultItem,
        },
    })
    const formValues = watch()
    const [disabledFields, setDisabledFields] = useState({
        roleSite: true,
        team: true,
        unit: true,
        reportingLocation: true,
    })

    const [loading, setLoading] = useState({
        roleSite: false,
        team: false,
        unit: false,
        reportingLocation: false,
    })

    const roleOptionsSite = advancedSearchOptions.rolesSite?.map((roleSite: RoleAPIProps) => ({
        label: roleSite.name,
        value: roleSite.externalId,
    }))
    const roleOptionsApp = advancedSearchOptions.rolesApp?.map((roleApp: RoleAPIProps) => ({
        label: roleApp.name,
        value: roleApp.externalId,
    }))
    const siteOptions = advancedSearchOptions.site?.map((site: SiteAPIProps) => ({
        label: site.name,
        value: site.externalId,
    }))
    const teamOptions = advancedSearchOptions.teams?.map((team: TeamAPIProps) => ({
        label: team.name,
        value: team.externalId,
    }))
    const unitOptions = advancedSearchOptions.unit?.map((unit: UnitAPIProps) => ({
        label: unit.description,
        value: unit.externalId,
    }))
    const locationOptions = advancedSearchOptions.location?.map((location: LocationAPIProps) => ({
        label: location.description,
        value: location.externalId,
    }))

    const handleCloseFilter = () => {
        setDisabledFields({
            roleSite: true,
            team: true,
            unit: true,
            reportingLocation: true,
        })
        reset()
    }

    const onSubmit: SubmitHandler<ValidationSchemaAdvancedSeacrh> = (values) => {
        const params: Record<string, any> = {}
        const rolesExternalIds: string[] = []

        if (values.reportingSite?.value) {
            params.reportingSiteExternalId = values.reportingSite.value
        }

        if (values.unit?.value) {
            params.reportingUnitExternalId = values.unit.value
        }

        if (values.reportingLocation?.value) {
            params.reportingLocationExternalId = values.reportingLocation.value
        }

        if (values.team?.value) {
            params.team = values.team.value
        }

        if (values.roleSite?.value) {
            rolesExternalIds.push(values.roleSite.value)
        }

        if (values.roleApplication?.value) {
            rolesExternalIds.push(values.roleApplication.value)
        }

        if (rolesExternalIds.length > 0) {
            params.roleExternalId = rolesExternalIds.join(',')
        }

        applyFilter(params)
        handleCloseFilter()
        handleClose()
    }

    const handleClearFilterClick = () => {
        setTimeout(() => {
            applyFilter()
        }, 0)
        handleCloseFilter()
        handleClose()
    }

    useEffect(() => {
        if (formValues.reportingSite?.value) {
            getFieldsOptions.searchRolesBySite(formValues.reportingSite.value)
            getFieldsOptions.searchTeamsBySite(formValues.reportingSite.value)
            getFieldsOptions.searchUnitsBySite(formValues.reportingSite.value)
        }
    }, [formValues.reportingSite])

    useEffect(() => {
        if (formValues.unit?.value) {
            getFieldsOptions.searchReportingLocationsByUnit(formValues.unit.value)
        }
    }, [formValues.unit])

    useEffect(() => {
        setDisabledFields((prev) => ({ ...prev, roleSite: false }))
        setLoading((prev) => ({ ...prev, roleSite: false }))
    }, [advancedSearchOptions.rolesSite])

    useEffect(() => {
        setDisabledFields((prev) => ({ ...prev, team: false }))
        setLoading((prev) => ({ ...prev, team: false }))
    }, [advancedSearchOptions.teams])

    useEffect(() => {
        setDisabledFields((prev) => ({ ...prev, unit: false }))
        setLoading((prev) => ({ ...prev, unit: false }))
    }, [advancedSearchOptions.unit])

    useEffect(() => {
        setDisabledFields((prev) => ({ ...prev, reportingLocation: false }))
        setLoading((prev) => ({ ...prev, reportingLocation: false }))
    }, [advancedSearchOptions.location])

    return {
        id,
        open,
        handleClose,
        handleClearFilterClick,
        control,
        siteOptions,
        setValue,
        defaultItem,
        setDisabledFields,
        setLoading,
        advancedSearchOptions,
        disabledFields,
        roleOptionsSite,
        loading,
        roleOptionsApp,
        teamOptions,
        unitOptions,
        locationOptions,
        handleSubmit,
        onSubmit,
        handleCloseFilter,
    }
}