import { CronObject } from '@/common/models/cronObject'
import { ValidationSchemaCustomizeTemplates } from '@/common/models/customizeTemplates'
import { translate, TranslationContext, TranslationContextState } from '@celanese/celanese-sdk'
import { SelectItem } from '@celanese/ui-lib'
import { Dispatch, SetStateAction, useContext, useEffect, useState } from 'react'
import { UseFormSetValue } from 'react-hook-form'
import dayjs from 'dayjs'

const MonthsNames: Record<number, string> = {
    1: 'January',
    2: 'February',
    3: 'March',
    4: 'April',
    5: 'May',
    6: 'June',
    7: 'July',
    8: 'August',
    9: 'September',
    10: 'October',
    11: 'November',
    12: 'December',
}

const MonthsNamesTranslation: Record<number, string> = {
    1: 'app.common.months.january',
    2: 'app.common.months.february',
    3: 'app.common.months.march',
    4: 'app.common.months.april',
    5: 'app.common.months.may',
    6: 'app.common.months.june',
    7: 'app.common.months.july',
    8: 'app.common.months.august',
    9: 'app.common.months.september',
    10: 'app.common.months.october',
    11: 'app.common.months.november',
    12: 'app.common.months.december',
}

const DaysNamesTranslation: Record<string, string> = {
    Monday: 'app.common.days.monday',
    Tuesday: 'app.common.days.tuesday',
    Wednesday: 'app.common.days.wednesday',
    Thursday: 'app.common.days.thursday',
    Friday: 'app.common.days.friday',
    Saturday: 'app.common.days.saturday',
    Sunday: 'app.common.days.sunday',
}

export enum FrequencyType {
    OnDemand = 'On demand',
    ByMinute = 'By minute',
    ByHour = 'By hour',
    Weekly = 'Weekly',
    Monthly = 'Monthly',
}

const parseMonthsIndexToMonthsNames = (monthsIndexes: number[] | undefined) => {
    const monthsNames = monthsIndexes?.map((month) => MonthsNames[month])
    return monthsNames
}

const parseTimeTo24Format = (time: string) => {
    let parsedTime = ''

    const timeAndAMPM = time.split(' ')
    const hourAndMinute = timeAndAMPM[0].split(':')
    if (timeAndAMPM[1] === 'AM') {
        if (hourAndMinute[0] === '12') {
            parsedTime = `00:${hourAndMinute[1]}`
        } else {
            parsedTime = hourAndMinute.join(':')
        }
    } else {
        const hour = parseInt(hourAndMinute[0]) + 12
        parsedTime = `${hour}:${hourAndMinute[1]}`
    }

    return parsedTime
}

const handleShowUserInput = (
    cronRequest: CronObject,
    initialSelectItems: SelectItem[],
    setSelectItems: Dispatch<SetStateAction<SelectItem[]>>
) => {
    if (Object.keys(cronRequest).length > 0) {
        const newItems = initialSelectItems

        switch (cronRequest.schedule_type) {
            case 'minute':
                newItems[1] = {
                    value: 'By minute',
                    label: `${translate('app.templates.frequency.frequencyTypes.byMinute')} - ${translate(
                        'app.templates.frequency.occursFrom'
                    )} ${cronRequest.time} ${translate('app.templates.frequency.to')} ${
                        cronRequest.end_time
                    } ${translate('app.templates.frequency.inIntervalsOf')} ${cronRequest.interval} ${(
                        translate('app.templates.frequency.minutes') as string
                    ).toLowerCase()}`,
                }
                setSelectItems(newItems)
                break

            case 'hourly':
                newItems[2] = {
                    value: 'By hour',
                    label: `${translate('app.templates.frequency.frequencyTypes.byHour')} - ${translate(
                        'app.templates.frequency.occursFrom'
                    )} ${cronRequest.time} ${translate('app.templates.frequency.to')} ${
                        cronRequest.end_time
                    } ${translate('app.templates.frequency.inIntervalsOf')} ${cronRequest.interval} ${(
                        translate('app.templates.frequency.hours') as string
                    ).toLowerCase()}`,
                }
                setSelectItems(newItems)
                break

            case 'weekly': {
                const labels = cronRequest.day_of_week?.map((day) => DaysNamesTranslation[day])
                const weekdaysLabels = labels?.map((label) => translate(label))

                newItems[3] = {
                    value: 'Weekly',
                    label: `${translate('app.templates.frequency.frequencyTypes.weekly')} - ${translate(
                        'app.templates.frequency.occurs'
                    )} ${weekdaysLabels?.join(', ')} ${translate('app.templates.frequency.at')} ${cronRequest.time}`,
                }
                setSelectItems(newItems)
                break
            }

            case 'monthly': {
                const labels = cronRequest.months?.map((month) => MonthsNamesTranslation[month])
                const monthsLabels = labels?.map((label) => translate(label))

                newItems[4] = {
                    value: 'Monthly',
                    label: `${translate('app.templates.frequency.frequencyTypes.monthly')} - ${translate(
                        'app.templates.frequency.occurs'
                    )} ${monthsLabels?.join(', ')} ${translate('app.templates.frequency.atDays')} ${cronRequest.day_of_month}`,
                }
                setSelectItems(newItems)
                break
            }
        }
    } else {
        setSelectItems(initialSelectItems)
    }
}

interface UseFrequencyLogicProps {
    frequencyType?: SelectItem
    setValue?: UseFormSetValue<ValidationSchemaCustomizeTemplates>
    defaultValues?: any
    frequencyCronExpression?: any
    frequencyValue?: any
}

export default function useFrequencyLogic({
    frequencyType,
    setValue,
    defaultValues,
    frequencyCronExpression,
    frequencyValue,
}: UseFrequencyLogicProps = {}) {

    const frequencyTranslations: Record<string, string> = {
        'On demand': 'app.templates.frequency.frequencyTypes.onDemand',
        'By minute': 'app.templates.frequency.frequencyTypes.byMinute',
        'By hour': 'app.templates.frequency.frequencyTypes.byHour',
        Weekly: 'app.templates.frequency.frequencyTypes.weekly',
        Monthly: 'app.templates.frequency.frequencyTypes.monthly',
    }

    const initialSelectItems = Object.values(FrequencyType).map((freq) => {
        return {
            label: frequencyTranslations[freq] || freq.toString(),
            value: freq.toString(),
        }
    })

    const [selectItems, setSelectItems] = useState<SelectItem[]>(initialSelectItems)
    const [cronRequest, setCronRequest] = useState<CronObject>({} as CronObject)

    const { locale } = useContext<TranslationContextState>(TranslationContext)

    useEffect(() => {
        const checkLocalStorage = () => {
            if (window.localStorage && locale && window.localStorage.getItem(`APP-NTFTranslationData${locale}`)) {
                setSelectItems(initialSelectItems)
            } else {
                setTimeout(checkLocalStorage, 2000)
            }
        }

        checkLocalStorage()
    }, [locale])

    const [isDrawerOpen, setIsDrawerOpen] = useState(false)

    const handleFrequencyChange = (value: any) => {
        const selectedValue = value as SelectItem
        setValue && setValue('frequency', selectedValue)
        if (selectedValue.value !== FrequencyType.OnDemand) {
            setIsDrawerOpen(true)
        } else {
            setCronRequest({} as CronObject)
        }
    }

    const handleOptionClick = (value: string) => {
        if (frequencyType?.value === value && frequencyType?.value !== FrequencyType.OnDemand) {
            setIsDrawerOpen(true)
        }
    }

    const handleCloseDrawer = (value: any) => {
        setValue && setValue('frequency', value)
        setCronRequest({} as CronObject)

        setIsDrawerOpen(false)
    }

    useEffect(() => handleShowUserInput(cronRequest, initialSelectItems, setSelectItems), [cronRequest])

    useEffect(() => {
        const frequencyCronExpression =
            Object.keys(cronRequest).length > 0
                ? {
                      ...cronRequest,
                      time: cronRequest.time ? parseTimeTo24Format(cronRequest.time) : undefined,
                      end_time: cronRequest.end_time ? parseTimeTo24Format(cronRequest.end_time) : undefined,
                  }
                : ''
        setValue && setValue('frequencyCronExpression', frequencyCronExpression)
    }, [cronRequest])

    useEffect(() => {
        if (frequencyCronExpression || frequencyValue?.value == 'On demand') {
            defaultValues = defaultValues ? { ...defaultValues, frequencyCronExpression } : { frequencyCronExpression }
        }
        if (defaultValues?.frequencyCronExpression) {
            const startTimeSplit = defaultValues.frequencyCronExpression.time
                ? defaultValues.frequencyCronExpression.time.split(':')
                : []
            const formatedStartTime =
                startTimeSplit.length > 0
                    ? dayjs().hour(parseInt(startTimeSplit[0])).minute(parseInt(startTimeSplit[1])).format('hh:mm A')
                    : ''

            const endTimeSplit = defaultValues.frequencyCronExpression.end_time
                ? defaultValues.frequencyCronExpression.end_time.split(':')
                : []
            const formatedEndTime =
                endTimeSplit.length > 0
                    ? dayjs().hour(parseInt(endTimeSplit[0])).minute(parseInt(endTimeSplit[1])).format('hh:mm A')
                    : ''

            setCronRequest({
                ...defaultValues.frequencyCronExpression,
                time: defaultValues.frequencyCronExpression.time ? formatedStartTime : undefined,
                end_time: defaultValues.frequencyCronExpression.end_time ? formatedEndTime : undefined,
            })
        }
    }, [defaultValues])

    return {
        selectItems,
        handleFrequencyChange,
        handleOptionClick,
        isDrawerOpen,
        setIsDrawerOpen,
        setCronRequest,
        cronRequest,
        parseMonthsIndexToMonthsNames,
        handleCloseDrawer,
    }
}
