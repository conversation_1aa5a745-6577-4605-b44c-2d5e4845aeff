import { useApiService } from '@/common/hooks/useApiService'
import { ErrorResponseData } from '@/common/models/errorResponseData'
import {
    notificationApplicationGroupSchema,
    NotificationApplicationGroupSchema,
} from '@/common/models/notificationApplicationGroup'
import { zodResolver } from '@hookform/resolvers/zod'
import { AxiosError } from 'axios'
import { Dispatch, SetStateAction, useEffect, useState } from 'react'
import { useFieldArray, useForm, UseFormGetValues, UseFormSetValue } from 'react-hook-form'
import {
    saveNotificationApplicationGroupURI,
    getTemplateByNotificationApplicationGroupIdURI,
    deleteNotificationApplicationGroupURI,
} from '@/common/configurations/endpoints'
import { SelectItem } from '@celanese/ui-lib'
import { ValidationSchemaCustomizeTemplates } from '@/common/models/customizeTemplates'

interface UseNotificationApplicationGroupProps {
    usersFieldArray: any
    rolesFieldArray: any
    externalUsersFieldArray: any
    blockUsersFieldArray: any
    blockRolesFieldArray: any
    applicationGroupsFieldArray: any
    setValue: UseFormSetValue<ValidationSchemaCustomizeTemplates>
    getValues: UseFormGetValues<ValidationSchemaCustomizeTemplates>
    selectedGroups: NotificationApplicationGroupSchema[]
    setSelectedGroups: Dispatch<SetStateAction<NotificationApplicationGroupSchema[]>>
    clickedGroup: NotificationApplicationGroupSchema | null
    setClickedGroup: Dispatch<SetStateAction<NotificationApplicationGroupSchema | null>>
    backendError: string[]
    setBackendError: Dispatch<SetStateAction<string[]>>
    application: {
        name: string
        id: string
    }
    getNotificationApplicationGroups: (groupId?: string, setLoading?: (value: SetStateAction<boolean>) => void) => void
    setNotificationApplicationGroups: Dispatch<SetStateAction<NotificationApplicationGroupSchema[]>>
    notificationApplicationGroups: NotificationApplicationGroupSchema[]
}

export default function useNotificationApplicationGroup({
    usersFieldArray,
    rolesFieldArray,
    externalUsersFieldArray,
    blockUsersFieldArray,
    blockRolesFieldArray,
    applicationGroupsFieldArray,
    setValue,
    selectedGroups,
    setSelectedGroups,
    clickedGroup,
    setClickedGroup,
    backendError,
    setBackendError,
    application,
    getNotificationApplicationGroups,
    setNotificationApplicationGroups,
    notificationApplicationGroups,
}: UseNotificationApplicationGroupProps) {
    const axios = useApiService()

    const [showCreateGroupModal, setShowCreateGroupModal] = useState(false)
    const [showEditGroupModal, setShowEditGroupModal] = useState(false)
    const [showCreateNewGroupModal, setShowCreateNewGroupModal] = useState(false)
    const [isLoading, setIsLoading] = useState(false)
    const [isLoadingGroup, setIsLoadingGroup] = useState(false)

    const [selectItemsGroups, setSelectItemsGroups] = useState<SelectItem[]>([])
    const [selectedNewGroup, setSelectedNewGroup] = useState<string>()
    const [isNewGroup, setIsNewGroup] = useState(false)
    const [templateGroups, setTemplateGroups] = useState<ValidationSchemaCustomizeTemplates[]>([])

    const [editModelConfirmation, setEditModelConfirmation] = useState(false)
    const [isEditGroup, setIsEditGroup] = useState(false)

    const [deleteGroupModalConfirmation, setDeleteGroupModalConfirmation] = useState(false)

    const [currentSentTo, setCurrentSentTo] = useState({
        users: [],
        roles: [],
        externalUsers: [],
        blockUsers: [],
        blockRoles: [],
    })

    useEffect(() => {
        const groupSelected = selectedGroups.map((group) => group.externalId)
        const groupOptions = notificationApplicationGroups
            .filter((group) => group.externalId && !groupSelected.includes(group.externalId))
            .map((group) => ({
                label: group.name,
                value: group.externalId,
            })) as SelectItem[]

        setSelectItemsGroups(
            [...groupOptions].sort((a, b) =>
                a.label.toUpperCase().localeCompare(b.label.toUpperCase(), undefined, { numeric: true })
            )
        )
    }, [notificationApplicationGroups, selectedGroups])

    useEffect(() => {
        if (selectedGroups.length === 0 && clickedGroup) {
            setClickedGroup(null)
            unbindGroup()
        }
        refreshApplicationGroupBindings(selectedGroups)
    }, [selectedGroups, clickedGroup])

    const getTemplatesByGroup = (groupId: string) => {
        setIsLoading(true)

        axios
            .get(getTemplateByNotificationApplicationGroupIdURI(groupId))
            .then((res) => {
                const { message } = res.data
                setTemplateGroups(message)
                setIsLoading(false)
                if (message.length > 0) {
                    setEditModelConfirmation(true)
                } else {
                    setIsEditGroup(true)
                }
            })
            .catch((err) => {
                setIsLoading(false)
                const typedErr = (err as AxiosError).response?.data as ErrorResponseData
                if (typeof typedErr.exception === 'string') {
                    setBackendError([typedErr.exception])
                } else if (Array.isArray(typedErr.exception)) {
                    setBackendError(typedErr.exception)
                }
            })
    }

    const {
        handleSubmit,
        control,
        watch,
        getValues,
        resetField,
        clearErrors,
        setError,
        formState: { errors },
        reset,
    } = useForm<NotificationApplicationGroupSchema>({
        resolver: zodResolver(notificationApplicationGroupSchema),
        defaultValues: { name: '', description: '', users: [], externalUsers: [], blocklist: [] },
        values: {
            externalId: clickedGroup?.externalId ?? undefined,
            name: clickedGroup?.name ?? '',
            description: clickedGroup?.description ?? '',
            users: clickedGroup?.users ?? [],
            usersRoles: clickedGroup?.usersRoles ?? [],
            externalUsers: clickedGroup?.externalUsers ?? [],
            blocklist: clickedGroup?.blocklist ?? [],
            blocklistRoles: clickedGroup?.blocklistRoles ?? [],
        },
    })

    const { append: appendUsers, remove: removeUsers } = useFieldArray({
        control,
        name: 'users',
    })

    const { append: appendUsersRoles, remove: removeUsersRoles } = useFieldArray({
        control,
        name: 'usersRoles',
    })

    const { append: appendExternalUsers, remove: removeExternalUsers } = useFieldArray({
        control,
        name: 'externalUsers',
    })

    const { append: appendBlocklist, remove: removeBlocklist } = useFieldArray({
        control,
        name: 'blocklist',
    })

    const { append: appendBlocklistRoles, remove: removeBlocklistRoles } = useFieldArray({
        control,
        name: 'blocklistRoles',
    })

    const onSubmit = (values: NotificationApplicationGroupSchema) => {
        const externalIdProp = values.externalId ? { externalId: values.externalId } : {}

        const saveModel = {
            ...externalIdProp,
            externalId: values.externalId,
            name: values.name,
            description: values.description,
            users: values.users ? values.users.map((user) => user.externalId) : [],
            externalUsers: values.externalUsers ? values.externalUsers.map((externalUser) => externalUser?.email) : [],
            blocklist: values.blocklist ? values.blocklist.map((blocklist) => blocklist?.email) : [],
            application: application.id,
            blocklistRoles: values.blocklistRoles ? values.blocklistRoles.map((role) => role.externalId) : [],
            usersRoles: values.usersRoles ? values.usersRoles.map((role) => role.externalId) : [],
        }

        setIsLoading(true)

        axios
            .post(saveNotificationApplicationGroupURI, saveModel)
            .then((res) => {
                const { message } = res.data
                getNotificationApplicationGroups(message.externalId, setIsLoadingGroup)

                setClickedGroup(null)

                clearBingRecipients()

                setIsNewGroup(false)
                setShowCreateGroupModal(false)
                setIsLoading(false)
                setIsEditGroup(false)
                reset()
            })
            .catch((err) => {
                setIsLoading(false)
                const typedErr = (err as AxiosError).response?.data as ErrorResponseData
                if (typeof typedErr.exception === 'string') {
                    setBackendError([typedErr.exception])
                } else if (Array.isArray(typedErr.exception)) {
                    setBackendError(typedErr.exception)
                }
            })
    }

    const onDeleteGroup = () => {
        setIsLoading(true)

        axios
            .delete(deleteNotificationApplicationGroupURI(clickedGroup?.externalId ?? ''))
            .then((res) => {
                handleGroupDeletionSuccess()
            })
            .catch((err) => {
                setIsLoading(false)
                const typedErr = (err as AxiosError).response?.data as ErrorResponseData
                if (typeof typedErr.exception === 'string') {
                    setBackendError([typedErr.exception])
                } else if (Array.isArray(typedErr.exception)) {
                    setBackendError(typedErr.exception)
                }
            })
    }

    const handleCreateNewGroup = () => {
        const selectedGroupFilter = notificationApplicationGroups.find((group) => group.externalId === selectedNewGroup)

        clearBingRecipients()

        if (selectedGroupFilter) {
            bindSelectGroup(selectedGroupFilter)
        }

        setSelectedGroups([])
        setSelectedNewGroup('')
        setClickedGroup(null)
        setShowCreateNewGroupModal(false)
        setIsNewGroup(true)
    }

    const handleCancelOperationsGroup = () => {
        const selectedGroupFilter = notificationApplicationGroups.find(
            (group) => group.externalId === clickedGroup?.externalId
        )

        if (selectedGroupFilter) {
            clearBingRecipients()
            bindSelectGroup(selectedGroupFilter)
        }

        setIsNewGroup(false)
        setIsEditGroup(false)
    }

    const handleEditGroup = () => {
        getTemplatesByGroup(clickedGroup?.externalId ?? '')
    }

    const handleDeleteGroup = () => {
        setDeleteGroupModalConfirmation(true)
    }

    const handleOptionClickGroups = (value: SelectItem) => {
        if (!clickedGroup) {
            setCurrentSentTo({
                users: usersFieldArray.subscribedUsers || [],
                roles: rolesFieldArray.subscribedRoles || [],
                externalUsers:
                    externalUsersFieldArray.subscribedExternalUsers.map((user: any) => ({ email: user })) || [],
                blockUsers: blockUsersFieldArray.blockSubscribedUsers || [],
                blockRoles: blockRolesFieldArray.blockSubscribedRoles || [],
            })
        }

        if (value.value === clickedGroup?.externalId) {
            setClickedGroup(null)
            unbindGroup()
            return
        }

        const selectedGroupFilter = notificationApplicationGroups.find((group) => group.externalId === value.value)
        setClickedGroup(selectedGroupFilter || null)

        if (selectedGroupFilter) {
            bindSelectGroup(selectedGroupFilter)
        }
    }

    const handleOptionClickRemoveGroup = (optionToDelete: SelectItem) => {
        const updatedGroups = selectedGroups.filter((group) => group.externalId !== optionToDelete.value)
        setSelectedGroups(updatedGroups)

        if (clickedGroup?.externalId === optionToDelete.value) {
            setClickedGroup(null)
            unbindGroup()
        }
    }

    const handleSelectGroups = (selectedGroups: SelectItem[]) => {
        const groups: NotificationApplicationGroupSchema[] = []

        selectedGroups.forEach((group) => {
            const selectedGroupFilter = notificationApplicationGroups.find(
                (groupFilter) => groupFilter.externalId === group.value
            )
            if (selectedGroupFilter) {
                groups.push(selectedGroupFilter)
            }
        })

        setSelectedGroups(groups)
    }

    const handleUpsertGroup = () => {
        removeUsers()
        removeUsersRoles()
        removeExternalUsers()
        removeBlocklist()
        removeBlocklistRoles()

        appendUsers(usersFieldArray.subscribedUsers)
        appendUsersRoles(rolesFieldArray.subscribedRoles)
        appendExternalUsers(
            externalUsersFieldArray.subscribedExternalUsers.map((user: any) => ({
                email: user.email.trim(),
            }))
        )
        appendBlocklist(blockUsersFieldArray.blockSubscribedUsers)
        appendBlocklistRoles(blockRolesFieldArray.blockSubscribedRoles)

        setShowCreateGroupModal(true)
    }

    const onNewGroupOptionClick = (value: string) => {
        setSelectedNewGroup(value)
    }

    function unbindGroup() {
        clearBingRecipients()

        usersFieldArray.replaceUsers(currentSentTo.users)
        rolesFieldArray.replaceRoles(currentSentTo.roles)
        externalUsersFieldArray.removeExternalUsers()
        currentSentTo.externalUsers.forEach((user: any) => {
            externalUsersFieldArray.appendExternalUsers(user.email)
        })
        blockUsersFieldArray.replaceBlockUsers(currentSentTo.blockUsers)
        blockRolesFieldArray.replaceBlockRoles(currentSentTo.blockRoles)
    }

    function handleGroupDeletionSuccess() {
        setNotificationApplicationGroups((prev) =>
            prev.filter((group) => group.externalId !== clickedGroup?.externalId)
        )

        const updatedSelectedGroup = selectedGroups.filter((group) => group.externalId !== clickedGroup?.externalId)

        setSelectedGroups(updatedSelectedGroup)
        setClickedGroup(null)

        clearBingRecipients()

        setDeleteGroupModalConfirmation(false)
        setIsNewGroup(false)
        setShowCreateGroupModal(false)
        setIsLoading(false)
        setIsEditGroup(false)
    }

    function refreshApplicationGroupBindings(updatedGroups: any) {
        applicationGroupsFieldArray.removeApplicationGroups()

        updatedGroups.forEach((group: any) => {
            applicationGroupsFieldArray.appendApplicationGroups({
                ...group,
                application: group?.application?.externalId ?? '',
                externalUsers:
                    group?.externalUsers?.map((user: any) =>
                        typeof user === 'string' ? { email: user } : { email: user.email }
                    ) ?? [],
            })
        })
    }

    function clearBingRecipients() {
        usersFieldArray.removeUsers()
        rolesFieldArray.removeRoles()
        externalUsersFieldArray.removeExternalUsers()
        blockUsersFieldArray.removeBlockUsers()
        blockRolesFieldArray.removeBlockRoles()
    }

    function bindSelectGroup(selectedGroupFilter: NotificationApplicationGroupSchema | undefined) {
        usersFieldArray.replaceUsers(selectedGroupFilter?.users || [])
        rolesFieldArray.replaceRoles(selectedGroupFilter?.usersRoles || [])
        externalUsersFieldArray.removeExternalUsers()
        selectedGroupFilter?.externalUsers.forEach((user) => {
            externalUsersFieldArray.appendExternalUsers({ email: user })
        })
        blockUsersFieldArray.replaceBlockUsers(selectedGroupFilter?.blocklist || [])
        blockRolesFieldArray.replaceBlockRoles(selectedGroupFilter?.blocklistRoles || [])
    }

    return {
        showCreateGroupModal,
        setShowCreateGroupModal,
        showEditGroupModal,
        setShowEditGroupModal,
        showCreateNewGroupModal,
        setShowCreateNewGroupModal,
        selectedNewGroup,
        handleSubmitGroup: handleSubmit,
        onSubmitGroup: onSubmit,
        controlGroup: control,
        watch,
        setValue,
        getValues,
        resetFieldGroup: resetField,
        clearErrors,
        setError,
        errorsGroup: errors,
        isLoading,
        selectItemsGroups,
        handleOptionClickGroups,
        handleSelectGroups,
        onNewGroupOptionClick,
        handleCreateNewGroup,
        isNewGroup,
        handleCancelOperationsGroup,
        isEditGroup,
        setIsEditGroup,
        handleUpsertGroup,
        clickedGroup,
        templateGroups,
        editModelConfirmation,
        setEditModelConfirmation,
        handleOptionClickRemoveGroup,
        isLoadingGroup,
        handleEditGroup,
        deleteGroupModalConfirmation,
        setDeleteGroupModalConfirmation,
        handleDeleteGroup,
        onDeleteGroup,
        backendError,
        setBackendError,
        currentSentTo,
    }
}
