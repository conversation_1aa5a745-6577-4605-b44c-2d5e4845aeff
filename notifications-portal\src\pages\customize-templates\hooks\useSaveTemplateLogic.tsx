/* eslint-disable */

import { useRouter, useSearchParams } from 'next/navigation'
import { useForm, type SubmitHandler, useFieldArray } from 'react-hook-form'
import { translate } from '@celanese/celanese-sdk'
import {
    ScheduleToFrequencyType,
    UseSaveTemplateLogicProps,
    validationSchemaCustomizeTemplates,
    ValidationSchemaCustomizeTemplates,
} from '@/common/models/customizeTemplates'
import { zodResolver } from '@hookform/resolvers/zod'
import { CheckboxItem, SelectItem } from '@celanese/ui-lib'
import { useEffect, useState, useCallback, SetStateAction, useRef } from 'react'
import { useTheme } from '@mui/material/styles'
import { Message } from '@/common/components/MessageContainer/message-container'
import WarningIcon from '@mui/icons-material/Warning'
import { useApiService } from '@/common/hooks/useApiService'
import {
    saveTemplateURI,
    notificationTypeURI,
    getTemplateByIdURI,
    lastedNotificationEventURI,
    userSearch,
    reportingSitesURI,
    rolesSearchByApplicationURI,
    getNotificationApplicationGroupURI,
} from '@/common/configurations/endpoints'
import { AxiosError } from 'axios'
import { ErrorResponseData } from '@/common/models/errorResponseData'
import { NotificationEvent } from '@/common/models/notificationEvent'
import {
    replaceTextVariables,
    removeDuplicatedVariables,
    updateIsNumeric,
    propertyNameToLowerCase,
    createDefaultValues,
} from '@/common/utils/customizeTemplates'
import { User } from '../../../common/models/user'
import { NotificationApplicationGroupSchema } from '@/common/models/notificationApplicationGroup'

const _ = require('lodash')

const addSMSNotAllowedMessage = (messages: Message[]) => {
    const isMessageExist = messages.some((message) => message.text === translate('app.templates.alerts.smsMessageText'))
    if (!isMessageExist) {
        return {
            icon: <WarningIcon />,
            text: translate('app.templates.alerts.smsMessageText'),
            iconColor: 'orange',
            backgroundColor: 'grey[300]',
        }
    }
    return null
}

const addTeamsNotAllowedMessage = (messages: Message[]) => {
    const isMessageExist = messages.some(
        (message) => message.text === translate('app.templates.alerts.teamsMessageText')
    )
    if (!isMessageExist) {
        return {
            icon: <WarningIcon />,
            text: translate('app.templates.alerts.teamsMessageText'),
            iconColor: 'orange',
            backgroundColor: 'grey[300]',
        }
    }
    return null
}

const customizations = [
    { value: 'channels', label: 'app.templates.channels' },
    { value: 'frequency', label: 'app.templates.frequencyOption' },
]

export default function useSaveTemplateLogic(): UseSaveTemplateLogicProps {
    const theme = useTheme()
    const axios = useApiService()
    const router = useRouter()
    const searchParams = useSearchParams()

    const [warningMessages, setWarningMessages] = useState<Message[]>([
        {
            icon: <WarningIcon />,
            text: translate('app.templates.alerts.onDemandFrequency'),
            iconColor: 'orange',
            backgroundColor: theme.palette.grey[300],
        },
    ])
    const query = new URLSearchParams(window.location.search)
    const decodedFormType = decodeURIComponent(atob(query.get('f') ?? ''))
    const decodedIsAdminLevel = decodeURIComponent(atob(query.get('a') ?? ''))
    const [notificationTypeId, setNotificationTypeId] = useState<string>('')
    const [notificationTypeName, setNotificationTypeName] = useState<string>('')
    const [messageVariables, setMessageVariables] = useState<{ name: string; value: any; type?: string }[]>([])
    const [hasProperties, setHasProperites] = useState<boolean>(true)
    const formType = decodedFormType != '' ? decodedFormType : 'view'
    const isAdminLevel = decodedIsAdminLevel !== '' ? decodedIsAdminLevel === 'true' : false
    const isAdminEdit = isAdminLevel && formType === 'edit'
    const [selectedCustomizations, setSelectedCustomizations] = useState<CheckboxItem[]>([])
    const [showModalLostData, setShowModalLostData] = useState<boolean>(false)
    const [backendError, setBackendError] = useState<string[]>([])
    const [isLoading, setIsLoading] = useState<boolean>(true)
    const [previousFrequencyType, setPreviousFrequencyType] = useState('On demand')
    const [defaultValues, setDefaultValues] = useState<any>()
    const [showDuplicateModal, setShowDuplicateModal] = useState<boolean>(false)
    const [isDuplicate, setIsDuplicate] = useState<boolean>(false)
    const [application, setApplication] = useState<{ name: string; id: string }>({ name: '', id: '' })
    const [allDisabled, setAllDisabled] = useState<boolean>(false)
    const [keepRecipients, setKeepRecipients] = useState<CheckboxItem[]>([])
    const [activeStep, setActiveStep] = useState(0)
    const [isNextStepOneClicked, setIsNextStepOneClicked] = useState(false)
    const [notificationEntities, setNotificationEntities] = useState<[string]>()
    const [isEdition, setIsEdition] = useState<boolean>(false)
    const [loadingUsersFilter, setLoadingUsersFilter] = useState(false)
    const [hasId, setHasId] = useState(false)
    const [editInfo, setEditInfo] = useState({
        showEditInfo: false,
        editedBy: '',
        editedAt: '',
    })
    const [selectedGroups, setSelectedGroups] = useState<NotificationApplicationGroupSchema[]>([])
    const [notificationApplicationGroups, setNotificationApplicationGroups] = useState<
        NotificationApplicationGroupSchema[]
    >([])
    const [clickedGroup, setClickedGroup] = useState<NotificationApplicationGroupSchema | null>(null)
    const [canChangeStep, setCanChangeStep] = useState({
        canChange: true,
        message: '',
    })

    const [advancedSearchOptions, setAdvancedSearchOptions] = useState<any>({
        site: [],
        rolesSite: [],
        rolesApp: [],
        teams: [],
        location: [],
        unit: [],
    })

    const initialValues = {
        externalId: undefined,
        adminLevel: isAdminLevel,
        allUsers: [],
        channels: [],
        conditionals: [],
        customChannelEnabled: false,
        customFrequencyEnabled: false,
        deleted: false,
        externalUsers: [],
        frequencyCronExpression: '',
        frequency: {
            value: translate('app.templates.frequency.frequencyTypes.onDemand'),
            label: translate('app.templates.frequency.frequencyTypes.onDemand'),
        },
        name: '',
        notificationType: notificationTypeId,
        severity: {
            value: 'NTFSVT-LOW',
            label: translate('app.notifications.filter.severity.low'),
        },
        subject: '',
        subscribedApplicationGroups: [],
        subscribedExternalUsers: [],
        subscribedRoles: [],
        subscribedUsers: [],
        blocksubscribedApplicationGroups: [],
        blocksubscribedRoles: [],
        blocksubscribedUsers: [],
        text: '',
        textExample: '',
        externalUsersInput: '',
        usersInput: '',
    }

    const crumbs = [
        {
            title: translate('app.templates.title'),
            onClickHandler: () => {
                router.push('/templates')
            },
        },
        {
            title: hasId
                ? translate('app.templates.buttons.editTemplate')
                : translate('app.templates.buttons.createTemplate'),
            onClickHandler: () => { },
            disabled: true,
        },
    ]

    const steps = isAdminLevel
        ? [translate('app.steps.one'), translate('app.steps.two'), translate('app.steps.three')]
        : [translate('app.steps.one'), translate('app.steps.three')]

    const firstValues = defaultValues || initialValues
    const [filteredUsers, setFilteredUsers] = useState<User[]>([])

    const {
        handleSubmit,
        control,
        watch,
        setValue,
        getValues,
        resetField,
        clearErrors,
        setError,
        trigger,
        getFieldState,
        formState: { errors },
    } = useForm<ValidationSchemaCustomizeTemplates>({
        resolver: zodResolver(validationSchemaCustomizeTemplates),
        defaultValues: initialValues,
        values: firstValues,
    })

    const applyFilter = useCallback((params?: any) => {
        setLoadingUsersFilter(true)
        axios.get(userSearch(params ? 1000 : 100), { params }).then((result) => {
            const { message } = result.data
            setFilteredUsers(message)
            setLoadingUsersFilter(false)
        })
    }, [])

    const [selectedUsers] = useState<any[]>([])

    const {
        fields: conditionals,
        append: appendConditional,
        remove: removeConditional,
    } = useFieldArray({
        control, // control props comes from useForm (optional: if you are using FormContext)
        name: 'conditionals', // unique name for your Field Array
    })

    const {
        fields: subscribedExternalUsers,
        append: appendExternalUsers,
        remove: removeExternalUsers,
        replace: replaceExternalUsers,
    } = useFieldArray({
        control, // control props comes from useForm (optional: if you are using FormContext)
        name: 'subscribedExternalUsers', // unique name for your Field Array
    })

    const {
        fields: subscribedUsers,
        append: appendUsers,
        remove: removeUsers,
        replace: replaceUsers,
    } = useFieldArray({
        control, // control props comes from useForm (optional: if you are using FormContext)
        name: 'subscribedUsers', // unique name for your Field Array
    })

    const {
        fields: subscribedRoles,
        append: appendRoles,
        remove: removeRoles,
        replace: replaceRoles,
    } = useFieldArray({
        control, // control props comes from useForm (optional: if you are using FormContext)
        name: 'subscribedRoles', // unique name for your Field Array
    })

    const {
        fields: subscribedApplicationGroups,
        append: appendApplicationGroups,
        remove: removeApplicationGroups,
        replace: replaceApplicationGroups,
    } = useFieldArray({
        control, // control props comes from useForm (optional: if you are using FormContext)
        name: 'subscribedApplicationGroups', // unique name for your Field Array
    })

    const {
        fields: blockSubscribedUsers,
        append: appendBlockUsers,
        remove: removeBlockUsers,
        replace: replaceBlockUsers,
    } = useFieldArray({
        control,
        name: 'blockSubscribedUsers',
    })

    const {
        fields: blockSubscribedRoles,
        append: appendBlockRoles,
        remove: removeBlockRoles,
        replace: replaceBlockRoles,
    } = useFieldArray({
        control,
        name: 'blockSubscribedRoles',
    })

    const {
        fields: blockSubscribedApplicationGroups,
        append: appendBlockApplicationGroups,
        remove: removeBlockApplicationGroups,
        replace: replaceBlockApplicationGroups,
    } = useFieldArray({
        control,
        name: 'blockSubscribedApplicationGroups',
    })

    const channels = watch('channels')
    const customFrequencyEnabled = watch('customFrequencyEnabled')
    const customChannelEnabled = watch('customChannelEnabled')
    const notificationText = watch('text')
    const text = watch('text')
    const conditionalsField = watch('conditionals')
    const templateName = watch('name')
    const formValues = watch()

    const validateFields = (values: ValidationSchemaCustomizeTemplates) => {
        let hasError = false

        if (isDuplicate && values.name.includes(translate('app.templates.copy'))) {
            hasError = true
            setError('name', { type: 'custom', message: 'String must contain at least 1 character(s)' })
        }

        return hasError
    }

    const onSubmit: SubmitHandler<ValidationSchemaCustomizeTemplates> = async (values) => {
        const isValid = validateFields(values)

        if (!isValid) {
            const subscribedUsersId = values.subscribedUsers?.map((user) => user.externalId)
            const subscribedRolesId = values.subscribedRoles?.map((role) => role.externalId)
            const subscribedApplicationsId = values.subscribedApplicationGroups?.map(
                (application) => application.externalId
            )
            const subscribedExternalUsersEmail = values.subscribedExternalUsers && values.subscribedExternalUsers.length > 0
                ? values.subscribedExternalUsers?.map((user) => user.email)
                : []
            const blockSubscribedUsersId = values.blockSubscribedUsers?.map((user) => user.externalId)
            const blockSubscribedRolesId = values.blockSubscribedRoles?.map((role) => role.externalId)
            const blockSubscribedApplicationsId = values.blockSubscribedApplicationGroups?.map(
                (application) => application.externalId
            )
            const externalIdProp = values.externalId ? { externalId: values.externalId } : {}

            const formatConditionals = values.conditionals?.map((cond) => {
                const messageVariableMatch = messageVariables.find((el) => el.name === cond.variable)
                return updateIsNumeric(cond, messageVariableMatch)
            })

            const saveRequest = {
                notificationTemplate: {
                    ...externalIdProp,
                    externalId: values.externalId,
                    name: values.name,
                    notificationType: {
                        externalId: values.notificationType,
                    },
                    text: values.text,
                    severity: {
                        externalId: values.severity.value,
                    },
                    conditionalExpression: JSON.stringify(formatConditionals),
                    adminLevel: values.adminLevel,
                    customChannelEnabled: values.customChannelEnabled,
                    customFrequencyEnabled: values.customFrequencyEnabled,
                    channels: values.channels?.map((channel) => {
                        return {
                            externalId: channel.value,
                        }
                    }),
                    frequencyCronExpression: values.frequencyCronExpression,
                    subscribedUsers: subscribedUsersId,
                    subscribedUserRoles: subscribedRolesId,
                    subscribedApplicationGroups: subscribedApplicationsId,
                    subscribedExternalUsers: subscribedExternalUsersEmail,
                    blocklist: blockSubscribedUsersId,
                    blocklistRoles: blockSubscribedRolesId,
                    blockSubscribedApplicationGroups: blockSubscribedApplicationsId,
                    deleted: false,
                    allUsers: values.allUsers.length > 0,
                    externalUsers: subscribedExternalUsersEmail && subscribedExternalUsersEmail.length > 0,
                    subject: values.subject,
                },
                isAdminEdition: isAdminEdit,
            }

            setIsLoading(true)
            axios
                .post(saveTemplateURI, saveRequest)
                .then(() => {
                    setIsLoading(false)
                    router.push('/templates')
                })
                .catch((err) => {
                    const typedErr = (err as AxiosError).response?.data as ErrorResponseData
                    if (typeof typedErr.exception === 'string') {
                        setBackendError([typedErr.exception])
                    } else if (Array.isArray(typedErr.exception)) {
                        setBackendError(typedErr.exception)
                    }
                    setIsLoading(false)
                })
        }
    }

    const handleBackClick = () => {
        if (!_.isEqual(firstValues, formValues) && !allDisabled) {
            setShowModalLostData(true)
        } else {
            navigateBack()
        }
    }

    const navigateBack = () => {
        setShowModalLostData(false)
        router.push('/templates')
    }

    const handleAddField = (field: string, start: number, end: number) => {
        const isHTML = messageVariables.find((el) => el.name === field)?.type === 'html'
        if (isHTML) {
            const messageText = translate('app.templates.alerts.htmlMVariable')
            const isMessageExist = warningMessages.some((message) => message.text === messageText)
            if (!isMessageExist) {
                const newMessage = {
                    icon: <WarningIcon />,
                    text: messageText,
                    iconColor: 'orange',
                    backgroundColor: theme.palette.grey[300],
                }
                setWarningMessages([...warningMessages, newMessage])
            }
        }
        const currentText = getValues('text') || ''
        const newText = currentText.slice(0, start) + `{{${field}}}` + currentText.slice(end)
        setValue('text', newText)
    }

    const handleDuplicateTemplate = () => {
        sessionStorage.setItem('duplicateTemplateName', formValues.name)

        if (keepRecipients.length > 0) {
            sessionStorage.setItem('keepRecipients', 'true')
        } else sessionStorage.setItem('keepRecipients', 'false')

        router.push(
            `/customize-templates?id=${formValues.externalId}&a=${btoa(String(isAdminLevel))}&f=${btoa(
                'edit'
            )}&duplicate=true`
        )
        setShowDuplicateModal(false)
        setIsLoading(true)
    }

    const updateSelectedChannels = (channels: SelectItem[]) => {
        const smsChannelSelected = channels.some((channel) => channel.label === 'SMS')
        const teamsChannelSelected = channels.some((channel) => channel.label === 'TEAMS')

        let updatedMessages = [...warningMessages]

        if (teamsChannelSelected) {
            const teamsMessage = addTeamsNotAllowedMessage(warningMessages)
            if (teamsMessage) {
                updatedMessages.push(teamsMessage)
            }
        } else {
            updatedMessages = updatedMessages.filter(
                (message) => message.text !== translate('app.templates.alerts.teamsMessageText')
            )
        }

        if (smsChannelSelected) {
            const smsMessage = addSMSNotAllowedMessage(warningMessages)
            if (smsMessage) {
                updatedMessages.push(smsMessage)
            }
        } else {
            updatedMessages = updatedMessages.filter(
                (message) => message.text !== translate('app.templates.alerts.smsMessageText')
            )
        }

        setWarningMessages(updatedMessages)
    }

    const getNotificationTypeProperties = () => {
        setHasProperites(true)
        axios.get(notificationTypeURI, { params: { id: notificationTypeId } }).then((res) => {
            const application = res.data.message[0]?.application
            const properties = res.data.message[0]?.properties
            if (properties != null && properties.length > 0) {
                setMessageVariables(propertyNameToLowerCase(properties))
            } else {
                setHasProperites(false)
            }
            setApplication({
                name: application.alias,
                id: application.externalId,
            })
            setNotificationTypeName(res.data.message[0]?.name || '')
            setIsLoading(false)
        })
    }

    const getNotificationTypeLastEvents = (id: string) => {
        const url = lastedNotificationEventURI
            .replace('{notification_type_external_id}', id)
            .replace('{count}', (50).toString())

        axios.get(url).then((response) => {
            const allProperties: any = []
            const propertiesArray = (response.data.message as NotificationEvent[] | undefined)?.flatMap(
                (item) => item?.properties
            )
            propertiesArray?.forEach((propObj) => {
                allProperties.push({
                    name: propObj.name.toLowerCase(),
                    type: propObj.type,
                    value: propObj.value,
                })
            })

            const result = removeDuplicatedVariables(allProperties)
            if (messageVariables.length === 0) {
                setMessageVariables(result)
            }
        })
    }

    const getNotificationApplicationGroups = (
        groupId?: string,
        setLoading?: (value: SetStateAction<boolean>) => void
    ) => {
        if (!application.id) {
            return
        }

        setLoading && setLoading(true)

        axios
            .get(getNotificationApplicationGroupURI(application.id))
            .then((res) => {
                const { message } = res.data

                if (groupId) {
                    const selectedGroupFilter = message.find((g: any) => g.externalId === groupId)

                    if (selectedGroupFilter) {
                        const updatedSelectedGroup = [
                            ...selectedGroups.filter((group) => group.externalId !== selectedGroupFilter.externalId),
                        ]
                        setSelectedGroups([...updatedSelectedGroup, selectedGroupFilter])
                    }
                }

                setNotificationApplicationGroups(message)
                setLoading && setLoading(false)
            })
            .catch((err) => {
                setLoading && setIsLoading(false)
                const typedErr = (err as AxiosError).response?.data as ErrorResponseData
                if (typeof typedErr.exception === 'string') {
                    setBackendError([typedErr.exception])
                } else if (Array.isArray(typedErr.exception)) {
                    setBackendError(typedErr.exception)
                }
            })
    }

    const handleChangeKeepRecipientList = (e: CheckboxItem[]) => {
        setKeepRecipients(e)
    }
    useEffect(() => {
        setIsLoading(true)
        setIsDuplicate(searchParams.has('duplicate'))
        const isEdit = formType === 'edit' || formType === 'editMyTemplate'
        if (searchParams.has('type')) {
            const notificationType = searchParams.get('type') || ''
            setNotificationTypeId(notificationType)
            setValue('notificationType', notificationType)
        }

        if (searchParams.has('id')) {
            setIsEdition(true)
            setHasId(true)
            const id = searchParams.get('id') || ''
            axios.get(getTemplateByIdURI(id, isAdminLevel)).then((result) => {
                const data = result.data.message
                setNotificationEntities(data.notificationTypeEntity != '' ? data.notificationTypeEntity.split(',').map((s: string) => s.trim()) : [])

                const customizations = []
                if (data.customChannelEnabled) {
                    customizations.push({
                        value: 'channels',
                        label: 'app.templates.channels',
                    })
                }

                if (data.customFrequencyEnabled) {
                    customizations.push({
                        value: 'frequency',
                        label: 'app.templates.frequencyOption',
                    })
                }

                setSelectedCustomizations(customizations)

                setPreviousFrequencyType(
                    data.frequencyCronExpression != null
                        ? ScheduleToFrequencyType[data.frequencyCronExpression.schedule_type]
                        : 'On demand'
                )

                const defaultValues = createDefaultValues(data, translate, searchParams.has('duplicate'))
                setSelectedGroups(defaultValues.subscribedApplicationGroups)
                setDefaultValues(defaultValues)
                if (data.notificationTypeProperties.length > 0) {
                    setMessageVariables(propertyNameToLowerCase(data.notificationTypeProperties))
                } else {
                    getNotificationTypeLastEvents(data.notificationType)
                }
                setEditInfo({
                    showEditInfo: !(formType === 'editMyTemplate' || formType === 'view'),
                    editedBy: data.editedBy,
                    editedAt: data.editedAt,
                })
                setNotificationTypeName(data.notificationTypeName)
                setApplication({
                    name: data.applicationName,
                    id: data.application,
                })
                setIsLoading(false)
            })
        }

        setAllDisabled(!isEdit)
    }, [searchParams])

    useEffect(() => {
        const text = replaceTextVariables(notificationText, messageVariables)
        const decodedText = decodeHtml(text);
        const textWithBreaks = decodedText
            .replace(/ {2,}/g, (match) => '&nbsp;'.repeat(match.length)) 
            .replace(/\n/g, '<br />')
        setValue('textExample', textWithBreaks)
    }, [notificationText, activeStep])

    const decodeHtml = (html: string) => {
        const txt = document.createElement("textarea");
        txt.innerHTML = html;
        return txt.value;
    };

    useEffect(() => {
        const channels = selectedCustomizations?.find((el) => el.value === 'channels')
        const frequency = selectedCustomizations?.find((el) => el.value === 'frequency')

        setValue('customChannelEnabled', !!channels)
        setValue('customFrequencyEnabled', !!frequency)
    }, [selectedCustomizations])

    useEffect(() => {
        if (errors.channels && channels && channels.length > 0) {
            clearErrors('channels')
        }

        if (errors.text && text.length > 0) {
            clearErrors('text')
        }

        if (errors.name) {
            clearErrors('name')
        }

        if (
            errors.conditionals &&
            conditionals.length > 0 &&
            conditionals[0].variable != '' &&
            conditionals[0].operator != '' &&
            conditionals[0].value != ''
        ) {
            clearErrors('conditionals')
        }

        if (channels && channels.length > 0) {
            updateSelectedChannels(channels)
        }
    }, [channels, text, conditionalsField, templateName])

    useEffect(() => {
        if (notificationTypeId) {
            getNotificationTypeProperties()
        }
    }, [notificationTypeId])

    useEffect(() => {
        if (application) {
            getNotificationApplicationGroups()
        }
    }, [application])

    useEffect(() => {
        if (notificationTypeId && messageVariables.length === 0 && !hasProperties) {
            getNotificationTypeLastEvents(notificationTypeId)
        }
    }, [notificationTypeId, messageVariables, hasProperties])

    const [showAdvancedSearchModal, setShowAdvancedSearchModal] = useState(false)

    const handleOpenAdvancedSearchModal = () => setShowAdvancedSearchModal(true)
    const handleCloseAdvancedSearchModal = () => {
        setShowAdvancedSearchModal(false)
    }

    const [showAllowlistModal, setShowAllowlistModal] = useState(false)
    const [showBlocklistModal, setShowBlocklistModal] = useState(false)

    const handleOpenAllowlistModal = () => {
        setShowAllowlistModal(true)
    }

    const handleCloseAllowlistModal = () => {
        setShowAllowlistModal(false)
    }

    const handleOpenBlocklistModal = () => {
        setShowBlocklistModal(true)
    }

    const handleCloseBlocklistModal = () => {
        setShowBlocklistModal(false)
    }

    const stepValidation = async (activeStep: number) => {
        await trigger()
        const currentErrors = Object.keys(watch()).reduce((acc, fieldName: any) => {
            const fieldState = getFieldState(fieldName)
            if (fieldState.invalid) {
                acc[fieldName] = fieldState.error
            }
            return acc
        }, {} as Record<string, any>)
        const allUsersValue = watch('allUsers')
        const isAllUsersSelected = allUsersValue.length > 0

        let isValid = false
        if (activeStep == 0) {
            if (!('text' in currentErrors)) {
                setActiveStep(activeStep + 1)
                clearErrors(['conditionals', 'subscribedUsers', 'subscribedRoles', 'subscribedApplicationGroups'])
            }
        }
        if (activeStep == 1) {
            setIsNextStepOneClicked(true)
            const subscribedUsers = watch('subscribedUsers')
            const subscribedRoles = watch('subscribedRoles')
            const subscribedApplicationGroups = watch('subscribedApplicationGroups')

            const isSubscribedValid =
                (subscribedUsers && subscribedUsers.length > 0) ||
                (subscribedRoles && subscribedRoles.length > 0) ||
                (subscribedApplicationGroups && subscribedApplicationGroups.length > 0)

            if (isSubscribedValid || isAllUsersSelected) {
                setActiveStep(activeStep + 1)
                clearErrors('conditionals')
            }
        }
        return isValid
    }

    useEffect(() => {
        if (sessionStorage.getItem('keepRecipients') === 'false' && isDuplicate) {
            replaceApplicationGroups([])
            setSelectedGroups([])
            replaceUsers([])
            replaceRoles([])
            replaceExternalUsers([])
            replaceBlockUsers([])
            replaceBlockRoles([])

            resetField('subscribedExternalUsers')
            resetField('allUsers')
            resetField('usersInput')
        }
    }, [sessionStorage.getItem('keepRecipients'), defaultValues])

    const isStepFailed = (step: number) => {
        if (step === 0 && errors.name) {
            return true
        }
    }

    useEffect(() => {
        applyFilter()

        axios.get(reportingSitesURI()).then((result) => {
            setAdvancedSearchOptions((prev: any) => ({ ...prev, site: result.data.message }))
        })
    }, [])

    useEffect(() => {
        if (application.id) {
            axios.get(rolesSearchByApplicationURI(application.id)).then((response) => {
                setAdvancedSearchOptions((prev: any) => ({ ...prev, rolesApp: response.data.message }))
            })
        }
    }, [application])

    return {
        crumbs,
        control,
        onSubmit,
        handleSubmit,
        handleBackClick,
        isAdminLevel,
        isAdminEdit,
        warningMessages,
        handleAddField,
        notificationTypeId,
        messageVariables,
        setMessageVariables,
        getValues,
        selectedCustomizations,
        setSelectedCustomizations,
        customizations,
        errors,
        frequencyType: getValues('frequency') || {
            value: translate('app.templates.frequency.frequencyTypes.onDemand'),
            label: translate('app.templates.frequency.frequencyTypes.onDemand'),
        },
        setValue,
        showModalLostData,
        setShowModalLostData,
        navigateBack,
        watch,
        externalUsersFieldArray: {
            subscribedExternalUsers,
            appendExternalUsers,
            removeExternalUsers,
            replaceExternalUsers,
        },
        usersFieldArray: {
            subscribedUsers,
            appendUsers,
            removeUsers,
            replaceUsers,
        },
        rolesFieldArray: {
            subscribedRoles,
            appendRoles,
            removeRoles,
            replaceRoles,
        },
        applicationGroupsFieldArray: {
            subscribedApplicationGroups,
            appendApplicationGroups,
            removeApplicationGroups,
            replaceApplicationGroups,
        },
        blockUsersFieldArray: {
            blockSubscribedUsers,
            appendBlockUsers,
            removeBlockUsers,
            replaceBlockUsers,
        },
        blockRolesFieldArray: {
            blockSubscribedRoles,
            appendBlockRoles,
            removeBlockRoles,
            replaceBlockRoles,
        },
        blockApplicationGroupsFieldArray: {
            blockSubscribedApplicationGroups,
            appendBlockApplicationGroups,
            removeBlockApplicationGroups,
            replaceBlockApplicationGroups,
        },
        conditionalsFieldArray: {
            conditionals,
            removeConditional,
            appendConditional,
        },
        resetField,
        backendError,
        setBackendError,
        isLoading,
        previousFrequencyType,
        setPreviousFrequencyType,
        defaultValues,
        showDuplicateModal,
        setShowDuplicateModal,
        editInfo,
        handleDuplicateTemplate,
        isDuplicate,
        notificationTypeName,
        application,
        disabled: allDisabled || formType === 'view' || formType === 'editMyTemplateAdmin',
        selectedChannels: channels,
        customFrequencyEnabled,
        customChannelEnabled,
        steps,
        showAdvancedSearchModal,
        handleOpenAdvancedSearchModal,
        handleCloseAdvancedSearchModal,
        filteredUsers,
        applyFilter,
        selectedUsers,
        templateName,
        keepRecipients,
        handleChangeKeepRecipientList,
        showAllowlistModal,
        showBlocklistModal,
        handleOpenAllowlistModal,
        handleCloseAllowlistModal,
        handleOpenBlocklistModal,
        handleCloseBlocklistModal,
        stepValidation,
        activeStep,
        setActiveStep,
        isNextStepOneClicked,
        selectedGroups,
        setSelectedGroups,
        clickedGroup,
        setClickedGroup,
        isStepFailed,
        canChangeStep,
        setCanChangeStep,
        advancedSearchOptions,
        setAdvancedSearchOptions,
        loadingUsersFilter,
        getNotificationApplicationGroups,
        setNotificationApplicationGroups,
        notificationApplicationGroups,
        formType,
        notificationEntities,
        isEdition,
    }
}
