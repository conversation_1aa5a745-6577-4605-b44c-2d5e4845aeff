import { UseFormSetValue } from 'react-hook-form'
import { translate } from '@celanese/celanese-sdk'
import { Dispatch, SetStateAction, useEffect, useState } from 'react'
import { NotificationRole } from '@/common/models/notificationRole'
import { useApiService } from '@/common/hooks/useApiService'
import { NotificationUser } from '@/common/models/notificationUser'
import { stringIsNotNullOrEmpty, validateEmail } from '@/common/utils/utils'
import {
    userSearchURI,
    rolesSearchBySiteURI,
    teamURI,
    reportingUnitURI,
    reportingLocationURI,
} from '@/common/configurations/endpoints'
import { AdvancedSearchOptionsProps, ValidationSchemaCustomizeTemplates } from '@/common/models/customizeTemplates'
import { User } from '../../../common/models/user'

interface UseSendToLogicProps {
    watch: any
    externalUsersFieldArray: any
    resetField: any
    application: any
    setValue: UseFormSetValue<ValidationSchemaCustomizeTemplates>
    filteredUsers: any
    applicationId: string
    setAdvancedSearchOptions: Dispatch<SetStateAction<AdvancedSearchOptionsProps>>
}
export default function UseSendToLogic({
    watch,
    externalUsersFieldArray,
    resetField,
    application,
    filteredUsers,
    applicationId,
    setAdvancedSearchOptions,
}: UseSendToLogicProps) {
    const [rolesOptions, setRolesOptions] = useState([] as NotificationRole[])
    const [usersOptions, setUsersOptions] = useState([] as NotificationUser[])
    const [showSuggestions, setShowSuggestions] = useState(false)
    const [showExternalSuggestions, setShowExternalSuggestions] = useState(false)
    const [rolesBlockOptions, setRolesBlockOptions] = useState([] as NotificationRole[])
    const [usersBlockOptions, setUsersBlockOptions] = useState([] as NotificationUser[])
    const [showBlockSuggestions, setShowBlockSuggestions] = useState(false)
    const [isLoadingSearch, setIsLoadingSearch] = useState(false)
    const [isLoadingBlockSearch, setIsLoadingBlockSearch] = useState(false)
    const [showNoResultsMessage, setShowNoResultsMessage] = useState(false)
    const [showNoResultsMessageForBlockUsers, setShowNoResultsMessageForBlockUsers] = useState(false)
    const [externalUserEmailisValid, setExternalUserEmailisValid] = useState(false)

    const axios = useApiService()

    const isAllUsers = watch('allUsers').length > 0
    const isExternalUsers = watch('externalUsers').length > 0
    const externalUsersInput = watch('externalUsersInput') || ''
    const usersInput = watch('usersInput') || ''
    const blockUsersInput = watch('blockUsersInput') || ''
    const [users, setUsers] = useState<User[]>([])
    const [loading, setLoading] = useState(true)

    const searchRolesBySite = async (params: any) => {
        try {
            const result = await axios.get(rolesSearchBySiteURI(params))
            setAdvancedSearchOptions((prev: any) => ({ ...prev, rolesSite: result.data.message }))
        } catch (error) {
            console.error(error)
            return []
        }
    }

    const searchTeamsBySite = async (site: any) => {
        try {
            const result = await axios.get(teamURI(), {
                params: {
                    reportingSiteExternalId: site,
                },
            })
            setAdvancedSearchOptions((prev: any) => ({ ...prev, teams: result.data.message }))
        } catch (error) {
            console.error(error)
            return []
        }
    }

    const searchUnitsBySite = async (params: any) => {
        try {
            const result = await axios.get(reportingUnitURI(params))
            setAdvancedSearchOptions((prev: any) => ({ ...prev, unit: result.data.message }))
        } catch (error) {
            console.error(error)
            return []
        }
    }

    const searchReportingLocationsByUnit = async (unit: any) => {
        try {
            const result = await axios.get(reportingLocationURI(), {
                params: {
                    reportingUnitExternalId: unit,
                },
            })
            setAdvancedSearchOptions((prev: any) => ({ ...prev, location: result.data.message }))
        } catch (error) {
            console.error(error)
            return []
        }
    }

    useEffect(() => {
        setUsers(filteredUsers)
        setLoading(false)
    }, [filteredUsers])

    useEffect(() => {
        const delayDebounceFn = setTimeout(() => {
            if (stringIsNotNullOrEmpty(usersInput)) {
                setIsLoadingSearch(true)
                setShowNoResultsMessage(false)
                const trimmedInput = usersInput.trim()
                axios.get(userSearchURI(trimmedInput, application.id)).then((result) => {
                    const { roles, users } = result.data.message
                    setRolesOptions(roles)
                    setUsersOptions(users)
                    setShowSuggestions(true)
                    setIsLoadingSearch(false)
                    if (roles.length === 0 && users.length === 0) {
                        setShowNoResultsMessage(true)
                    }
                })
            }
        }, 2000)

        return () => clearTimeout(delayDebounceFn)
    }, [usersInput])

    useEffect(() => {
        const delayDebounceFn = setTimeout(() => {
            if (stringIsNotNullOrEmpty(blockUsersInput)) {
                setIsLoadingBlockSearch(true)
                setShowNoResultsMessage(false)
                const trimmedInput = blockUsersInput.trim()
                axios.get(userSearchURI(trimmedInput, application.id)).then((result) => {
                    const { roles, users } = result.data.message
                    setRolesBlockOptions(roles)
                    setUsersBlockOptions(users)
                    setShowBlockSuggestions(true)
                    setIsLoadingBlockSearch(false)
                    if (roles.length === 0 && users.length === 0) {
                        setShowNoResultsMessage(true)
                    }
                })
            }
        }, 2000)

        return () => clearTimeout(delayDebounceFn)
    }, [blockUsersInput])

    useEffect(() => {
        setShowExternalSuggestions(true)
    }, [externalUsersInput])

    const handleAppendExternalUser = (value: string) => {
        const isValid = validateEmail(value)

        if (isValid) {
            externalUsersFieldArray.appendExternalUsers({ email: value })
            resetField('externalUsersInput')
            setExternalUserEmailisValid(false)
        } else {
            setExternalUserEmailisValid(true)
        }
    }

    const handleResetExternalUsersStates = () => {
        resetField('externalUsersInput', { defaultValue: '' })
        setShowExternalSuggestions(false)
        setShowNoResultsMessage(false)
    }
    const handleResetStates = () => {
        resetField('usersInput', { defaultValue: '' })
        setShowSuggestions(false)
        setShowNoResultsMessage(false)
    }

    const handleResetBlockStates = () => {
        resetField('blockUsersInput', { defaultValue: '' })
        setShowBlockSuggestions(false)
        setShowNoResultsMessageForBlockUsers(false)
    }

    const searchLabel = () => {
        if (isAllUsers) {
            return translate('app.templates.allUsers')
        } else {
            return translate('app.templates.selectedUsers')
        }
    }

    const handleCreateGroupClick = () => {
        console.log('Create Group button clicked!')
    }

    const hasError = (isNextStepOneClicked: boolean) => {
        const hasError =
            !isAllUsers &&
            isNextStepOneClicked &&
            Boolean(
                !(
                    watch('subscribedUsers')?.length > 0 ||
                    watch('subscribedRoles')?.length > 0 ||
                    watch('subscribedApplicationGroups')?.length > 0
                )
            )
        return hasError
    }

    return {
        location,
        users,
        loading,
        isAllUsers,
        isExternalUsers,
        externalUsersInput,
        usersInput,
        blockUsersInput,
        rolesOptions,
        usersOptions,
        showSuggestions,
        showExternalSuggestions,
        rolesBlockOptions,
        usersBlockOptions,
        showBlockSuggestions,
        isLoadingSearch,
        isLoadingBlockSearch,
        showNoResultsMessage,
        showNoResultsMessageForBlockUsers,
        externalUserEmailisValid,
        setShowSuggestions,
        setShowExternalSuggestions,
        setShowBlockSuggestions,
        setIsLoadingSearch,
        setIsLoadingBlockSearch,
        handleAppendExternalUser,
        handleResetStates,
        handleResetExternalUsersStates,
        handleResetBlockStates,
        searchLabel,
        handleCreateGroupClick,
        hasError,
        getFieldsOptions: {
            searchRolesBySite,
            searchTeamsBySite,
            searchUnitsBySite,
            searchReportingLocationsByUnit,
        },
    }
}
