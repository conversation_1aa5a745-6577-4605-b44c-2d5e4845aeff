import { useContext, useEffect } from 'react'
import { Cln<PERSON>readcrumb, ClnAlert } from '@celanese/ui-lib'
import { translate, TranslationContext, TranslationContextState } from '@celanese/celanese-sdk'
import { Box, Stepper, Step, StepLabel, Backdrop, CircularProgress } from '@mui/material'
import * as styles from './styles'
import useSaveTemplateLogic from './hooks/useSaveTemplateLogic'
import StepperButtons from './components/stepper-buttons'
import TemplateContent from './components/formSteps/step-content'
import TemplateRecipients from './components/formSteps/step-recipients'
import TemplateCustomization from './components/formSteps/step-customization'
import LostDataDialog from '@/common/components/LostDataDialog/lost-data-dialog'

const DevCustomizeTemplatesPage = () => {
    const allProperties = useSaveTemplateLogic()

    const {
        crumbs,
        steps,
        handleBackClick,
        handleSubmit,
        onSubmit,
        showModalLostData,
        setShowModalLostData,
        navigateBack,
        isLoading,
        backendError,
        setBackendError,
        stepValidation,
        activeStep,
        setActiveStep,
        isStepFailed,
        canChangeStep,
        isAdminLevel,
        formType,
        isEdition,
    } = allProperties

    const myTemplatesLogic = isAdminLevel ? 2 : 1

    function isValidJSON(str: string) {
        try {
            JSON.parse(str)
            return true
        } catch (e) {
            return false
        }
    }

    const { locale } = useContext<TranslationContextState>(TranslationContext)
    useEffect(() => {
        const checkLocalStorage = () => {
            const localeData = window.localStorage?.getItem('LocaleData')
            const translationData = window.localStorage?.getItem(
                `APP-NTFTranslationData${localeData}`,
            )
            if (!localeData || !translationData) {
                setTimeout(checkLocalStorage, 200)
            }
            else {
                document.title = translate('app.templates.customizeTemplate') + ' | Notifications Portal'
            }
        }
        checkLocalStorage()
    }, [locale])

    return (
        <>
            <Box sx={styles.container}>
                <ClnBreadcrumb crumbs={crumbs} />
                <form className="form">
                    <Box sx={styles.newFormContainer}>
                        <Stepper activeStep={activeStep} alternativeLabel>
                            {steps.map((label, index) => {
                                const labelProps: any = {}
                                if (isStepFailed(index)) {
                                    labelProps.error = true
                                }
                                return (
                                    <Step key={label} completed={isEdition}>
                                        <StepLabel {...labelProps}>{label}</StepLabel>
                                    </Step>
                                )
                            })}
                        </Stepper>
                        {activeStep === 0 && (
                            <TemplateContent {...allProperties} />
                        )}
                        {activeStep === 1 && isAdminLevel && (
                            <TemplateRecipients {...allProperties} />
                        )}
                        {activeStep === myTemplatesLogic && (
                            <TemplateCustomization {...allProperties} />
                        )}
                        <StepperButtons
                            disabled={formType === 'view'}
                            activeStep={activeStep}
                            setActiveStep={setActiveStep}
                            steps={steps}
                            handleCancel={handleBackClick}
                            handleSave={handleSubmit(onSubmit)}
                            stepValidation={stepValidation}
                            canChangeStep={canChangeStep}
                        />
                    </Box>
                </form>
                <LostDataDialog
                    showModalLostData={showModalLostData}
                    setShowModalLostData={setShowModalLostData}
                    navigateBack={navigateBack}
                />
            </Box>
            <Backdrop sx={{ zIndex: 10 }} open={isLoading}>
                <CircularProgress color="inherit" />
            </Backdrop>
            {backendError.length > 0 &&
                backendError.map((errorString) => {
                    const normalizedString = errorString.replace(/'/g, '"')

                    if (isValidJSON(normalizedString)) {
                        const arr = JSON.parse(normalizedString)

                        return arr.map((error: string) => (
                            <ClnAlert
                                key={error}
                                onClose={() => setBackendError([])}
                                position="secondary"
                                content={error}
                                open={true}
                                severity="error"
                            />
                        ))
                    } else {
                        return (
                            <ClnAlert
                                key={errorString}
                                onClose={() => setBackendError([])}
                                position="secondary"
                                content={errorString}
                                open={true}
                                severity="error"
                            />
                        )
                    }
                })}
        </>
    )
}

export default DevCustomizeTemplatesPage
