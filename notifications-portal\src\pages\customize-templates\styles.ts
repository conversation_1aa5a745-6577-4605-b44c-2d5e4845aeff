/* eslint-disable */

import { CSSObject } from '@emotion/react'
import { useTheme } from '@mui/material/styles'

export const container: CSSObject = {
    height: '100%',
    width: '100%',
    display: 'grid',
    gridTemplateColumns: '100%',
    gridTemplateRows: 'auto auto 1fr',
    gridGap: '1rem',
    overflow: 'hidden',

    '.form': {
        overflow: 'auto',
        backgroundColor: 'background.paper',
        border: '1px solid',
        borderColor: 'divider',
        borderRadius: '8px',
        padding: '1.5rem',
        height: '100%',

        'input[type="number"]::-webkit-inner-spin-button, input[type="number"]::-webkit-outer-spin-button': {
            WebkitAppearance: 'none',
            margin: '0',
        },
    },
}

export const title: CSSObject = {
    color: 'primary.main',
    fontWeight: 'bold',
    fontSize: '1.5rem',
    lineHeight: '32px',
}

export const formContainer: CSSObject = {
    display: 'flex',
    flexDirection: 'column',
    flexGrow: 1,
    gap: '1.5rem',
}

export const newFormContainer: CSSObject = {
    display: 'flex',
    flexDirection: 'column',
    flexGrow: 1,
    gap: '1rem',
}

export const templateName: CSSObject = {
    color: 'primary.main',
    fontWeight: 'bold',
    fontSize: '20px',
}

export const formRow: CSSObject = {
    display: 'flex',
    justifyContent: 'space-between',
    width: '100%',
    gap: '32px',
}

export const formRowNoSpace: CSSObject = {
    display: 'flex',
    width: '100%',
    gap: '32px',
}

export const formRowWithoutSpace: CSSObject = {
    display: 'flex',
    width: '100%',
    gap: '10px',
}

export const formRowAlignedRight: CSSObject = {
    display: 'flex',
    justifyContent: 'flex-end',
    width: '100%',
    gap: '10px',
}

export const inputContainer: CSSObject = {
    flex: 1,
    minWidth: 0,
    alignContent: 'center',
    '& div': {
        '& .Mui-focused': {
            color: 'primary.main',
            borderColor: 'primary.main',
            '& .MuiOutlinedInput-notchedOutline': {
                borderColor: 'primary.main',
            },
        },
    },
}

export const inputLabel: CSSObject = {
    fontSize: '16px',
    fontWeight: 'bold',
    color: 'primary.main',
}

export const select: CSSObject = {
    '& div': {
        display: 'flex',
        alignItems: 'center',
        gap: '8px',
    },
}

export const selectItem: CSSObject = {
    display: 'flex',
    alignItems: 'center',
    gap: '8px',
}

export const circleIcon: CSSObject = {
    fontSize: '16px',
}

export const addFieldButton: CSSObject = {
    borderRadius: '20px',
    textTransform: 'capitalize',
    minWidth: 0,
}

export const addFieldsContainer: CSSObject = {
    display: 'flex',
    gap: '8px',
    alignItems: 'center',
    flexWrap: 'wrap',
    marginTop: '16px',
    '& button': {
        padding: '2px 8px',
        color: 'primary.main',
        borderColor: 'primary.main',
        fontSize: '13px',
    },
}

export const messageVariables: CSSObject = {
    marginTop: '1rem',

    '.title': {
        display: 'flex',
        gap: '5px',

        '> .text': {
            marginBottom: '0.5rem',
            fontSize: '14px',
            color: 'text.primary',
        },
    },
}

export const secondaryTextField: CSSObject = {
    backgroundColor: 'grey[100]',
    color: 'text.secondary',
}

export const checkbox: CSSObject = {
    '& div': {
        display: 'flex',
        gap: '40px',
        padding: '2px 0px 5px 2px',
        '& div': {
            '& label': {
                marginRight: 0,
            },
        },
    },
}

export const customizationDescription: CSSObject = {
    marginTop: '16px',
    color: 'text.secondary',
}

export const inputTemplateNameField: CSSObject = {
    '& .MuiInputBase-root': {
        '.MuiInputBase-input': {
            padding: '1px 3px',
            fontSize: '24px',
            lineHeight: '1.25rem',
            color: 'primary.main',
            fontWeight: 'bold',
        },
    },
}

export const drawerDefault: CSSObject = {
    width: '430px !important',
    padding: '15px',
    '& .header div .MuiAvatar-root': {
        display: 'none',
    },
}

export const footer: CSSObject = {
    display: 'flex',
    justifyContent: 'space-between',
    width: '100%',
    position: 'fixed',
    bottom: 32,
    zIndex: 1000,
}

export const drawerFooter: CSSObject = {
    display: 'flex',
    justifyContent: 'flex-end',
    position: 'absolute',
    bottom: 0,
    width: '98%',
    padding: '16px',
    gap: '8px',
}

export const buttonRoundedWeekday: CSSObject = {
    borderRadius: '50%',
    width: 24,
    height: 24,
    minWidth: 0,
}

export const buttonRoundedWeekdayComponent: CSSObject = {
    borderRadius: '50%',
    width: 35,
    height: 35,
    minWidth: 0,
}

export const buttonRoundedMonth: CSSObject = {
    borderRadius: '20',
    width: 100,
    height: 24,
    minWidth: 20,
}

export const buttonRoundedMonthComponent: CSSObject = {
    borderRadius: '20px',
    width: 120,
    height: 24,
    minWidth: 'auto',
    textTransform: 'uppercase',
    fontWeight: 'bold',
    fontSize: '12px',
}

export const messageAlertDefault: CSSObject = {
    color: 'text.primary',
    '& svg': {
        fill: 'orange',
    },
}

export const stepperButtonsContainer: CSSObject = {
    display: 'flex',
    justifyContent: 'flex-end',
    gap: 1,

    '& > button': {
        margin: '0.25rem'
    }
}


export const drawerContainer: CSSObject = {
    width: '450px !important',
    '& > div:nth-of-type(3)': {
        height: '100%',
    },
    '& .header': {
        marginTop: '0px',
    },
}

export const drawerContentContainer: CSSObject = {
    gridTemplateRows: 'repeat(5, auto) 1fr auto',
    gap: '16px',
    backgroundColor: 'background.paper',
    border: '1px solid',
    borderColor: 'divider',
    borderRadius: '8px',
    padding: '1.5rem 1rem',
    marginTop: '15px',

    display: 'flex',
    flexDirection: 'column',
    height: '100%',


}

export const subHeader: CSSObject = {
    fontSize: '16px',
    fontWeight: 'bold',
    color: 'primary.main',
    alignItems: 'center',
    width: '100%',
    paddingLeft: '10px',
    display: 'flex',
}

export const expansibleTextContainer: CSSObject = {
    position: 'relative',
    height: '21px',
    marginBottom: '30px',
    '& p': {
        backgroundColor: 'grey.200',
        padding: '5px 10px 10px 10px',
        borderRadius: '4px',
        border: '1px solid',
        borderColor: 'divider',
    },
    '& p:hover': {
        backgroundColor: 'grey.200',
    },
}

export const sendToInputContainer: CSSObject = {
    display: 'flex',
    alignItems: 'center',
    position: 'relative',
    marginTop: '10px',
    width: '100%',
        '& .MuiFormHelperText-root': {
            width: '100px',
            alignSelf: 'flex-start'
            },
        '& .MuiInputLabel-root': {
            padding: '10px 0px 0px 9px'
            },
        '& .MuiTextField-root': {
            padding: '8px 8px 0px 8px'
            },
            
}

export const sendToInput: CSSObject = {
    '& input': {
        width: '1%',
        fontSize: '1rem'
    },
}

export const loadingIndicator: CSSObject = {
    position: 'absolute',
    right: '10px',
    width: '30px !important',
    height: '30px !important',
}

export const suggestionsContainter: CSSObject = {
    width: 'calc(100% - 16px)',
    position: 'absolute',
    left: '8px',
    backgroundColor: 'background.paper',
    border: '1px solid',
    borderColor: 'divider',
    borderRadius: '4px',
    zIndex: 9999,
    boxShadow: '0px 2px 4px rgba(0, 0, 0, 0.1)',
    '& .MuiButton-root':{
        fontSize: '16px'
        }
  }

export const noResultsMessage: CSSObject = {
    color: 'primary.main',
    padding: '5px',
    fontSize: '14px',
    fontWeight: '500',
}

export const searchSugestion: CSSObject = {
    padding: '8px 16px !important',
    width: '100%',
    justifyContent: 'start',
    textAlign: 'left',
    textTransform: 'none !important' as 'none',
    '&:hover': {
      backgroundColor: 'action.hover'
    }
  }

export const selectedUsersContainer: CSSObject = {
    display: 'flex',
    flexWrap: 'wrap',
    maxWidth: '100%',
    alignItems: 'center',
    gap: '4px',
    padding: '14px 0px 14px 0px'
}

export const circularButton: CSSObject = {
    backgroundColor: 'action.hover',
    borderRadius: '50px',
    textTransform: 'none !important' as 'none',
}

export const selectedChipContainer: CSSObject = {
    display: 'flex',
    alignItems: 'center',
    backgroundColor: 'action.hover',
    padding: '5px 8px',
    gap: '1px',
    borderRadius: '50px',
    maxWidth: 'calc(25vw - 84px)',
    '& .MuiButtonBase-root': {
        minWidth: 0,
        padding: 0,
    },
    ':hover': {
        backgroundColor: 'action.selected',
        cursor: 'pointer',
    },
}

export const buttonLabel: CSSObject = {
    textOverflow: 'ellipsis',
    whiteSpace: 'nowrap',
    overflow: 'hidden',
}

export const icon: CSSObject = {
    color: 'text.primary',
}

export const expandIcon: CSSObject = {
    ...icon,
    fontSize: '20px',
}

export const saveButton: CSSObject = {
    width: '100%',
}

export const externalUsersTitle: CSSObject = {
    margin: '14px 0'
}

export const invalidEmail: CSSObject = {
    color: 'error.dark'
}

export const fieldGroups: CSSObject = {
    border: '1px solid',
    borderColor: 'divider',
    padding: '1.25rem',
    borderRadius: '8px',

    '.group_title': {
        marginBottom: '1rem',
        fontSize: '16px',
        color: 'text.primary',
        fontWeight: '500',
    },

    '.group_fields': {
        display: 'grid',
        gridTemplateColumns: 'repeat(2, 1fr)',
        gap: '1.25rem',

        '&.three-columns': {
            gridTemplateColumns: 'repeat(3, 1fr)',
        },
    },
}

export const unsavedChanges: CSSObject = {
    fontSize: '14px',
    fontWeight: '500',
    color: 'text.primary',
}

export const duplicateModalContent: CSSObject = {
    display: 'flex',
    flexDirection: 'column',
    gap: '1rem',
}

export const customizationTemplateCard = (): CSSObject => {
    const theme = useTheme()
    return {
        border: `1px solid ${theme.palette.divider}`,
        backgroundColor: theme.palette.background.paper,
        borderRadius: theme.shape.borderRadius,
        boxShadow: theme.shadows[2],
        padding: '0px 4px 0px 4px',
        display: 'flex',
        flexDirection: 'column',
        gap: theme.spacing(2),
        minWidth: 300,
        height: 134,
        flexShrink: 0,
    }
}

export const tagAllowList: CSSObject = {
    height: '24px',
    padding: '10px',
    gap: '0px',
    borderRadius: '50px',
    backgroundColor: 'primary.100',
    color: 'primary.main',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    opacity: 1,
    fontSize: '13px'
}

export const tagsContainer: CSSObject = {
    display: 'inline-flex',
    gap: '8px',
    backgroundColor: 'background.default',
    width: '1,222px',
    height: '44px',
    padding: '8px 16px 8px 16px',
    borderRadius: '4px',
    border: '0px 0px 1px 0px',
    opacity: '0px',

}

export const createGroupButton: CSSObject = {
    display: 'flex',
    justifyContent: 'flex-end'
}
export const advancedSearchButton: CSSObject = {
    position: 'absolute',
    right: 90,
    height: '30px',
    padding: '4px 10px',
    gap: '8px',
    borderRadius: '4px',
    borderWidth: '1px',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center'
}

export const usersCount: CSSObject = {
    paddingLeft: '8px',
    marginTop: 'auto',
    alignSelf: 'flex-start',
    height: '16px',
}

export const advancedButton: CSSObject = {
    marginLeft: 'auto',
    height: '30px',
    padding: '4px 10px',
    gap: '8px',
    borderRadius: '4px',
    borderWidth: '1px',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    textTransform: 'uppercase',
}

export const boxDescription: CSSObject = {
    backgroundColor: '#f5f5f5',
    padding: '1rem',
    borderRadius: '4px',
}

export const buttons: CSSObject = {
    textTransform: 'uppercase'
}