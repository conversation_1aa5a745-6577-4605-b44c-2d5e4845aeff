import { CSSObject } from '@emotion/react'

export const cardContainer = (isSelected: boolean): CSSObject => {
    return {
        backgroundColor: isSelected ? 'primary.secondaryBackground' : 'unset',
        display: 'flex',
        width: '100%',
        gap: '15px',
        padding: '12px 20px'
    }
}

export const avatarContainer: CSSObject = {
    width: 'fit-content'
}

export const contentContainer: CSSObject = {
    flexGrow: '1',
    display: 'flex',
    flexDirection: 'column',
    gap: '5px'
}

export const contentHeaderContainer: CSSObject = {
    display: 'flex',
    width: '100%',
    justifyContent: 'space-between'
}

export const titleContainer: CSSObject = {
    display: 'flex',
    gap: '5px'
}

export const severityContainer: CSSObject = {
    display: 'flex',
    alignItems: 'center',
    gap: '5px'
}

export const boldText: CSSObject = { fontWeight: 'bold' }

export const severityCircle = (severity: string): CSSObject => {
    const circleColorDict: Record<string, string> = {
        'NTFSVT-LOW': 'success.light',
        'NTFSVT-MEDIUM': 'warning.light',
        'NTFSVT-HIGH': 'error.light'
    }
    
    return {
        fontSize: '16px',
        color: circleColorDict[severity]
    }
}

export const fontSize14: CSSObject = { fontSize: '14px' }

export const notificationTextContainer: CSSObject = {
    width: '100%',
    display: '-webkit-box',
    WebkitLineClamp: '2',
    fontSize: '16px',
    lineHeight: '22px',
    WebkitBoxOrient: 'vertical',
    overflow: 'hidden',
}

export const timeContainer: CSSObject = {
    width: '100%',
    display: 'flex',
    justifyContent: 'flex-end'
}

export const time: CSSObject = {
    fontSize: '14px',
    color: 'text.secondary'
}