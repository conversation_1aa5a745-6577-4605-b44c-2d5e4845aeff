import { NoTranslate, translate } from '@celanese/celanese-sdk'
import { Severity } from '@/common/models/severitiy'
import { ClnAvatar, ClnTooltip } from '@celanese/ui-lib'
import CircleIcon from '@mui/icons-material/Circle'
import { Box, IconButton, Typography } from '@mui/material'
import { Dispatch, SetStateAction } from 'react'
import * as styles from './notification-card.styles'

interface NotificationCardProps {
    externalId: string
    application: string
    applicationIconUrl?: string
    site: string
    severity: Severity
    text: string
    date: string
    updateDate?: string
    selectedNotification: string
    setSelectedNotification: Dispatch<SetStateAction<string>>
}

export default function NotificationCard({
    externalId,
    application,
    applicationIconUrl,
    site,
    severity,
    text,
    date,
    updateDate,
    selectedNotification,
    setSelectedNotification,
}: NotificationCardProps) {
    const isSelected = externalId === selectedNotification
    const displayText = text.includes('#htmlmark') ? translate('app.notifications.chats.htmlContentNotAvailable') : text

    return (
        <Box sx={styles.cardContainer(isSelected)} onClick={() => setSelectedNotification(externalId)}>
            <Box sx={styles.avatarContainer}>
                <NoTranslate>
                    <ClnTooltip arrow={true} placement="bottom" title={application}>
                        <IconButton sx={{ padding: 0 }}>
                            <ClnAvatar
                                alt={application}
                                src={applicationIconUrl}
                                sx={{
                                    color: 'primary.contrastText',
                                    backgroundColor: 'primary.main',
                                }}
                            />
                        </IconButton>
                    </ClnTooltip>
                </NoTranslate>
            </Box>
            <Box sx={styles.contentContainer}>
                <Box sx={styles.contentHeaderContainer}>
                    <Box sx={styles.titleContainer}>
                        <NoTranslate>
                            <Typography sx={styles.boldText}>{application}</Typography>
                        </NoTranslate>
                        <NoTranslate>
                            <Typography>{site != '' ? `(${site})` : ''}</Typography>
                        </NoTranslate>
                    </Box>
                    <Box sx={styles.severityContainer}>
                        <CircleIcon sx={styles.severityCircle(severity.externalId)} />
                        <Typography sx={styles.fontSize14}>{severity.description}</Typography>
                    </Box>
                </Box>
                <div dangerouslySetInnerHTML={{ __html: displayText }}></div>
                <Box sx={styles.timeContainer}>
                    <Typography sx={styles.time}>
                        {updateDate
                            ? `${translate('app.notifications.smartFeed.timeSeries.details.updatedAt')}: ${updateDate}`
                            : date}
                    </Typography>
                </Box>
            </Box>
        </Box>
    )
}
