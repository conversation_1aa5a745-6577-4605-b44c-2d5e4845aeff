import { CSSObject } from '@emotion/react'

export const mainContainer: CSSObject = {
    width: '100%',
    height: '100%',
    border: '1px solid',
    borderColor: 'divider',
    backgroundColor: 'background.paper',
    borderRadius: '15px',
    padding: '1.5rem',
    display: 'flex',
    flexDirection: 'column',
    gap: '15px',
    position: 'relative',
}

export const headerContainer: CSSObject = {
    height: '60px',
    display: 'flex',
    flexDirection: 'column',
    gap: '10px',
    '& div': {
        display: 'flex',
        justifyContent: 'space-between',
    },
    '& > p': {
        fontSize: '12px',
        color: 'text.secondary'
    }
}

export const appAndSiteContainer = {
    display: 'flex',
    alignItems: 'center',
    gap: '5px',
    '& .MuiAvatar-root': {
        backgroundColor: 'primary.main',
    },
}

export const appAndSite = { color: 'primary.main', fontSize: '20px' }

export const severityContainer = {
    display: 'flex',
    alignItems: 'center',
    gap: '5px',
}

export const severityCircle = (severity: string): CSSObject => {
    const circleColorDict: Record<string, string> = {
        'NTFSVT-LOW': 'success.light',
        'NTFSVT-MEDIUM': 'warning.light',
        'NTFSVT-HIGH': 'error.light'
    }
    
    return {
        fontSize: '16px',
        color: circleColorDict[severity]
    }
}

export const fontSize14: CSSObject = { fontSize: '14px' }

export const fontSize24: CSSObject = { fontSize: '24px' }

export const messageContainer: CSSObject = {
    display: 'flex',
    flexDirection: 'column',
    gap: '5px',
    height: '40%',
    overflow: 'auto',
}

export const notificationType: CSSObject = {
    fontWeight: 'bold',
    fontSize: '16px'
}

export const commentsHeader: CSSObject = {
    height: '48px',
    fontSize: '15px',
    color: 'text.secondary',
    display: 'flex',
    alignItems: 'center',
    gap: '5px',
    borderTop: '1px solid',
    borderBottom: '1px solid',
    borderColor: 'divider',
    padding: '5px',
    marginTop: 'auto'
}

export const chatContainer: CSSObject = {
    display: 'flex',
    flexDirection: 'column',
    gap: '5px',
    maxHeight: 'calc(60% - 198px)',
    overflow: 'auto',
    '& .MuiAvatar-root': {
        backgroundColor: 'primary.main',
    },
}

export const chatMessageRow: CSSObject = {
    display: 'flex',
    gap: '10px',
    alignItems: 'flex-start',
    marginRight: '5px'
}

export const avatar = (isCurrentUser: boolean): CSSObject => {
    return {
        marginTop: '10px',
        order: isCurrentUser ? 1 : 0
    }
}

export const chatBubble: CSSObject = {
    backgroundColor: 'grey[100]',
    border: '1px solid',
    borderColor: 'divider',
    width: '100%',
    borderRadius: '8px',
    padding: '10px',
    display: 'flex',
    justifyContent: 'space-between'
}

export const dateAndDeleteContainer: CSSObject = {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'end',
    justifyContent: 'flex-end',
    minHeight: '57px',
}

export const deleteButton: CSSObject = {
    color: 'text.secondary',
    fontSize: '18px'
}

export const chatMessageDate: CSSObject = {
    textAlign: 'right',
    fontSize: '0.8em',
    marginTop: '10px',
    color: 'text.secondary',
}

export const footer: CSSObject = {
    display: 'flex',
    gap: '10px',
    justifyContent: 'space-between',
    width: '100%',
    zIndex: 2,
    alignItems: 'flex-start',
    padding: '0px',
    '& .MuiAvatar-root': {
        backgroundColor: 'primary.main',
    },
}

export const textContainer: CSSObject = {
    display: 'flex',
    flexDirection: 'row',
    padding: '5px',
    alignItems: 'flex-end',
    width: '100%',
    border: '1px solid',
    borderColor: 'divider',
    backgroundColor: 'grey[100]',
    borderRadius: '8px',
}

export const loading: CSSObject = {
    zIndex: 5,
    position: 'absolute',
    borderRadius: '12px'
}

export const timeSeriesDrawer: CSSObject = {
    width: '700px !important',
    height: '100%',
    '.MuiBox-root:last-of-type': {
        height: '100%'
    }
}