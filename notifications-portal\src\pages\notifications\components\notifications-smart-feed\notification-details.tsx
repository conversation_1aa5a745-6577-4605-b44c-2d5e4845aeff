import { HeaderNavbarContextParams } from '@/common/contexts/HeaderContex'
import { UserContextParams } from '@/common/contexts/UserContext'

import {
    useDeleteChatMessageRequest,
    useGetChatMessagesRequest,
    usePosChattNotificationEvent,
    usePostChatMessageRequest,
} from '@/common/hooks/useChatMessagesRequest'
import { NtfSmartFeedContextParams } from '@/common/contexts/NtfSmartFeedContext'
import { ChatMessages } from '@/common/models/chatMessages'
import { ClnAvatar, ClnBaseIcon, ClnButton, ClnDrawer, ClnTextField, ClnTooltip } from '@celanese/ui-lib'
import CircleIcon from '@mui/icons-material/Circle'
import { Backdrop, Box, CircularProgress, IconButton, Typography } from '@mui/material'
import dayjs from 'dayjs'
import customParseFormat from 'dayjs/plugin/customParseFormat'
import { useEffect, useRef, useState } from 'react'
import * as styles from './notification-details.styles'
import TimeSeriesDrawer from './time-series-drawer/time-series-drawer'
import { NoTranslate, translate } from '@celanese/celanese-sdk'

interface NotificationDetailsProps {
    selectedNotification: string
}

export default function NotificationDetails({ selectedNotification }: NotificationDetailsProps) {
    dayjs.extend(customParseFormat)

    const [comment, setComment] = useState('')
    const [chatMessages, setChatMessages] = useState({} as ChatMessages)
    const { getChatMessages, isLoading: loadingGet } = useGetChatMessagesRequest(selectedNotification)
    const { postChatMessage, isLoading: loadingPost } = usePostChatMessageRequest(selectedNotification)
    const { deleteChatMessage, isLoading: loadingDelete } = useDeleteChatMessageRequest()
    const { postChatNotificationEvent, isLoading: loadingEvent } = usePosChattNotificationEvent()
    const { refetchSmartFeed } = NtfSmartFeedContextParams()

    const { userName } = HeaderNavbarContextParams()
    const { handleGetUsername } = UserContextParams()
    const currentUser = handleGetUsername()

    const [selectedTimeSeries, setSelectedTimeSeries] = useState<string | undefined>(undefined)

    const handleTimeSeriesClick = (timeSeriesCode: string) => {
        setSelectedTimeSeries(timeSeriesCode)
    }

    const textRef = useRef<HTMLDivElement>(null)

    useEffect(() => {
        const desiredElement = textRef.current?.getElementsByClassName('clickable-tag')[0]
        const timeSeriesCode = desiredElement?.getAttribute('data-eid') ?? ''
        desiredElement?.addEventListener('click', () => handleTimeSeriesClick(timeSeriesCode))
    }, [textRef.current, chatMessages?.text])

    useEffect(() => {
        if (selectedNotification != '') {
            getChatMessages().then((result) => {
                setChatMessages(result)
            })
        }
    }, [getChatMessages, selectedNotification])

    const handleSendComment = () => {
        postChatNotificationEvent(chatMessages.rawEvent).then(() => {
            setTimeout(() => refetchSmartFeed(), 60000)
        })
        postChatMessage(comment).then(() => {
            getChatMessages().then((result) => setChatMessages(result))
        })
        setComment('')
    }

    const handleDeleteComment = (externalId: string) => {
        deleteChatMessage(externalId).then(() => {
            getChatMessages().then((result) => setChatMessages(result))
        })
    }

    return (
        <Box sx={styles.mainContainer}>
            {chatMessages && Object.keys(chatMessages).length > 0 && (
                <>
                    <Box sx={styles.headerContainer}>
                        <Box>
                            <Box sx={styles.appAndSiteContainer}>
                                <NoTranslate>
                                    <ClnTooltip arrow={true} placement="bottom" title={chatMessages.application}>
                                        <IconButton sx={{ padding: 0 }}>
                                            <ClnAvatar
                                                alt={chatMessages.application}
                                                sx={{
                                                    color: 'primary.contrastText',
                                                    backgroundColor: 'primary.main',
                                                }}
                                            />
                                        </IconButton>
                                    </ClnTooltip>
                                </NoTranslate>
                                <NoTranslate>
                                    <Typography sx={styles.appAndSite}>
                                        {chatMessages.application}
                                        {chatMessages.site ? ` (${chatMessages.site})` : ''}
                                    </Typography>
                                </NoTranslate>
                            </Box>
                            <Box sx={styles.severityContainer}>
                                <CircleIcon sx={styles.severityCircle(chatMessages.severityExternalId)} />
                                <Typography sx={styles.fontSize14}>{chatMessages.severityDescription}</Typography>
                            </Box>
                        </Box>
                        <Typography>{dayjs(chatMessages.createdTime).format('MM/DD/YYYY hh:mm A')}</Typography>
                    </Box>

                    <Box sx={styles.messageContainer}>
                        <Typography sx={styles.notificationType}>{chatMessages?.notificationType}</Typography>
                        <div
                            ref={textRef}
                            dangerouslySetInnerHTML={{ __html: chatMessages.text.replaceAll('#htmlmark', '') }}
                        ></div>
                    </Box>

                    <Typography sx={styles.commentsHeader}>
                        <ClnBaseIcon className="cln-ico-message" sx={styles.fontSize24} />
                        {chatMessages.comments.length} {translate('app.notifications.chats.comments')}
                    </Typography>

                    <Box sx={styles.chatContainer}>
                        {chatMessages.comments.map((message, index) => (
                            <Box key={index} sx={styles.chatMessageRow}>
                                <Box sx={styles.avatar(message.user.email === currentUser)}>
                                    <NoTranslate>
                                        <ClnTooltip
                                            arrow={true}
                                            placement="bottom"
                                            title={message.user.displayName ?? ''}
                                        >
                                            <IconButton sx={{ padding: 0 }}>
                                            <NoTranslate>
                                                <ClnAvatar
                                                    alt={userName}
                                                    sx={{
                                                        color: 'primary.contrastText',
                                                        backgroundColor: 'primary.main',
                                                    }}
                                                />
                                            </NoTranslate>
                                            </IconButton>
                                        </ClnTooltip>
                                    </NoTranslate>
                                </Box>
                                <Box sx={styles.chatBubble}>
                                    <NoTranslate>
                                        <Typography>{message.comment}</Typography>
                                    </NoTranslate>
                                    <Box sx={styles.dateAndDeleteContainer}>
                                        {message.user.email === currentUser && (
                                            <IconButton
                                                sx={{ padding: 0 }}
                                                onClick={() => handleDeleteComment(message.externalId)}
                                            >
                                                <ClnBaseIcon className="cln-ico-delete" sx={styles.deleteButton} />
                                            </IconButton>
                                        )}
                                        <Typography sx={styles.chatMessageDate}>
                                            {dayjs(message.createdTime).format('MM/DD/YYYY hh:mm A')}
                                        </Typography>
                                    </Box>
                                </Box>
                            </Box>
                        ))}
                    </Box>

                    <Box sx={styles.footer}>
                        <NoTranslate>
                            <ClnAvatar
                                alt={userName}
                                sx={{
                                    marginTop: '10px',
                                    color: 'primary.contrastText',
                                    backgroundColor: 'primary.main',
                                }}
                            />
                        </NoTranslate>
                        <Box sx={styles.textContainer}>
                            <ClnTextField
                                helperText=""
                                variant="standard"
                                label=""
                                fullWidth
                                multiline
                                rows={3}
                                spellCheck={false}
                                InputProps={{
                                    disableUnderline: true,
                                }}
                                onChange={(e) => setComment(e.target.value)}
                                value={comment}
                                placeholder={translate('app.notifications.chats.writeYourComment')}
                            />
                            <ClnButton label="SEND" onClick={() => handleSendComment()} variant="contained" />
                        </Box>
                    </Box>
                </>
            )}

            <ClnDrawer
                open={selectedTimeSeries != undefined}
                content={<TimeSeriesDrawer selectedTimeSeries={selectedTimeSeries} />}
                caption="Tag Information"
                overlineMeta={selectedTimeSeries}
                closeDrawer={() => setSelectedTimeSeries(undefined)}
                sxProps={styles.timeSeriesDrawer}
            />

            <Backdrop open={loadingGet || loadingPost || loadingDelete || loadingEvent} sx={styles.loading}>
                <CircularProgress color="inherit" />
            </Backdrop>
        </Box>
    )
}
