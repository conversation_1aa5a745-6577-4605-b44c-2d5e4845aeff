import { CSSObject } from '@emotion/react'

export const filterContainer: CSSObject = {
    minWidth: '220px',
    padding: '20px 10px 10px 10px',
    display: 'flex',
    gap: '10px',
    flexDirection: 'column',
}

export const buttonsContainer: CSSObject = {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'flex-start'
}

export const button = (selected: boolean): CSSObject => {
    return { '& button': {color: selected ? 'primary.main' : 'text.secondary'} }
}
