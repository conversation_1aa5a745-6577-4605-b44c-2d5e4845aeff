import { translate } from '@celanese/celanese-sdk'
import { ClnButton } from '@celanese/ui-lib'
import { Order } from '@celanese/ui-lib'
import { Box, Divider, Menu, Typography } from '@mui/material'
import { Dispatch, SetStateAction } from 'react'
import * as styles from './notification-smart-feed-sort.styles'

interface NotificationsSmartFeedSortProps {
    anchorEl: HTMLElement | null
    handleCloseMenu: () => void
    order: Order
    setOrder: Dispatch<SetStateAction<Order>>
    orderBy: string
    setOrderBy: Dispatch<SetStateAction<string>>
    setSelectedOrderName: Dispatch<SetStateAction<string>>
}

export default function NotificationsSmartFeedSort({
    anchorEl,
    handleCloseMenu,
    order,
    setOrder,
    orderBy,
    setOrderBy,
    setSelectedOrderName,
}: NotificationsSmartFeedSortProps) {
    

    const open = Boolean(anchorEl)

    const handleOptionClick = (order: Order, orderBy: string, name: string) => {
        setOrder(order)
        setOrderBy(orderBy)
        setSelectedOrderName(name)
        handleCloseMenu()
    }

    return (
        <Menu
            anchorEl={anchorEl}
            open={open}
            onClose={handleCloseMenu}
            anchorOrigin={{
                vertical: 'bottom',
                horizontal: 'left',
            }}
            transformOrigin={{
                vertical: 'top',
                horizontal: 'left',
            }}
        >
            <Box sx={styles.filterContainer}>
                <Typography>{translate('app.notifications.smartFeed.sort.sortBy')}</Typography>
                <Divider />
                <Box sx={styles.buttonsContainer}>
                    <Box sx={styles.button(order == 'desc' && orderBy == 'date')}>
                        <ClnButton
                            variant="text"
                            label={translate('app.notifications.smartFeed.sort.mostRecent')}
                            onClick={() =>
                                handleOptionClick('desc', 'date', translate('app.notifications.smartFeed.sort.mostRecent'))
                            }
                        />
                    </Box>
                    <Box sx={styles.button(order == 'asc' && orderBy == 'severityLevel')}>
                        <ClnButton
                            variant="text"
                            label={translate('app.notifications.smartFeed.sort.severityAsc')}
                            onClick={() =>
                                handleOptionClick(
                                    'asc',
                                    'severityLevel',
                                    translate('app.notifications.smartFeed.sort.severityAsc')
                                )
                            }
                        />
                    </Box>
                    <Box sx={styles.button(order == 'desc' && orderBy == 'severityLevel')}>
                        <ClnButton
                            variant="text"
                            label={translate('app.notifications.smartFeed.sort.severityDesc')}
                            onClick={() => {
                                handleOptionClick(
                                    'desc',
                                    'severityLevel',
                                    translate('app.notifications.smartFeed.sort.severityDesc')
                                )
                            }}
                        />
                    </Box>
                </Box>
            </Box>
        </Menu>
    )
}
