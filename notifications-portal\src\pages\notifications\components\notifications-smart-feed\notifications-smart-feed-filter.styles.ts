import { CSSObject } from '@emotion/react'

export const filterContainer: CSSObject = {
    minWidth: '220px',
    padding: '20px 10px 10px 10px',
    display: 'flex',
    flexDirection: 'column',
    gap: '25px',
    '& .MuiInputLabel-outlined': {
        top: '-9px',
        fontSize: '14px'
    }
}

export const buttonContainer: CSSObject = {
    textAlign: 'center',
    display: 'flex',
    gap: '5px',
    justifyContent: 'center',
    width: '100%',
    '& div': {
        width: '100%',
        '& .MuiButtonBase-root': {
            width: '100%'
        }
    },
}

export const select: CSSObject = {
    '& .MuiSelect-select': {
        padding: '5px'
    },
}
