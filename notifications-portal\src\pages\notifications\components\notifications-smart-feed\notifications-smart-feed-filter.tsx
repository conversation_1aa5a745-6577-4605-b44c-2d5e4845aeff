import FilterCheckboxSection from '@/common/components/FilterCheckboxSection/filter-checkbox-section'
import { useSeverityRequest } from '@/common/hooks/useSeverityRequest'
import { PaginatedNotifications } from '@/common/models/paginatedNotifications'
import { Cln<PERSON>utton, ClnSelect } from '@celanese/ui-lib'
import { CheckboxItem } from '@celanese/ui-lib'
import { SelectItem } from '@celanese/ui-lib'
import { Box, Menu } from '@mui/material'
import { useState } from 'react'
import useNotificationsSmartFeedLogic from '../../hooks/useNotificationsSmartFeedLogic'
import * as styles from './notifications-smart-feed-filter.styles'
import { NoTranslate, translate } from '@celanese/celanese-sdk'

interface NotificationsSmartFeedFilterProps {
    anchorEl: HTMLElement | null
    handleCloseMenu: () => void
    paginatedSmartFeed: PaginatedNotifications
}

function getSelectOptions(object: Record<string, string>, translate: boolean = true) {
    const selectOptions: any[] = []
    if (translate) {
        for (const property in object) {
            selectOptions.push({ value: property, label: object[property] })
        }
    } else {
        for (const property in object) {
            selectOptions.push({ value: property, label: <NoTranslate>{object[property]}</NoTranslate> })
        }
    }
    return selectOptions
}

export default function NotificationsSmartFeedFilter({
    anchorEl,
    handleCloseMenu,
    paginatedSmartFeed,
}: NotificationsSmartFeedFilterProps) {
    const { setFilterBySeverities, setFilterByApplication, setFilterByNotificationType } =
        useNotificationsSmartFeedLogic()

    const { data: severities } = useSeverityRequest(true, ['severity'])
    const [selectedSeverities, setSelectedSeverities] = useState<CheckboxItem[]>([])

    const [selectedApplication, setSelectedApplication] = useState<SelectItem | any | undefined>(undefined)
    const [selectedNotificationType, setSelectedNotificationType] = useState<SelectItem | undefined>(undefined)

    const applicationOptions = getSelectOptions(paginatedSmartFeed.applications, false)
    const notificationTypeOptions = getSelectOptions(paginatedSmartFeed.notificationTypes)

    const translations: Record<string, string> = {
        'LOW': translate('app.notifications.filter.severity.low'),
        'MEDIUM': translate('app.notifications.filter.severity.medium'),
        'HIGH': translate('app.notifications.filter.severity.high')
    }

    const mappedSeverities: CheckboxItem[] =
        severities?.map((severity) => {
            return {
                label: translations[severity.name] || severity.description,
                value: severity.externalId,
            }
        }) || []

    const handleApplyFilter = () => {
        const severitiesIds = selectedSeverities.map((severity) => severity.value as string)

        setFilterBySeverities(severitiesIds)
        setFilterByApplication(selectedApplication ? selectedApplication.value : '')
        setFilterByNotificationType(selectedNotificationType ? selectedNotificationType.value : '')
        handleCloseMenu()
    }

    const resetFilters = () => {
        setSelectedSeverities([])
        setSelectedApplication(undefined)
        setSelectedNotificationType(undefined)

        setFilterBySeverities([])
        setFilterByApplication(undefined)
        setFilterByNotificationType(undefined)

        handleCloseMenu()
    }

    

    const open = Boolean(anchorEl)

    return (
        <Menu
            anchorEl={anchorEl}
            open={open}
            onClose={handleCloseMenu}
            anchorOrigin={{
                vertical: 'bottom',
                horizontal: 'left',
            }}
            transformOrigin={{
                vertical: 'top',
                horizontal: 'left',
            }}
        >
            <Box sx={styles.filterContainer}>
                <NoTranslate>
                    <ClnSelect
                        options={applicationOptions}
                        label={translate('app.notifications.application')}
                        fullWidth
                        sx={styles.select}
                        value={selectedApplication}
                        onChange={(e) => setSelectedApplication(e as SelectItem)}
                        variant="outlined"
                    />
                </NoTranslate>

                <ClnSelect
                    options={notificationTypeOptions}
                    label={translate('app.notifications.notificationType')}
                    fullWidth
                    sx={styles.select}
                    value={selectedNotificationType}
                    onChange={(e) => setSelectedNotificationType(e as SelectItem)}
                    variant="outlined"
                />

                <FilterCheckboxSection
                    label={translate('app.templates.severity')}
                    items={mappedSeverities}
                    value={selectedSeverities}
                    onChange={(severities) => setSelectedSeverities(severities)}
                />

                <Box sx={styles.buttonContainer}>
                    <ClnButton variant={'outlined'} label={translate('app.common.reset')} onClick={resetFilters} />
                    <ClnButton variant={'contained'} label={translate('app.common.apply')} onClick={handleApplyFilter} />
                </Box>
            </Box>
        </Menu>
    )
}
