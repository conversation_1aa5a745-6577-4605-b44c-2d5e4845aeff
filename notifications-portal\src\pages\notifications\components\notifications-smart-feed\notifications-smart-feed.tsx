import MessageContainer from '@/common/components/MessageContainer/message-container'
import { NoTranslate, translate } from '@celanese/celanese-sdk'
import { ClnBaseIcon, ClnTable } from '@celanese/ui-lib'
import { ActionButton } from '@celanese/ui-lib'
import WarningIcon from '@mui/icons-material/Warning'
import { Backdrop, Box, CircularProgress } from '@mui/material'
import { useEffect, useRef } from 'react'
import useNotificationsSmartFeedLogic from '../../hooks/useNotificationsSmartFeedLogic'
import NotificationDetails from './notification-details'
import NotificationsSmartFeedSort from './notification-smart-feed-sort'
import NotificationsSmartFeedFilter from './notifications-smart-feed-filter'
import * as styles from './notifications-smart-feedstyles'

export default function NotificationsSmartFeed() {
    const {
        paginatedSmartFeed,
        rows,
        headCells,
        handleChangePage,
        handleChangeRowsPerPage,
        rowsPerPage,
        page,
        setPage,
        isLoading,
        selectedNotification,
        setSelectedNotification,
        setSearch,
        filterAnchorEl,
        handleOpenFilter,
        handleCloseFilter,
        sortAnchorEl,
        handleOpenSort,
        handleCloseSort,
        order,
        setOrder,
        orderBy,
        setOrderBy,
        selectedOrderName,
        setSelectedOrderName,
    } = useNotificationsSmartFeedLogic()

    const divRef = useRef<HTMLDivElement>(null)

    const handleScrollToTop = () => {
        if (divRef.current) {
            divRef.current.children[0].scrollTo({
                top: 0,
                behavior: 'smooth',
            })
        }
    }

    

    const buttons: ActionButton[] = [
        {
            label: 'Filters',
            variant: 'outlined',
            icon: <ClnBaseIcon className="cln-ico-filter" />,
            sxProps: {
                ml: '5px',
            },
            onClick: handleOpenFilter,
        },
        {
            label: `Sort By: ${selectedOrderName}`,
            variant: 'outlined',
            sxProps: {
                ml: '1.25rem',
            },
            onClick: handleOpenSort,
        },
    ]

    enum TableTranslateKey {
        Search = 'Search',
    }

    const translatedLabels = new Map<TableTranslateKey, string>()
    translatedLabels.set(TableTranslateKey.Search, translate('app.common.search'))

    useEffect(() => {
        if (paginatedSmartFeed && paginatedSmartFeed.totalItems > 0) {
            setSelectedNotification(paginatedSmartFeed.notifications[0].externalId)
        } else if (
            (!paginatedSmartFeed || paginatedSmartFeed.totalItems < 1) &&
            selectedNotification != 'None' &&
            selectedNotification != ''
        ) {
            setSelectedNotification('None')
        }
    }, [paginatedSmartFeed])

    return (
        <>
            <Box sx={styles.layoutContainer} ref={divRef}>
                {paginatedSmartFeed && (
                    <>
                        <Box sx={styles.smartTable}>
                            <ClnTable
                                isSelect={false}
                                isCollapsible={false}
                                toolbar={false}
                                searchBox={true}
                                filter={false}
                                leftButton={false}
                                rightButton={false}
                                rowsPerPage={[10, 20, 50]}
                                sxPropsTableSize={{ height: '100%' }}
                                sxProps={styles.table}
                                rows={rows}
                                headCells={headCells}
                                page={page}
                                handleChangePage={(e, p) => {
                                    handleChangePage(e, p)
                                    handleScrollToTop()
                                }}
                                rowPageValue={rowsPerPage}
                                handleChangeRowsPerPage={(e) => {
                                    handleChangeRowsPerPage(e)
                                    handleScrollToTop()
                                }}
                                totalPages={paginatedSmartFeed.totalPages}
                                totalItems={paginatedSmartFeed.totalItems}
                                visibleRows={rows}
                                order="asc"
                                orderBy=""
                                disableChips={true}
                                handleRequestSort={() => { }}
                                onKeyDownSearch={(_, property: string) => {
                                    setSearch(property)
                                    if(property.length > 0){
                                        setPage(0)
                                    }
                                }}
                                buttons={buttons}
                                translatedLabels={translatedLabels}
                            />
                            {paginatedSmartFeed.totalItems == 0 && (
                                <NoTranslate><MessageContainer
                                    messages={[
                                        {
                                            icon: <WarningIcon />,
                                            text: translate('app.notifications.smartFeed.noNotificationsToday'),
                                        },
                                    ]}
                                    sx={styles.zeroNotifications}
                                /></NoTranslate>
                            )}
                        </Box>
                        <NotificationDetails selectedNotification={selectedNotification} />
                        <NotificationsSmartFeedFilter
                            anchorEl={filterAnchorEl}
                            handleCloseMenu={handleCloseFilter}
                            paginatedSmartFeed={paginatedSmartFeed}
                        />
                        <NotificationsSmartFeedSort
                            anchorEl={sortAnchorEl}
                            handleCloseMenu={handleCloseSort}
                            order={order}
                            orderBy={orderBy}
                            setOrder={setOrder}
                            setOrderBy={setOrderBy}
                            setSelectedOrderName={setSelectedOrderName}
                        />
                    </>
                )}
            </Box>
            <Backdrop open={isLoading} sx={{ zIndex: 5 }}>
                <CircularProgress color="inherit" />
            </Backdrop>
        </>
    )
}
