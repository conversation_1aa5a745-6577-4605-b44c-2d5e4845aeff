import { CSSObject } from '@emotion/react'

export const layoutContainer: CSSObject = {
    display: 'grid',
    gridTemplateColumns: '55% auto',
    gridTemplateRows: '100%',
    gridGap: '1rem',
    borderColor: 'divider',
    borderRadius: '15px',
    height: '100%',
    overflow: 'hidden',
}

export const table: CSSObject = {
    height: '100%',
    width: '100%',
    overflow: 'hidden',
    display: 'grid',
    gridTemplateColumns: '100%',
    gridTemplateRows: '40px 0 auto',

    '& .MuiPaper-elevation': {
        height: 'calc(100% - 1rem)',
        marginTop: '1rem',
        display: 'flex',
        flexDirection: 'column',
        overflowY: 'auto',
        boxShadow: 'none'
    }
}

export const zeroNotifications: CSSObject =  {
    position: 'absolute',
    top: '50%',
    left: '0',
    margin: '0 3rem',
    width: 'calc(100% - 6rem)',
    '& svg': {
        fill: 'orange',
    },
}

export const smartTable: CSSObject = {
    border: '1px solid',
    borderColor: 'divider',
    borderRadius: '15px',
    padding: '1.5rem',
    backgroundColor: 'background.paper',
    position: 'relative'
}