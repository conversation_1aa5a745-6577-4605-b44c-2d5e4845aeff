import { CSSObject } from '@emotion/react'

export const container = (isEven: boolean): CSSObject => ({
    display: 'flex',
    justifyContent: 'space-between',
    padding: '10px',
    backgroundColor: isEven ? 'primary.secondaryBackground': 'white',
    borderTop: '1px solid',
    borderBottom: '1px solind',
    borderColor: 'divider'
})

export const label: CSSObject = {
    textAlign: 'left',
    fontWeight: 600
}

export const value: CSSObject = {
    textAlign: 'right'
}