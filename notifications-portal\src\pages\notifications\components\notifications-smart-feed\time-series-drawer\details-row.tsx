import OpenInNewIcon from '@mui/icons-material/OpenInNew'
import { Box, Link, Typography } from '@mui/material'
import * as styles from './details-row.styles'

interface DetailsRowProps {
    label: string
    isEven: boolean
    link?: string
    value?: string | number | boolean
}

export default function DetailsRow({ label, value, isEven, link }: DetailsRowProps) {
    return (
        value ? (
            <Box sx={styles.container(isEven)}>
                <Typography sx={styles.label}>{label}</Typography>
                {link ? (
                    <Link target="_blank" href={`${link}`} sx={styles.value}>
                        {value}
                        <OpenInNewIcon sx={{ fontSize: '16px', verticalAlign: 'middle' }} />
                    </Link>
                ) : (
                    <Typography sx={styles.value}>{value}</Typography>
                )}
            </Box>
        ) :
        <></>
    )
}
