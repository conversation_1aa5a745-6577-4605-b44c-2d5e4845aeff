import { translate } from '@celanese/celanese-sdk'
import { TimeSeries } from '@/common/models/timeSeries'
import OpenInNewIcon from '@mui/icons-material/OpenInNew'
import { Box, Link } from '@mui/material'
import DetailsRow from './details-row'
import * as styles from './details-tab.styles'

interface DetailsTabProps {
    timeSeries: TimeSeries
}

export default function DetailsTab({ timeSeries }: DetailsTabProps) {
    

    return (
        <Box sx={styles.container}>
            <DetailsRow
                label={translate('app.notifications.smartFeed.timeSeries.details.name')}
                value={timeSeries.name}
                isEven={false}
            />
            <DetailsRow
                label={translate('app.notifications.smartFeed.timeSeries.details.description')}
                value={timeSeries.description}
                isEven={true}
            />
            <DetailsRow
                label={translate('app.notifications.smartFeed.timeSeries.details.unit')}
                value={timeSeries.units}
                isEven={false}
            />
            <DetailsRow
                label={translate('app.notifications.smartFeed.timeSeries.details.id')}
                value={timeSeries.id}
                isEven={true}
                link={timeSeries.urlTimeSeriesDetails}
            />
            <DetailsRow
                label={translate('app.notifications.smartFeed.timeSeries.details.externalId')}
                value={timeSeries.externalId}
                isEven={false}
            />
            <DetailsRow
                label={translate('app.notifications.smartFeed.timeSeries.details.isString')}
                value={timeSeries.isString ?
                    translate('app.notifications.smartFeed.timeSeries.details.isTrue') :
                    translate('app.notifications.smartFeed.timeSeries.details.isFalse')}
                isEven={true}
            />
            <DetailsRow
                label={translate('app.notifications.smartFeed.timeSeries.details.isStep')}
                value={timeSeries.isStep ?
                    translate('app.notifications.smartFeed.timeSeries.details.isTrue') :
                    translate('app.notifications.smartFeed.timeSeries.details.isFalse')}
                isEven={false}
            />
            <DetailsRow
                label={translate('app.notifications.smartFeed.timeSeries.details.dataSet')}
                value={timeSeries.datasetName}
                isEven={true}
                link={timeSeries.urlDataSetDetails}
            />
            <DetailsRow
                label={translate('app.notifications.smartFeed.timeSeries.details.linkedAssets')}
                value={timeSeries.assetName}
                isEven={false}
                link={timeSeries.urlLinkedAssets}
            />
            <DetailsRow
                label={translate('app.notifications.smartFeed.timeSeries.details.createdAt')}
                value={timeSeries.createdAt}
                isEven={true}
            />
            <DetailsRow
                label={translate('app.notifications.smartFeed.timeSeries.details.updatedAt')}
                value={timeSeries.updatedAt}
                isEven={false}
            />
            <DetailsRow
                label={translate('app.notifications.smartFeed.timeSeries.details.lastReading')}
                value={`${timeSeries.lastReading} ${translate('app.common.minutesAgo')}`}
                isEven={true}
            />
            <Box sx={styles.seeDetailsLink}>
                <Link target="_blank" href={`${timeSeries.urlTimeSeriesDetails}`}>
                    {translate('app.notifications.smartFeed.timeSeries.details.seeMoreDetails')}
                    <OpenInNewIcon sx={{ fontSize: '16px', verticalAlign: 'middle' }} />
                </Link>
            </Box>
        </Box>
    )
}
