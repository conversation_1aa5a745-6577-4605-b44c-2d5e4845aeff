import { DataPointModel } from '@/common/models/timeSeries'
import { ClnDateRangePicker } from '@celanese/ui-lib'
import { Box } from '@mui/material'
import { DateRange } from '@models/dateRangeTypes'
import { ApexOptions } from 'apexcharts'
import dayjs from 'dayjs'
import dynamic from 'next/dynamic'
import { Dispatch, SetStateAction, useEffect, useState } from 'react'
import * as styles from './time-series-chart.styles'

const Chart = dynamic(() => import('react-apexcharts'), { ssr: false })

interface TimeSeriesChartProps {
    datapoints: DataPointModel[]
    filterByPeriod: DateRange<dayjs.Dayjs>
    setFilterByPeriod: Dispatch<SetStateAction<DateRange<dayjs.Dayjs>>>
}

const handleCreateChartOptions = (categories: string[], data: number[]): ApexOptions => {
    return {
        chart: {
            id: 'timeseries-chart',
            foreColor: '#083D5B',
            type: 'line',
            width: '100%',
            zoom: {
                enabled: true,
                type: 'x',
            },
        },
        colors: ['#083D5B'],
        xaxis: {
            categories: categories,
            labels: {
                show: false,
            },
        },
        series: [
            {
                name: '',
                type: 'line',
                data: data,
            },
        ],
    }
}

export default function TimeSeriesChart({ datapoints, filterByPeriod, setFilterByPeriod }: TimeSeriesChartProps) {
    const [chartData, setChartData] = useState<ApexOptions | undefined>()
    const [localFilterByPeriod, setLocalFilterByPeriod] = useState(filterByPeriod)

    const handleDateRangeAccept = (value: DateRange<dayjs.Dayjs>) => {
        setFilterByPeriod(value)
        setLocalFilterByPeriod(value)
    }

    useEffect(() => {
        const categories = datapoints.map((item) => item.timestamp)
        const data = datapoints.map((item) => parseFloat(item.value.toFixed(2)))
        const chatOptions = handleCreateChartOptions(categories, data)
        setChartData(chatOptions)
    }, [datapoints])

    return typeof window !== 'undefined' && chartData ? (
        <>
            <ClnDateRangePicker
                label=""
                sx={{ marginLeft: '15px' }}
                closeOnSelect={false}
                value={localFilterByPeriod}
                onAccept={handleDateRangeAccept}
                localeLanguage='en'
            />
            <Box sx={styles.timeSeriesContainer}>
                <Chart options={chartData} series={chartData.series} />
            </Box>
        </>
    ) : (
        <></>
    )
}
