import { useGetTimeSeries } from '@/common/hooks/useTimeseriesRequest'
import { TimeSeries } from '@/common/models/timeSeries'
import { TabProps } from '@celanese/ui-lib'
import { Box } from '@mui/material'
import { useEffect, useState } from 'react'
import DetailsTab from './details-tab'
import TimeSeriesChart from './time-series-chart'
import * as styles from './time-series-drawer.styles'

import { ClnTabs } from '@celanese/ui-lib'
import { Backdrop, CircularProgress } from '@mui/material'
import { DateRange } from '@models/dateRangeTypes'
import dayjs, { Dayjs } from 'dayjs'

interface TimeSeriesDrawerProps {
    selectedTimeSeries?: string
}

export default function TimeSeriesDrawer({ selectedTimeSeries }: TimeSeriesDrawerProps) {
    const [tabIndex, setTabIndex] = useState(0)
    const [tabs, setTabs] = useState([] as TabProps[])
    const [timeSeries, setTimeSeries] = useState<TimeSeries | undefined>()
    const [filterByPeriod, setFilterByPeriod] = useState<DateRange<Dayjs>>([dayjs().subtract(1, 'month'), dayjs()])

    const { getTimeSeriesResponse, isLoading } = useGetTimeSeries()

    const handleChange = (_: React.SyntheticEvent, newValue: number) => {
        setTabIndex(newValue)
    }

    useEffect(() => {
        if (selectedTimeSeries) {
            const startPeriod = filterByPeriod[0]
                ? filterByPeriod[0].hour(0).minute(0).second(0).format('YYYY-MM-DDTHH:mm:ss')
                : ''
            const endPeriod = filterByPeriod[1]
                ? filterByPeriod[1].hour(23).minute(59).second(59).format('YYYY-MM-DDTHH:mm:ss')
                : ''

            getTimeSeriesResponse({ externalId: selectedTimeSeries, startDate: startPeriod, endDate: endPeriod }).then(
                (response) => {
                    return setTimeSeries(response)
                }
            )
        }
    }, [selectedTimeSeries, filterByPeriod])

    useEffect(() => {
        const tabsReceived = [] as TabProps[]
        if (timeSeries) {
            tabsReceived.push({
                label: 'Details',
                content: <DetailsTab timeSeries={timeSeries} />,
            })
            if (timeSeries.datapointsItems) {
                tabsReceived.push({
                    label: 'Time Series',
                    content: (
                        <TimeSeriesChart
                            datapoints={timeSeries.datapointsItems}
                            filterByPeriod={filterByPeriod}
                            setFilterByPeriod={setFilterByPeriod}
                        />
                    ),
                })
            }
            setTabs(tabsReceived)
        }
    }, [timeSeries])

    return (
        <Box sx={styles.drawerContent}>
            <ClnTabs value={tabIndex} tabs={tabs} onChange={handleChange} />
            <Backdrop open={isLoading} sx={{ zIndex: 5, position: 'absolute' }}>
                <CircularProgress color="inherit" />
            </Backdrop>
        </Box>
    )
}
