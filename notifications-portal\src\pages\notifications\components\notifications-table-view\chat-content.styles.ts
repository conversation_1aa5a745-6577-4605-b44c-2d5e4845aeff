import { CSSObject } from '@emotion/react'

export const drawerDefault: CSSObject = {
    width: '430px !important',
    '& > div:last-of-type': {
        height: 'calc(100% - 160px)'
    },
    '& .header div .MuiAvatar-root': {
        display: 'none',
    },
}

export const container: CSSObject = {
    height: '100%',
    position: 'relative',
    display: 'flex',
    flexDirection: 'column',
    gap: '15px',
    '& > div': {
        borderColor: 'divider',
        padding: '10px'
    },
    '& div:nth-of-type(2) .MuiPaper-root .MuiBox-root div:nth-of-type(2) nav ul li .Mui-selected': {
        backgroundColor: 'primary.main',
    },
    '& .MuiTabs-root .MuiTabs-scroller .MuiTabs-flexContainer button': {
        color: 'primary.main',
    },
}

export const formRowNoSpace: CSSObject = {
    display: 'flex',
    flexDirection: 'column',
    width: '100%',
    gap: '5px',
    height: '40%',
    overflow: 'auto'
}

export const content: CSSObject = {
    width: '100%',
    maxHeight: 'calc(60% - 100px)',
    overflow: 'auto',
    paddingRight: '5px',
    marginBottom: '20px',
    display: 'flex',
    flexDirection: 'column',
    gap: '15px',
    padding: '10px'
}

export const inputLabel: CSSObject = {
    fontSize: '16px',
    marginBottom: '1px',
    fontWeight: 'normal',
    color: 'text.primary',
}

export const inputLabelLight: CSSObject = {
    fontSize: '12px',
    marginBottom: '1px',
    fontWeight: 'normal',
    color: 'text.secondary',
}

export const chatBubble: CSSObject = {
    display: 'flex',
    justifyContent: 'space-between',
    backgroundColor: 'grey[100]',
    border: '1px solid',
    borderColor: 'divider',
    width: '100%',
    borderRadius: '8px',
    padding: '10px',
}

export const dateAndDeleteContainer: CSSObject = {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'end',
    justifyContent: 'flex-end',
    minHeight: '57px',
}

export const deleteButton: CSSObject = {
    color: 'text.secondary',
    fontSize: '18px'
}

export const chatMessageDate: CSSObject = {
    textAlign: 'right',
    fontSize: '0.8em',
    marginTop: '10px',
    color: 'text.secondary',
}

export const footer: CSSObject = {
    display: 'flex',
    gap: '10px',
    justifyContent: 'space-between',
    width: '100%',
    zIndex: 1,
    alignItems: 'flex-start',
    padding: '0px',
    marginTop: 'auto'
}

export const boxSendButton: CSSObject = {
    display: 'flex',
    flexDirection: 'row',
    padding: '5px',
    alignItems: 'flex-end',
    width: '100%',
    border: '1px solid',
    borderColor: 'divider',
    backgroundColor: 'grey[100]',
    borderRadius: '8px'
}

export const chatMessageRow : CSSObject = {
    display: 'flex',
    gap: '10px',
    alignItems: 'flex-start',
}

export const avatar = (isCurrentUser: boolean): CSSObject => {
    return {
        marginTop: '10px',
        order: isCurrentUser ? 1 : 0
    }
}

export const sendButton: CSSObject = {
    height: '100%',
    margin: '0 10px 5px 0',
}