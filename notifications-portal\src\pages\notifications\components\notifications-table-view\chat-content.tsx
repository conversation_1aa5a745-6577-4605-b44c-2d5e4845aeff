import { HeaderNavbarContextParams } from '@/common/contexts/HeaderContex'
import { UserContextParams } from '@/common/contexts/UserContext'
import { NoTranslate, translate } from '@celanese/celanese-sdk'
import {
    useDeleteChatMessageRequest,
    useGetChatMessagesRequest,
    usePosChattNotificationEvent,
    usePostChatMessageRequest,
} from '@/common/hooks/useChatMessagesRequest'
import { ChatMessages } from '@/common/models/chatMessages'
import { ClnAvatar, ClnBaseIcon, ClnButton, ClnDrawer, ClnTextField, ClnTooltip } from '@celanese/ui-lib'
import { Backdrop, Box, CircularProgress, IconButton, Typography } from '@mui/material'
import dayjs from 'dayjs'
import customParseFormat from 'dayjs/plugin/customParseFormat'
import { Dispatch, SetStateAction, useEffect, useState } from 'react'
import * as styles from './chat-content.styles'
import { NtfTableViewContextParams } from '@/common/contexts/NtfTableViewContext'

interface ChatContentProps {
    selectedNotification: string
    isDrawerOpen: boolean
    setIsDrawerOpen: Dispatch<SetStateAction<boolean>>
}

export default function ChatDrawerContent({ selectedNotification, isDrawerOpen, setIsDrawerOpen }: ChatContentProps) {
    dayjs.extend(customParseFormat)

    const { getChatMessages, isLoading: loadingGet } = useGetChatMessagesRequest(selectedNotification)
    const { postChatMessage, isLoading: loadingPost } = usePostChatMessageRequest(selectedNotification)
    const { deleteChatMessage, isLoading: loadingDelete } = useDeleteChatMessageRequest()
    const { postChatNotificationEvent, isLoading: loadingEvent } = usePosChattNotificationEvent()
    const { setNewCommentPublished } = NtfTableViewContextParams()

    const [chatMessages, setChatMessages] = useState<ChatMessages>({} as ChatMessages)
    const [comment, setComment] = useState('')

    useEffect(() => {
        if (selectedNotification != '') {
            getChatMessages().then((result) => setChatMessages(result))
        }
    }, [selectedNotification, getChatMessages])

    const { userName } = HeaderNavbarContextParams()
    const { handleGetUsername } = UserContextParams()

    const currentUser = handleGetUsername()

    const handleSendComment = () => {
        postChatNotificationEvent(chatMessages.rawEvent).then(() => {
            setTimeout(() => setNewCommentPublished(true), 60000)
        })
        postChatMessage(comment).then(() => {
            getChatMessages().then((result) => setChatMessages(result))
        })
        setComment('')
    }

    const handleDeleteComment = (externalId: string) => {
        deleteChatMessage(externalId).then(() => {
            getChatMessages().then((result) => setChatMessages(result))
        })
    }

    return (
        <ClnDrawer
            open={isDrawerOpen}
            overlineMeta={<NoTranslate>{chatMessages?.application}</NoTranslate> ?? ''}
            caption={chatMessages?.notificationType ?? ''}
            header={translate('app.notifications.chats.name')}
            anchor="right"
            sxProps={styles.drawerDefault}
            closeDrawer={() => setIsDrawerOpen(false)}
            content={
                <Box sx={styles.container}>
                    {Object.keys(chatMessages).length > 0 && (
                        <>
                            <Box sx={styles.formRowNoSpace}>
                                <div dangerouslySetInnerHTML={{ __html: chatMessages.text }}></div>
                                <Typography sx={styles.inputLabelLight}>
                                    {dayjs(chatMessages.createdTime).format('MM/DD/YYYY hh:mm A')}
                                </Typography>
                            </Box>

                            <Box sx={styles.content}>
                                {chatMessages.comments.map((message, index) => (
                                    <Box key={index} sx={styles.chatMessageRow}>
                                        <Box sx={styles.avatar(message.user.email === currentUser)}>
                                            <ClnTooltip arrow={true} title={message.user.displayName ?? ''}>
                                                <IconButton sx={{ padding: 0 }}>
                                                    <ClnAvatar
                                                        alt={message.user.displayName}
                                                        sx={{
                                                            color: 'primary.contrastText',
                                                            backgroundColor: 'primary.main',
                                                        }}
                                                    />
                                                </IconButton>
                                            </ClnTooltip>
                                        </Box>
                                        <Box sx={styles.chatBubble}>
                                            <NoTranslate>
                                                <Typography>{message.comment}</Typography>
                                            </NoTranslate>
                                            <Box sx={styles.dateAndDeleteContainer}>
                                                {message.user.email === currentUser && (
                                                    <IconButton
                                                        sx={{ padding: 0 }}
                                                        onClick={() => handleDeleteComment(message.externalId)}
                                                    >
                                                        <ClnBaseIcon
                                                            className="cln-ico-delete"
                                                            sx={styles.deleteButton}
                                                        />
                                                    </IconButton>
                                                )}
                                                <Typography sx={styles.chatMessageDate}>
                                                    {dayjs(message.createdTime).format('MM/DD/YYYY hh:mm A')}
                                                </Typography>
                                            </Box>
                                        </Box>
                                    </Box>
                                ))}
                            </Box>

                            <Box sx={styles.footer}>
                                <NoTranslate>
                                    <ClnAvatar
                                        alt={userName}
                                        sx={{
                                            marginTop: '10px',
                                            color: 'primary.contrastText',
                                            backgroundColor: 'primary.main',
                                        }}
                                    />
                                </NoTranslate>
                                <Box sx={styles.boxSendButton}>
                                    <ClnTextField
                                        helperText=""
                                        variant="standard"
                                        label=""
                                        fullWidth
                                        multiline
                                        rows={3}
                                        spellCheck={false}
                                        InputProps={{
                                            disableUnderline: true,
                                        }}
                                        onChange={(e) => setComment(e.target.value)}
                                        value={comment}
                                        placeholder={translate('app.notifications.chats.writeYourComment')}
                                    />
                                    <ClnButton
                                        sxProps={styles.sendButton}
                                        label={translate('app.common.send')}
                                        onClick={() => handleSendComment()}
                                        variant="contained"
                                    />
                                </Box>
                            </Box>
                        </>
                    )}
                    <Backdrop open={loadingGet || loadingPost || loadingDelete || loadingEvent} sx={{ zIndex: 5 }}>
                        <CircularProgress color="inherit" />
                    </Backdrop>
                </Box>
            }
        />
    )
}
