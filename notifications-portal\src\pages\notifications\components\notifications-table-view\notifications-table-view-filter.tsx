import FilterCheckboxSection from '@/common/components/FilterCheckboxSection/filter-checkbox-section'
import { NoTranslate, translate } from '@celanese/celanese-sdk'
import { useSeverityRequest } from '@/common/hooks/useSeverityRequest'
import { PaginatedNotifications } from '@/common/models/paginatedNotifications'
import { ClnButton, ClnDateRangePicker, ClnSelect } from '@celanese/ui-lib'
import { CheckboxItem } from '@celanese/ui-lib'
import { SelectItem } from '@celanese/ui-lib'
import { Box, Menu } from '@mui/material'
import { DateRange } from '@models/dateRangeTypes'
import dayjs, { Dayjs } from 'dayjs'
import { useState } from 'react'
import useNotificationsTableViewLogic from '../../hooks/useNotificationsTableViewLogic'
import * as styles from './notifications-table-view-filter.styles'

interface NotificationsTableViewFilterProps {
    anchorEl: HTMLElement | null
    handleCloseMenu: () => void
    paginatedNotifications: PaginatedNotifications
}

function getSelectOptions(object: Record<string, string>, translate: boolean = true) {
    const selectOptions: any[] = []
    if (translate) {
        for (const property in object) {
            selectOptions.push({ value: property, label: object[property] })
        }
    } else {
        for (const property in object) {
            selectOptions.push({ value: property, label: <NoTranslate>{object[property]}</NoTranslate> })
        }
    }
    return selectOptions
}

const oneMontPeriod: DateRange<Dayjs> = [dayjs().subtract(1, 'month'), dayjs()]

export default function NotificationsTableViewFilter({
    anchorEl,
    handleCloseMenu,
    paginatedNotifications,
}: NotificationsTableViewFilterProps) {
    const { setFilterBySeverities, setFilterByPeriod, setFilterByApplication, setFilterByNotificationType } =
        useNotificationsTableViewLogic()

    const { data: severities } = useSeverityRequest(true, ['severity'])
    const [selectedSeverities, setSelectedSeverities] = useState<CheckboxItem[]>([])

    const [selectedPeriod, setSelectedPeriod] = useState<DateRange<Dayjs>>(oneMontPeriod)
    const [selectedApplication, setSelectedApplication] = useState<SelectItem | any | undefined>(undefined)
    const [selectedNotificationType, setSelectedNotificationType] = useState<SelectItem | undefined>(undefined)

    const applicationOptions = getSelectOptions(paginatedNotifications.applications, false)
    const notificationTypeOptions = getSelectOptions(paginatedNotifications.notificationTypes)

    const translations: Record<string, string> = {
        'LOW': translate('app.notifications.filter.severity.low'),
        'MEDIUM': translate('app.notifications.filter.severity.medium'),
        'HIGH': translate('app.notifications.filter.severity.high')
    }

    const mappedSeverities: CheckboxItem[] =
        severities?.map((severity) => {
            return {
                label: translations[severity.name] || severity.description,
                value: severity.externalId,
            }
        }) || []

    const handleApplyFilter = () => {
        const severitiesIds = selectedSeverities.map((severity) => severity.value as string)

        setFilterBySeverities(severitiesIds)
        setFilterByPeriod(selectedPeriod)
        setFilterByApplication(selectedApplication ? selectedApplication.value : '')
        setFilterByNotificationType(selectedNotificationType ? selectedNotificationType.value : '')
        handleCloseMenu()
    }

    const resetFilters = () => {
        setSelectedSeverities([])
        setFilterBySeverities([])

        setSelectedPeriod(oneMontPeriod)
        setFilterByPeriod(oneMontPeriod)

        setSelectedApplication(undefined)
        setFilterByApplication(undefined)

        setSelectedNotificationType(undefined)
        setFilterByNotificationType(undefined)
        handleCloseMenu()
    }

    

    const open = Boolean(anchorEl)

    return (
        <Menu
            anchorEl={anchorEl}
            open={open}
            onClose={handleCloseMenu}
            anchorOrigin={{
                vertical: 'bottom',
                horizontal: 'left',
            }}
            transformOrigin={{
                vertical: 'top',
                horizontal: 'left',
            }}
        >
            <Box sx={styles.filterContainer}>
                <ClnSelect
                    options={applicationOptions}
                    label={translate('app.notifications.application')}
                    fullWidth
                    sx={styles.select}
                    value={selectedApplication}
                    onChange={(e) => setSelectedApplication(e as SelectItem)}
                    variant="outlined"
                />

                <ClnSelect
                    options={notificationTypeOptions}
                    label={translate('app.notifications.notificationType')}
                    fullWidth
                    sx={styles.select}
                    value={selectedNotificationType}
                    onChange={(e) => setSelectedNotificationType(e as SelectItem)}
                    variant="outlined"
                />

                <FilterCheckboxSection
                    label={translate('app.templates.severity')}
                    items={mappedSeverities}
                    value={selectedSeverities}
                    onChange={(severities) => setSelectedSeverities(severities)}
                />

                <ClnDateRangePicker
                    label={translate('app.notifications.period')}
                    sx={styles.dateRangePicker}
                    value={selectedPeriod}
                    onChange={(e: any) => setSelectedPeriod(e)}
                    localeLanguage='en'
                />

                <Box sx={styles.buttonContainer}>
                    <ClnButton variant="outlined" label={translate('app.common.reset')} onClick={resetFilters} />
                    <ClnButton variant="contained" label={translate('app.common.apply')} onClick={handleApplyFilter} />
                </Box>
            </Box>
        </Menu>
    )
}
