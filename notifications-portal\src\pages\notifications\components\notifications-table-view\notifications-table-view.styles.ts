import { CSSObject } from '@emotion/react'
import { CSSProperties } from 'react'

export const tableContainer: CSSObject = {
    padding: '1.5rem',
    borderRadius: '15px',
    backgroundColor: 'background.paper',
    border: '1px solid',
    borderColor: 'divider',
    height: '100%',
    overflow: 'hidden',
}

export const table: CSSObject = {
    height: '100%',
    boxShadow: 'none',
    display: 'grid',
    gridTemplateRows: 'auto 0px 1fr',
    gridTemplateColumns: '100%',
    overflow: 'hidden',
        
    '& .MuiPaper-elevation': {
        height: 'calc(100% - 1rem)',
        marginTop: '1rem',
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'space-between',
        overflowY: 'auto',
        boxShadow: 'none',

        '& .MuiTableContainer-root, & .MuiBox-root': {
            backgroundColor: 'background.paper',
        },

        '& .MuiTableContainer-root': {
            height: '100%'
        },

        '& .MuiTableCell-root': {
            position: 'relative',

            '& .MuiTypography-root': {
                backgroundColor: 'background.paper',
            },
        },
    },
}

export const severityContainer: CSSObject = {
    fontSize: '14px',
    display: 'flex',
    alignItems: 'center',
    gap: '5px'
}

export const severityCircle = (severity: string): CSSObject => {
    const circleColorDict: Record<string, string> = {
        'NTFSVT-LOW': 'success.light',
        'NTFSVT-MEDIUM': 'warning.light',
        'NTFSVT-HIGH': 'error.light'
    }
    
    return {
        fontSize: '15px',
        color: circleColorDict[severity]
    }
}

export const notificationChatsBox: CSSProperties = {
    position: 'relative',
    width: 'fit-content',
    margin: '0px auto'
}

export const notificationChats: CSSObject = {
    padding: '0 !important',
    fontSize: '22px !important',
}

export const notificationCircle: CSSObject = {
    position: 'absolute',
    top: '12px',
    right: '24px',
}

export const backDrop: CSSObject = {
    zIndex: '5'
}


export const drawerDefault: CSSObject = {
    width: '430px !important',
    '& > div:last-of-type': {
        height: 'calc(100% - 160px)'
    },
    '& .header div .MuiAvatar-root': {
        display: 'none',
    },
}


export const messageAlertDefault: CSSObject = {
    position: 'absolute',
    top: '50%',
    left: '0',
    margin: '0 3rem',
    width: 'calc(100% - 6rem)',
    '& svg': {
        fill: 'orange',
    },
}

export const tableSearch: CSSObject = {
    marginRight: '1.25rem',
    '> div': {
        marginRight: '0'
    }
}