import MessageContainer from '@/common/components/MessageContainer/message-container'
import { translate } from '@celanese/celanese-sdk'
import { ClnTable } from '@celanese/ui-lib'
import WarningIcon from '@mui/icons-material/Warning'
import { Backdrop, CircularProgress, Box } from '@mui/material'
import useNotificationsTableViewLogic from '../../hooks/useNotificationsTableViewLogic'
import ChatDrawerContent from './chat-content'
import NotificationsTableViewFilter from './notifications-table-view-filter'
import * as styles from './notifications-table-view.styles'

export default function NotificationsTableView() {
    const {
        paginatedNotifications,
        rows,
        headCells,
        page,
        handleChangePage,
        rowsPerPage,
        handleChangeRowsPerPage,
        handleRequestSort,
        order,
        orderBy,
        setSearch,
        isLoading,
        anchorEl,
        handleCloseMenu,
        handleClickMenu,
        isDrawerOpen,
        setIsDrawerOpen,
        selectedNotification,
        setPage,
    } = useNotificationsTableViewLogic()

    enum TableTranslateKey {
        Search = 'Search',
    }

    const translatedLabels = new Map<TableTranslateKey, string>()
    translatedLabels.set(TableTranslateKey.Search, translate('app.common.search'))

    

    return (
        <>
            {paginatedNotifications && (
                <>
                    <Box sx={styles.tableContainer}>
                        <ClnTable
                            isSelect={false}
                            isCollapsible={false}
                            toolbar={false}
                            searchBox={true}
                            sxPropsSearch={styles.tableSearch}
                            filter={true}
                            leftButton={false}
                            rightButton={false}
                            sxProps={styles.table}
                            rowsPerPage={[10, 20, 50]}
                            rows={rows}
                            headCells={headCells}
                            page={page}
                            handleChangePage={handleChangePage}
                            rowPageValue={rowsPerPage}
                            handleChangeRowsPerPage={handleChangeRowsPerPage}
                            totalPages={paginatedNotifications.totalPages}
                            totalItems={paginatedNotifications.totalItems}
                            visibleRows={rows}
                            handleRequestSort={handleRequestSort}
                            onClickFilter={handleClickMenu}
                            order={order}
                            orderBy={orderBy}
                            disableChips={true}
                            onKeyDownSearch={(event: any, property: string) => {
                                setSearch(property)
                                setPage(0)
                            }}
                            translatedLabels={translatedLabels}
                        />
                        {paginatedNotifications.totalItems == 0 && (
                            <MessageContainer
                                messages={[
                                    {
                                        icon: <WarningIcon />,
                                        text: translate('app.notifications.table.noNotificationsAvailable'),
                                    },
                                ]}
                                sx={styles.messageAlertDefault}
                            />
                        )}
                    </Box>
                    
                    <ChatDrawerContent
                        selectedNotification={selectedNotification}
                        isDrawerOpen={isDrawerOpen}
                        setIsDrawerOpen={setIsDrawerOpen}
                    />
                    <NotificationsTableViewFilter
                        anchorEl={anchorEl}
                        handleCloseMenu={handleCloseMenu}
                        paginatedNotifications={paginatedNotifications}
                    />
                </>
            )}

            <Backdrop open={isLoading} sx={styles.backDrop}>
                <CircularProgress color="inherit" />
            </Backdrop>
        </>
    )
}
