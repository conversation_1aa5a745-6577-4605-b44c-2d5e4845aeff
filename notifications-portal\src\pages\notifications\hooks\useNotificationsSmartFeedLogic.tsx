import { NtfSmartFeedContextParams } from '@/common/contexts/NtfSmartFeedContext'
import { translate } from '@celanese/celanese-sdk'
import { NotificationOnScreen } from '@/common/models/paginatedNotifications'
import { ClnRows } from '@celanese/ui-lib'
import { SelectChangeEvent } from '@mui/material'
import dayjs from 'dayjs'
import { ChangeEvent, Dispatch, SetStateAction, useState } from 'react'
import NotificationCard from '../components/notifications-smart-feed/notification-card'

const createRows = (
    notifications: NotificationOnScreen[],
    selectedNotification: string,
    setSelectedNotification: Dispatch<SetStateAction<string>>
) => {
    const rows: ClnRows[] = notifications.map((notification, index) => {
        const date = dayjs(notification.date).format('MM/DD/YY - hh:mm A')
        const updateDate = notification.updateDate ? dayjs(notification.updateDate).format('MM/DD/YY - hh:mm A') : undefined
        const { externalId, application, applicationIconUrl, site, severity, notificationMessage } = notification

        return {
            id: `${index}`,
            columns: [
                {
                    name: 'Application',
                    value: (
                        <NotificationCard
                            externalId={externalId}
                            application={application}
                            applicationIconUrl={applicationIconUrl}
                            site={site}
                            severity={severity}
                            text={notificationMessage}
                            date={date}
                            updateDate={updateDate}
                            selectedNotification={selectedNotification}
                            setSelectedNotification={setSelectedNotification}
                        />
                    ),
                },
            ],
        }
    })

    return rows
}

export default function useNotificationsSmartFeedLogic() {
    

    const [selectedNotification, setSelectedNotification] = useState('')
    const creatHeadCells = () => {
        return [{ id: '0', label: translate('app.notifications.todaysNotifications'), isSorted: false }]
    }
    const headCells = creatHeadCells()

    const {
        page,
        setPage,
        rowsPerPage,
        setRowsPerPage,
        setFilterByApplication,
        setFilterByNotificationType,
        setFilterBySeverities,
        paginatedSmartFeed,
        isLoading,
        setSearch,
        order,
        setOrder,
        orderBy,
        setOrderBy,
    } = NtfSmartFeedContextParams()

    const rows: ClnRows[] = paginatedSmartFeed
        ? createRows(paginatedSmartFeed.notifications, selectedNotification, setSelectedNotification)
        : []

    const handleChangePage = (event: ChangeEvent<unknown>, newPage: number) => {
        setPage(newPage - 1)
    }

    const handleChangeRowsPerPage = (event: SelectChangeEvent) => {
        setRowsPerPage(parseInt(event.target.value))
        setPage(0)
    }

    const [filterAnchorEl, setFilterAnchorEl] = useState<null | HTMLElement>(null)

    const handleCloseFilter = () => {
        setFilterAnchorEl(null)
    }

    const handleOpenFilter = (event: React.MouseEvent<HTMLElement>) => {
        setFilterAnchorEl(event.currentTarget)
    }

    const [sortAnchorEl, setSortAnchorEl] = useState<null | HTMLElement>(null)

    const handleCloseSort = () => {
        setSortAnchorEl(null)
    }

    const handleOpenSort = (event: React.MouseEvent<HTMLElement>) => {
        setSortAnchorEl(event.currentTarget)
    }

    const [selectedOrderName, setSelectedOrderName] = useState('')

    return {
        headCells,
        rows,
        paginatedSmartFeed,
        page,
        setPage,
        handleChangePage,
        rowsPerPage,
        handleChangeRowsPerPage,
        isLoading,
        setFilterByApplication,
        setFilterByNotificationType,
        setFilterBySeverities,
        selectedNotification,
        setSelectedNotification,
        setSearch,
        filterAnchorEl,
        handleCloseFilter,
        handleOpenFilter,
        sortAnchorEl,
        handleCloseSort,
        handleOpenSort,
        order,
        setOrder,
        orderBy,
        setOrderBy,
        selectedOrderName,
        setSelectedOrderName,
    }
}
