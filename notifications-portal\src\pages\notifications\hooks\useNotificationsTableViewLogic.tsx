import ExpansibleText from '@/common/components/ExpansibleText/expansible-text'
import { NtfTableViewContextParams } from '@/common/contexts/NtfTableViewContext'
import { translate } from '@celanese/celanese-sdk'
import { NotificationOnScreen } from '@/common/models/paginatedNotifications'
import { ClnButton, ClnBadge } from '@celanese/ui-lib'
import { ClnRows } from '@celanese/ui-lib'
import CircleIcon from '@mui/icons-material/Circle'
import { Box, SelectChangeEvent, Typography } from '@mui/material'
import dayjs from 'dayjs'
import { ChangeEvent, Dispatch, SetStateAction, useState } from 'react'
import * as styles from '../components/notifications-table-view/notifications-table-view.styles'
import { useEffect } from 'react'
import { HeaderNavbarContextParams } from '@/common/contexts/HeaderContex'

const createRows = (
    notifications: NotificationOnScreen[],
    setSelectedNotification: Dispatch<SetStateAction<string>>,
    setIsDrawerOpen: Dispatch<SetStateAction<boolean>>,
    userLastAccess: string | undefined,
    translate: (label: string) => any,
    unreadNotifications: string | undefined
) => {

    const rows: ClnRows[] = notifications.map((notification, index) => {
        const date = dayjs(notification.date).format('MM/DD/YYYY - hh:mm A')

        const displayText = notification.notificationMessage.includes('#htmlmark')
            ? translate('app.notifications.chats.htmlContentNotAvailable')
            : notification.notificationMessage

        const displayHtml = <div dangerouslySetInnerHTML={{ __html: displayText }}></div>

        return {
            id: `${index}`,
            columns: [
                {
                    name: translate('app.notifications.table.application'),
                    value: <span className='no-translate'>{notification.application}</span>
                },
                { name: translate('app.notifications.table.site'), value: notification.site },
                {
                    name: translate('app.notifications.table.severity'),
                    value: (
                        <Typography sx={styles.severityContainer}>
                            <CircleIcon sx={styles.severityCircle(notification.severity.externalId)} />
                            {notification.severity.description}
                        </Typography>
                    ),
                },
                { name: translate('app.notifications.table.date'), value: date },
                { name: translate('app.notifications.table.notificationType'), value: notification.notificationType },
                {
                    name: translate('app.notifications.table.notificationMessage'),
                    value: <ExpansibleText text={displayHtml} maxWidth={200} />,
                },
                {
                    name: translate('app.notifications.table.notificationChats'),
                    value: (
                        <Box>
                            <div style={styles.notificationChatsBox}>
                                <ClnButton
                                    variant="text"
                                    sxProps={styles.notificationChats}
                                    className="cln-ico-message"
                                    onClick={() => {
                                        setSelectedNotification(notification.externalId)
                                        setIsDrawerOpen(true)
                                    }}
                                />
                                { unreadNotifications != '0' && (
                                    <ClnBadge type="primary" sxProps={styles.notificationCircle}><div /></ClnBadge>
                                )}
                            </div>
                        </Box>
                    ),
                },
            ],
        }
    })

    return rows
}

const creatHeadCells = (translate: (label: string) => any) => {
    return [
        { id: '0', label: translate('app.notifications.table.application'), isSorted: true },
        { id: '1', label: translate('app.notifications.table.site'), isSorted: true },
        { id: '2', label: translate('app.notifications.table.severity'), isSorted: false },
        { id: '3', label: translate('app.notifications.table.date'), isSorted: true },
        { id: '4', label: translate('app.notifications.table.notificationType'), isSorted: true },
        { id: '5', label: translate('app.notifications.table.notificationMessage'), isSorted: true },
        { id: '6', label: translate('app.notifications.table.notificationChats'), isSorted: false },
    ]
}

export default function useNotificationsTableViewLogic() {
    const {
        page,
        setPage,
        rowsPerPage,
        setRowsPerPage,
        order,
        setOrder,
        orderBy,
        setOrderBy,
        setSearch,
        setFilterByPeriod,
        setFilterByApplication,
        setFilterByNotificationType,
        setFilterBySeverities,
        paginatedNotifications,
        isLoading,
        newCommentPublished,
        setNewCommentPublished,
        refetchTableView,
        userLastAccess,
    } = NtfTableViewContextParams()

    const { unreadNotifications } = HeaderNavbarContextParams()

    const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null)

    const [selectedNotification, setSelectedNotification] = useState('')
    const [isDrawerOpen, setIsDrawerOpen] = useState(false)

    const handleChangePage = (event: ChangeEvent<unknown>, newPage: number) => {
        setPage(newPage - 1)
    }

    const handleChangeRowsPerPage = (event: SelectChangeEvent) => {
        setRowsPerPage(parseInt(event.target.value))
        setPage(0)
    }

    const handleRequestSort = (property: string) => {
        const isAsc = orderBy === property && order === 'asc'
        setOrder(isAsc ? 'desc' : 'asc')
        setOrderBy(property)
    }



    const rows: ClnRows[] = createRows(paginatedNotifications?.notifications ?? [], setSelectedNotification, setIsDrawerOpen, userLastAccess, translate, unreadNotifications)

    const headCells = creatHeadCells(translate)

    const handleCloseMenu = () => {
        setAnchorEl(null)
    }

    const handleClickMenu = (event: React.MouseEvent<HTMLElement>) => {
        setAnchorEl(event.currentTarget)
    }

    useEffect(() => {
        if (newCommentPublished) {
            refetchTableView()
            setNewCommentPublished(false)
        }
    }, [newCommentPublished])



    return {
        paginatedNotifications,
        rows,
        headCells,
        page,
        handleChangePage,
        rowsPerPage,
        handleChangeRowsPerPage,
        handleRequestSort,
        order,
        orderBy,
        setSearch,
        isLoading,
        anchorEl,
        handleCloseMenu,
        handleClickMenu,
        setFilterByPeriod,
        setFilterByApplication,
        setFilterByNotificationType,
        setFilterBySeverities,
        isDrawerOpen,
        setIsDrawerOpen,
        selectedNotification,
        setPage,
        setNewCommentPublished,
    }
}
