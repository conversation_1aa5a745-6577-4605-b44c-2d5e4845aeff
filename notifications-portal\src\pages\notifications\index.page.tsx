import { updateNotificationLastAccessURI } from '@/common/configurations/endpoints'
import { HeaderNavbarContextParams } from '@/common/contexts/HeaderContex'
import { NtfSmartFeedContextParams } from '@/common/contexts/NtfSmartFeedContext'
import { NtfTableViewContextParams } from '@/common/contexts/NtfTableViewContext'
import { UserContextParams } from '@/common/contexts/UserContext'
import { useApiService } from '@/common/hooks/useApiService'
import { NoTranslate, translate, TranslationContext, TranslationContextState } from '@celanese/celanese-sdk'
import { ClnTabs } from '@celanese/ui-lib'
import { Box, Typography } from '@mui/material'
import React, { useContext, useEffect } from 'react'
import NotificationsSmartFeed from './components/notifications-smart-feed/notifications-smart-feed'
import NotificationsTableView from './components/notifications-table-view/notifications-table-view'
import * as styles from './index.styles'

export default function Notifications() {

    const { locale } = useContext<TranslationContextState>(TranslationContext)
    useEffect(() => {
        const checkLocalStorage = () => {
            const localeData = window.localStorage?.getItem('LocaleData')
            const translationData = window.localStorage?.getItem(
                `APP-NTFTranslationData${localeData}`,
            )
            if (!localeData || !translationData) {
                setTimeout(checkLocalStorage, 200)
            }
            else {
                document.title = translate('app.menu.notifications') + ' | Notifications Portal'
            }
        }
        checkLocalStorage()
    }, [locale])

    const { refetchSmartFeed } = NtfSmartFeedContextParams()
    const { refetchTableView, userLastAccess, setUserLastAccess } = NtfTableViewContextParams()

    const tabs = [
        {
            label: translate('app.notifications.smartFeed.name'),
            content: <NotificationsSmartFeed key="smart-view" />,
        },
        {
            label: translate('app.notifications.table.name'),
            content: <NotificationsTableView key="table-view" />,
        },
    ]

    const [value, setValue] = React.useState(0)
    const handleChange = (event: React.SyntheticEvent, newValue: number) => {
        setValue(newValue)
        if (newValue == 0) {
            refetchSmartFeed()
        } else {
            refetchTableView()
        }
    }

    const axios = useApiService()
    const { handleGetUsername } = UserContextParams()
    const { refetch } = HeaderNavbarContextParams()

    useEffect(() => {
        const fetchData = async () => {
            await axios.get(updateNotificationLastAccessURI).then((res) => {
                if (res.data.message != 0) {
                    setUserLastAccess(res.data.message)
                } else {
                    axios.post(updateNotificationLastAccessURI)
                }
            })
        }

        if (userLastAccess === undefined) {
            fetchData()
        }
    }, [userLastAccess])

    setTimeout(() => {
        if (handleGetUsername() != '' && userLastAccess !== undefined) {
            axios.post(updateNotificationLastAccessURI).then(() => {
                refetch()
            })
        }
    }, 3000)

    return (
        <Box sx={styles.container}>
            <NoTranslate><Typography sx={styles.notificationsHeader}>{translate('app.title')}</Typography></NoTranslate>
            <ClnTabs value={value} tabs={tabs} onChange={handleChange} />
        </Box>
    )
}
