import { CSSObject } from '@emotion/react'

export const container: CSSObject = {
    height: '100%',
    width: '100%',
    display: 'grid',
    gridTemplateRows: 'auto auto 1fr',
    gridTemplateColumns: '100%',
    gap: '1rem',
    overflow: 'hidden',
    position: 'relative',
}

export const notificationsHeader: CSSObject = {
    color: 'primary.main',
    fontWeight: 'bold',
    fontSize: '1.5rem',
    lineHeight: '32px',
}