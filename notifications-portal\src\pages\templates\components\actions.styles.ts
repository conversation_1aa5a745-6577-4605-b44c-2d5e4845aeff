import { CSSObject } from '@emotion/react'

export const actionsContainer: CSSObject = {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'flex-start',
    '& div > button': {
        minWidth: 'unset',
        padding: '4px',
    },
}

export const editIcon: CSSObject = { fontSize: '22px' }

export const deleteIcon: CSSObject = { fontSize: '20px' }

export const viewIcon: CSSObject = { fontSize: '21px' }
