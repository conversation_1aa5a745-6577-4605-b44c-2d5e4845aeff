import { ActionsContextParams } from '@/common/contexts/ActionsContext'
import { useAuthGuard } from '@/common/hooks/useAuthGuard'
import { ClnButton, MatIcon } from '@celanese/ui-lib'
import VisibilityOutlinedIcon from '@mui/icons-material/VisibilityOutlined'
import { Box } from '@mui/material'
import { useEffect, useState } from 'react'
import * as styles from './actions.styles'
import { UserContextParams } from '@/common/contexts/UserContext'

interface ActionsProps {
    isAdminLevel: boolean
    externalId: string
    creator: string
    customChannelEnabled: boolean
    customFrequencyEnabled: boolean
    adminLevelFromTemplate: boolean
}

export default function Actions({
    isAdminLevel,
    externalId,
    creator,
    customChannelEnabled,
    customFrequencyEnabled,
    adminLevelFromTemplate,
}: Readonly<ActionsProps>) {
    const { editAction, deleteAction, actionApplication, viewAction } = ActionsContextParams()
    const { checkPermissionsFromComponentsPerApplication } = useAuthGuard()
    const [enableButton, setEnableButton] = useState<boolean>(false)
    const { handleGetUsername } = UserContextParams()
    const userIsCreator = handleGetUsername() === creator

    const app = actionApplication ?? ''
    const editMyTemplate = userIsCreator && !adminLevelFromTemplate ? btoa('editMyTemplate') : btoa('editMyTemplateAdmin')

    const params = new URLSearchParams({
        a: btoa(String(isAdminLevel)),
        f: isAdminLevel ? btoa('edit') : editMyTemplate,
    })

    const edit = (
        <a
            href={`customize-templates?id=${externalId}&${params}`}
            style={{
                color: 'inherit',
                textDecoration: 'none',
                fontSize: '20px',
                display: 'inline-flex',
                alignItems: 'center',
            }}
        >
            <MatIcon icon="edit" sx={styles.editIcon} />
        </a>
    )

    const del = <MatIcon icon="delete" sx={styles.deleteIcon} onClick={() => deleteAction(externalId)} />
    const view = <VisibilityOutlinedIcon onClick={() => viewAction(externalId)} sx={styles.viewIcon} />

    const actions = [] as JSX.Element[]

    useEffect(() => {
        const authorizedButton = checkPermissionsFromComponentsPerApplication('Edit Admin Template', app)
        setEnableButton(authorizedButton.isAuthorized)
    }, [isAdminLevel, checkPermissionsFromComponentsPerApplication, setEnableButton, app])

    if (isAdminLevel) {
        if (enableButton) {
            actions.push(edit)
            actions.push(del)
        } else {
            actions.push(view)
        }
    } else if (!isAdminLevel) {
        if (!adminLevelFromTemplate && userIsCreator) {
            actions.push(edit)
            actions.push(del)
        } else if (adminLevelFromTemplate && (customChannelEnabled || customFrequencyEnabled)) {
            actions.push(edit)
        } else {
            actions.push(view)
        }
    }

    return (
        <Box sx={styles.actionsContainer}>
            {actions.map((element, index) => (
                <ClnButton key={index} startIcon={element} variant="text" />
            ))}
        </Box>
    )
}
