import { CSSObject } from '@emotion/react'

export const container: CSSObject = {
    display: 'flex',
    flexDirection: 'column',
    height: '100%',
    overflowY: 'auto',
}

export const formContainer: CSSObject = {
    position: 'relative',
    backgroundColor: 'background.paper',
    display: 'flex',
    flexDirection: 'column',
    flexGrow: 1,
    width: '450px',
    padding: '1.5rem',
    overflow: 'auto',
    gap: '25px',
    border: '1px solid',
    borderColor: 'divider',
    borderRadius: '8px',
}

export const backdrop: CSSObject = {
    color: 'white',
    position: 'absolute',
}
