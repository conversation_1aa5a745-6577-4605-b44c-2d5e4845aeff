import { Application } from '@/common/models/application'
import { Backdrop, Box, CircularProgress } from '@mui/material'
import useLateralMenuLogic from '../hooks/useLateralMenuLogic'
import * as styles from './lateral-menu.styles'
import NotificationTypeList from './notification-type-list'
import SelectApplication from './select-application'

interface LateralMenuProps {
    applications: Application[] | undefined
}

export default function LateralMenu({ applications }: LateralMenuProps) {
    const { applicationsNames, selectedApplications, setSelectedApplications } = useLateralMenuLogic(applications)

    return (
        <Box sx={styles.container}>
            <Box sx={styles.formContainer}>
                {applications && applicationsNames ? (
                    <>
                        <SelectApplication
                            applicationsNames={applicationsNames}
                            selectedApplications={selectedApplications}
                            setSelectedApplications={setSelectedApplications}
                        />
                        <NotificationTypeList applications={applications} selectedApplications={selectedApplications} />
                    </>
                ) : (
                    <Backdrop open={true} sx={styles.backdrop}>
                        <CircularProgress color="inherit" />
                    </Backdrop>
                )}
            </Box>
        </Box>
    )
}
