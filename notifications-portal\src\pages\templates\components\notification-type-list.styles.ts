import { CSSObject } from '@emotion/react'

export const notificationsHeader: CSSObject = {
    color: 'text.contrast',
    fontWeight: 'medium',
    fontSize: '16px',
}

export const divider: CSSObject = {
    marginBottom: '15px',
}

export const appName: CSSObject = {
    color: 'text.contrast',
    fontWeight: 'medium',
    fontSize: '15px',
}

export const notificationTypeButton = (myEid: string, selectedEid: string | undefined): CSSObject => {
    return {
        display: 'flex',
        justifyContent: 'space-between',
        backgroundColor: myEid === selectedEid ? 'action.selected' : 'unset',
        '&:hover': {
            backgroundColor: 'primary.opacity8',
            borderRadius: '5px',
        },
    }
}

export const notificationTypeText: CSSObject = {
    color: 'text.contrast',
    alignItems: 'center',
    fontSize: '15px',
}

export const iconsContainer: CSSObject = {
    display: 'flex',
    alignItems: 'center',
}

export const errorIcon: CSSObject = {
    color: 'warning.dark',
}

export const arrowRight: CSSObject = {
    color: 'text.primary',
}
export const messageAlertDefault: CSSObject = {
    position: 'absolute',
    top: '30%',
    left: '0',
    width: 'calc(100% - 40px)',
    margin: '0 20px',
    gap: '10px',
    '& svg': {
        fill: 'orange',
    },
}
