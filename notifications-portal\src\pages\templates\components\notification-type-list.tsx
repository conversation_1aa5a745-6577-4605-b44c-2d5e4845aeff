import MessageContainer from '@/common/components/MessageContainer/message-container'
import { TemplatesContextParams } from '@/common/contexts/TemplatesContext'
import { NoTranslate, translate } from '@celanese/celanese-sdk'
import { Application } from '@/common/models/application'
import { NotificationType } from '@/common/models/notificationType'
import WarningIcon from '@mui/icons-material/Warning'
import { Box, Divider, List, ListItemButton, Typography } from '@mui/material'
import * as styles from './notification-type-list.styles'

// COMMENTED IMPORTS
// import { ClnTooltip } from '@celanese/ui-lib'
// import ArrowRightIcon from '@mui/icons-material/ArrowRight'
// import ErrorIcon from '@mui/icons-material/Error'

interface NotificationTypeListProps {
    applications: Application[]
    selectedApplications: String[]
}

export default function NotificationTypeList({ applications, selectedApplications }: NotificationTypeListProps) {
    const {
        setSelectedApplication,
        setNotificationType,
        setCurrentPageInfiniteScroll,
        notificationType,
        setSearch,
        setNotificationTypeName,
        setApplicationName,
        setNotificationTypeSelected,
        handleScrollToTop,
    } = TemplatesContextParams()

    const handleClickOnNotificationType = (
        notificationTypeReceived: NotificationType,
        application: string,
        notificationTypeName: string,
        applicationName: string
    ) => {
        const table = document.querySelector('[data-testid="virtuoso-scroller"]')
        if (notificationTypeReceived.externalId === notificationType) {
            setNotificationType(undefined)
            setSelectedApplication('')
            setNotificationTypeName('')
            setApplicationName('')
            setNotificationTypeSelected(undefined)
        } else {
            setNotificationType(notificationTypeReceived.externalId)
            setSelectedApplication(application.trim())
            setNotificationTypeName(notificationTypeName)
            setApplicationName(applicationName)
            setNotificationTypeSelected(notificationTypeReceived)
        }
        setCurrentPageInfiniteScroll(0)
        setSearch('')
        handleScrollToTop(table)
    }

    

    return applications.length > 0 ? (
        <Box>
            <Typography sx={styles.notificationsHeader}>{translate('app.templates.sideMenu.notificationTypes')}</Typography>
            <Divider sx={styles.divider} />
            {applications.map((app) => {
                if (selectedApplications.includes(app.alias)) {
                    return (
                        <Box key={app.alias}>
                            <NoTranslate><Typography sx={styles.appName}>{app.alias}</Typography></NoTranslate>
                            <List>
                                {app.notificationTypes.map((notification: NotificationType, index: number) => (
                                    <ListItemButton
                                        key={index}
                                        sx={styles.notificationTypeButton(notification.externalId, notificationType)}
                                        onClick={() =>
                                            handleClickOnNotificationType(
                                                notification,
                                                app.externalId,
                                                notification.name,
                                                app.name
                                            )
                                        }
                                    >
                                        <Typography sx={styles.notificationTypeText}>{notification.name}</Typography>
                                        {/* TODO: warning icon should be per user - future working */}
                                        {/* <Box sx={styles.iconsContainer}>
                                            {!notification.hasTemplates && (
                                                <ClnTooltip
                                                    placement="right"
                                                    arrow={true}
                                                    title={translate(
                                                        'app.templates.alerts.noTemplateForThisNotificationType'
                                                    )}
                                                >
                                                    <ErrorIcon sx={styles.errorIcon} />
                                                </ClnTooltip>
                                            )}
                                            <ArrowRightIcon sx={styles.arrowRight} />
                                        </Box> */}
                                    </ListItemButton>
                                ))}
                            </List>
                        </Box>
                    )
                }
            })}
        </Box>
    ) : (
        <Box>
            <Typography sx={styles.notificationsHeader}>Notification Types</Typography>
            <Divider sx={styles.divider} />
            <MessageContainer
                messages={[
                    {
                        icon: <WarningIcon />,
                        text: translate('app.templates.alerts.noApplicationsOrNotificationsTypesAvailable'),
                    },
                ]}
                sx={styles.messageAlertDefault}
            />
        </Box>
    )
}
