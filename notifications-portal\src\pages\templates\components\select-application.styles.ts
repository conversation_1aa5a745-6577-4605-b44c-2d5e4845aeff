import { CSSObject } from '@emotion/react'

export const selectLabel: CSSObject = {
    color: 'text.secondary',
    fontWeight: 'bold',
    fontSize: '16px'
}

export const menuItem: CSSObject = {
    padding: 0
}

export const checkbox: CSSObject = {
    padding: '9px 16px'
}

export const listItemText: CSSObject = {
    padding: '9px 0'
}

export const optionsContainer: CSSObject = {
    maxHeight: '177px',
    overflow: 'auto'
}