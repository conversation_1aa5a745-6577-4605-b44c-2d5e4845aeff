import { TemplatesContextParams } from '@/common/contexts/TemplatesContext'
import { NoTranslate, translate } from '@celanese/celanese-sdk'
import { ClnButton } from '@celanese/ui-lib'
import { Box, Typography } from '@mui/material'
import Checkbox from '@mui/material/Checkbox'
import Divider from '@mui/material/Divider'
import FormControl from '@mui/material/FormControl'
import ListItemText from '@mui/material/ListItemText'
import MenuItem from '@mui/material/MenuItem'
import Select, { SelectChangeEvent } from '@mui/material/Select'
import { Dispatch, MouseEvent, useEffect, useState } from 'react'
import * as styles from './select-application.styles'

interface notificationType {
    name: string
    description: string
    notificationTemplates: []
}

export interface Application {
    name: string
    description: string
    notificationTypes: notificationType[]
}

interface SelectApplicationProperties {
    selectedApplications: string[]
    setSelectedApplications: Dispatch<React.SetStateAction<string[]>>
    applicationsNames: string[]
    }

export default function SelectApplication({
    selectedApplications,
    applicationsNames,
    setSelectedApplications,
}: SelectApplicationProperties) {
    const [internApplications, setInternApplications] = useState<string[]>(selectedApplications)
    const [isOpen, setIsOpen] = useState<boolean>(false)
    const { setNotificationType, setNotificationTypeName, setNotificationTypeSelected, handleScrollToTop } = TemplatesContextParams()
    const [reloadKey, setReloadKey] = useState<number>(0)

    const selectAll = (event: MouseEvent) => {
        event.stopPropagation()
        if (applicationsNames) {
            setInternApplications(internApplications.length >= applicationsNames?.length ? [] : applicationsNames)
        }
    }

    const handleChange = (event: SelectChangeEvent<typeof selectedApplications>) => {
        const value = event.target.value as string[]
        if (value.includes('all')) {
            setInternApplications(internApplications.length >= applicationsNames.length ? [] : applicationsNames)
        } else {
            setInternApplications(value)
        }
    }

    const handleIndividualChange = (app: string) => {
        setInternApplications(prev =>
            prev.includes(app) ? prev.filter(item => item !== app) : [...prev, app]
        )
    }

    const applyChanges = () => {
        const table = document.querySelector('[data-testid="virtuoso-scroller"]')

        setIsOpen(false)
        setSelectedApplications(internApplications)
        setNotificationType(undefined)
        setNotificationTypeSelected(undefined)
        setNotificationTypeName('')
        handleScrollToTop(table)
    }

    const handleCloseSelect = () => {
        setIsOpen(false)
        setInternApplications(selectedApplications)
    }

    useEffect(() => {
        if (selectedApplications) {
            setInternApplications(selectedApplications)
        }
    }, [selectedApplications])

    const reloadFormControl = () => {
        setReloadKey(prevKey => prevKey + 1)
    }

    useEffect(() => {
        if (isOpen) {
            reloadFormControl()
        }
    }, [isOpen])

    return (
        <FormControl key={reloadKey}>
            <Typography sx={styles.selectLabel}>{translate('app.templates.sideMenu.app')}:</Typography>
            <Select
                multiple
                value={internApplications}
                onChange={handleChange}
                onOpen={() => setIsOpen(true)}
                onClose={handleCloseSelect}
                open={isOpen}
                renderValue={(selected) => (
                    <span className="no-translate">{selected.join(', ')}</span>
                )}
                MenuProps={{
                    PaperProps: {
                        style: { maxHeight: '300px' },
                    },
                }}
            >
                <MenuItem value="all" sx={styles.menuItem}>
                    <Checkbox
                        sx={styles.checkbox}
                        checked={applicationsNames.length > 0 && internApplications.length === applicationsNames.length}
                        indeterminate={
                            internApplications.length > 0 && internApplications.length < applicationsNames.length
                        }
                        onClick={selectAll}
                    />
                    <ListItemText sx={styles.listItemText} primary='Select All' onClick={selectAll} />
                </MenuItem>
                <Divider />

                <NoTranslate>
                    {applicationsNames.map((app) => (
                        <MenuItem key={app} value={app} sx={styles.menuItem} onClick={() => handleIndividualChange(app)}>
                            <Checkbox sx={styles.checkbox} checked={internApplications.includes(app)} />
                            <ListItemText sx={styles.listItemText} primary={app} />
                        </MenuItem>
                    ))}
                </NoTranslate>

                <Box sx={{ marginTop: '12px' }}>
                    <ClnButton label="APPLY CHANGES" onClick={applyChanges} sxProps={{ width: '100%' }} />
                </Box>
            </Select>
        </FormControl>
    )
}
