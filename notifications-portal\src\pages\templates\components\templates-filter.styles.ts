import { CSSObject } from '@emotion/react'

export const filtersContainer: CSSObject = {
    minWidth: '220px',
    display: 'flex',
    flexDirection: 'column',
    gap: '10px',
    padding: '10px'
}

export const subscribersContainer: CSSObject = {
    maxHeight: '150px',
    overflow: 'auto'
}

export const filterCheckboxSection: CSSObject ={
    marginLeft: '12px',
    '& .MuiTypography-root': {
        marginTop: '10px'
    }
}

export const buttonContainer: CSSObject = {
    textAlign: 'center',
    display: 'flex',
    gap: '5px',
    justifyContent: 'center',
    marginTop: '10px',
    '& div': {
        width: '100%',
        '& button': {
            width: '100%'
        }
    }
}