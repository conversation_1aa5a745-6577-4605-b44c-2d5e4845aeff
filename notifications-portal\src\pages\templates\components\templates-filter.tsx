import FilterCheckboxSection from '@/common/components/FilterCheckboxSection/filter-checkbox-section'
import { TemplatesContextParams } from '@/common/contexts/TemplatesContext'
import { NoTranslate, translate } from '@celanese/celanese-sdk'
import { useChannelsRequest } from '@/common/hooks/useChannelsRequest'
import { ClnButton } from '@celanese/ui-lib'
import { CheckboxItem } from '@celanese/ui-lib'
import { Box, Menu } from '@mui/material'
import { useState } from 'react'
import * as styles from './templates-filter.styles'
import { useSendToByNotificationTypeRequest } from '@/common/hooks/useSendToByNotificationTypeRequest'

interface TemplatesFilterProps {
    anchorEl: HTMLElement | null
    handleCloseMenu: () => void
    notificationType: string
    isAdminLevel: boolean
}

export default function TemplatesFilter({ anchorEl, handleCloseMenu, notificationType, isAdminLevel }: TemplatesFilterProps) {
    const { setSubscribersIds, setChannelsIds, setCurrentCursor, setHasNextPage } = TemplatesContextParams()
    const [selectedSubscribers, setSelectedSubscribers] = useState<CheckboxItem[]>([])
    const [selectedChannels, setSelectedChannels] = useState<CheckboxItem[]>([])

    const { data: CHANNELS } = useChannelsRequest(true, ['channels'])
    const mappedChannels =
        CHANNELS?.map((channel) => ({
            value: channel.externalId,
            label: channel.description,
        })) || []

    const { data: sendTo } = useSendToByNotificationTypeRequest(true, [`sendTo-${notificationType}`], notificationType)

    const getSubscribersExternalIds = () => {
        const subscribersExternalIds = selectedSubscribers.map((subscriber) => subscriber.value)
        const channelsEternalIds = selectedChannels.map((channel) => channel.value)

        setSubscribersIds(subscribersExternalIds)
        setChannelsIds(channelsEternalIds)
        setCurrentCursor(undefined)
        setHasNextPage(false)
        handleCloseMenu()
    }

    const resetFilters = () => {
        setSelectedSubscribers([])
        setSubscribersIds([])

        setSelectedChannels([])
        setChannelsIds([])
        setCurrentCursor(undefined)
        setHasNextPage(false)
        handleCloseMenu()
    }

    const open = Boolean(anchorEl)

    

    return (
        <Menu
            anchorEl={anchorEl}
            open={open}
            onClose={handleCloseMenu}
            anchorOrigin={{
                vertical: 'bottom',
                horizontal: 'left',
            }}
            transformOrigin={{
                vertical: 'top',
                horizontal: 'left',
            }}
        >
            <Box sx={styles.filtersContainer}>
                <Box sx={styles.subscribersContainer}>
                    {isAdminLevel && (
                        <NoTranslate>
                            <FilterCheckboxSection
                                label={translate('app.templates.sendTo')}
                                items={sendTo || []}
                                value={selectedSubscribers}
                                onChange={(cis) => setSelectedSubscribers(cis)}
                                sx={styles.filterCheckboxSection}
                            />
                        </NoTranslate>
                    )}
                </Box>
                <NoTranslate>
                    <FilterCheckboxSection
                        label={translate('app.templates.channels')}
                        items={mappedChannels}
                        value={selectedChannels}
                        onChange={(cis) => setSelectedChannels(cis)}
                        sx={styles.filterCheckboxSection}
                    />
                </NoTranslate>

                <Box sx={styles.buttonContainer}>
                    <ClnButton variant="outlined" label={translate('app.common.reset')} onClick={resetFilters} />
                    <ClnButton
                        variant="contained"
                        label={translate('app.common.apply')}
                        onClick={getSubscribersExternalIds}
                    />
                </Box>
            </Box>
        </Menu>
    )
}
