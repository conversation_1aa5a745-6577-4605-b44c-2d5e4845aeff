import { CSSObject } from '@emotion/react'

export const container = (isLoading: boolean) => {
    return {
        position: 'relative',
        width: '100%',
        height: '100%',
        display: 'grid',
        gridTemplateRows: 'auto auto 1fr',
        gridTemplateColumns: '100%',
        backgroundColor: 'background.paper',
        padding: '1.5rem',
        borderRadius: '8px',
        border: '1px solid',
        borderColor: 'divider',
        overflow: isLoading ? 'hidden' : 'hidden auto',
        scrollBehavior: 'smooth'
    }
}

export const tabs: CSSObject = {
    marginBottom: '1rem',
}

export const backdrop: CSSObject = {
    color: 'white',
    position: 'absolute',
    borderRadius: '6px',
    zIndex: '100',
}

export const messageAlertDefault: CSSObject = {
    position: 'absolute',
    top: '10%',
    left: '0',
    width: 'calc(100% - 40px)',
    margin: '0 20px',
    backgroundColor: 'grey.200',
    gap: '10px',
    color: 'text.primary',
    '& svg': {
        fill: 'orange',
    },
}

export const notificationTypeHeader: CSSObject = {
    color: 'text.contrast',
    fontWeight: 'medium',
    fontSize: '16px',
    marginBottom: '0.25rem',
}

export const notificationTypeDescription: CSSObject = {
    color: 'text.secondary',
    alignItems: 'center',
    fontSize: '14px',
    margin: '0px',
}