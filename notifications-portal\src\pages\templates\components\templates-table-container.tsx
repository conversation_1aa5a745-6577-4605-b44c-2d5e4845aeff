import { TemplatesContextParams } from '@/common/contexts/TemplatesContext'
import { AuthGuardResult, useAuthGuard } from '@/common/hooks/useAuthGuard'
import { ClnTabs } from '@celanese/ui-lib'
import { Backdrop, Box, CircularProgress, Typography } from '@mui/material'
import React, { useCallback, useEffect, useState } from 'react'
import TemplatesTable from './templates-table'
import * as styles from './templates-table-container.styles'
import { ActionsContextParams } from '@/common/contexts/ActionsContext'
import { translate } from '@celanese/celanese-sdk'

interface tabItem {
    label: string
    oficialLabel: string
    content: any
}

function handleAuthorizedTabs(
    checkPermissionsFromComponentsPerApplication: (componentName: string, application?: string) => AuthGuardResult,
    tabs: tabItem[],
    selectedApplication: string | undefined
) {
    const authorizedTabs: tabItem[] = []
    tabs.forEach((item) => {
        const authorized = checkPermissionsFromComponentsPerApplication(item.oficialLabel, selectedApplication)
        if (authorized.componentName == item.oficialLabel && authorized.isAuthorized) {
            authorizedTabs.push(item)
        }
    })
    return authorizedTabs
}

export default function TemplatesTableContainer() {
    const tabs: tabItem[] = [
        {
            label: translate('app.templates.adminTemplates'),
            oficialLabel: 'Admin Templates',
            content: <TemplatesTable isAdminLevel={true} key="admin-templates" />,
        },
        {
            label: translate('app.templates.myTemplates'),
            oficialLabel: 'My Templates',
            content: <TemplatesTable isAdminLevel={false} key="my-templates" />,
        },
    ]

    const cachedCheckForPermissions = useCallback(useAuthGuard().checkPermissionsFromComponentsPerApplication, [])
    const { isLoading, setCurrentPageInfiniteScroll, tabIndex, setTabIndex, selectedApplication, setSearch, notificationTypeSelected, setCurrentCursor, setHasNextPage } = TemplatesContextParams()
    const { isLoadingDelete } = ActionsContextParams()

    const [authorizedTab, setAuthorizedTab] = useState<tabItem[]>([])

    const handleChange = (event: React.SyntheticEvent, newValue: number) => {
        setTabIndex(newValue)
        setCurrentPageInfiniteScroll(0)
        setCurrentCursor(undefined)
        setHasNextPage(false)
        setSearch('')
    }

    useEffect(() => {
        setAuthorizedTab(handleAuthorizedTabs(cachedCheckForPermissions, tabs, selectedApplication))
        if (authorizedTab.length > 0 && tabIndex > authorizedTab.length - 1) {
            setTabIndex(0)
        }
    }, [selectedApplication, cachedCheckForPermissions, authorizedTab.length])

    useEffect(() => {
        const container = document.getElementById('container')
        if (isLoading && container) {
            container.scrollTop = 0
        }
    }, [isLoading])

    return (
        <Box id="container" sx={styles.container(isLoading)}>
            <div>
                {notificationTypeSelected && (
                    <>
                        <Typography sx={styles.notificationTypeHeader}>{notificationTypeSelected.name}</Typography>
                        <Typography sx={styles.notificationTypeDescription}>{notificationTypeSelected.description}</Typography>
                    </>
                )}
            </div>
            <ClnTabs value={tabIndex} tabs={authorizedTab} onChange={handleChange} sxProps={styles.tabs} />
            <Backdrop sx={styles.backdrop} open={isLoading || isLoadingDelete}>
                <CircularProgress color="inherit" />
            </Backdrop>
        </Box>
    )
}
