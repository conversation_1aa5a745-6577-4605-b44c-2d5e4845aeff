import { CSSObject } from '@emotion/react'

export function tableContainer(enableCreate: boolean, numberOfColumns: number, tableWidth?: number) {
    let columnWidth

    if (numberOfColumns == 4) {
        columnWidth = tableWidth ? 0.25 * (tableWidth - 7) : 'initial'
    } else {
        const widthFactor = tableWidth && tableWidth < 1000 ? 0.200 : 0.142
        columnWidth = tableWidth ? widthFactor * (tableWidth - 7) : 'initial'
    }
    return {
        width: '100%',
        height: '100%',
        display: 'grid',
        gridTemplateRows: '40px 0px 1fr',
        gridTemplateColumns: '100%',
        '& .MuiPaper-root': {
            boxShadow: 'none',
            display: 'flex',
            flexDirection: 'column',
            '& div > table': {
                width: '100%',
            },
            '& div > table > tbody > tr > tr': {
                width: '100%',

                '& > td': {
                    width: columnWidth,
                    maxWidth: columnWidth,
                    minWidth: columnWidth,

                    '& > span > p': {
                        backgroundColor: 'background.paper',
                    },
                },
            },
            '& div > table > thead > thead > tr': {
                width: '100%',

                '& > th': {
                    width: columnWidth,
                    maxWidth: columnWidth,
                    minWidth: columnWidth,

                    '& > span > p': {
                        backgroundColor: 'background.paper',
                    },
                },
            },
            '& > .MuiBox-root': {
                backgroundColor: 'background.paper',
            },
        },
        ...(!enableCreate && {
            '& > div:first-of-type': {
                '& > div:nth-of-type(4) > button': {
                    cursor: 'not-allowed',
                    pointerEvents: 'none',
                    backgroundColor: 'action.disabled',
                },
            },
        }),
    } as CSSObject
}

export const tableContent: CSSObject = {
    height: '100%',
    backgroundColor: 'background.paper',
}

export const searchField: CSSObject = {
    width: '340px',
}

export const messageAlertDefault: CSSObject = {
    position: 'absolute',
    top: '50%',
    left: '0',
    width: 'calc(100% - 6rem)',
    margin: '0 3rem',
    gap: '10px',
    '& svg': {
        fill: 'orange',
    },
}
