import { ActionsContextParams } from '@/common/contexts/ActionsContext'
import { TemplatesContextParams } from '@/common/contexts/TemplatesContext'
import { useAuthGuard } from '@/common/hooks/useAuthGuard'
import { stringIsNotNullOrEmpty } from '@/common/utils/utils'
import { ClnDialog, ClnTable, ClnTooltip, MatIcon } from '@celanese/ui-lib'
import { ActionButton } from '@celanese/ui-lib'
import { Box } from '@mui/material'
import { useRouter } from 'next/router'
import { useEffect, useRef, useState } from 'react'
import useTemplatesTableLogic from '../hooks/useTemplatesTableLogic'
import TemplatesFilter from './templates-filter'
import * as styles from './templates-table.styles'
import { translate } from '@celanese/celanese-sdk'

export default function TemplatesTable({ isAdminLevel }: { isAdminLevel: boolean }) {

    const {
        response,
        handleCloseMenu,
        handleClickMenu,
        anchorEl,
        rows,
        order,
        orderBy,
        handleRequestSort,
        setSearch,
        headCells,
        notificationType,
        currentPageInfiniteScroll,
        handlePageChangeInfiniteScroll,
        rowsPerPageOptions,
        rowsPerPageInfiniteScroll,
        handleRowsPerPageChangeInfiniteScroll,
        calculateTotalPages,
        visibleRowsInfiniteScroll,
        scrollListenerInfiniteScroll,
        loadingInfinityScroll,
    } = useTemplatesTableLogic(isAdminLevel)

    const { checkPermissionsFromComponentsPerApplication } = useAuthGuard()
    const { isOpen, handleClose, confirmDeletion, setActionApplication } = ActionsContextParams()

    const { selectedApplication, setCurrentCursor, setHasNextPage } = TemplatesContextParams()
    const [enableCreateTemplate, setEnableCreateTemplate] = useState<boolean>(false)
    const [buttonName, setButtonName] = useState<string>('')

    const router = useRouter()
    setActionApplication(selectedApplication ?? '')

    const handleCreateTemplateClick = () => {
        if (selectedApplication) {
            router.push({
                pathname: '/customize-templates',
                query: { type: notificationType, a: btoa(String(isAdminLevel)), f: btoa('edit') },
            })
        }
    }

    useEffect(() => {
        sessionStorage.setItem('isAdminLevel', String(isAdminLevel))
        if (isAdminLevel) {
            setButtonName('Create Admin Template')
        } else {
            setButtonName('Create Individual Template')
        }
        const authorizedButton = checkPermissionsFromComponentsPerApplication(buttonName, selectedApplication)
        if (authorizedButton.componentName == buttonName) {
            setEnableCreateTemplate(stringIsNotNullOrEmpty(notificationType) && authorizedButton.isAuthorized)
        }
    }, [
        isAdminLevel,
        enableCreateTemplate,
        buttonName,
        setEnableCreateTemplate,
        setButtonName,
        checkPermissionsFromComponentsPerApplication,
    ])

    enum TableTranslateKey {
        Search = 'Search',
    }

    const translatedLabels = new Map<TableTranslateKey, string>()
    translatedLabels.set(TableTranslateKey.Search, translate('app.common.search'))

    const buttons: ActionButton[] = [
        {
            label: '',
            variant: 'text',
            icon: (
                <ClnTooltip arrow={true} title={translate('app.templates.buttons.enableCreateTemplateInfo')}>
                    <span style={{ display: 'flex' }}><MatIcon icon='info' /></span>
                </ClnTooltip>
            ),
            onClick: () => { },
        },
        {
            label: translate('app.templates.buttons.createTemplate'),
            variant: 'contained',
            onClick: handleCreateTemplateClick,
            disabled: !enableCreateTemplate,
        },
    ]

    const divRef = useRef<HTMLDivElement>(null)

    return response ? (
        <>
            <Box ref={divRef} sx={{ height: '100%', width: '100%' }} id="table-container">
                <ClnTable
                    isSelect={false}
                    isCollapsible={false}
                    toolbar={false}
                    searchBox={true}
                    filter={true}
                    leftButton={false}
                    rightButton={false}
                    sxPropsTableSize={styles.tableContent}
                    sxPropsSearch={styles.searchField}
                    sxProps={styles.tableContainer(enableCreateTemplate, headCells.length, divRef.current?.offsetWidth)}
                    rowsPerPage={rowsPerPageOptions}
                    rows={rows}
                    headCells={headCells}
                    onClickFilter={handleClickMenu}
                    page={currentPageInfiniteScroll}
                    handleChangePage={handlePageChangeInfiniteScroll}
                    rowPageValue={rowsPerPageInfiniteScroll}
                    handleChangeRowsPerPage={handleRowsPerPageChangeInfiniteScroll}
                    totalPages={calculateTotalPages(rows, rowsPerPageInfiniteScroll)}
                    totalItems={rows.length}
                    visibleRows={visibleRowsInfiniteScroll}
                    handleRequestSort={handleRequestSort}
                    order={order}
                    orderBy={orderBy}
                    disableChips={true}
                    onKeyDownSearch={(event: any, property: string) => {
                        setSearch(property)
                        setCurrentCursor(undefined)
                        setHasNextPage(false)
                    } }
                    buttons={buttons}
                    translatedLabels={translatedLabels}
                    infiniteScrollActive
                    loading={loadingInfinityScroll}
                    infiniteScrollListener={scrollListenerInfiniteScroll}
                />
            </Box>
            <TemplatesFilter
                anchorEl={anchorEl}
                handleCloseMenu={handleCloseMenu}
                notificationType={notificationType ?? ''}
                isAdminLevel={isAdminLevel}
            />
            <ClnDialog
                title={translate('app.templates.alerts.deleteTemplate')}
                contentText={translate('app.templates.alerts.AreYouSureYouWantToDeleteThisTemplate')}
                labelLeftButton={translate('app.common.cancel')}
                labelRightButton={translate('app.common.confirm')}
                variantLeftButton='text'
                variantRightButton='outlined'
                open={isOpen}
                handleClose={handleClose}
                leftButtonOnClick={handleClose}
                rightButtonOnClick={() => confirmDeletion(isAdminLevel)}
            />
        </>
    ) : (
        <></>
    )
}
