import { TemplatesContextParams } from '@/common/contexts/TemplatesContext'
import { Application } from '@/common/models/application'
import { useEffect } from 'react'

const createApplicationsDictionary = (applications: Application[] | undefined) => {
    const dict: { [key: string]: string } = {}
    if (applications) {
        for (const iterator of applications) {
            dict[iterator.alias] = iterator.externalId
        }
    }
    return dict
}

export default function useLateralMenuLogic(applications: Application[] | undefined) {
    const applicationsNames = applications?.map((app) => app.alias)
    const applicationsDictionary = createApplicationsDictionary(applications)

    const { setApplicationsIds, selectedApplications, setSelectedApplications } = TemplatesContextParams()

    useEffect(() => {
        if (applications && applicationsNames && selectedApplications.length === 0) {
            setSelectedApplications(applicationsNames)
        }
    }, [applications])

    useEffect(() => {
        if (applications) {
            const selectedIds = selectedApplications.map((application) => applicationsDictionary[application])
            setApplicationsIds(selectedIds)
        }
    }, [selectedApplications])

    return {
        applicationsNames,
        selectedApplications,
        setSelectedApplications,
    }
}
