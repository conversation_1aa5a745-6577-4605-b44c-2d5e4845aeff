import ExpansibleText from '@/common/components/ExpansibleText/expansible-text'
import { TemplatesContextParams } from '@/common/contexts/TemplatesContext'
import { Template } from '@/common/models/template'
import { stringIsNotNullOrEmpty } from '@/common/utils/utils'
import { ClnRows } from '@celanese/ui-lib'
import dayjs from 'dayjs'
import { useEffect, useState, useMemo } from 'react'
import Actions from '../components/actions'
import { NoTranslate, translate } from '@celanese/celanese-sdk'

const createRows = (templates: Template[], isAdminLevel: boolean, translate: any) => {
    const rows: ClnRows[] = templates.map((template) => {
        const sendToText = template.sendTo.join(' | ')
        const sendToColumn = isAdminLevel
            ? [
                  {
                      name: translate('app.templates.table.sendTo'),
                      value: stringIsNotNullOrEmpty(sendToText) ? (
                        <NoTranslate><ExpansibleText text={sendToText} maxWidth={250} /></NoTranslate>
                      ) : (
                          '-'
                      ),
                  },
              ]
            : []

        const editedByColumn = isAdminLevel
            ? [
                  {
                      name: translate('app.templates.table.editedBy'),
                      value: <NoTranslate><ExpansibleText text={template.editedBy} maxWidth={250} /></NoTranslate>,
                  },
              ]
            : []

        const editedAtColumn = isAdminLevel
            ? [
                  {
                      name: translate('app.templates.table.editedAt'),
                      value: (
                          <ExpansibleText text={dayjs(template.editedAt).format('MM/DD/YYYY hh:mm A')} maxWidth={250} />
                      ),
                  },
              ]
            : []

        return {
            id: template.externalId,
            columns: [
                { name: translate('app.templates.table.application'),
                    value: <span className='no-translate'>{template.application}</span> },
                ...sendToColumn,
                {
                    name: translate('app.templates.table.channels'),
                    value: <span className='no-translate'>{template.channels.length > 0 ? template.channels.join(' | ') : '-'}</span>,
                },
                {
                    name: translate('app.templates.table.templateName'),
                    value: <ExpansibleText text={template.templateName} maxWidth={250} isAdminLevel={isAdminLevel} />,
                },
                ...editedByColumn,
                ...editedAtColumn,
                {
                    name: translate('app.templates.table.actions'),
                    value: (
                        <Actions
                            isAdminLevel={isAdminLevel}
                            externalId={template.externalId}
                            creator={template.creator}
                            customChannelEnabled={template.customChannelEnabled}
                            customFrequencyEnabled={template.customFrequencyEnabled}
                            adminLevelFromTemplate={template.adminLevel}
                        />
                    ),
                },
            ],
        }
    })

    return rows
}

const creatHeadCells = (isAdminLevel: boolean, translate: any) => {
    const sendToHead = isAdminLevel ? [{ id: '1', label: translate('app.templates.table.sendTo'), isSorted: true }] : []
    const editedByHead = isAdminLevel ? [{ id: '4', label: translate('app.templates.table.editedBy'), isSorted: true }] : []
    const editedAtHead = isAdminLevel ? [{ id: '5', label: translate('app.templates.table.editedAt'), isSorted: true }] : []

    return [
        { id: '0', label: translate('app.templates.table.application'), isSorted: true },
        ...sendToHead,
        { id: '2', label: translate('app.templates.channels'), isSorted: true },
        { id: '3', label: translate('app.templates.table.templateName'), isSorted: true },
        ...editedByHead,
        ...editedAtHead,
        { id: '6', label: translate('app.templates.table.actions'), isSorted: false },
    ]
}

export default function useTemplatesTableLogic(isAdminLevel: boolean) {

    const {
        response,
        order,
        setOrder,
        orderBy,
        setOrderBy,
        setSearch,
        setIsAdminLevel,
        notificationType,
        applicationName,
        notificationTypeName,
        isLoading,
        currentPageInfiniteScroll,
        setCurrentPageInfiniteScroll,
        rowsPerPageOptions,
        rowsPerPageInfiniteScroll,
        setRowsPerPageInfiniteScroll,
        getTemplatesData,
        hasNextPage,
        loadingInfinityScroll,
    } = TemplatesContextParams()

    useEffect(() => {
        setIsAdminLevel(isAdminLevel)
    }, [setIsAdminLevel, isAdminLevel])

    const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null)

    const rows: ClnRows[] = response ? createRows(response, isAdminLevel, translate) : []

    const headCells = creatHeadCells(isAdminLevel, translate)

    const handleRequestSort = (property: string) => {
        const isAsc = orderBy === property && order === 'asc'
        setOrder(isAsc ? 'desc' : 'asc')
        setOrderBy(property)
    }

    const handlePageChangeInfiniteScroll = (_event: React.ChangeEvent<unknown>, page: number) => {
        setCurrentPageInfiniteScroll(page - 1)
    }

    const handleCloseMenu = () => {
        setAnchorEl(null)
    }

    const handleClickMenu = (event: React.MouseEvent<HTMLElement>) => {
        setAnchorEl(event.currentTarget)
    }

    const handleRowsPerPageChangeInfiniteScroll = (event: React.ChangeEvent<HTMLInputElement>) => {
        setRowsPerPageInfiniteScroll(parseInt(event.target.value))
        setCurrentPageInfiniteScroll(0)
    }

    function calculateTotalPages(rows: ClnRows[], rowPageValue: number) {
        return (rows.length / rowPageValue) % 1 === 0
            ? rows.length / rowPageValue
            : Math.floor(rows.length / rowPageValue) + 1
    }

    const getRowsInfiniteScroll = () => {
        return rows.slice(0, currentPageInfiniteScroll * rowsPerPageInfiniteScroll + rowsPerPageInfiniteScroll)
    }

    const visibleRowsInfiniteScroll = useMemo(
        () => getRowsInfiniteScroll(),
        [rows, currentPageInfiniteScroll, rowsPerPageInfiniteScroll]
    )

    const scrollListenerInfiniteScroll = () => {

        if (hasNextPage && !loadingInfinityScroll && visibleRowsInfiniteScroll.length >= 999) {
            getTemplatesData(true)
            setCurrentPageInfiniteScroll(currentPageInfiniteScroll + 1)
        }

        if (rows.length > 24 && visibleRowsInfiniteScroll.length !== rows.length) {
            getRowsInfiniteScroll()
            setCurrentPageInfiniteScroll(currentPageInfiniteScroll + 1)
        }
        
        return
    }

    return {
        response,
        handleCloseMenu,
        handleClickMenu,
        anchorEl,
        rows,
        order,
        orderBy,
        handleRequestSort,
        setSearch,
        headCells,
        notificationType,
        applicationName,
        notificationTypeName,
        isLoading,
        currentPageInfiniteScroll,
        handlePageChangeInfiniteScroll,
        rowsPerPageOptions,
        rowsPerPageInfiniteScroll,
        handleRowsPerPageChangeInfiniteScroll,
        calculateTotalPages,
        visibleRowsInfiniteScroll,
        scrollListenerInfiniteScroll,
        loadingInfinityScroll,
    }
}
