import { ActionsContextProvider } from '@/common/contexts/ActionsContext'
import { Backdrop, Box, CircularProgress, Typography } from '@mui/material'
import LateralMenu from './components/lateral-menu'
import TemplatesTableContainer from './components/templates-table-container'
import * as styles from './index.styles'
import { translate, TranslationContext, TranslationContextState } from '@celanese/celanese-sdk'

import { ApplicationContextParams } from '@/common/contexts/ApplicationContext'
import { useContext, useEffect } from 'react'

import useLateralMenuLogic from './hooks/useLateralMenuLogic'

export default function Templates() {

    const { locale } = useContext<TranslationContextState>(TranslationContext)
    useEffect(() => {
        const checkLocalStorage = () => {
            const localeData = window.localStorage?.getItem('LocaleData')
            const translationData = window.localStorage?.getItem(
                `APP-NTFTranslationData${localeData}`,
            )
            if (!localeData || !translationData) {
                setTimeout(checkLocalStorage, 200)
            }
            else {
                document.title = translate('app.templates.title') + ' | Notifications Portal'
            }
        }
        checkLocalStorage()
    }, [locale])

    
    const { loading, applications } = ApplicationContextParams()

    const { applicationsNames, selectedApplications, setSelectedApplications } = useLateralMenuLogic(applications)

    useEffect(() => {
        applications && selectedApplications.length < applications.length && setSelectedApplications(applicationsNames || [])
    }, [])


    return (
        <Box sx={styles.pageContainer}>
            <ActionsContextProvider>
                <Typography sx={styles.templatesHeader}>{translate('app.templates.title')}</Typography>
                {!loading ? (
                    <Box sx={styles.contentContainer}>
                        <LateralMenu applications={applications} />
                        <TemplatesTableContainer />
                    </Box>
                ) : (
                    <Backdrop open={loading} sx={{ zIndex: 5 }}>
                        <CircularProgress color="inherit" />
                    </Backdrop>
                )}
            </ActionsContextProvider>
        </Box>
    )
}
