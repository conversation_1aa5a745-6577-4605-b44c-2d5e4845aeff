import { CSSObject } from '@emotion/react'

export const pageContainer: CSSObject = {
    display: 'grid',
    gridTemplateRows: 'auto 1fr',
    height: '100%',
    width: '100%',
    overflowY: 'hidden',
}

export const contentContainer: CSSObject = {
    display: 'grid',
    gridTemplateColumns: 'auto 1fr',
    gap: '1rem',
    height: '100%',
    width: '100%',
    overflow: 'hidden',
}

export const templatesHeader: CSSObject = {
    color: 'primary.main',
    fontWeight: 'bold',
    fontSize: '1.5rem',
    marginBottom: '1rem',
    lineHeight: '32px',
}
