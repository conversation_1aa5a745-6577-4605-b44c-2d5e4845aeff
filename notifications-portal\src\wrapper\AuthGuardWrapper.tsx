import React, { useEffect, useState, ReactNode } from 'react'
import { usePermissions } from '@/common/contexts/AuthGuardContext'
import ErrorScreen from '@/common/components/ErrorScreen'

interface AuthGuardWrapperProps {
    children: ReactNode
    componentName: string
    application: string
    returnMessage: boolean
}

const AuthGuardWrapper: React.FC<AuthGuardWrapperProps> = ({ children, componentName, application, returnMessage }) => {
    const { checkPermissions } = usePermissions()
    const [hasPermission, setHasPermission] = useState(false)
    const [errorMessage, setErrorMessage] = useState<string>('')

    useEffect(() => {
        const nameToCheck = componentName || 'UnknownComponent'
        const appToCheck = application || ''

        try {
            const userPermission = checkPermissions(nameToCheck, appToCheck)
            if (userPermission !== undefined) {
                setHasPermission(userPermission.isAuthorized)

                if (!userPermission.isAuthorized) {
                    const message = userPermission.message
                    setErrorMessage(message)
                }
            }
        } catch (e) {
            console.error('Error fetching permissions:', e)
        }
    }, [componentName, checkPermissions])

    if (hasPermission) return <>{children}</>
    else if (!hasPermission && returnMessage) return <ErrorScreen />

    return null
}

export default AuthGuardWrapper
