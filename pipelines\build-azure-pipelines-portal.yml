trigger: none

variables:
  - group: components-licenses-keys
  - group: templates-common
  - name: nodeVersion
    value: '22.14.0'
  - name: buildPath
    value: $(System.DefaultWorkingDirectory)/notifications-portal
  - name: packageType
    value: 'npm'
  - name: environment
    value: 'dev'
  - name: useEnvInBuild
    value: 'yes'
  - name: npmrcPath
    value: "$(System.DefaultWorkingDirectory)/notifications-portal/.npmrc"
  - name: appType
    value: 'portal'

resources:
  repositories:
    - repository: templates
      type: git
      name: Templates  
      ref: dev  
      clean: true  

pool:
  vmImage: "ubuntu-latest"

stages:
  - stage: BuildPortal
    jobs:
      - job: Build
        displayName: Build Portal
        steps:
          - template: build/template-build-react.yml@templates