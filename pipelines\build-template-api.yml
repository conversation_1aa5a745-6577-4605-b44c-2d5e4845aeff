steps:
  - task: UsePythonVersion@0
    inputs:
      versionSpec: '3.11'
      addToPath: true
  
  - script: |
      pip3 download -r requirements.txt -d ./packages
    displayName: "Download dependencies"
    workingDirectory: $(System.DefaultWorkingDirectory)/notification-api

  - script: |
      sed -i '1i --find-link packages' ./requirements.txt
    displayName: "Add Find-Link to requirements.txt"    
    workingDirectory: $(System.DefaultWorkingDirectory)/notification-api