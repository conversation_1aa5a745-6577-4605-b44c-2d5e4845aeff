steps:
  - task: npmAuthenticate@0
    inputs:
      workingFile: 'notifications-portal/.npmrc'
  
  - task: NodeTool@0
    inputs:
      versionSpec: "22.14.0"
    displayName: "Install Node.js"   

  - script: |
      cd $(System.DefaultWorkingDirectory)/notifications-portal
      echo "NEXT_PUBLIC_MUI_LICENSE_KEY=$(NEXT_PUBLIC_MUI_LICENSE_KEY)" >> .env
    displayName: 'Set env vars' 

  - script: |
      cd $(System.DefaultWorkingDirectory)/notifications-portal
      npm install
      npm run build --verbose
    displayName: "npm install and build the Portal"