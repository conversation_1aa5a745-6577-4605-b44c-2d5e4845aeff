parameters:
  - name: DeploymentEnvironment
    type: string
  - name: ContainerRegistry
    type: string
  - name: AppServiceSubscription
    type: string
  - name: DockerNamespace
    type: string

stages:
- stage: Build
  displayName: Build and push stage
  jobs:
  - job: BuildAndPush
    displayName: Build API
    pool:
      name: "GST-Backend-Linux"
    steps:

    - task: Docker@2
      displayName: Build and push Notifications API image to container registry
      inputs:
        command: buildAndPush
        repository: notifications/${{ parameters.DeploymentEnvironment }}/api
        buildContext: $(Build.SourcesDirectory)/notification-api
        dockerfile: $(Build.SourcesDirectory)/notification-api/Dockerfile
        containerRegistry: ${{ parameters.ContainerRegistry }}
        tags: |
          $(Build.BuildId)

- stage: Deploy
  displayName: Deploy stage
  condition: succeeded('Build')
  jobs:
  - deployment: Deploy
    environment: notifications-${{ parameters.DeploymentEnvironment }}
    displayName: 'Deploy job'
    pool:
      name: "GST-Backend-Linux"
    strategy:
      runOnce:
        deploy:
          steps:
          - checkout: self
          - task: AzureRMWebAppDeployment@4
            displayName: Azure App Service Deploy
            inputs:
              appType: webAppContainer
              azureSubscription: ${{ parameters.AppServiceSubscription }}
              WebAppName: 'app-dplantnotificationsapi-${{ parameters.DeploymentEnvironment }}-ussc-01'
              DockerNamespace: ${{ parameters.DockerNamespace }}
              DockerRepository: notifications/${{ parameters.DeploymentEnvironment }}/api
              DockerImageTag: $(Build.BuildId)
