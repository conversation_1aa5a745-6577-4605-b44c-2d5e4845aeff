parameters:
  - name: DeploymentEnvironment
    type: string
  - name: ContainerRegistry
    type: string
  - name: AppServiceSubscription
    type: string
  - name: DockerNamespace
    type: string

stages:
- stage: Build
  displayName: Build and push stage
  jobs:
  - job: BuildAndPush
    displayName: Build Frontend
    pool:
      name: "GST-Backend-Linux"
    steps:

    - task: npmAuthenticate@0
      displayName: Authenticate with private NPM registry
      inputs:
        workingFile: '$(Build.SourcesDirectory)/notifications-portal/.npmrc'

    - task: Docker@2
      displayName: Build
      inputs:
        command: build
        containerRegistry: ${{ parameters.ContainerRegistry }}
        repository: notifications/${{ parameters.DeploymentEnvironment }}/portal
        buildContext: $(Build.SourcesDirectory)/notifications-portal
        dockerfile: $(Build.SourcesDirectory)/notifications-portal/dockerfile
        tags: |
          $(Build.BuildId)
        arguments: |
          --build-arg ARG_ENVIRONMENT=$(DeploymentEnvironment) 
    
    - task: Docker@2
      displayName: Push
      inputs:
        command: push
        containerRegistry: ${{ parameters.ContainerRegistry }}
        repository: notifications/${{ parameters.DeploymentEnvironment }}/portal
        tags: |
          $(Build.BuildId)

- stage: Deploy
  displayName: Deploy stage
  condition: succeeded('Build')
  jobs:
  - deployment: Deploy
    environment: notifications-${{ parameters.DeploymentEnvironment }}
    displayName: 'Deploy job'
    pool:
      name: "GST-Backend-Linux"
    strategy:
      runOnce:
        deploy:
          steps:
          - checkout: self
          - task: AzureRMWebAppDeployment@4
            displayName: Azure App Service Deploy
            inputs:
              appType: webAppContainer
              azureSubscription: ${{ parameters.AppServiceSubscription }}
              WebAppName: 'app-dplantnotificationsportal-${{ parameters.DeploymentEnvironment }}-ussc-01'
              DockerNamespace: ${{ parameters.DockerNamespace }}
              DockerRepository: notifications/${{ parameters.DeploymentEnvironment }}/portal
              DockerImageTag: $(Build.BuildId)
