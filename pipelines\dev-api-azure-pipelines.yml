trigger:
  branches:
    include:
      - dev
  paths:
    include:
      - notification-api/*

variables:
  - group: notifications-dev
  - group: notifications-common

stages:
- template: container-template-pipeline-api.yml
  parameters:
    DeploymentEnvironment: $(deploymentEnvironment)
    ContainerRegistry: $(containerRegistry)
    AppServiceSubscription: $(environmentAppServiceSubscription)
    DockerNamespace: $(dockerNamespace)