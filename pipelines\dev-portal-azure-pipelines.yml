trigger:
  branches:
    include:
      - dev

variables:
  - group: components-licenses-keys
  - group: templates-common
  - name: azureSubscription
    value: 'Notifications Deploy - DEV'
  - name: webAppName
    value: 'app-dplantnotificationsportal-d-ussc-01'
  - name: nodeVersion
    value: '22.14.0'
  - name: buildPath
    value: "$(System.DefaultWorkingDirectory)/notifications-portal"
  - name: packageType
    value: 'npm'
  - name: environment
    value: 'dev'
  - name: packageToDeployPath
    value: "$(System.DefaultWorkingDirectory)/notifications-portal"
  - name: useEnvInBuild
    value: 'yes'
  - name: npmrcPath
    value: "$(System.DefaultWorkingDirectory)/notifications-portal/.npmrc"
  - name: appType
    value: 'portal'

pool:
  name: "GST-Frontend-Linux"
  
resources:
  repositories:
    - repository: templates
      type: git
      name: Templates  
      ref: dev
      clean: true 

stages:
- template: deploy/template-deploy-node-code.yml@templates