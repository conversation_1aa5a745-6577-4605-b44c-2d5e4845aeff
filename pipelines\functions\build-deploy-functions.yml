variables:
  - group: notifications-functions
  - name: skipLintTest
    value: $(SKIP_LINT_TEST)
  - name: celaneseProject
    ${{ if eq(variables['Build.SourceBranchName'], 'qa') }}:
      value: celanese-stg
    ${{ elseif eq(variables['Build.SourceBranchName'], 'dev') }}:
      value: celanese-dev
    ${{ elseif eq(variables['Build.SourceBranchName'], 'prod') }}:
      value: celanese
    ${{ else }}:
      value: celanese-dev
  - name: dataSetId
    ${{ if eq(variables['Build.SourceBranchName'], 'qa')}}:
      value: $(COGNITE_DATA_SET_ID_QA)
    ${{ elseif eq(variables['Build.SourceBranchName'], 'dev') }}:
      value: $(COGNITE_DATA_SET_ID_DEV)
    ${{ elseif eq(variables['Build.SourceBranchName'], 'prod') }}:
      value: $(COGNITE_DATA_SET_ID_PROD)
    ${{ else }}:
      value: $(COGNITE_DATA_SET_ID_DEV)
  - name: authSecret
    ${{ if eq(variables['Build.SourceBranchName'], 'qa') }}:
      value: $(S_AUTH_SECRET_QA)
    ${{ elseif eq(variables['Build.SourceBranchName'], 'dev') }}:
      value: $(S_AUTH_SECRET_DEV)
    ${{ elseif eq(variables['Build.SourceBranchName'], 'prod')}}:
      value: $(S_AUTH_SECRET_PROD)
    ${{ else }}:
      value: $(S_AUTH_SECRET_DEV)
  - name: authClientId
    ${{ if eq(variables['Build.SourceBranchName'], 'qa') }}:
      value: $(AUTH_CLIENT_ID_QA)
    ${{ elseif eq(variables['Build.SourceBranchName'], 'dev') }}:
      value: $(AUTH_CLIENT_ID_DEV)
    ${{ elseif eq(variables['Build.SourceBranchName'], 'prod') }}:
      value: $(AUTH_CLIENT_ID_PROD)
    ${{ else }}:
      value: $(AUTH_CLIENT_ID_DEV)
  - name: functionAuthClientId
    ${{ if eq(variables['Build.SourceBranchName'], 'prod') }}:
      value: $(FUNCTION_AUTH_CLIENT_ID_PROD)
    ${{ else }}:
      value: $(FUNCTION_AUTH_CLIENT_ID_DEV)
  - name: functionAuthSecret
    ${{ if eq(variables['Build.SourceBranchName'], 'prod') }}:
      value: $(S_FUNCTION_AUTH_SECRET_PROD)
    ${{ else }}:
      value: $(S_FUNCTION_AUTH_SECRET_DEV)
  - name: logicAppUrl
    ${{ if eq(variables['Build.SourceBranchName'], 'prod') }}:
      value: $(S_LOGIC_APP_URL_PROD)
    ${{ else }}:
      value: $(S_LOGIC_APP_URL_DEV)
  - name: smsConnectionUrl
    value: $(S_SMS_CONNECTION_URL)
  - name: smsFromNumber
    ${{ if eq(variables['Build.SourceBranchName'], 'prod') }}:
      value: $(S_SMS_FROM_NUMBER_PROD)
    ${{ else }}:
      value: $(S_SMS_FROM_NUMBER_DEV)
  - name: singleEnvironmentId
    ${{ if eq(variables['Build.SourceBranchName'], 'prod') }}:
      value: 'PROD'
    ${{ else }}:
      value: 'DEV'
  - name: dataModelVersion
    ${{ if eq(variables['Build.SourceBranchName'], 'qa') }}:
      value: $(DATAMODEL_VERSION_QA)
    ${{ elseif eq(variables['Build.SourceBranchName'], 'dev') }}:
      value: $(DATAMODEL_VERSION_DEV)
    ${{ elseif eq(variables['Build.SourceBranchName'], 'prod') }}:
      value: $(DATAMODEL_VERSION_PROD)
    ${{ else }}:
      value: $(DATAMODEL_VERSION_DEV)


stages:
  - stage: LintAndTestFunction
    displayName: 'Run Lint and Pytest for ${{ variables.celaneseProject }} Project'
    jobs:
      - job: LintAndTest
        condition: ne(variables['SKIP_LINT_TEST'],1)
        steps:
        - task: UsePythonVersion@0
          inputs:
            versionSpec: "$(PYTHON_VERSION)"
          displayName: "Use python $(PYTHON_VERSION)"

        - script: |
            cd $(System.DefaultWorkingDirectory)/$(FUNCTION_PATH)
            python -m pip install --upgrade pip
            pip install -r requirements.txt
          displayName: 'Install function dependencies'

        - script: |
            cd $(System.DefaultWorkingDirectory)/$(FUNCTION_PATH)
            source ./scripts/lint.sh
          displayName: 'Lint $(FUNCTION_NAME)'
        
        - script: |
            cd $(System.DefaultWorkingDirectory)/$(FUNCTION_PATH)
            pytest
          displayName: 'Testing $(FUNCTION_NAME)'


  - stage: PublishCogniteFunction
    dependsOn: LintAndTestFunction
    displayName: "Publish Function to Cognite for ${{ variables.celaneseProject }} Project"
    jobs:
      - job: PublishFunction
        timeoutInMinutes: 10
        cancelTimeoutInMinutes: 2
        steps:
          - task: UsePythonVersion@0
            inputs:
              versionSpec: "3.10"
            displayName: "Use python 3.10"
          
          - script: |
              echo $DATAMODEL_VERSION
            displayName:
              'Check Notification Data Model reference version'
            env:
              DATAMODEL_VERSION: ${{ variables.dataModelVersion}}

          - script: |
              cd $(System.DefaultWorkingDirectory)/notification-function-deployment
              python -m pip install --upgrade pip
              pip install -r requirements.txt
            displayName: 'Install dependencies'

          - script: |
              cd $(System.DefaultWorkingDirectory)/notification-function-deployment
              python -m upload-cognite-function $(FUNCTION_NAME)
            displayName: 'Deploy $(FUNCTION_NAME)'
            env:
              FUNCTION_AUTH_CLIENT_ID: ${{ variables.functionAuthClientId }}
              FUNCTION_AUTH_SECRET: ${{ variables.functionAuthSecret }}
              AUTH_SECRET: ${{ variables.authSecret }}
              AUTH_CLIENT_ID: ${{ variables.authClientId }}
              COGNITE_PROJECT: ${{ variables.celaneseProject }}
              COGNITE_DATA_SET_ID: ${{ variables.dataSetId }}
              COGNITE_GRAPHQL_URI: https://az-eastus-1.cognitedata.com/api/v1/projects/${{ variables.celaneseProject }}/userapis/spaces/NTF-COR-ALL-DMD/datamodels/NotificationDOM/versions/${{ variables.dataModelVersion}}/graphql
              COGNITE_GRAPHQL_APMAPPDATA_URI: https://az-eastus-1.cognitedata.com/api/v1/projects/${{ variables.celaneseProject }}/userapis/spaces/cdf_apm/datamodels/ApmAppData/versions/$(APMAPPDATA_DATAMODEL_VERSION)/graphql
              LOGIC_APP_URL: ${{ variables.logicAppUrl }}
              SMS_CONNECTION_URL: ${{ variables.smsConnectionUrl }}
              SMS_FROM_NUMBER: ${{ variables.smsFromNumber }}
              ENVIRONMENT_ID: ${{ variables.singleEnvironmentId}}
              DATAMODEL_VERSION: ${{ variables.dataModelVersion}}
