trigger:
  branches:
    include:
      - prod
  paths:
    include:
      - notification-api/*

variables:
  - group: notifications-prod
  - group: notifications-common

stages:
- template: container-template-pipeline-api.yml
  parameters:
    DeploymentEnvironment: $(deploymentEnvironment)
    ContainerRegistry: $(containerRegistry)
    AppServiceSubscription: $(environmentAppServiceSubscription)
    DockerNamespace: $(dockerNamespace)