trigger:
  branches:
    include:
      - prod
  paths:
    include:
      - notifications-portal/*

variables:
  - group: notifications-prod
  - group: notifications-common
  - group: components-licenses-keys
  - group: templates-common
  - name: azureSubscription
    value: 'Notifications Deploy - PROD'
  - name: webAppName
    value: 'app-dplantnotificationsportal-p-ussc-01'
  - name: rootFolderOrFile
    value: 'notifications-portal'
  - name: appName
    value: 'notifications'
  - name: appType
    value: 'portal'
  - name: environment
    value: 'p'

pool:
  name: "GST-Frontend-Linux"

resources:
  repositories:
    - repository: templates
      type: git
      name: Templates  
      ref: dev
      clean: true  
      
stages:
- template: deploy/template-deploy-all-container.yml@templates 
    