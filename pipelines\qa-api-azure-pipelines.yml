trigger:
  branches:
    include:
      - qa
  paths:
    include:
      - notification-api/*

variables:
  - group: notifications-qa
  - group: notifications-common

stages:
- template: container-template-pipeline-api.yml
  parameters:
    DeploymentEnvironment: $(deploymentEnvironment)
    ContainerRegistry: $(containerRegistry)
    AppServiceSubscription: $(environmentAppServiceSubscription)
    DockerNamespace: $(dockerNamespace)