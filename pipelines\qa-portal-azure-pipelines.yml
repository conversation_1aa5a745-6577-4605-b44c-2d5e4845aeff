trigger:
  branches:
    include:
      - qa
  paths:
    include:
      - notifications-portal/*

variables:
  - group: notifications-qa
  - group: notifications-common
  - group: components-licenses-keys
  - group: templates-common
  - name: azureSubscription
    value: 'Notifications Deploy - QA'
  - name: webAppName
    value: 'app-dplantnotificationsportal-qa-ussc-01'
  - name: rootFolderOrFile
    value: 'notifications-portal'
  - name: appName
    value: 'notifications'
  - name: appType
    value: 'portal'
  - name: environment
    value: 'qa'

pool:
  name: "GST-Frontend-Linux"

resources:
  repositories:
    - repository: templates
      type: git
      name: Templates  
      ref: dev
      clean: true  
      
stages:
- template: deploy/template-deploy-all-container.yml@templates 
    