parameters:
  - name: AzureSubscription
    type: string
  - name: AppType
    type: string
  - name: WebAppName
    type: string
  - name: NodeEnv
    type: string

stages:
- stage: Build
  jobs:
  - job: Build
    displayName: Build API
    pool:
      vmImage: "ubuntu-latest"
    steps:
      - template: build-template-api.yml

      - task: ArchiveFiles@2
        displayName: "Archive files"
        inputs:
          rootFolderOrFile: "$(System.DefaultWorkingDirectory)/notification-api"
          includeRootFolder: false
          archiveType: zip
          archiveFile: $(Build.ArtifactStagingDirectory)/$(Build.BuildId).zip
          replaceExistingArchive: true
            
      - task: PublishPipelineArtifact@1
        displayName: "Publish pipeline artifact"
        inputs:
          targetPath: '$(Pipeline.Workspace)'              
          artifact: 'NotificationsAPI'
          publishLocation: 'pipeline'
  
  - job: Deploy
    displayName: 'Deploy to Azure App Service'
    pool:
      name: "GST-Backend-Linux"
    dependsOn: Build
    steps:
      - task: DownloadPipelineArtifact@2
        inputs:
          buildType: 'current'
          targetPath: '$(Pipeline.Workspace)'
          artifactName: 'NotificationsAPI'

      - task: AzureRmWebAppDeployment@4
        inputs:
          ConnectionType: "AzureRM"
          azureSubscription: '${{ parameters.AzureSubscription }}'
          appType: '${{ parameters.AppType }}'
          WebAppName: '${{ parameters.WebAppName }}'
          packageForLinux: "$(Pipeline.Workspace)/a/$(Build.BuildId).zip"
          startupCommand: 'gunicorn -k uvicorn.workers.UvicornWorker app.main:app'