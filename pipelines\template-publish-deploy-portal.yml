parameters:
  - name: AzureSubscription
    type: string
  - name: AppType
    type: string
  - name: WebAppName
    type: string
  - name: NodeEnv
    type: string

stages:
- stage: Build
  jobs:
  - job: Build
    displayName: Build Portal
    steps:
      - template: build-template-portal.yml

      - task: ArchiveFiles@2
        displayName: "Archive files"
        env:
          NODE_ENV: '${{ parameters.NodeEnv }}'
          NEXT_PUBLIC_NODE_ENV: '${{ parameters.NodeEnv }}'
        inputs:
          rootFolderOrFile: '$(System.DefaultWorkingDirectory)/notifications-portal'
          includeRootFolder: false
          archiveType: zip
          archiveFile: $(Build.ArtifactStagingDirectory)/app.zip
          replaceExistingArchive: true

      - task: AzureRmWebAppDeployment@4
        env:
          NODE_ENV: '${{ parameters.NodeEnv }}'
          NEXT_PUBLIC_NODE_ENV: '${{ parameters.NodeEnv }}'
        inputs:
          ConnectionType: 'AzureRM'
          azureSubscription: '${{ parameters.AzureSubscription }}'
          appType: '${{ parameters.AppType }}'
          WebAppName: '${{ parameters.WebAppName }}'
          packageForLinux: '$(Build.ArtifactStagingDirectory)/**/app.zip'